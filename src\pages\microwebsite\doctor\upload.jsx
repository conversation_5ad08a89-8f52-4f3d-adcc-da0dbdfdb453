import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Tabs, Upload, Button, message, Progress, Modal } from 'antd';
import { Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import loading from '../../../components/loading/Loading';
import * as CONSTANT from '../../../config/constant/constant';
import * as utils from '../../../utils/utils';

const TabPane = Tabs.TabPane;
const Dragger = Upload.Dragger;

export default connect(state => {
  return {
    hisList: state.microwebsite.hospital.list,
    imgData: state.microwebsite.imgData,
  };
})(
  class DepartmentUpload extends React.Component {
    state = {
      status: 1,
      errorText: '操作失败',
      fileList: [],
      errList: [],
      sucessNum: 0,
      sucessList: [],
      showModal: false,
    };

    componentDidMount() {
      const { dispatch, hisList = [] } = this.props;
      if (!hisList || hisList.length == 0) {
        dispatch({
          type: 'microwebsite/hospitalList',
        });
      }
    }
    beforeUpload = (file, fileList) => {
      this.setState({
        fileList,
      });
      const isImage = file.type && file.type.indexOf('image') > -1;
      if (!isImage) {
        message.error('请选择图片进行上传!');
      }
      const size = file.size / 1024 / 1024 < 1;
      if (!size) {
        message.destroy();
        message.error('图片大小不能超过1MB!');
        if (this.state.errList.length + 1 === fileList.length) {
          // 所上传的照片全是大于1M的则清空
          this.setState({
            errList: [],
          });
        } else {
          this.setState({ errList: [...this.state.errList, { name: file.name }] });
        }
        return false;
      }
      const isJPG = file.type == 'image/jpg' || file.type == 'image/jpeg';
      if (!isJPG) {
        message.destroy();
        message.error('请选择.jpg格式的图片进行上传!');
        if (this.state.errList.length + 1 === fileList.length) {
          // 所上传的照片全没通过的则清空
          this.setState({
            errList: [],
          });
        } else {
          this.setState({ errList: [...this.state.errList, { name: file.name }] });
        }
        return false;
      }
      if (fileList.length > 50) {
        message.destroy();
        message.error('上传的文件不能超过50个，请重新选择');
        return false;
      }
      return true;
    };
    autoSyncImg = () => {
      const { dispatch } = this.props;
      const { sucessList = [] } = this.state;
      const doctorName = sucessList.length > 0 ? sucessList.map(item => item.fileName) : [];
      const headImg = sucessList.length > 0 ? sucessList.map(item => item.url) : [];
      this.setState({
        showModal: true,
      });
      new Promise((resolve, reject) => {
        // eslint-disable-line no-new
        dispatch({
          type: 'microwebsite/syncDocImg',
          payload: {
            reqData: {
              doctorName: doctorName.join(';'),
              headImg: headImg.join(';'),
            },
            resolve,
            reject,
          },
        });
      }).then(() => {
        const { imgData = {} } = this.props;
        this.setState({
          showModal: false,
          status: imgData.failCount && imgData.failCount > 0 ? 5 : 4,
        });
      });
    };
    hideModal = () => {
      this.setState({
        fileList: [],
        sucessList: [],
        errList: [],
        status: 2,
        showModal: false,
      });
    };
    render() {
      const { hisList = [], location, dispatch, imgData = {} } = this.props;
      if (!hisList || hisList.length == 0) return null;
      const imgArr = [];
      if (imgData.result) {
        for (let name in imgData.result) {
          imgArr.push({ key: name, value: imgData.result[name] });
        }
      }
      const hisId = utils.queryStringToJson(location.search).hisId || (hisList[0] && hisList[0].hisId);
      return (
        <div className="page-department">
          <Tabs
            animated={false}
            defaultActiveKey={`${hisId}`}
            style={{ display: 'flex', flex: 'auto', flexDirection: 'column' }}
            onChange={() => {
              this.setState({
                status: 1,
              });
            }}
          >
            {hisList.map(item => {
              return (
                <TabPane tab={item.hisName} key={`${item.hisId}`}>
                  {this.state.status == 1 ? (
                    <div style={{ display: 'flex', margin: 24, borderRadius: 4, background: '#fff', textAlign: 'center', padding: '80px 0' }}>
                      <div style={{ flex: 1 }}>
                        <div style={{ margin: '17px auto 9px', width: 380, height: 194 }}>
                          <div style={{ color: '#404040', textAlign: 'left', marginBottom: 17 }}>批量上传医生信息</div>
                          <Dragger
                            name="idFile"
                            multiple={false}
                            showUploadList={false}
                            action={`${CONSTANT.DOMAIN}/api/doctorinfo/batchuploadlist?hisId=${item.hisId}&showMode=microSite`} // eslint-disable-line
                            onChange={info => {
                              loading.show();
                              const { response } = info.file;
                              if (response) {
                                loading.destroy();
                                if (response.code == 0) {
                                  this.setState({
                                    status: 2,
                                  });
                                } else {
                                  this.setState({
                                    status: 3,
                                    errorText: response.msg || '操作失败',
                                  });
                                }
                              }
                            }}
                          >
                            <div>
                              <Icon type="inbox" style={{ fontSize: 40, color: '#3F969D' }} />
                            </div>
                            <p style={{ marginTop: 24 }} className="ant-upload-text">
                              点击这里批量上传医生信息
                            </p>
                          </Dragger>
                        </div>
                        <div style={{ padding: '43px 0 26px 0', display: 'flex', justifyContent: 'center' }}>
                          <div style={{ paddingTop: 1 }}>
                            <Icon type="infocircle" style={{ fontSize: 14, color: '#3F969D', marginRight: 8 }} />
                          </div>
                          <div>
                            此功能只支持使用医生模板编辑的内容，如果您还没有模板，请
                            <a
                              href="./rBAEh1mWZQWAVmkZAAAlCs_X2Pg70.xlsx" // eslint-disable-line
                            >
                              点击下载
                            </a>
                          </div>
                        </div>
                      </div>
                      <div style={{ width: 50, height: 445, borderRight: '1px solid #E9E9E9' }}></div>
                      <div style={{ flex: 1 }}>
                        <div style={{ margin: '17px auto 9px', width: 450, height: 194, paddingRight: 70 }}>
                          <div style={{ color: '#404040', textAlign: 'left', marginBottom: 17 }}>批量上传医生头像</div>
                          <Dragger
                            data={{
                              partnerId: 'merchant',
                              serviceType: 'test',
                            }}
                            beforeUpload={this.beforeUpload}
                            name="upfile"
                            multiple={true}
                            showUploadList={false}
                            listType="picture"
                            accept="image/jpeg"
                            action={`${CONSTANT.DOMAIN}/api/files/uploadpic?hisId=${item.hisId}&showMode=microwebsite`} // eslint-disable-line
                            onChange={info => {
                              loading.show();
                              const { response } = info.file;
                              if (response) {
                                loading.destroy();
                                if (response.code == 0) {
                                  // 前端过滤之后的图片张数等于上传完成的张数
                                  if (this.state.fileList.length - this.state.errList.length === this.state.sucessList.length + 1) {
                                    this.setState({ sucessNum: this.state.sucessNum + 1, sucessList: [...this.state.sucessList, info.file.response.data] });
                                    this.autoSyncImg();
                                  } else {
                                    this.setState({ sucessNum: this.state.sucessNum + 1, sucessList: [...this.state.sucessList, info.file.response.data] });
                                  }
                                } else {
                                  this.setState({
                                    status: 5,
                                    errorText: response.msg || '操作失败',
                                  });
                                }
                              } else {
                                loading.destroy();
                              }
                            }}
                          >
                            <div>
                              <Icon type="inbox" style={{ fontSize: 40, color: '#3F969D' }} />
                            </div>
                            <p style={{ marginTop: 24 }} className="ant-upload-text">
                              点击这里批量上传医生头像
                            </p>
                          </Dragger>
                        </div>
                        <div style={{ padding: '43px 0 26px 0', textAlign: 'left', margin: '0 auto 9px', width: '450px', color: '#646464' }}>
                          <div>1、最多一次选择50张</div>
                          <div>2、图片必须为jpg格式</div>
                          <div>3、图片名称格式为“医生姓名.jpg”，如：王小军.jpg</div>
                          <div>4、图片建议尺寸为295*413（即标准一寸照电子版），单张图片大小不超过1M</div>
                          <div>5、若对应医生已有旧头像，上传图片匹配成功将覆盖旧头像</div>
                          <div>6、重名医生头像，请单独修改</div>
                        </div>
                      </div>
                    </div>
                  ) : this.state.status == 2 ? (
                    <div style={{ flex: 1, margin: 24, borderRadius: 4, background: '#fff', textAlign: 'center' }}>
                      <div style={{ margin: '168px 0 155px', width: 126, height: 240, display: 'inline-block' }}>
                        <div>
                          <Icon type="hook-circle-o" style={{ fontSize: 126, color: '#3F969D' }} />
                        </div>
                        <div style={{ marginTop: 37, fontSize: 18, color: '#404040' }}>成功提交</div>
                        <div style={{ marginTop: 19 }}>
                          <Button
                            onClick={() => {
                              dispatch({
                                type: 'microwebsite/saveDoctor',
                                payload: {
                                  list: [],
                                  detail: {},
                                },
                              });
                              history.push(`/microwebsite/doctor?hisId=${item.hisId}`);
                            }}
                          >
                            返回医生介绍
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : this.state.status == 3 ? (
                    <div style={{ flex: 1, margin: 24, borderRadius: 4, background: '#fff', textAlign: 'center' }}>
                      <div style={{ margin: '168px 0 155px', width: 150, height: 240, display: 'inline-block' }}>
                        <div>
                          <Icon type="sign-circle-o" style={{ fontSize: 126, color: '#F56A00' }} />
                        </div>
                        <div style={{ marginTop: 44, fontSize: 18, color: '#404040' }}>文件异常</div>
                        <div style={{ marginTop: 19, fontSize: 14, color: '#F56A00', textAlign: 'center' }}>
                          <div style={{ display: 'inline-block', textAlign: 'left' }}>{this.state.errorText}</div>
                        </div>
                        <div style={{ marginTop: 23 }}>
                          <Button
                            onClick={() => {
                              this.setState({
                                status: 1,
                              });
                            }}
                          >
                            重新添加
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : this.state.status == 4 ? (
                    <div style={{ flex: 1, margin: 24, borderRadius: 4, background: '#fff', textAlign: 'center' }}>
                      <div style={{ margin: '168px 0 155px', height: 240, display: 'inline-block' }}>
                        <div>
                          <Icon type="hook-circle-o" style={{ fontSize: 126, color: '#3F969D' }} />
                        </div>
                        <div style={{ marginTop: 37, fontSize: 18, color: '#404040' }}>上传成功</div>
                        <div style={{ marginTop: 19, fontSize: 14, color: '#000000', textAlign: 'center' }}>
                          <div style={{ display: 'inline-block', textAlign: 'left' }}>
                            上传图片{this.props.imgData.successCount + this.props.imgData.failCount}张，成功匹配{this.props.imgData.successCount}张
                          </div>
                        </div>
                        <div style={{ marginTop: 19, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          <Button
                            style={{ marginRight: 16 }}
                            onClick={() => {
                              dispatch({
                                type: 'microwebsite/saveDoctor',
                                payload: {
                                  list: [],
                                  detail: {},
                                },
                              });
                              history.push(`/microwebsite/doctor?hisId=${item.hisId}`);
                            }}
                          >
                            返回医生介绍
                          </Button>
                          <Button
                            onClick={() => {
                              this.setState({
                                status: 1,
                                errorText: '操作失败',
                                fileList: [],
                                errList: [],
                                sucessNum: 0,
                                sucessList: [],
                                showModal: false,
                              });
                            }}
                          >
                            继续上传
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : this.state.status == 5 ? (
                    <div style={{ flex: 1, margin: 24, borderRadius: 4, background: '#fff', textAlign: 'center' }}>
                      <div style={{ margin: '40px 0 155px', height: 240, display: 'inline-block' }}>
                        <div>
                          <Icon type="sign-circle-o" style={{ fontSize: 126, color: '#F56A00' }} />
                        </div>
                        <div style={{ marginTop: 16, fontSize: 18, color: '#404040' }}>匹配异常</div>
                        <div style={{ marginTop: 10, fontSize: 14, color: '#000000', textAlign: 'center' }}>
                          <div style={{ display: 'inline-block', textAlign: 'left' }}>
                            上传图片{this.props.imgData.successCount + this.props.imgData.failCount}张，成功匹配{this.props.imgData.successCount}张
                          </div>
                        </div>
                        <div style={{ marginTop: 10, fontSize: 14, color: '#F56A00', textAlign: 'center' }}>
                          <div style={{ display: 'inline-block', textAlign: 'left' }}>以下为匹配失败的图片，请核对医生信息/命名/图片格式后重试</div>
                        </div>
                        <div style={{ margin: '12px 0', fontSize: 12, color: '#000000', display: 'flex', alignItems: 'center', justifyContent: 'center', maxWidth: 500, flexWrap: 'wrap' }}>
                          {imgArr.length > 0 &&
                            imgArr.map((item, key) => {
                              return (
                                <div style={{ marginRight: 20 }}>
                                  <div style={{ display: 'flex', flexDirection: 'column' }}>
                                    {imgArr.length <= 5 && <img style={{ width: 64, height: 90, marginBottom: 8 }} src={item.value} alt="头像" />}
                                    <div>{item.key}</div>
                                  </div>
                                </div>
                              );
                            })}
                        </div>
                        <div style={{ marginTop: 32, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          <Button
                            style={{ marginRight: 16 }}
                            onClick={() => {
                              dispatch({
                                type: 'microwebsite/saveDoctor',
                                payload: {
                                  list: [],
                                  detail: {},
                                },
                              });
                              history.push(`/microwebsite/doctor?hisId=${item.hisId}`);
                            }}
                          >
                            返回医生介绍
                          </Button>
                          <Button
                            onClick={() => {
                              this.setState({
                                status: 1,
                                errorText: '操作失败',
                                fileList: [],
                                errList: [],
                                sucessNum: 0,
                                sucessList: [],
                                showModal: false,
                              });
                            }}
                          >
                            继续上传
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : null}
                </TabPane>
              );
            })}
          </Tabs>
          <Modal visible={this.state.showModal} footer={null} closable={false} maskClosable={false} bodyStyle={{ padding: '20px 30px 80px' }}>
            <div style={{ marginBottom: 27 }}>图片上传中</div>
            <Progress percent={((this.state.sucessList.length * 100) / (this.state.fileList.length * 100)) * 100} status="active" />
            <div style={{ color: '#3F969D', marginTop: 20 }}>图片正在上传，请耐心等待...</div>
          </Modal>
        </div>
      );
    }
  },
);
