// type 合同类型  payway 付款方式  method 结算方式  mastfilds 添加产品需录入字段
export const CON_MODAL_ENUM = [
  {
    value: 'CM1',
    label: '直营',
    type: [
      {
        value: 'CT1',
        label: 'B2B',
        payway: [
          {
            value: 'PT1',
            label: '公对公汇款',
            method: [
              {
                value: 'ST1',
                label: '约定结算金额',
                mastfilds: ['fixedSettlementPrice'],
              },
              {
                value: 'ST2',
                label: '终端价格百分比',
                mastfilds: ['terminalPrice', 'settlementRatio'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT2',
        label: 'B2C',
        payway: [
          {
            value: 'PT2',
            label: '个人汇款',
            method: [
              {
                value: 'ST3',
                label: '无',
                mastfilds: ['basePrice', 'terminalPrice', 'cxgUnitPrice'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT5',
        label: '委托检测',
        payway: [
          {
            value: 'PT1',
            label: '公对公汇款',
            method: [
              {
                value: 'ST1',
                label: '约定结算金额',
                mastfilds: ['fixedSettlementPrice'],
              },
              {
                value: 'ST2',
                label: '终端价格百分比',
                mastfilds: ['terminalPrice', 'settlementRatio'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT3',
        label: '共建',
        payway: [
          {
            value: 'PT1',
            label: '公对公汇款',
            method: [
              {
                value: 'ST1',
                label: '约定结算金额',
                mastfilds: ['fixedSettlementPrice'],
              },
              {
                value: 'ST2',
                label: '终端价格百分比',
                mastfilds: ['terminalPrice', 'settlementRatio'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT4',
        label: '转诊',
        payway: [
          {
            value: 'PT1',
            label: '公对公汇款',
            method: [
              {
                value: 'ST1',
                label: '约定结算金额',
                mastfilds: ['fixedSettlementPrice'],
              },
              {
                value: 'ST2',
                label: '终端价格百分比',
                mastfilds: ['terminalPrice', 'settlementRatio'],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    value: 'CM2',
    label: '代理',
    type: [
      {
        value: 'CT6',
        label: '直接代理',
        payway: [
          {
            value: 'PT1',
            label: '公对公汇款',
            method: [
              {
                value: 'ST1',
                label: '约定结算金额',
                mastfilds: ['fixedSettlementPrice', 'cxgUnitPrice', 'promisedSales'],
              },
              {
                value: 'ST2',
                label: '终端价格百分比',
                mastfilds: ['terminalPrice', 'settlementRatio'],
              },
              {
                value: 'ST4',
                label: '阶梯计价',
                mastfilds: ['tieredSettlementPrice', 'cxgUnitPrice', 'promisedSales'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT7',
        label: '间接代理',
        payway: [
          {
            value: 'PT1',
            label: '公对公汇款',
            method: [
              {
                value: 'ST1',
                label: '约定结算金额',
                mastfilds: ['basePrice', 'cxgUnitPrice', 'promisedSales'],
              },
              {
                value: 'ST5',
                label: '底价百分比',
                mastfilds: ['settlementRatio'],
              },
              {
                value: 'ST4',
                label: '阶梯计价',
                mastfilds: ['tieredSettlementPrice', 'cxgUnitPrice', 'promisedSales'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT8',
        label: '个人代理',
        payway: [
          {
            value: 'PT2',
            label: '个人汇款',
            method: [
              {
                value: 'ST4',
                label: '阶梯计价',
                mastfilds: ['tieredSettlementPrice', 'cxgUnitPrice', 'promisedSales'],
              },
              {
                value: 'ST3',
                label: '无',
                mastfilds: ['basePrice', 'terminalPrice', 'cxgUnitPrice'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT5',
        label: '委托检测',
        payway: [
          {
            value: 'PT1',
            label: '公对公汇款',
            method: [
              {
                value: 'ST1',
                label: '约定结算金额',
                mastfilds: ['fixedSettlementPrice', 'cxgUnitPrice'],
              },
              {
                value: 'ST4',
                label: '阶梯计价',
                mastfilds: ['tieredSettlementPrice', 'cxgUnitPrice', 'promisedSales'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT3',
        label: '共建',
        payway: [
          {
            value: 'PT1',
            label: '公对公汇款',
            method: [
              {
                value: 'ST1',
                label: '约定结算金额',
                mastfilds: ['fixedSettlementPrice', 'cxgUnitPrice'],
              },
              {
                value: 'ST2',
                label: '终端价格百分比',
                mastfilds: ['terminalPrice', 'settlementRatio', 'cxgUnitPrice'],
              },
              {
                value: 'ST4',
                label: '阶梯计价',
                mastfilds: ['tieredSettlementPrice', 'cxgUnitPrice', 'promisedSales'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT4',
        label: '转诊',
        payway: [
          {
            value: 'PT1',
            label: '公对公汇款',
            method: [
              {
                value: 'ST1',
                label: '约定结算金额',
                mastfilds: ['fixedSettlementPrice', 'cxgUnitPrice'],
              },
              {
                value: 'ST2',
                label: '终端价格百分比',
                mastfilds: ['terminalPrice', 'settlementRatio', 'cxgUnitPrice'],
              },
              {
                value: 'ST4',
                label: '阶梯计价',
                mastfilds: ['tieredSettlementPrice', 'cxgUnitPrice', 'promisedSales'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT1',
        label: 'B2B',
        payway: [
          {
            value: 'PT1',
            label: '公对公汇款',
            method: [
              {
                value: 'ST1',
                label: '约定结算金额',
                mastfilds: ['fixedSettlementPrice'],
              },
              {
                value: 'ST2',
                label: '终端价格百分比',
                mastfilds: ['terminalPrice', 'settlementRatio'],
              },
              {
                value: 'ST4',
                label: '阶梯计价',
                mastfilds: ['tieredSettlementPrice', 'cxgUnitPrice', 'promisedSales'],
              },
            ],
          },
        ],
      },
      {
        value: 'CT2',
        label: 'B2C',
        payway: [
          {
            value: 'PT2',
            label: '个人汇款',
            method: [
              {
                value: 'ST4',
                label: '阶梯计价',
                mastfilds: ['tieredSettlementPrice', 'cxgUnitPrice', 'promisedSales'],
              },
              {
                value: 'ST3',
                label: '无',
                mastfilds: ['basePrice', 'terminalPrice', 'cxgUnitPrice'],
              },
            ],
          },
        ],
      },
    ],
  },
];

export const HZMS = { CM1: '直营', CM2: '代理' };

export const HTLX = {
  CT1: 'B2B',
  CT2: 'B2C',
  CT3: '共建',
  CT4: '转诊',
  CT5: '委托检测',
  CT6: '直接代理',
  CT7: '间接代理',
  CT8: '个人代理',
};

export const HTZT = {
  1: '已签约',
  2: '签约中',
  3: '已过期',
  4: '未签约（B2C）',
  5: '已作废',
};

export const FKFS = {
  PT1: '公对公汇款',
  PT2: '个人汇款',
};

export const JSFS = {
  ST1: '约定结算金额',
  ST2: '终端价格百分比',
  ST3: '无',
  ST4: '阶梯计价',
};

export const JSZQ = {
  P1: '月度结算',
  P2: '季度结算',
  P3: '年度结算',
  P4: '其他',
};
