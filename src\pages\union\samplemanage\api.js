import { post, download } from '@/utils/request';

// 分中心客户列表
export const getinstitutions = (param = {}) => post('/api/sub-sample/get-institutions', { data: param });

// 客户列表
export const getinstitutionbylist = (param = {}) => post('/api/institution/get-by-list', { data: param });

// 账号列表
export const getAllUser = (param = {}) => post('/api/userinfo/findAllUser', { data: param });

// 团队列表
export const getAllGroup = (param = {}) => post('/api/team/paging', { data: param });

// 产品列表
export const getproducts = (param = {}) => post('/api/product/get-products', { data: param });

// 所有样品列表
export const getsamplebypage = (param = {}) => post('/api/sample/get-by-page', { data: param });
export const getsubsamplebypage = (param = {}) => post('/api/sub-sample/get-by-page', { data: param });

// 批量下载报告
export const batchPreviewReport = (param = {}) => download('/api/sample/batchPreviewReport', { data: param });
// 导出所有样品
export const exportsampleall = (param = {}) => download('/api/sample/exportAll', { data: param });
// 导出系统样品
export const exportsample = (param = {}) => download('/api/sample/export', { data: param });

// 删除合样品
export const deletesample = (param = {}) => post('/api/sample/delete', { data: param });

// 新增样品
export const addsample = (param = {}) => post('/api/sample/add-sample', { data: param });

// 更新样品
export const updatesample = (param = {}) => post('/api/sample/update', { data: param });

// 更新样品支付信息
export const updatePayInfo = (param = {}) => post('/api/sample/updatePayInfo', { data: param });

// 样品详情
export const getDetail = (param = {}) => post('/api/sample/get-by-id', { data: param });

// 获取合作客户的所有有效合同
export const getcontractbyinstitution = (param = {}) => post('/api/contract/get-contract-by-institution', { data: param });

// 根据合同获取产品/根据合同及父产品获取子产品
export const getproductsbycontract = (param = {}) => post('/api/contract/get-product-by-contract', { data: param }, true, false);

// 根据采样管编号获取相关信息
export const getinfobysampling = (param = {}) => post('/api/contract/get-contract-by-sampling-package', { data: param }, true, false);

// 检查编号是否重复
export const checksample = (param = {}) => post('/api/sample/check-no-unique', { data: param });

// 获取二维码
export const getQrcode = (param = {}) => post('/api/qrcode/sence/xcxqrcode', { data: param });

// 根据样品编号获取对应的样品图片
export const getImgList = (param = {}) => post('/api/sample/get-path-by-no', { data: param }, false, false);

// 根据样品编号获取对应的样品图片
export const uploadImgList = (param = {}) => post('/api/sample/upload-path-by-no', { data: param });

// 获取样品的所有报告
export const getSampleReports = (param = {}) => post('/api/sample/getSampleReports', { data: param });

// 预览样品报告
export const previewReport = (param = {}) => download('/api/sample/previewReport', { data: param });

// 下载批量结算错误详情文件
export const downloadsettle = (param = {}) => download('/api/sample/download-batch-settle', { data: param });

// 下载批量导入错误详情文件
export const downloadresult = (param = {}) => download('/api/sample/download-sample-result', { data: param });
