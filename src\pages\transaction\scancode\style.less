@charset "utf-8";
@import '../../../resources/styles/mixins';

.page-transaction {
  .tsc-top-panle {
    .col-item {
      padding: 0 21px 0 24px;
    }

    .ant-calendar-picker {
      display: block;
    }

    .btn-bar {
      text-align: right;
      .col-item();
      .ant-btn {
        margin-left: 8px;
      }
    }
  }
  .scancode-search-ipt {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 8px;
  }
}
.order-all {
  .ant-pagination-jump-prev {
    display: none;
  }
  .ant-pagination-item {
    // display: none;
    &.ant-pagination-item-active {
      display: inline-block;
    }
  }
  .ant-pagination-next,
  .ant-pagination-prev {
    border: none;
    a:after {
      display: none;
    }
  }
}
.foot-btn {
  span {
    display: inline-block;
    height: 24px;
    margin-right: 16px;
    padding: 0 12px;
    color: #000;
    line-height: 24px;
    background-color: #ffffff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    &.active {
      color: #3f969d;
      background-color: #f0fbf9;
      border: 1px solid #3f969d;
    }
  }
}
