import React, { Component, Fragment } from 'react';
import { connect } from 'dva';
import { Layout, message, List, Modal, Empty, Button, Input, Select } from 'antd';
import MsgVideoWhite from '@/assets/message-video.png';

import Ellipsis from '@/components/Ellipsis';
import GroupOper from './GroupOper';
import ChartEditor from './ChartEditor';
import Message from './Message';
import RightContent from './RightContent';
import ChatHistory from './MsgHistory';
import UserGroup from './UserGroup';
import ConsultationOpinion from './ConsultationOpinion';
import styles from './index.less';
import * as Api from './api';

@connect(({ user = {} }) => {
  return { currentUser: user.currentUser };
})
class Index extends Component {
  constructor(prop) {
    super(prop);
    this.clientHeight = document.body.clientHeight;
    this.state = {
      chatInfo: {},
      getMessage: () => {},
      getChatList: () => {},
      showfreeHistory: false,
      consultationOpinionStatus: false,
      name: '会诊意见',
      content: '',
      orderNo: '',
      plannedTime: '',
      applyTime: '',
      consultationId: '',
      suggestShow: false,
      show: false,
      msgHistory: {},
      queryFlg: 'xcx',
      filter: '',
    };
  }

  componentDidMount() {}

  getChatMsgHistory = async () => {
    const { chatInfo = {} } = this.state;

    const { code: code1, data: data1 = {} } = await Api.getFreeChatByPid({
      pid: chatInfo.pid,
    });
    if (code1 === 0) {
      const { msgHistory = {}, queryFlg } = this.state;
      const { currentPage = 1 } = msgHistory;
      const { code, data = {} } = await Api.queryChatHistoryRecord({
        groupId: data1,
        numPerPage: 100,
        pageNum: currentPage,
        queryFlg,
      });
      if (code === 0) {
        this.setState({ msgHistory: data });
      }
    }
  };

  getMaindoctorId = async id => {
    const { currentUser } = this.props;
    if (id) {
      const { code, data, msg = '' } = await Api.getApplyInfoAndFile({ id });
      if (code == 0) {
        const { mainDoctorId } = data;
        if (currentUser.id != mainDoctorId) {
          this.setState({ suggestShow: true });
        }
      }
    } else {
      this.setState({ suggestShow: false });
    }
  };

  setChatInfo = chatInfo => {
    this.setState({ chatInfo });
  };

  setConsultationInfo = consultationInfo => {
    const { orderNo, plannedTime, applyTime, id } = consultationInfo;
    this.getMaindoctorId(id);
    this.setState({ orderNo, plannedTime, applyTime, consultationId: id });
  };

  setMessageCall = fn => {
    this.setState({ getMessage: fn });
  };

  setChatListCall = fn => {
    this.setState({ getChatList: fn });
  };

  previewImg = fileUrl => {
    if (fileUrl) {
      window.open(fileUrl);
    }
  };

  getImgMsg = item => {
    let leftIcon = null;

    if (item.type == 3) {
      leftIcon = <img src={item.content} alt="" style={{ maxWidth: 200, maxHeight: 200, cursor: 'pointer' }} onClick={() => this.previewImg(item.content)} />;
    }
    if (item.type == 5) {
      leftIcon = <img src={item.content} alt="" style={{ width: 32 }} />;
    }
    if (item.type == 6) {
      leftIcon = <img src={MsgVideoWhite} alt="" style={{ width: 32 }} />;
    }
    return leftIcon;
  };

  searchContent = val => {
    // const val = e.currentTarget.value;
    this.setState({ filter: val });
  };

  renderSraechType = () => {
    return (
      <Select defaultValue="xcx" style={{ width: 100 }} onChange={this.changeTipType}>
        <Select.Option value="xcx">小程序</Select.Option>
        <Select.Option value="old">企业微信</Select.Option>
      </Select>
    );
  };

  renderFooter = () => {
    const { msgHistory = {} } = this.state;
    const { pageCount, currentPage } = msgHistory;
    return (
      <div className={styles.msgFooter}>
        <Button disabled={currentPage === 1}>上一页</Button>
        <Button disabled={currentPage >= pageCount}>下一页</Button>
      </div>
    );
  };

  chat = () => {
    this.getChatMsgHistory();
    this.setState({
      show: true,
    });
  };

  getMsgHead = () => {
    const { chatInfo } = this.state;
    if (chatInfo.chatFilterType === '6' && chatInfo.account) {
      return (
        <div className={styles.feeChatHead}>
          {/* <div>订单号：***********</div> */}
          <div>预约时间：{chatInfo.appointmentTime}</div>
        </div>
      );
    }
    if (chatInfo.chatFilterType == '10') {
      const { orderNo, plannedTime = '', applyTime } = this.state;
      return (
        <div className={styles.consultationInfo}>
          <div>会诊订单号：{orderNo}</div>
          <div>创建时间：{applyTime}</div>
          <div>计划会诊时间：{plannedTime ? plannedTime.substr(0, 10) : applyTime ? '暂无' : ''}</div>
        </div>
      );
    }
    if (chatInfo.chatFilterType !== '6') {
      return <Ellipsis lines={1}>{chatInfo.name}</Ellipsis>;
    }
    return null;
  };

  getMsgTip = () => {
    const { chatInfo } = this.state;
    if (chatInfo.chatFilterType === '6' && chatInfo.account && !chatInfo.id) {
      return (
        <div className={styles.feeChatStartTip}>
          您还未开启该笔订单的问诊，请先点击&nbsp;&nbsp;
          <span style={{ color: PRIMARY_COLOR }}>开启问诊</span>&nbsp;&nbsp;开启聊天
        </div>
      );
    }
    return null;
  };

  consultationSuggest = async () => {
    const { consultationId } = this.state;
    const { code, data } = await Api.queryReport({ consultationId, reportType: 0 });
    const param = { consultationOpinionStatus: true };
    if (code == 0) {
      if (data.length > 0 && data[0].report) {
        param.content = data[0].report;
      }
    }
    this.setState(param);
  };

  cancelSuggest = async (val, tipContent) => {
    const { consultationId, content } = this.state;
    if (val) {
      const { code, data, msg } = await Api.addReport({
        content: tipContent,
        consultationId,
        reportType: 0,
      });
      if (code == 0) {
        this.setState({
          consultationOpinionStatus: false,
          content: '',
        });
        message.success({ content: '会诊意见填写成功！', duration: 1 });
      } else {
        message.error({ content: msg, duration: 1 });
      }
    } else {
      this.setState({
        consultationOpinionStatus: false,
        content: '',
      });
    }
  };

  render() {
    const { suggestShow, chatInfo, getMessage, getChatList, name, content, consultationOpinionStatus } = this.state;
    const { vipStartDate } = chatInfo;
    const { vipEndDate } = chatInfo;

    const { msgHistory = {}, filter = '', show } = this.state;
    const { recordList = [] } = msgHistory;
    const showData = recordList.filter(item => {
      return (!filter && !filter.length) || (item.content || '').indexOf(filter) > -1 || (item.sendUserName || '').indexOf(filter) > -1;
    });
    return (
      <div className={`${styles.contentBox} ${!chatInfo.id && chatInfo.chatFilterType !== '6' ? styles.nouser : ''}`}>
        <Layout>
          <Layout.Sider width={350} theme="light" className={styles.left}>
            {/* 左侧tabbar */}
            <UserGroup onChat={this.setChatInfo} setChatListCall={this.setChatListCall} />
          </Layout.Sider>
          {/* 中间弹窗聊天 */}
          <Layout.Sider width={chatInfo.chatFilterType != '11' ? 450 : '79vw'} theme="light" className={styles.center}>
            <div className={styles.chart}>
              <div className={styles.chartHead}>
                <div className={styles.chartName}>{this.getMsgHead()}</div>
                <GroupOper setConsultationInfo={this.setConsultationInfo} chatInfo={chatInfo} getChatList={getChatList} onChat={this.setChatInfo} />
              </div>

              {chatInfo.chatFilterType === '12' && chatInfo.vipStartDate && (
                <div className={styles.chartHeadtimes}>
                  <div className={styles.chartName}>
                    服务期限：{vipStartDate?.slice(0, 10)}至{vipEndDate?.slice(0, 10)}
                  </div>
                  <div className={styles.chartInfo} onClick={() => this.chat()}>
                    聊天记录
                  </div>
                </div>
              )}
              <div
                className={styles.chartBody}
                style={{
                  minHeight: window.BrowserVersion.startsWith('49') ? this.clientHeight - 340 : 'auto',
                }}
              >
                {chatInfo.chatFilterType == '10' && suggestShow && (
                  <div onClick={this.consultationSuggest} className={styles.consultationSuggest}>
                    会诊意见
                  </div>
                )}
                {/* 填写/查看会诊意见弹出框 */}
                <ConsultationOpinion status={consultationOpinionStatus} title={content.length > 0 ? '我的会诊意见' : '创建会诊意见'} name={name} content={content} onCancel={this.cancelSuggest} />
                {this.getMsgTip()}
                {chatInfo.id ? <Message key={chatInfo.id} type={chatInfo.chatFilterType} setMessage={this.setMessageCall} chatInfo={chatInfo} groupId={chatInfo.id} /> : null}
              </div>
              <div className={styles.chartFoot}>
                <ChartEditor key={chatInfo.id} getMessage={getMessage} getChatList={getChatList} groupId={chatInfo.id} chatInfo={chatInfo} />
              </div>
            </div>
          </Layout.Sider>
          {chatInfo.chatFilterType != '11' && (
            <Layout.Content className={styles.end}>
              {/* 右侧详细信息 */}
              <RightContent chatInfo={chatInfo} key={chatInfo.chatFilterType === '6' ? chatInfo.pid : chatInfo.id} />
            </Layout.Content>
          )}
        </Layout>

        <Modal
          title="消息记录"
          visible={show}
          onOk={this.handleSubmit}
          onCancel={() => {
            this.setState({
              show: false,
            });
          }}
          footer={this.renderFooter()}
          destroyOnClose
        >
          <Input.Search placeholder="请输入关键字搜索" onSearch={this.searchContent} style={{ width: '100%' }} addonBefore={this.renderSraechType()} />
          <List className={styles.list}>
            {showData.map((item, i) => {
              let leftIcon = null;

              if (item.type == 3) {
                leftIcon = <img src={item.content} alt="" style={{ maxWidth: 200, maxHeight: 200, cursor: 'pointer' }} onClick={() => this.previewImg(item.content)} />;
              }
              if (item.type == 5) {
                leftIcon = <img src={item.content} alt="" style={{ width: 32 }} />;
              }
              if (item.type == 6) {
                leftIcon = <img src={MsgVideoWhite} alt="" style={{ width: 32 }} />;
              }

              let filterStr;
              if (!filter && !filter.length) {
                filterStr = item.sendUserName || '';
              } else {
                filterStr = `${item.sendUserName || ''}`.replace(new RegExp(filter, 'g'), `<span class="${styles.keyword}">${filter}</span>`);
              }
              filterStr = `${filterStr}\xa0\xa0${item.createTime}`;
              let filterContentStr = '';
              if (!filter && !filter.length) {
                filterContentStr = item.content && <div>{item.content}</div>;
              } else {
                filterContentStr = item.content && <div>{item.content}</div>;
              }

              return (
                <List.Item key={item.key || i}>
                  <List.Item.Meta
                    className={styles.meta}
                    title={
                      <div>
                        <div
                          className={styles.title}
                          dangerouslySetInnerHTML={{
                            __html: filterStr,
                          }}
                        />
                        <div style={{ marginTop: 6 }}>{this.getImgMsg(item)}</div>
                      </div>
                    }
                    description={!leftIcon ? <div className={styles.descriptionModule}>{filterContentStr}</div> : null}
                  />
                </List.Item>
              );
            })}
            {!showData.length ? <Empty style={{ marginTop: 100 }} /> : null}
          </List>
        </Modal>
        {/* <ChatHistory chatInfo={chatInfo} show={showfreeHistory} onCancel={this.showfreeHistory} /> */}
      </div>
    );
  }
}

export default connect()(Index);
