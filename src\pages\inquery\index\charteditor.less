@import '~antd/lib/style/themes/default.less';

.iconQuestion {
  width: 19px;
  height: 19px;
  background: url(../../../assets/icon-question.png) no-repeat center center / 100% auto;
}

.iconAnswer {
  width: 19px;
  height: 19px;
  background: url(../../../assets/icon-answer.png) no-repeat center center / 100% auto;
}

.iconUpload {
  width: 19px;
  height: 19px;
  background: url(../../../assets/icon-upload.png) no-repeat center bottom / 100% auto;
}

.iconEmoji {
  width: 19px;
  height: 19px;
  background: url(../../../assets/icon-emoj.png) no-repeat center center / 100% auto;
}

.iconAdduser {
  width: 19px;
  height: 19px;
  background: url(../../../assets/icon-adduser.png) no-repeat center center / 100% auto;
}

.iconExit {
  width: 19px;
  height: 19px;
  background: url(../../../assets/exit.png) no-repeat center center / 100% auto;
}

.iconMsgHistory {
  width: 19px;
  height: 19px;
  background: url(../../../assets/icon-msghistory.png) no-repeat center center / 100% auto;
}

.iconVideo {
  width: 21px;
  height: 21px;
  background: url(../../../assets/icon-video.png) no-repeat center center / 100% auto;

  &.inVideo {
    position: relative;

    &:before {
      content: '';
      position: absolute;
      right: -6px;
      top: -4px;
      padding: 3px;
      border-radius: 50%;
      background: red;
    }
  }
}

.iconReback {
  width: 19px;
  height: 19px;
  background: url(../../../assets/icon-reback.png) no-repeat center center / 100% auto;
}

:global {
  .antd-pro-pages-inquery-index-index-chart .bf-controlbar .control-item.media {
    transform: scale(0);
  }

  .bf-controlbar {
    box-shadow: none;
    border-top: 1px solid #e9e9e9;
  }
}

.list {
  height: 400px;
  overflow: auto;
  white-space: pre-wrap;
  text-align: justify;

  &::-webkit-scrollbar {
    display: none;
  }

  .item {
    padding-right: 24px;
    padding-left: 24px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;

    .meta {
      width: 100%;
    }

    .avatar {
      margin-top: 4px;
      background: #fff;
    }

    .iconElement {
      font-size: 32px;
    }

    &.read {
      opacity: 0.4;
    }

    &:last-child {
      border-bottom: 0;
    }

    &:hover {
      background: @primary-1;
    }

    .title {
      margin-bottom: 8px;
      font-weight: normal;
    }

    .descriptionModule {
      display: flex;
    }

    .description {
      font-size: 12px;
      line-height: @line-height-base;
    }

    .datetime {
      margin-top: 4px;
      font-size: 12px;
      line-height: @line-height-base;
    }

    .extra {
      float: right;
      margin-top: -1.5px;
      margin-right: 0;
      color: @text-color-secondary;
      font-weight: normal;
    }
  }

  .loadMore {
    padding: 8px 0;
    color: @primary-6;
    text-align: center;
    cursor: pointer;

    &.loadedAll {
      color: rgba(0, 0, 0, 0.25);
      cursor: unset;
    }
  }
}

.msgFooter {
  text-align: right;
}

.keyword {
  color: @primary-color;
}

.disableMask {
  position: relative;

  &:before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    opacity: 0;
    z-index: 2;
  }
}
