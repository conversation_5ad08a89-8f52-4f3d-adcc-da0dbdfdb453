import React, { useEffect, useMemo, useState } from 'react';
import { useAntdTable } from 'ahooks';
import { merge } from 'lodash';
import { connect, history } from 'umi';
import { Form, Table } from 'antd';
import queryString from 'query-string';

import * as Api from './service';

const Index = props => {
  const [form] = Form.useForm();
  const { moduleCode, moduleId } = queryString.parse(props.location.search);

  useEffect(() => {}, []);

  const fetchList = async ({ current = 1, pageSize = 10 }) => {
    try {
      const params = {
        pageNum: current,
        numPerPage: pageSize,
        moduleCode,
        moduleId,
      };

      const data = await Api.fetchList({ ...params });

      return {
        total: Number(data.data?.totalCount) || 0,
        list: data?.data?.recordList || [],
      };
    } catch (error) {
      console.log(error);
    }
  };

  const { loading, tableProps, search } = useAntdTable(fetchList, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
  });

  const tableRealProps = useMemo(
    () =>
      merge(tableProps, {
        pagination: {
          showQuickJumper: true,
          // showSizeChanger: true,
          showTotal: total => `共 ${total} 条`,
        },
      }),
    [tableProps],
  );

  const { submit, reset } = search;

  const columns = [
    {
      title: '更新时间',
      dataIndex: 'updateTime',
    },
    {
      title: '更新人',
      dataIndex: 'updateByName',
    },
    {
      title: '账号',
      dataIndex: 'updateByAccount',
    },
    {
      title: '字段',
      dataIndex: 'changFieldName',
    },
    {
      title: '原值',
      dataIndex: 'oldValue',
    },
    {
      title: '新值',
      dataIndex: 'newValue',
    },
  ];

  return (
    <div className="g-page p-product-list">
      <div className="p-product-table">
        <Table rowKey="id" {...tableRealProps} loading={loading} columns={columns} scroll={{ x: 'max-content' }} />
      </div>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Index);
