import React from 'react';
import update from 'immutability-helper';
import { findDOMNode } from 'react-dom';
import { connect } from 'dva';
import { Button, message } from 'antd';
import { DragDropContext, DragSource, DropTarget } from 'react-dnd';
import HTML5Backend from 'react-dnd-html5-backend';
import ItemTypes from './ItemTypes';

const cardSource = {
  beginDrag(props) {
    return {
      id: props.id,
      index: props.index,
    };
  },
};

const cardTarget = {
  hover(props, monitor, component) {
    const dragIndex = monitor.getItem().index;
    const hoverIndex = props.index;
    if (dragIndex === hoverIndex) {
      return;
    }
    const hoverBoundingRect = findDOMNode(component).getBoundingClientRect(); // eslint-disable-line
    const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
    const clientOffset = monitor.getClientOffset();
    const hoverClientY = clientOffset.y - hoverBoundingRect.top;
    if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
      return;
    }
    if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
      return;
    }
    props.moveCard(dragIndex, hoverIndex);
    monitor.getItem().index = hoverIndex;
  },
};

export default function IIHOCSortable({ hisId, list = [], WrappedComponent, cellClickable = false, submitEffectType, refreshEffectType, btnIdentity }) {
  const Container = DropTarget(ItemTypes.CARD, cardTarget, connectDrop => ({
    connectDropTarget: connectDrop.dropTarget(),
  }))(
    DragSource(ItemTypes.CARD, cardSource, (connectDrag, monitor) => ({
      connectDragSource: connectDrag.dragSource(),
      isDragging: monitor.isDragging(),
    }))(WrappedComponent),
  );

  class Sortable extends React.Component {
    constructor(props) {
      super(props);
      this.state = {
        list,
        hisId,
        disabled: true,
      };
    }

    moveCard = (dragIndex, hoverIndex) => {
      const items = this.state.list;
      const dragCard = items[dragIndex];
      this.setState({
        disabled: false,
      });
      this.setState(
        update(this.state, {
          list: {
            $splice: [
              [dragIndex, 1],
              [hoverIndex, 0, dragCard],
            ],
          },
        }),
      );
    };

    submit = () => {
      const items = this.state.list;
      const { dispatch } = this.props;
      dispatch({
        type: submitEffectType,
        payload: {
          hisId,
          deptNo: items[0].pid || items[0].deptNo,
          sortArray: items.map(item => item.no),
        },
      });
    };

    render() {
      const items = this.state.list;
      const { dispatch, backIds = [0], permissionData = {} } = this.props;
      const { btns = {} } = permissionData;
      if (items && items.length == 0) {
        return (
          <div className="merchant-pet-panel">
            <div className="merchant-pet" />
          </div>
        );
      }
      return (
        <div
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <div
            style={{
              flex: 1,
              display: 'flex',
              flexWrap: 'wrap',
              alignItems: 'flex-start',
              alignContent: 'flex-start',
            }}
          >
            {items &&
              items.map((item, idx) => (
                <Container
                  key={item.no}
                  index={idx}
                  id={item.no}
                  text={item.name}
                  moveCard={this.moveCard}
                  refreshEffectType={refreshEffectType}
                  onClick={() => {
                    if (cellClickable) {
                      if (item.hasChild != 0) {
                        message.destroy();
                        message.warn('该科室没有下级科室');
                      } else {
                        backIds.push(item.pid);
                        dispatch({
                          type: 'microwebsite/saveSortable',
                          payload: {
                            backIds,
                          },
                        });
                        dispatch({
                          type: refreshEffectType,
                          payload: {
                            hisId,
                            pid: item.no,
                          },
                        });
                      }
                    }
                  }}
                />
              ))}
          </div>
          {btns[btnIdentity] ? (
            <div style={{ textAlign: 'center' }}>
              {backIds && backIds.length > 0 ? (
                <Button
                  size="default"
                  style={{ marginRight: 16 }}
                  onClick={() => {
                    dispatch({
                      type: refreshEffectType,
                      payload: {
                        hisId,
                        pid: backIds[backIds.length - 1],
                      },
                    });
                    backIds.splice(backIds.length - 1, 1);
                    dispatch({
                      type: 'microwebsite/saveSortable',
                      payload: {
                        backIds,
                      },
                    });
                  }}
                >
                  返回上一级
                </Button>
              ) : null}
              <Button onClick={this.submit} type="primary" style={{ marginRight: 16 }} size="default">
                保存当前顺序
              </Button>
              <Button
                size="default"
                disabled={this.state.disabled}
                onClick={() => {
                  this.setState({
                    list,
                    disabled: true,
                  });
                }}
              >
                取消
              </Button>
            </div>
          ) : null}
        </div>
      );
    }
  }

  return DragDropContext(HTML5Backend)(
    connect(state => {
      return {
        backIds: state.microwebsite.sortable.backIds,
        permissionData: state.root.permissionData,
      };
    })(Sortable),
  );
}
