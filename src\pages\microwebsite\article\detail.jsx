import React from 'react';
import { connect } from 'dva';
import { history } from 'umi';

import IIHOCArticlePanel from './components/IIHOCArticlePanel';

import * as utils from '../../../utils/utils';

export default connect(state => {
  return {
    detail: state.microwebsite.article.detail,
    typeList: state.microwebsite.article.typeList,
  };
})(
  class ArticleDetail extends React.Component {
    componentDidMount() {
      const { dispatch, location, typeList } = this.props;
      const articleId = utils.queryStringToJson(location.search).id;
      if (articleId) {
        dispatch({
          type: 'microwebsite/articleTypeList',
        });
        dispatch({
          type: 'microwebsite/articleDetail',
          payload: {
            articleId,
          },
        });
      } else {
        history.push('/microwebsite/article');
      }
    }

    render() {
      const { detail, typeList } = this.props;
      const ArticleDetailPanel = IIHOCArticlePanel({ editable: false, article: detail, typeList });
      return <ArticleDetailPanel />;
    }
  },
);
