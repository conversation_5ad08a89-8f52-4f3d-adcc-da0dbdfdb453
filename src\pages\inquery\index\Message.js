import React, { Component, Fragment } from 'react';
import { withRouter } from 'dva/router';
import { Avatar, Modal, Button, Menu, message } from 'antd';
import MsgVideo from '@/assets/message-video.png';
import MsgVideoWhite from '@/assets/message-video-white.png';
import * as Api from './api';
import PubSub from 'pubsub-js';

import styles from './index.less';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.clientHeight = document.body.clientHeight;

    this.state = {
      list: [],
      previewImage: '',
      previewModal: false,
      contextMenuStyle: {
        display: 'none',
      },
      contextItem: {},
      preZoom: 1,
      preRotate: 0,
      preMoveX: 0,
      preMoveY: 0,
      status: '',
    };

    this.hasMoreInfo = false;

    this.defaultDownPosition = {
      x: 0,
      y: 0,
    };
    this.tempPosition = {
      x: 0,
      y: 0,
    };
  }

  componentDidMount() {
    PubSub.subscribe('queryChatInfo', () => {
      this.queryChatInfo();
    });
    this.queryChatInfo();
    const { setMessage } = this.props;
    if (typeof setMessage === 'function') {
      setMessage(this.queryChatInfo);
    }
    const {
      chatInfo: { consultationId = null },
    } = this.props;
    if (consultationId) {
      this.getConsultationStatus(consultationId);
    }
  }

  getConsultationStatus = async consultationId => {
    const { code, data, msg = '' } = await Api.getApplyInfoAndFile({ id: consultationId });
    if (code == 0) {
      const { status } = data;
      if (status == 3 || status == 6) {
        this.setState({ status });
      }
    }
  };

  componentWillUnmount() {
    this.clearMessageTimer();
    this.isDestoryComponet = true;
  }

  clearMessageTimer = () => {
    clearTimeout(this.msgTimer);
    // clearTimeout(this.userScrollTimer);
    clearTimeout(this.moveTimer);
  };

  // richTextReplace = (richText = '') => {
  //   let html = richText.trim();
  //   html = html.replace(/\s+\w+="[^"]*?"/ig, function(a){
  //     if (a.indexOf('src=') > -1) {
  //       return a;
  //     }
  //     return '';
  //   });
  //   html = html.replace(/&nbsp;/ig, '');
  //   html = html.replace(/&emsp;/ig, '');
  //   html = html.replace(/<br(\s|\S)*?>/ig, '');
  //   html = html.replace(/>([^>])+?<\//g, function(a){
  //     if (a.indexOf('src')) {
  //       a.replace(/\s/g, '');
  //     }
  //     return a.replace(/\s/g, '');
  //   });
  //   html = html.replace(/<div>/ig, '<p>');
  //   html = html.replace(/<\/div>/ig, '</p>');
  //   html = html.replace(/<p><\/p>/ig, '');
  //   html = html.replace(/<p><span><\/span><\/p>/ig, '');
  //   if (!html.startsWith('<')) {
  //     html = '<p>' + html + '</p>';
  //   }
  //   html = html.replace(/<p/ig, '<p class="global-article-elm-p"');
  //   html = html.replace(/<p\sclass="global-article-elm-p">((\s|\S)+?)<\/p>/ig, function(a, b, c){
  //     if (b.endsWith(':') || b.endsWith('：')) {
  //       return '<div class="global-article-title">' + b + '</div>';
  //     }
  //     return a;
  //   });
  //   html = html.replace(/<em/ig, '<em class="global-article-elm-em"');
  //   html = html.replace(/<img/ig, '<img class="global-article-elm-img"');
  //   return html;
  // }

  queryChatInfo = async (param = {}) => {
    if (this.msgTimer !== undefined) {
      clearTimeout(this.msgTimer);
    }
    if (this.isDestoryComponet) {
      return false;
    }
    const {
      location: { pathname = '' },
    } = this.props;
    if (pathname !== '/inquery/index') {
      return false;
    }
    const { groupId } = this.props;
    const { list = [] } = this.state;
    if (param.getNewMsg) {
      param.endTime = (list[list.length - 1] || {}).createTime || '';
    }
    const { code, data = {} } = await Api.queryChatInfo({ ...param, groupId, numPerPage: 15 });
    if (code == 0) {
      const { recordList = [], revokeChatList = [] } = data; // currentPage = 1, totalCount = 0, messageUserType
      let historyList = null;
      if (param.startTime) {
        historyList = [...recordList.reverse(), ...list];
      } else {
        historyList = list.concat(recordList.reverse());
      }

      if ((param.startTime || !Object.keys(param).length) && recordList.length >= 15) {
        this.hasMoreInfo = true;
      }

      if (revokeChatList.length) {
        const revokeChatIdStr = revokeChatList
          .reduce((oldIds, itm) => {
            oldIds.push(itm.id);
            return oldIds;
          }, [])
          .join('|');
        historyList = historyList.filter(itm => {
          return `|${revokeChatIdStr}|`.indexOf(`|${itm.id}|`) === -1;
        });
      }
      if (!param.startTime && recordList.length && recordList[0].messageUserType == 1) {
        // 当前用户发送消息，把消息记录拉到最底下
        this.setState({ isScrollIng: false });
      }

      this.setState({ list: historyList }, () => {
        if (param.startTime && recordList.length) {
          this.msgContent.scrollTop = 86 * recordList.length; // 每一条消息大概86高度，滚动条数*86的距离使消息滚动到可视区域
        }
        this.scrollIntoView();
      });
    }
    this.msgTimer = setTimeout(() => {
      const { list: infoList = [] } = this.state;
      this.queryChatInfo({ endTime: (infoList[infoList.length - 1] || {}).createTime || '' });
    }, 2000);
  };

  scrollIntoView = () => {
    const { isScrollIng } = this.state;
    if (!isScrollIng) {
      this.msgContent.scrollTop = 1000000;
    }
  };

  previewImage = path => {
    const newPath = this.changeUrl({ url: path });
    const preZoom = localStorage.getItem(`${newPath}-preZoom`) || 1;
    const preRotate = localStorage.getItem(`${newPath}-preRotate`) || 0;
    const preMoveX = localStorage.getItem(`${newPath}-preMoveX`) || 0;
    const preMoveY = localStorage.getItem(`${newPath}-preMoveY`) || 0;
    this.setState({
      previewImage: newPath,
      previewModal: true,
      preZoom: preZoom * 1,
      preRotate: preRotate * 1,
      preMoveX: preMoveX * 1,
      preMoveY: preMoveY * 1,
    });
  };

  closePreviewImage = () => {
    if (!this.isMoving) {
      this.setState({ previewModal: false });
    }
  };

  setChatContentNodeEvent = node => {
    if (node) {
      this.msgContent = node;
      this.msgContent.onscroll = () => {
        const { scrollTop, scrollHeight, clientHeight } = this.msgContent;
        // clearTimeout(this.userScrollTimer);
        if (scrollHeight - scrollTop - clientHeight < 15) {
          this.setState({ isScrollIng: false });
        } else {
          if (this.isDestoryComponet) {
            return false;
          }
          // this.userScrollTimer = setTimeout(() => {
          //   this.setState({ isScrollIng: false });
          // }, 20000);
          this.setState({ isScrollIng: true });
        }
        if (scrollTop <= 1 && this.hasMoreInfo) {
          const { list = [] } = this.state;
          this.queryChatInfo({ startTime: (list[0] || {}).createTime || '' });
        }
      };
    }
  };

  contextMenu = item => {
    window.event.returnValue = false;
    window.event.cancelBubble = true;
    // if (item.messageUserType != 1) {
    //   return false;
    // }
    this.setState({
      contextMenuStyle: {
        left: window.event.clientX + 10,
        top: window.event.clientY,
        display: 'block',
      },
      contextItem: item,
    });
    return false;
  };

  hideContextMenu = () => {
    this.setState({
      contextMenuStyle: {
        display: 'none',
      },
      contextItem: {},
    });
  };

  contextMenuBody = () => {
    const { contextMenuStyle = {}, contextItem = {} } = this.state;
    return (
      <div className={styles.contextMenu} style={contextMenuStyle}>
        <Menu>
          {contextItem.messageUserType == 1 ? <Menu.Item onClick={this.handleContextMenuClick}>撤回消息</Menu.Item> : null}
          {contextItem.type == 1 ? <Menu.Item onClick={this.collect}>添加收藏</Menu.Item> : null}
        </Menu>
      </div>
    );
  };

  collect = async () => {
    this.hideContextMenu();
    const { contextItem = {} } = this.state;
    const reqData = {
      content: contextItem.content,
    };
    const { code } = await Api.addFastQuestion(reqData);
    if (code == 0) {
      message.success('收藏成功');
    }
  };

  revokeMessage = async () => {
    const { contextItem = {} } = this.state;
    const { code } = await Api.revokeMessage({ messageId: contextItem.id });
    if (code == 0) {
      const { list = [] } = this.state;
      const msgList = list.filter(item => item.id !== contextItem.id);
      this.setState({ list: msgList });
    }
  };

  handleContextMenuClick = () => {
    this.hideContextMenu();
    this.revokeMessage();
  };

  zoomImg = n => {
    const { preZoom = 1, previewImage } = this.state;
    let newZoom = preZoom + n * 0.1;
    if (newZoom <= 0) {
      newZoom = 0;
    }
    this.setState({ preZoom: newZoom });
    localStorage.setItem(`${previewImage}-preZoom`, newZoom);
  };

  rotateImg = e => {
    e.stopPropagation();
    const { preRotate = 0, previewImage } = this.state;
    const newRotate = preRotate + 90;
    this.setState({ preRotate: newRotate });
    localStorage.setItem(`${previewImage}-preRotate`, newRotate);
  };

  wheelImg = e => {
    if (e.nativeEvent.deltaY > 0) {
      this.zoomImg(-1);
    } else {
      this.zoomImg(1);
    }
  };

  moveImg = e => {
    e.stopPropagation();
  };

  onMouseDown = e => {
    e.stopPropagation();
    e.preventDefault();
    const { preMoveX, preMoveY } = this.state;
    this.defaultDownPosition.x = e.clientX;
    this.defaultDownPosition.y = e.clientY;
    this.tempPosition.x = preMoveX;
    this.tempPosition.y = preMoveY;
    this.isMoving = true;
  };

  onMouseMove = e => {
    e.stopPropagation();
    if (this.isMoving) {
      const preMoveX = e.clientX - this.defaultDownPosition.x + this.tempPosition.x;
      const preMoveY = e.clientY - this.defaultDownPosition.y + this.tempPosition.y;
      this.setState({
        preMoveX,
        preMoveY,
      });
    }
  };

  onMouseUpOrOut = e => {
    e.stopPropagation();
    e.preventDefault();
    const { preMoveX = 0, preMoveY = 0, previewImage } = this.state;
    localStorage.setItem(`${previewImage}-preMoveX`, preMoveX);
    localStorage.setItem(`${previewImage}-preMoveY`, preMoveY);
    this.moveTimer = setTimeout(() => {
      clearTimeout(this.moveTimer);
      this.isMoving = false;
    }, 50);
  };

  openReport = () => {
    let report = document.getElementsByClassName('antd-pro-pages-inquery-index-groupoper-chbtn2');
    report[0].click();
  };

  changeUrl(item) {
    if (!item.url) {
      return false;
    }

    const { protocol, host, port } = window.location;
    if (protocol == 'https:') {
      return item.url;
    }

    if (protocol == 'http:') {
      try {
        const imgUrl = item.url;
        const imgUrlArr = imgUrl.split('//')[1].split('/');
        imgUrlArr[0] = host;
        const newUrl = `${window.location.protocol}//${imgUrlArr.join('/')}`;
        return newUrl;
      } catch (error) {
        console.log('域名解析有误');
        return item.url;
      }
    }
  }

  render() {
    const { list = [], previewImage = '', previewModal, preZoom, preRotate, preMoveX = 0, preMoveY = 0, status } = this.state;
    const { chatInfo = {}, type = '' } = this.props;
    return (
      <Fragment>
        <div className={styles.msg} ref={ref => this.setChatContentNodeEvent(ref)} onClick={this.hideContextMenu}>
          {list.map((item, key) => {
            return (
              <div className={styles.msgItem} key={item.id} onContextMenu={() => this.contextMenu(item)}>
                {item.createTime ? <div className={styles.msgDate}>{item.createTime}</div> : null}
                {item.messageUserType == 1 ? (
                  <div className={`${styles.msgBox} ${styles.msgRight}`}>
                    <div className={styles.msgContent}>
                      <div className={styles.userInfo}>{item.sendUserName}</div>
                      {item.type == 1 ? <div className={styles.chartMsg} dangerouslySetInnerHTML={{ __html: item.content }} /> : null}
                      {item.type == 3 ? (
                        <div className={styles.chartMsg} onClick={() => this.previewImage(item.content)}>
                          <img alt="图片加载中..." src={this.changeUrl({ url: item.content })} onLoad={this.scrollIntoView} />
                        </div>
                      ) : null}
                      {item.type == 5 ? (
                        <div className={styles.chartMsg} onClick={() => this.previewImage(item.content)}>
                          <img alt="图片加载中..." style={{ width: 32 }} src={this.changeUrl({ url: item.content })} onLoad={this.scrollIntoView} />
                        </div>
                      ) : null}
                      {item.type == 6 ? (
                        <div className={styles.chartMsg}>
                          <img alt="图片加载中..." style={{ width: 32 }} src={MsgVideoWhite} />
                          {/* {item.content} */}
                        </div>
                      ) : null}
                      {item.type == 7 ? (
                        <div style={{ cursor: 'pointer' }} className={styles.chartMsg} onClick={this.openReport}>
                          <div style={{ borderBottom: '1px solid #fff', paddingBottom: '3px' }}>会诊报告</div>
                          <div style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{item.content}</div>
                        </div>
                      ) : null}
                    </div>
                    <div className={styles.msgHeader}>
                      <Avatar size="44" src={item.headImg} />
                    </div>
                  </div>
                ) : (
                  <div className={styles.msgBox}>
                    <div className={styles.msgHeader}>
                      <Avatar size="44" src={item.headImg} />
                    </div>
                    <div className={styles.msgContent}>
                      <div className={styles.userInfo}>{item.sendUserName}</div>
                      {item.type == 1 ? <div className={styles.chartMsg} dangerouslySetInnerHTML={{ __html: item.content }} /> : null}
                      {item.type == 3 ? (
                        <div className={styles.chartMsg} onClick={() => this.previewImage(item.content)}>
                          <img alt="图片加载中..." src={this.changeUrl({ url: item.content })} onLoad={this.scrollIntoView} />
                        </div>
                      ) : null}
                      {item.type == 5 ? (
                        <div className={styles.chartMsg} onClick={() => this.previewImage(item.content)}>
                          <img alt="图片加载中..." style={{ width: 32 }} src={this.changeUrl({ url: item.content })} onLoad={this.scrollIntoView} />
                        </div>
                      ) : null}
                      {item.type == 6 ? (
                        <div className={styles.chartMsg}>
                          <img alt="图片加载中..." style={{ width: 32 }} src={MsgVideo} />
                          {/* {item.content} */}
                        </div>
                      ) : null}
                      {item.type == 7 ? (
                        <div style={{ cursor: 'pointer' }} className={styles.chartMsg} onClick={this.openReport}>
                          {/* <div style={{ display: 'flex' }}> */}
                          {/* <div className={`${styles.chartHradBtn} ${styles.chbtn2}`} /> */}
                          <div style={{ borderBottom: '1px solid #fff', paddingBottom: '3px' }}>会诊报告</div>
                          {/* </div> */}
                          <div style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{item.content}</div>
                        </div>
                      ) : null}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
          {chatInfo.chatFilterType === '6' && chatInfo.id && chatInfo.isEndFeeType == 1 ? (
            <Fragment>
              <div className={styles.feeChatStartTip} style={{ paddingBottom: 0 }}>
                本次付费问诊已结束，您可以在“已结束”患者列表中继续发起对话。
              </div>
              <div className={styles.feeChatStartTip} style={{ paddingTop: 10 }}>
                本次全程服务已结束，您可以在“已结束”患者列表中继续发起对话。
              </div>
            </Fragment>
          ) : null}
          {type == '10' && status && (
            <div
              style={{
                width: '60%',
                background: '#ccc',
                borderRadius: '20px',
                textAlign: 'center',
                color: '#fff',
                margin: '20px auto',
              }}
            >
              {status == '3' ? '该笔问诊订单已结束' : '该笔问诊订单已取消'}
            </div>
          )}
        </div>
        {previewModal ? (
          <div className={styles.previewImg} onWheel={this.wheelImg} onClick={this.closePreviewImage} onMouseMove={this.onMouseMove} onMouseUp={this.onMouseUpOrOut}>
            <img
              onClick={e => e.stopPropagation()}
              src={previewImage}
              alt=""
              style={{
                maxHeight: '90%',
                maxWidth: '90%',
                transform: `scale(${preZoom}) translate(${preMoveX}px, ${preMoveY}px) rotate(${preRotate}deg)`,
              }}
              onMouseDown={this.onMouseDown}
            />
            <div className={styles.rotateOper} onClick={this.rotateImg} />
          </div>
        ) : null}
        {this.contextMenuBody()}
      </Fragment>
    );
  }
}

export default withRouter(Index);
