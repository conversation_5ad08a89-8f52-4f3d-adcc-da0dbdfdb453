import * as Api from './api'; // 确保路径正确
export default {
  namespace: 'reportmail',
  state: {
    detailVisible: false,
    currentRecord: null,
    queryParams: {
      reportStartDate: '',
      reportEndDate: '',
    },
    listData: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  },

  reducers: {
    openDetail(state, { payload }) {
      return { ...state, currentRecord: payload.record, queryParams: payload.queryParams, listData: [], detailVisible: true };
    },
    closeDetail(state) {
      return { ...state, detailVisible: false };
    },
    updateDetail(state, { payload }) {
      return {
        ...state,
        listData: payload.recordList,
        pagination: {
          current: payload.currentPage,
          pageSize: payload.numPerPage,
          total: payload.totalCount,
        },
      };
    },
  },
  effects: {
    *fetchDetail({ payload }, { call, put }) {
      const res = yield call(Api.getreportmaildetail, payload);
      if (res.code === 0) {
        yield put({ type: 'updateDetail', payload: res.data });
      }
    },
  },
};
