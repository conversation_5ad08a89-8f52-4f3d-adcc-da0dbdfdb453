@import '~antd/lib/style/themes/default.less';

.connect {
  background: #fff;
  min-height: 100vh;
  box-sizing: border-box;
  padding: 16px 21px;
}

.flexLine {
  display: flex;
  align-items: center;
  .flexItem {
    flex: 1;
  }
}

.modalLine {
  margin-bottom: 10px;
  .modalLabel {
    margin-bottom: 5px;
  }
}

.tableOperText {
  color: @primary-color;
  margin-left: 15px;
  cursor: pointer;

  &:first-child {
    margin-left: 0;
  }
}

.tableOperTextDisable {
  color: #888;
  cursor: normal;
}

.status1 {
  color: orange;
}
.status2 {
  color: greenyellow;
}
