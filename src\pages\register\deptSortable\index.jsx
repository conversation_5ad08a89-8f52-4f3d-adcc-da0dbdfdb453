import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Tabs } from 'antd';
import { Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';

import IIHOCSortable from './components/IIHOCSortable';
import Cell from './components/Cell';

import '../../microwebsite/style.less';

import * as utils from '../../../utils/utils';

const TabPane = Tabs.TabPane;

export default connect(state => {
  return {
    hisList: state.register.hospital.list,
    list: state.register.sortable.dept,
  };
})(
  class DeptmentSortable extends React.Component {
    componentDidMount() {
      const { dispatch } = this.props;
      dispatch({
        type: 'register/hospitalList',
        next: 'deptSortable',
      });
    }

    componentWillUnmount() {
      this.props.dispatch({
        type: 'register/saveSortable',
        payload: {
          backIds: [],
          dept: [],
        },
      });
    }

    deptSortable = payload => {
      const { dispatch } = this.props;
      dispatch({
        type: 'register/deptSortable',
        payload,
      });
    };

    render() {
      const { hisList = [], list = [], location } = this.props;
      if (!hisList || hisList.length == 0) return null;
      const hisId = utils.queryStringToJson(location.search).hisId || (hisList && hisList.length > 0 && hisList[0].hisId);

      const SortHtml = IIHOCSortable({
        hisId,
        list,
        WrappedComponent: Cell,
        cellClickable: true,
        submitEffectType: 'register/submitDeparmentSortable',
        refreshEffectType: 'register/deptSortable',
        btnIdentity: '/register/deptSortable/edit',
      });

      return (
        <div className="page-doctor">
          <Tabs
            animated={false}
            defaultActiveKey={`${hisId}`}
            style={{ display: 'flex', flex: 'auto', flexDirection: 'column' }}
            onChange={id => {
              this.deptSortable({
                hisId: id,
              });
              history.push(`/register/deptSortable?hisId=${id}`);
            }}
          >
            {hisList.map(item => {
              return (
                <TabPane tab={item.hisName} key={`${item.hisId}`}>
                  <div className="doctor-body">
                    <div className="doctor-list">
                      <div style={{ padding: '6px 0 26px', display: 'flex', justifyContent: 'center' }}>
                        <div style={{ paddingTop: 1 }}>
                          <Icon type="infocircle" style={{ fontSize: 14, color: '#3F969D', marginRight: 8 }} />
                        </div>
                        <div>操作说明：1、请拖动对象到相应的位置并保存，手机端将同步更新排列顺序；2、点击具体科室进入下级科室排序。</div>
                      </div>
                      <SortHtml />
                    </div>
                  </div>
                </TabPane>
              );
            })}
          </Tabs>
        </div>
      );
    }
  },
);
