/*
此文件不走 css modules，且会自动被引入，可以在这里写全局样式，以及做样式覆盖。
*/
.ant-tabs-nav {
  margin: 0 !important;
  padding: 14px 4px 0;
  background: #fff;
}
.ant-btn {
  padding: 0 15px;
}
// 菜单页容器
.g-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  .container {
    flex: 1;
    margin: 24px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 4px;
    .g-table-opt-cell {
      margin-bottom: 16px;
      .ant-btn {
        &:not(:last-child) {
          margin-right: 8px;
        }
      }
    }
  }
}
// 筛选条件查询
.g-query-box {
  padding: 24px;
  background: #fff;
  .ant-col-8 {
    display: flex;
    align-items: center;
    .query-lable {
      flex-shrink: 0;
      color: rgba(0, 0, 0, 0.85);
    }
    .ant-input {
      flex: 1;
    }
    .ant-btn {
      &:not(:last-child) {
        margin-right: 8px;
      }
    }
  }
  .text-right {
    display: block;
    text-align: right;
    .ant-btn:not(:last-child) {
      margin-right: 8px;
    }
  }
}
// 权限表格
.menu-tree-table {
  border: 1px solid #f0f0f0;
  border-radius: 2px;
  .table-title {
    color: rgba(0, 0, 0, 0.85);
    background: #fafafa;
  }
  .table-title,
  .menu-item {
    display: flex;
    .menu-first {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 200px;
      padding: 16px;
      border-right: 1px solid #f0f0f0;
      border-bottom: 1px solid #f0f0f0;
    }
    .menu-child {
      flex: 1;
      > div {
        display: flex;
        &.auto-height {
          height: 100%;
        }
        .menu-second {
          flex-shrink: 0;
          display: flex;
          flex-direction: column;
          justify-content: center;
          width: 200px;
          padding: 16px;
          border-right: 1px solid #f0f0f0;
          border-bottom: 1px solid #f0f0f0;
        }
        .actions {
          flex: 1;
          padding: 16px;
          border-bottom: 1px solid #f0f0f0;
          .ant-checkbox-wrapper {
            margin: 0 8px 8px 0;
          }
        }
      }
    }
    &:last-child {
      .menu-first,
      .menu-child > div:last-child .menu-second,
      .menu-child > div:last-child .actions {
        border-bottom: none;
      }
    }
  }
}
