/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select, DatePicker } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { fxstemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, fxstemplate: { ...fxstemplate, ...payload } },
      },
    });
  };

  const changeTableData = (val, ind, key) => {
    const { field20 = [] } = fxstemplate;
    field20[ind][key] = val;
    changeData({ field20 });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="临床信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="临床症状">
                <Checkbox.Group value={fxstemplate.field1 ? fxstemplate.field1.split(',') : []} onChange={v => changeData({ field1: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="智力低下">智力低下</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="发育迟缓">发育迟缓</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="孤独症谱系障碍">孤独症谱系障碍</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="行为异常">行为异常</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="震颤/共济失调">震颤/共济失调</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="卵巢早衰">卵巢早衰</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="其他">其他</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="不良生育史">
                <div className="flex-box">
                  <Radio.Group value={fxstemplate.field2} onChange={e => changeData({ field2: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="近亲婚配">
                <div className="flex-box">
                  <Radio.Group value={fxstemplate.field3} onChange={e => changeData({ field3: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">与配偶关系</div>
                    <Input placeholder="请输入" value={fxstemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
                  </div>
                  <div className="flex-box">
                    <div className="flex-shrink">说明</div>
                    <Input placeholder="请输入" value={fxstemplate.field5} onChange={e => changeData({ field5: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="家族史">
                <div className="flex-box">
                  <Radio.Group value={fxstemplate.field12} onChange={e => changeData({ field12: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">与受检者关系</div>
                    <Input placeholder="请输入" value={fxstemplate.field13} onChange={e => changeData({ field13: e.target.value })} />
                  </div>
                  <div className="flex-box">
                    <div className="flex-shrink">说明</div>
                    <Input placeholder="请输入" value={fxstemplate.field14} onChange={e => changeData({ field14: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="其他遗传病史">
                <div className="flex-box">
                  <Radio.Group value={fxstemplate.field15} onChange={e => changeData({ field15: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <Input placeholder="请输入" value={fxstemplate.field16} onChange={e => changeData({ field16: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="近期是否进行过骨髓移植、或细胞治疗、或接受输血">
                <div className="flex-box">
                  <Radio.Group value={fxstemplate.field17} onChange={e => changeData({ field17: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">最近一次日期</div>
                    <DatePicker format="YYYY-MM-DD" value={fxstemplate.field18 ? moment(fxstemplate.field18) : null} onChange={(date, dateString) => changeData({ field18: dateString })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="亲属（包括本人）做过此检测">
                <div className="flex-box">
                  <Radio.Group value={fxstemplate.field19} onChange={e => changeData({ field19: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24} style={{ marginBottom: '24px' }}>
              <Table
                dataSource={fxstemplate.field20 || []}
                columns={[
                  {
                    title: '亲属检测编号',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field1} onChange={e => changeTableData(e.target.value, index, 'field1')} />;
                    },
                  },
                  {
                    title: '亲属姓名',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field2} onChange={e => changeTableData(e.target.value, index, 'field2')} />;
                    },
                  },
                  {
                    title: '亲属关系',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field3} onChange={e => changeTableData(e.target.value, index, 'field3')} />;
                    },
                  },
                  {
                    title: '亲属检测结果',
                    render: (cur, col, index) => {
                      return (
                        <Select placeholder="请选择" style={{ width: '100px' }} value={col.field4} onChange={v => changeTableData(v, index, 'field4')}>
                          <Option value="正常">正常</Option>
                          <Option value="携带者">携带者</Option>
                          <Option value="SMA患者">患者</Option>
                        </Select>
                      );
                    },
                  },
                  {
                    title: '操作',
                    width: 60,
                    render: (cur, col, index) => (
                      <Button type="link" onClick={() => changeData({ field20: fxstemplate.field20.filter((item, ind) => index != ind) })}>
                        删除
                      </Button>
                    ),
                  },
                ]}
                pagination={false}
              />
              <Button type="dashed" block onClick={() => changeData({ field20: [...(fxstemplate.field20 || []), {}] })}>
                +添加亲属
              </Button>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
