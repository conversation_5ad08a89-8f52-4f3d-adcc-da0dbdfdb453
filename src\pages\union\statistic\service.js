import { post } from '@/utils/request';

export const fetchDetailList = (param = {}) => post('/api/sampleStat/productDetailCount', { data: param });

export const fetchTotalList = (param = {}) => post('/api/sampleStat/productAllCount', { data: param });

export const findAllUser = (param = {}) => post('/api/userinfo/findAllUser', { data: param });

export const getInstitution = (param = {}) => post('/api/institution/get-by-list', { data: param });

export const getProduct = (param = {}) => post('/api/product/get-products', { data: param });
