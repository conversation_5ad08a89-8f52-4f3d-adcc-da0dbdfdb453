import React from 'react';
import { Modal, Radio, Button, Checkbox, Select } from 'antd';

const RadioGroup = Radio.Group;
const { Option } = Select;

// 定时接收 时间列表
export const scheduleList = [
  { label: '08:00', value: '08:00' },
  { label: '09:00', value: '09:00' },
  { label: '10:00', value: '10:00' },
  { label: '11:00', value: '11:00' },
  { label: '12:00', value: '12:00' },
  { label: '13:00', value: '13:00' },
  { label: '14:00', value: '14:00' },
  { label: '15:00', value: '15:00' },
  { label: '16:00', value: '16:00' },
  { label: '17:00', value: '17:00' },
  { label: '18:00', value: '18:00' },
  { label: '19:00', value: '19:00' },
  { label: '20:00', value: '20:00' },
  { label: '21:00', value: '21:00' },
  { label: '22:00', value: '22:00' },
];

// 延时接收 时间列表
export const delayList = [
  { label: '10 分钟后', value: '10' },
  { label: '20 分钟后', value: '20' },
  { label: '30 分钟后', value: '30' },
  { label: '1 小时后', value: '60' },
  { label: '2 小时后', value: '120' },
  { label: '3 小时后', value: '180' },
  { label: '4 小时后', value: '240' },
  { label: '5 小时后', value: '300' },
  { label: '6 小时后', value: '360' },
  { label: '7 小时后', value: '420' },
  { label: '8 小时后', value: '480' },
  { label: '1 天后', value: '1440' },
  { label: '2 天后', value: '2880' },
  { label: '3 天后', value: '4320' },
  { label: '4 天后', value: '5760' },
  { label: '5 天后', value: '7200' },
  { label: '6 天后', value: '8640' },
  { label: '7 天后', value: '10080' },
  { label: '8 天后', value: '11520' },
  { label: '9 天后', value: '12960' },
  { label: '10 天后', value: '14400' },
];

// 接收后 时间列表
export const receiveList = [
  { label: '30 分钟内', value: '30' },
  { label: '1 小时内', value: '60' },
  { label: '2 小时内', value: '120' },
  { label: '3 小时内', value: '180' },
  { label: '4 小时内', value: '240' },
  { label: '5 小时内', value: '300' },
  { label: '6 小时内', value: '360' },
  { label: '7 小时内', value: '420' },
  { label: '8 小时内', value: '480' },
  { label: '9 小时内', value: '540' },
  { label: '10 小时内', value: '600' },
  { label: '11 小时内', value: '660' },
  { label: '12 小时内', value: '720' },
  { label: '1 天内', value: '1440' },
  { label: '2 天内', value: '2880' },
  { label: '3 天内', value: '4320' },
  { label: '4 天内', value: '5760' },
  { label: '5 天内', value: '7200' },
  { label: '6 天内', value: '8640' },
  { label: '7 天内', value: '10080' },
];

class ConditionModal extends React.Component {
  render() {
    const { defaultData, visible, cancleFunc, okFunc, busTypeChange, timePeriodChange, timeChange, labelData, limitFlagChange, pushTimelimitChange } = this.props;
    return (
      <Modal
        wrapClassName="condition-modal"
        title="选择参与条件"
        visible={visible}
        onCancel={() => cancleFunc()}
        footer={[
          <Button key="back" size="large" onClick={() => cancleFunc()}>
            取消
          </Button>,
          <Button key="submit" type="primary" size="large" onClick={() => okFunc()}>
            保存
          </Button>,
        ]}
      >
        {labelData.map(item => {
          return (
            <div key={item.dictKey} className="trait-modal-formitem">
              <Checkbox checked={defaultData[item.dictKey] ? 1 : false} value={item.dictKey} onChange={e => busTypeChange(e)}>
                {item.dictValue}
              </Checkbox>
              <RadioGroup value={defaultData[item.dictKey] && defaultData[item.dictKey].pushType ? defaultData[item.dictKey].pushType : ''} onChange={e => timePeriodChange(item.dictKey, e)}>
                <Radio value="0">立即接收</Radio>
                <Radio value="2">
                  <span>定时接收</span>
                  <Select
                    className="time-select"
                    defaultValue={defaultData[item.dictKey] && defaultData[item.dictKey].pushType ? (defaultData[item.dictKey].pushType == 2 ? defaultData[item.dictKey].pushParam : '10:00') : '10:00'}
                    onChange={value => timeChange(item.dictKey, value, '2')}
                  >
                    {scheduleList.map(v => (
                      <Option key={v.value} value={v.value}>
                        {v.label}
                      </Option>
                    ))}
                  </Select>
                </Radio>
                <Radio value="1">
                  <span>延时接收</span>
                  <Select
                    className="time-select"
                    defaultValue={defaultData[item.dictKey] && defaultData[item.dictKey].pushType ? (defaultData[item.dictKey].pushType == 1 ? defaultData[item.dictKey].pushParam : '10') : '10'}
                    onChange={value => timeChange(item.dictKey, value, '1')}
                  >
                    {delayList.map(v => (
                      <Option key={v.value} value={v.value}>
                        {v.label}
                      </Option>
                    ))}
                  </Select>
                </Radio>
              </RadioGroup>
              <Checkbox
                checked={!!(defaultData[item.dictKey] && defaultData[item.dictKey].limitFlag == 1)}
                // value={defaultData[item.dictKey] && defaultData[item.dictKey].limitFlag ? defaultData[item.dictKey].limitFlag : 0}
                onChange={e => limitFlagChange(item.dictKey, e)}
              >
                接收后
              </Checkbox>
              <Select
                className="time-select time-select-only"
                defaultValue={defaultData[item.dictKey] && defaultData[item.dictKey].limitFlag ? (defaultData[item.dictKey].limitFlag == 1 ? defaultData[item.dictKey].pushTimelimit : '30') : '30'}
                onChange={value => pushTimelimitChange(item.dictKey, value)}
              >
                {receiveList.map(v => (
                  <Option key={v.value} value={v.value}>
                    {v.label}
                  </Option>
                ))}
              </Select>
              不再推送
            </div>
          );
        })}
      </Modal>
    );
  }
}

export default ConditionModal;
