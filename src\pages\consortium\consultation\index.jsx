import React, { Component } from 'react';
import { connect } from 'dva';
import { Link } from 'react-router-dom';
import { Select, Row, Col, Button, Table, DatePicker, Input, Tooltip } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';
import * as Api from './api';
import styles from './index.less';

const { TextArea } = Input;

@connect(({ user }) => ({ currentUser: user.currentUser }))
@Form.create()
class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      tableData: {}, // 病友数据
      statusGroup: [
        { id: 0, name: '待审核' },
        { id: 1, name: '待会诊' },
        { id: 2, name: '会诊中' },
        { id: 3, name: '已结束' },
        { id: 4, name: '申请不通过' },
        { id: 5, name: '申请取消' },
        { id: 6, name: '会诊取消' },
      ], // 服务团队
      searchIpt: '', // 搜索框文本
      pageSize: 10,
      hospitalArr: [],
      doctorHospitalId: '',
    };
    // 表格每页条数
    this.tableSize = ['10', '20', '30', '40', '50', '100'];
    // 表单样式
    this.formItemLayout = {
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 14,
      },
    };
    // 表格表头和每列数据
    this.tableColumns = [
      {
        title: '申请时间',
        dataIndex: 'applyTime',
      },
      {
        title: '会诊单号',
        dataIndex: 'orderNo',
        width: 180,
        render: Text => {
          return (
            <Tooltip title={Text}>
              <span>{Text || '暂无'}</span>
            </Tooltip>
          );
        },
      },
      {
        title: 'PID',
        dataIndex: 'patientPid',
        width: 100,
      },
      {
        title: '患者姓名',
        dataIndex: 'patientName',
      },
      {
        title: '邀请方医院名称',
        dataIndex: 'doctorHospitalName',
        width: 150,
      },
      {
        title: '申请医生',
        dataIndex: 'doctorName',
        width: 120,
      },
      {
        title: '执行状态',
        dataIndex: 'status',
        render: val => {
          switch (val) {
            case 0:
              return '待审核';
            case 1:
              return '待会诊';
            case 2:
              return '会诊中';
            case 3:
              return '已结束';
            case 4:
              return '申请不通过';
            case 5:
              return '申请取消';
            case 6:
              return '会诊取消';
          }
        },
      },

      // {
      //   title: '创建时间',
      //   dataIndex: 'createTime',
      // },
      {
        title: '操作',
        width: 120,
        render: record => {
          const { protocol, host } = window.location;
          return <Link to={`/consortium/consultation/detail?consultationId=${record.id}&role=manage`}>详情</Link>;
        },
      },
    ];
  }

  componentDidMount() {
    this.getTableData();
    this.getAllianceinfo();
  }

  //获取所有医院
  getAllianceinfo = async () => {
    const { code, data } = await Api.getAllianceinfo({ numPerPage: 9999 });
    if (code == 0) {
      const { recordList = [] } = data;
      this.setState({
        hospitalArr: recordList,
      });
    }
  };

  // 获取查询参数
  getQueryParam = () => {
    const { tableData = {}, pageSize = 10, searchIpt = '' } = this.state;
    const {
      form: { getFieldsValue },
    } = this.props;
    const value = getFieldsValue();
    const { doctorName, status, orderNo, doctorHospitalId, patientPidOrName, applyTime = ['', ''], createTime = ['', ''] } = value;
    let param = {
      status,
      orderNo,
      patientPidOrName,
      doctorName,
    };
    // 处理注册时间和初诊时间
    if (applyTime[0] && applyTime[1]) {
      param.applyBeginDate = applyTime[0] && moment(applyTime[0]).format('YYYY-MM-DD');
      param.applyEndDate = applyTime[1] && moment(applyTime[1]).format('YYYY-MM-DD');
      delete param.applyTime;
    }
    if (createTime[0] && createTime[1]) {
      param.chatCreateBeginDate = createTime[0] && moment(createTime[0]).format('YYYY-MM-DD');
      param.chatCreateEndDate = createTime[1] && moment(createTime[1]).format('YYYY-MM-DD');
      delete param.createTime;
    }
    return param;
  };

  // 获取所有会诊记录
  getTableData = async (pageNum = 1) => {
    const { pageSize, doctorHospitalId } = this.state;
    const timeData = this.getQueryParam();
    const { doctorName, orderNo, patientPidOrName, status, applyEndDate, applyBeginDate, chatCreateBeginDate, chatCreateEndDate } = timeData;
    let param = {
      pageNum,
      numPerPage: pageSize || 10,
      chatCreateBeginDate,
      chatCreateEndDate,
      applyBeginDate,
      applyEndDate,
      status,
      orderNo,
      doctorHospitalId,
      patientPidOrName,
      doctorName,
    };
    const { code, data = {} } = await Api.getAllRecordList(param);
    if (code == 0) {
      this.setState({ tableData: data });
    }
  };

  selectAttendingDoctor = (value, option, judge) => {
    const {
      props: { id = '' },
    } = option;
    let param = {};
    if (judge == 'mainDoctor') {
      param.mainDoctorId = id;
      param.mainDoctorName = value;
    }
    if (judge == 'doctorHospitalId') {
      param.doctorHospitalId = id;
    }
    this.setState(param);
  };

  // 根据条件渲染下拉选择框
  getSelect = (label, name, options, optionConfig = {}, selectProps = {}) => {
    const { key = 'dictKey', value = 'dictValue' } = optionConfig;
    const {
      form: { getFieldDecorator },
    } = this.props;
    return (
      <Col span={8} className={styles['col-item']}>
        <Form.Item label={label}>
          {getFieldDecorator(name)(
            <Select placeholder="请选择" {...selectProps}>
              <Select.Option value="">全部</Select.Option>
              {options.map(opt => {
                return <Select.Option key={opt[key]}>{opt[value]}</Select.Option>;
              })}
            </Select>,
          )}
        </Form.Item>
      </Col>
    );
  };

  getFindSelect = (label, consulationOptions, judge, span) => {
    const {
      form: { getFieldDecorator },
    } = this.props;
    return (
      <Col span={span} className={styles['col-item']}>
        <Form.Item label={label}>
          {getFieldDecorator(judge)(
            <Select
              style={{ width: '100%' }}
              placeholder="请选择"
              onChange={(value, option) => this.selectAttendingDoctor(value, option, judge)}
              showSearch={true}
              filterOption={(input, option) => {
                return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }}
            >
              {judge != 'mainDoctor' && (
                <Select.Option id="" value="">
                  全部
                </Select.Option>
              )}
              {consulationOptions}
            </Select>,
          )}
        </Form.Item>
      </Col>
    );
  };

  // 表格每页行数改变
  onShowSizeChange = (c, size) => {
    this.setState({ pageSize: size }, this.getTableData);
  };

  // 大于当前日期不能选
  disabledDate = time => {
    if (!time) {
      return false;
    }
    return time > moment();
  };

  render() {
    const { statusGroup, tableData = {}, pageSize, hospitalArr = [], doctorHospitalId } = this.state;

    const {
      form,
      form: { getFieldDecorator },
    } = this.props;

    const hospitalOptions = hospitalArr.map(d => (
      <Select.Option id={d.id} key={d.id}>
        {d.institutionName}
      </Select.Option>
    ));

    return (
      <div id="consultationmanagement" className="g-page">
        <Form className="g-query-box">
          <Row gutter={[16, 24]}>
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="会诊单号">{getFieldDecorator('orderNo')(<Input style={{ width: '100%' }} placeholder="请输入" />)}</Form.Item>
            </Col>
            {this.getFindSelect('邀请方医院', hospitalOptions, 'doctorHospitalId', 8)}
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="医生姓名">{getFieldDecorator('doctorName')(<Input style={{ width: '100%' }} placeholder="请输入医联体医生姓名" />)}</Form.Item>
            </Col>
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="病友姓名">{getFieldDecorator('patientPidOrName')(<Input style={{ width: '100%' }} maxLength={10} placeholder="请输入病友姓名或PID" />)}</Form.Item>
            </Col>
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="申请时间">
                {getFieldDecorator('applyTime')(<DatePicker.RangePicker showTime allowClear disabledDate={this.disabledDate} format="YYYY-MM-DD" style={{ width: '100%' }} />)}
              </Form.Item>
            </Col>
            {/* <Col span={8} className={styles['col-item']}>
              <Form.Item label="创建时间">{getFieldDecorator('createTime')(<DatePicker.RangePicker allowClear format="YYYY-MM-DD" style={{ width: '100%' }} />)}</Form.Item>
            </Col> */}
            {this.getSelect('执行状态', 'status', statusGroup, { key: 'id', value: 'name' })}
            <Col span={24} className="text-right">
              <Button type="primary" onClick={() => this.getTableData()}>
                查询
              </Button>
              <Button
                onClick={() => {
                  form.resetFields(), this.setState({ doctorHospitalId: '' }, this.getTableData);
                }}
              >
                重置
              </Button>
            </Col>
          </Row>
        </Form>
        <Table
          key={tableData.currentPage}
          style={{ marginTop: 16, background: '#fff', margin: '20px' }}
          dataSource={tableData.recordList || []}
          columns={this.tableColumns}
          rowKey={(record, index) => {
            return record.id;
          }}
          pagination={{
            total: tableData.totalCount || 0,
            showTotal: total => `共 ${total} 条`,
            onChange: pageNum => {
              this.getTableData(pageNum);
            },
            current: tableData.currentPage || 0,
            pageSize,
            showQuickJumper: true,
            showSizeChanger: true,
            onShowSizeChange: this.onShowSizeChange,
            pageSizeOptions: this.tableSize,
          }}
        />
      </div>
    );
  }
}

export default Index;
