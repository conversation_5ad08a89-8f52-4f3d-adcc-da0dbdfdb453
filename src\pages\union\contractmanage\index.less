.page-contractmanage {
  .g-query-box {
    .col-item {
      .ant-form-item {
        flex: auto;
        margin-bottom: 0;
        .ant-picker {
          width: 100%;
        }
        .text-right {
          text-align: right;
          .ant-btn:not(:last-child) {
            margin-right: 8px;
          }
        }
      }
    }
    .ant-btn-link {
      color: #3f969d;
    }
  }
  .ellipsis {
    max-width: 110px;
    overflow: hidden;
    word-wrap: normal;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .ant-input-search-button {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .anticon-search {
    width: 16px;
  }
  .container {
    display: flex;
    flex-direction: column;
    .table-box {
      flex: 1;
      position: relative;
      .table-content {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        th,
        td {
          min-width: 88px;
          &:first-child {
            min-width: 60px;
          }
          &:nth-last-child(2) {
            min-width: 116px;
          }
          &:nth-last-child(3),
          &:nth-last-child(10) {
            min-width: 102px;
          }
        }
      }
    }
  }
}
