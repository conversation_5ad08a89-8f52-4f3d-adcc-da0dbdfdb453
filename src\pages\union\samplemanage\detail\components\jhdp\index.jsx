/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, DatePicker, Checkbox, Table, Button } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { jhdpsddpTemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, jhdpsddpTemplate: { ...jhdpsddpTemplate, ...payload } },
      },
    });
  };

  const changeTableData = (val, ind, key) => {
    const { field16 = [] } = jhdpsddpTemplate;
    field16[ind][key] = val;
    changeData({ field16 });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="临床信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="血液检测" style={{ marginBottom: 0 }}>
                <Row gutter={[16, 0]}>
                  <Col span={8}>
                    <Form.Item label="MCV(fl)">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field1} onChange={e => changeData({ field1: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="MCH(pg)">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field2} onChange={e => changeData({ field2: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="RBC(×10 12)">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field3} onChange={e => changeData({ field3: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="Hb(g/L)">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="HbA(%)">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field5} onChange={e => changeData({ field5: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="HbA2(%)">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field6} onChange={e => changeData({ field6: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="HbF(%)">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field7} onChange={e => changeData({ field7: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="HbBart’s(%)">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field8} onChange={e => changeData({ field8: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="血清铁( umol/L)">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field9} onChange={e => changeData({ field9: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="血清铁蛋白( ng/mL)">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field10} onChange={e => changeData({ field10: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="其他 Hb">
                      <Input placeholder="请输入" value={jhdpsddpTemplate.field11} onChange={e => changeData({ fiefield11ld10: e.target.value })} />
                    </Form.Item>
                  </Col>
                </Row>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="输血史">
                <div className="flex-box">
                  <Radio.Group value={jhdpsddpTemplate.field12} onChange={e => changeData({ field12: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">最近一次日期</div>
                    <DatePicker format="YYYY-MM-DD" value={jhdpsddpTemplate.field13 ? moment(jhdpsddpTemplate.field3) : null} onChange={(date, dateString) => changeData({ field13: dateString })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item>
                <Checkbox.Group value={jhdpsddpTemplate.field14 ? jhdpsddpTemplate.field14.split(',') : []} onChange={v => changeData({ field14: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={4}>
                      <Checkbox value="细胞治疗">细胞治疗</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="移植手术">移植手术</Checkbox>
                    </Col>
                    <Col span={5}>
                      <Checkbox value="家族遗传病">家族遗传病</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="否">否</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="是">是</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="亲属（包括本人）做过此检测">
                <div className="flex-box">
                  <Radio.Group value={jhdpsddpTemplate.field15} onChange={e => changeData({ field15: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24} style={{ marginBottom: '24px' }}>
              <Table
                dataSource={jhdpsddpTemplate.field16 || []}
                columns={[
                  {
                    title: '亲属检测编号',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field1} onChange={e => changeTableData(e.target.value, index, 'field1')} />;
                    },
                  },
                  {
                    title: '亲属姓名',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field2} onChange={e => changeTableData(e.target.value, index, 'field2')} />;
                    },
                  },
                  {
                    title: '亲属关系',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field3} onChange={e => changeTableData(e.target.value, index, 'field3')} />;
                    },
                  },
                  {
                    title: '亲属检测结果',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field4} onChange={e => changeTableData(e.target.value, index, 'field4')} />;
                    },
                  },
                  {
                    title: '操作',
                    width: 60,
                    render: (cur, col, index) => (
                      <Button type="link" onClick={() => changeData({ field16: jhdpsddpTemplate.field16.filter((item, ind) => index != ind) })}>
                        删除
                      </Button>
                    ),
                  },
                ]}
                pagination={false}
              />
              <Button type="dashed" block onClick={() => changeData({ field16: [...(jhdpsddpTemplate.field16 || []), {}] })}>
                +添加亲属
              </Button>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
