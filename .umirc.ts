import { defineConfig } from 'umi';

const { APP_TYPE, TEST } = process.env;

export default defineConfig({
  title: false,
  // favicon: '/favicon.ico',
  hash: true,
  history: {
    type: 'hash',
  },
  define: {
    APP_TYPE: APP_TYPE || '',
    HIS_NAME: '家辉医遗传',
    PRIMARY_COLOR: '#3F969D',
    DOMAIN: '/merchant',
    API_DOMAIN: '/merchant',
    IMG_DOMAIN: '/merchant/umi',
  },
  ignoreMomentLocale: true,
  publicPath: `./`,
  nodeModulesTransform: {
    type: 'none',
  },
  dva: {
    immer: false,
    hmr: false,
  },
  inlineLimit: 1000,
  theme: {
    '@primary-color': '#3F969D',
    '@link-color': '#0D85ED',
    '@info-color': '#0ae',
    '@success-color': '#3F969D',
    '@warning-color': '#FAAD14',
    '@error-color': '#FF613B',
    '@danger-color': '#FF613B',
    '@title-color': 'rgba(0, 0, 0, 0.85)',
    '@text-color': 'rgba(0, 0, 0, 0.65)',
  },
  proxy: {
    '/merchant/api': {
      target: 'https://wechatdev.jiahuiyiyuan.com',
      //target: 'https://wechat.jiahuiyiyuan.com',
      changeOrigin: true,
      // pathRewrite: { "^/api/oper" : "" },
    },
    '/barcode': {
      target: 'https://wechatdev.jiahuiyiyuan.com',
      //target: 'https://wechat.jiahuiyiyuan.com',
      changeOrigin: true,
      // pathRewrite: { "^/api/oper" : "" },
    },
    '/api/qrcode/withlogo': {
      target: 'https://wechatdev.jiahuiyiyuan.com',
      //target: 'https://wechat.jiahuiyiyuan.com',
      changeOrigin: true,
      // pathRewrite: { "^/api/oper" : "" },
    },
    '/daemon/sign/report': {
      target: 'https://wechatdev.jiahuiyiyuan.com',
      changeOrigin: true,
    },
  },
});
