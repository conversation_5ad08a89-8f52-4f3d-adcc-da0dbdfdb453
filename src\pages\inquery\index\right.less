@import '~antd/lib/style/themes/default.less';

.rightContent {
  margin: 0 24px 0 0;
  min-width: 500px;
  position: absolute;
  top: 0;
  bottom: 0;
  height: calc(100vh - 64px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.iconCall {
  width: 19px;
  min-width: 19px;
  height: 19px;
  background: url('../../../assets/phone.png') no-repeat center center / 100% auto;
}

.iconUnCall {
  width: 19px;
  min-width: 19px;
  height: 19px;
  background: url('../../../assets/light-phone.png') no-repeat center center / 100% auto;
}

.contentCard {
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 16px 16px 0 16px;
  box-shadow: 0px 2px 6px 0px rgba(15, 53, 81, 0.1);
  background: #fff;

  :global {
    .ant-tabs-nav {
      padding: 0;
    }
    .ant-tabs-nav-wrap {
      padding: 14px 25px;
    }
    .ant-tabs-ink-bar {
      display: none !important;
    }
    .ant-tabs-nav .ant-tabs-tab {
      padding: 4px 16px;
      border-radius: 16px;
      color: #1f2a3c;
      font-size: 14px;
      transition: none;
      margin: 0 32px 0 0;
    }
    .ant-tabs-nav .ant-tabs-tab-active {
      background: @primary-color;
      .ant-tabs-tab-btn {
        color: #fff;
      }
      &:hover {
      }
    }
    .ant-tabs-nav .ant-tabs-tab:last-child {
      margin-right: 50px;
    }
    .ant-tabs-bar {
      margin: 0;
    }
  }
}

.msgCreator {
  padding: 0 20px;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}

.cicleLabel {
  display: inline-block;
  width: 20px;
  height: 20px;
  position: relative;
  top: 4px;
  margin-right: 4px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);

  &:before {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    font-size: 12px;
    z-index: 1;
    color: #fff;
    border-radius: 50%;
    text-align: center;
    line-height: 18px;
  }

  &.label1:before {
    content: '初';
    background: #6fc164;
  }

  &.label2:before {
    content: '复';
    background: #ff8940;
  }

  &.label3:before {
    content: '备';
    background: #46c6ff;
  }
}

.tabsContent {
  // margin-top: 30px;
  flex: 1;
  display: flex;
  flex-direction: column;

  :global(.ant-tabs-content) {
    flex: auto;
  }

  :global {
    .ant-tabs-tabpane {
      display: flex;
      flex-direction: column;
      position: relative;
    }
  }

  .bsgkBox {
    flex: auto;
    display: flex;
    flex-direction: column;
  }
}

.tabIntro {
  flex: auto;
  display: flex;
  flex-direction: column;

  .recordTop {
    flex: 1;
    overflow-y: hidden;
    position: relative;
  }

  .recordScroll {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    overflow-y: auto;
    color: #000;
  }

  .timeline {
    margin-top: 16px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 20px;
    padding-left: 20px;
    color: #151d43;
    position: relative;
    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      border-left: 5px solid @primary-color;
      height: 24px;
    }
  }

  .tabStep {
    padding: 5px 0;
    margin: 0 20px;
    background: #f4f8fc;
    overflow-x: auto;

    .timeDateLine {
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      display: flex;

      .timeDate {
        width: 104px;
        text-align: center;
      }
    }

    .statusIcon1 {
      margin: 0 6px;
      width: 20px;
      height: 20px;
      background: url(../../../assets/stepStatus1.png) no-repeat center center / 12px auto;
    }
    .statusIcon2 {
      margin: 0 6px;
      width: 20px;
      height: 20px;
      background: url(../../../assets/stepStatus2.png) no-repeat center center / 12px auto;
    }
    .statusIcon3 {
      margin: 0 6px;
      width: 20px;
      height: 20px;
      background: url(../../../assets/stepStatus3.png) no-repeat center center / 12px auto;
    }
    .statusIcon4 {
      margin: 0 6px;
      width: 20px;
      height: 20px;
      background: url(../../../assets/stepStatus4.png) no-repeat center center / 22px auto;
    }
    :global {
      .ant-steps-item-content {
        margin-top: 0;
        .ant-steps-item-title {
          line-height: 1;
          font-size: 16px;
          color: #000;
        }
      }
    }
  }

  .recordlist {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;

    .listLeft {
      display: flex;
      align-items: center;

      .title {
        font-weight: 500;
        font-size: 14px;
        margin-right: 16px;
        color: #1f2a3c;
        padding-left: 20px;
        position: relative;
        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          border-left: 5px solid @primary-color;
          height: 24px;
        }
      }

      .btn {
        background: rgba(62, 206, 182, 1);
        border-radius: 4px;
        color: rgba(255, 255, 255, 1);
        line-height: 22px;
        padding: 5px 16px;
        cursor: pointer;
      }

      .icon {
        font-size: 18px;
      }
    }

    .listRight {
      color: rgba(62, 206, 182, 1);
      cursor: pointer;
      margin-right: 30px;
    }
  }

  .recordBox {
    background: rgba(255, 255, 255, 1);
    border-radius: 3px;
    border: 1px solid rgba(233, 233, 233, 1);
    margin: 15px 25px;

    .formitem {
      margin-bottom: 0;
    }

    .datepicker {
      width: 250px;
      margin-bottom: 0;
    }

    .timeBox {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 10px;
    }

    .star {
      font-size: 22px;
    }

    .onstar {
      color: rgba(62, 206, 182, 1);
    }

    .time {
      display: flex;
      align-items: center;
      padding: 12px 14px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 400;
      color: rgba(0, 0, 0, 1);

      .edit {
        margin-left: 10px;
        cursor: pointer;
        color: rgba(0, 0, 0, 0.85);

        &:hover,
        &:active {
          color: rgba(62, 206, 182, 1);
        }
      }
    }

    .listinfo {
      background: #f4f8fc;
      padding: 12px 20px;
      line-height: 2;

      .name {
        font-weight: 400;
        color: rgba(0, 0, 0, 1);
        width: 70px;
      }

      .item {
        display: flex;
        word-break: break-all;
        white-space: pre-wrap;
      }
    }

    .btnBox {
      text-align: center;
      margin-top: 16px;

      button {
        margin-right: 25px;
      }
    }
  }

  .hiddenRecord {
    display: none !important;
  }
}

.healthRecord {
  padding: 0 20px 10px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow-y: auto;

  .title {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }

  .info {
    display: flex;
    margin-top: 15px;
  }

  .infoTitle {
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
  }

  .detail {
    border-radius: 3px 3px 0px 0px;
    border: 1px solid rgba(233, 233, 233, 1);
    margin-top: 24px;
    display: flex;
    flex-direction: column;

    .title {
      background: rgba(31, 56, 88, 0.06);
      padding: 10px 24px;
    }
  }

  .basic {
    margin: 15px 24px;
    border-bottom: 1px solid rgba(233, 233, 233, 1);

    &:last-child {
      border-bottom: none;
    }

    .basicTitle {
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      font-weight: 500;
    }
  }

  .colInfo {
    display: flex;
    margin: 12px 0;

    .colLeft {
      color: rgba(0, 0, 0, 0.85);
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.inspection {
  font-size: 14px;
  padding: 0 20px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow-y: auto;

  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    margin-top: 8px;
    cursor: pointer;

    .titleInfo {
      font-size: 16px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);

      .titleName {
        padding-right: 16px;
        font-weight: 500;
      }
    }

    .listRight {
      color: rgba(62, 206, 182, 1);
      cursor: pointer;
    }
  }
}

.outExamination {
  overflow-y: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  padding: 0 20px;

  .btn {
    background: rgba(62, 206, 182, 1);
    border-radius: 4px;
    color: #fff;
    border: none;
    margin-left: 16px;
  }

  .imglist {
    margin-top: 20px;
    background: rgba(31, 56, 88, 0.06);
    padding: 0 40px;
    padding-top: 16px;
    padding-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    min-height: 150px;

    :global {
      .ant-upload.ant-upload-select-picture-card > .ant-upload {
        display: flex;
      }
    }

    .left {
      position: absolute;
      left: 12px;
      font-size: 24px;
      color: rgba(0, 0, 0, 0.25);
      cursor: pointer;
    }

    .right {
      position: absolute;
      right: 12px;
      font-size: 24px;
      color: rgba(0, 0, 0, 0.25);
      cursor: pointer;
    }
  }

  :global {
    .out-examination {
      overflow: auto;

      .ant-upload-list-picture-card {
        float: none;
        white-space: nowrap;

        .ant-upload-list-item-info {
          &:before {
            display: none;
          }
        }

        .ant-upload-list-item-actions {
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          a {
            width: 100%;
            height: 100%;
            display: inline-block;
            opacity: 0;
          }
        }
      }

      .ant-upload-list-picture-card .ant-upload-list-item {
        float: none;
        display: inline-block;
      }

      .anticon-delete {
        display: none;
      }
    }
  }
}

.tableLineOper {
  color: @primary-color;
  cursor: pointer;
}

.modal {
  top: 0;
  max-width: 100vw;
  max-height: 100vh;
  overflow: hidden;
  // width: 85% !important;
}

.modalBox {
  display: flex;
  height: 94.5vh;

  .Modaltitle {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    padding: 12px 24px;
    border-bottom: 1px solid rgba(233, 233, 233, 1);
  }

  .Modalflag {
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    padding: 15px 24px 0px 24px;
  }

  .report {
    padding: 0 10px;
    margin-right: 10px;
  }

  .Modalradio {
    padding: 5px 24px;
  }

  .date {
    margin-top: 5px;
    width: 300px;
  }

  .record {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  .delete {
    font-size: 14px;
    font-weight: 400;
    color: rgba(60, 153, 216, 1);
    cursor: pointer;
  }

  .info {
    padding: 3px 24px 7px;
    color: rgba(31, 56, 88, 0.8);
    border-bottom: 1px solid rgba(233, 233, 233, 1);
  }

  .icon {
    padding: 3px 0 7px;
    color: red;
  }

  .addBtn {
    font-weight: 400;
    font-size: 14px;
    color: rgba(62, 206, 182, 1);
    padding: 6px 12px;
    border-radius: 2px;
    border: 1px solid rgba(62, 206, 182, 1);
    width: 124px;
    margin-top: 9px;
    margin-bottom: 50px;
    cursor: pointer;
  }

  .btncells {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }

  .deleteReport {
    border-radius: 3px;
    border: 1px solid rgba(217, 217, 217, 1);
    padding: 5px 16px;
    margin-right: 8px;
    cursor: pointer;
  }

  .sureBtn {
    background: rgba(62, 206, 182, 1);
    border-radius: 3px;
    padding: 5px 16px;
    color: #fff;
    cursor: pointer;
  }

  .preInfo {
    width: 600px;
  }

  .imgBox {
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    padding: 0 50px 50px 50px;
    position: relative;
    justify-content: center;
    flex: auto;
  }

  .previewImg {
    z-index: 2;
    max-width: 100%;
    max-height: 75vh;
  }

  .left {
    position: absolute;
    left: 25px;
    font-size: 24px;
    color: #fff;
  }

  .right {
    position: absolute;
    right: 25px;
    font-size: 24px;
    color: #fff;
  }

  .imgOper {
    position: absolute;
    bottom: 6px;
    left: 0;
    right: 0;
    z-index: 3;
    text-align: center;

    .imgOperBox {
      padding: 6px 20px;
      background: #333;
      display: inline-block;

      .imgOperItem {
        color: #fff;
        font-size: 30px;
        margin-left: 20px;

        &:first-child {
          margin-left: 0;
        }
      }
    }

    .switchTool {
      position: absolute;
      right: 0;
      top: 50%;
      color: #fff;
      font-size: 14px;
      transform: translateY(-50%);
      padding: 10px;
      cursor: pointer;
    }
  }

  .sourceImgBox {
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    margin: 15px;
    position: relative;
    justify-content: center;
    flex: auto;
  }
}

.titleFlag {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  padding-top: 24px;
  padding-bottom: 16px;
}

.tabColumMax {
  max-width: 300px;
  word-break: break-all;
}

.timeStep {
  display: block;
  white-space: nowrap;

  :global(.ant-steps-item) {
    cursor: pointer;
  }
}

.groupUserHeadInfo {
  margin: 16px 16px 0;
  box-shadow: 0px 2px 6px 0px rgba(15, 53, 81, 0.1);
  background: #fff;

  .baseInfo {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e9e9e9;

    .title {
      font-size: 16px;
      font-weight: 500;
      margin-right: 16px;
      color: #000;
    }

    .pid {
      font-size: 16px;
      font-weight: 400;
      position: relative;
      // color: @primary-color;

      &.leftBorder:before {
        content: '';
        position: absolute;
        left: -7px;
        top: 4px;
        bottom: 4px;
        border-right: 1px solid #002d62;
      }
    }

    .selects {
      color: #000;
    }
  }
  .famInfo {
    padding: 20px 16px;
    .infoLine {
      display: flex;
      align-items: center;
      margin-top: 15px;
      justify-content: flex-start;

      &:first-child {
        margin-top: 0;
      }
      & > div {
        margin-right: 20px;
      }
    }
    .gutterItem {
      display: flex;
      font-size: 14px;
      word-wrap: break-word;
      height: 36px;
      align-items: center;

      &.sexG {
        padding-left: 50px;
        background: url(../../../assets/sex-girl.png) no-repeat left center / 36px auto;
      }

      &.sexB {
        padding-left: 50px;
        background: url(../../../assets/sex-boy.png) no-repeat left center / 36px auto;
      }

      .name {
        white-space: nowrap;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.5);
      }

      .value {
        white-space: nowrap;
        flex: 1;
        color: #000;
      }
    }
  }
}

.handleSwitch {
  flex: 1;
  padding-left: 24px;
}

.icon {
  color: white;
  background-color: gray;
  border-radius: 10px;
  position: absolute;
  right: 0px;
  top: -5px;
}

.uImage {
  position: relative;
  display: inline-block;
  margin: 5px 20px;
}
