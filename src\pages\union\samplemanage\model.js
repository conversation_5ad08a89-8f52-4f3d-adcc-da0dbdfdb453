import { message } from 'antd';
import moment from 'moment';
import * as api from './api';

export default {
  namespace: 'sample',
  state: {
    listData: {},
    institutionList: [], // 客户列表
    allPersonnel: [], // 账号列表
    allGroup: [], // 团队列表
    products: [], // 产品线
    subproducts: [], // 子产品
    institutionContract: [], // 客户有效合同
    contractproducts: [], // 合同内产品线
    contractsubproducts: [], // 合同内子产品
    samplingInfo: {}, // 采样管编号信息
    detail: {},
    queryParams: null,
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
    clear() {
      return {};
    },
  },
  effects: {
    *getinstitutions({ payload }, { call, put }) {
      const data = yield call(api.getinstitutions, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { institutionList: data.data || [] },
        });
      }
    },
    *getinstitutionbylist({ payload }, { call, put }) {
      const data = yield call(api.getinstitutionbylist, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { institutionList: data.data || [] },
        });
      }
    },
    *getAllUser({ payload }, { call, put }) {
      const data = yield call(api.getAllUser, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { allPersonnel: data.data || [] },
        });
      }
    },
    *getAllGroup({ payload }, { call, put }) {
      const data = yield call(api.getAllGroup, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { allGroup: data.data?.recordList || [] },
        });
      }
    },
    *getproducts({ payload = {} }, { call, put }) {
      const data = yield call(api.getproducts, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: payload.parentId ? { subproducts: data.data || [] } : { products: data.data || [] },
        });
      }
    },
    *getcontractbyinstitution({ payload }, { call, put }) {
      const data = yield call(api.getcontractbyinstitution, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { institutionContract: data.data || [] },
        });
      }
      return data.data || [];
    },
    *getproductsbycontract({ payload = {} }, { call, put }) {
      const data = yield call(api.getproductsbycontract, payload);
      yield put({
        type: 'save',
        payload: payload.productId ? { contractsubproducts: data.data || [] } : { contractproducts: data.data || [] },
      });
      return data.data || [];
    },
    *getinfobysampling({ payload }, { call }) {
      const data = yield call(api.getinfobysampling, payload);
      return data.data;
    },

    *getsubsamplebypage({ payload }, { call, put }) {
      const data = yield call(api.getsubsamplebypage, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { listData: data.data || {} },
        });
      }
    },
    *getsamplebypage({ payload }, { call, put }) {
      const data = yield call(api.getsamplebypage, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { listData: data.data || {} },
        });
      }
    },
    *getsamplebypageOfNo({ payload }, { call, put }) {
      const data = yield call(api.getsamplebypage, payload);
      return data.data || {};
    },
    *getDetail({ payload }, { call, put }) {
      const data = yield call(api.getDetail, payload);
      if (data.code === 0 && data.data) {
        const { data: detail } = data;
        if (detail.applyTime) {
          detail.applyTime = moment(detail.applyTime);
        }
        if (detail.sampleTime) {
          detail.sampleTime = moment(detail.sampleTime);
        }
        if (detail.extfiled8) {
          detail.extfiled8 = moment(detail.extfiled8);
        }
        if (detail.extfiled9) {
          detail.extfiled9 = moment(detail.extfiled9);
        }
        if (detail.payDate) {
          detail.payDate = moment(detail.extfiled9);
        }
        if (detail.sjzNativeProvinceId) {
          detail.sjzNative = [detail.sjzNativeProvinceId, detail.sjzNativeCityId, detail.sjzNativeAreaId];
        }
        if (detail.birth) {
          detail.birth = moment(detail.birth);
        }
        if (detail.files) detail.files = detail.files.split(';');
        if (!detail.upd || detail.upd == '空') detail.upd = undefined;

        yield put({
          type: 'save',
          payload: { detail },
        });
        return detail;
      }
      return false;
    },
    *deletesample({ payload }, { call }) {
      const data = yield call(api.deletesample, payload);
      return data.code === 0;
    },
    *addsample({ payload }, { call }) {
      const data = yield call(api.addsample, payload);
      return data.code === 0;
    },
    *updatesample({ payload }, { call }) {
      const data = yield call(api.updatesample, payload);
      return data.code === 0;
    },
    *updatePayInfo({ payload }, { call }) {
      const data = yield call(api.updatePayInfo, payload);
      return data.code === 0;
    },
    *checksample({ payload }, { call }) {
      const data = yield call(api.checksample, payload);
      return data.code === 0;
    },
    *getSampleReports({ payload }, { call }) {
      const data = yield call(api.getSampleReports, payload);
      return data.data;
    },
    *previewReport({ payload }, { call }) {
      const data = yield call(api.previewReport, payload);
      return data.data;
    },
    *getQrcode({ payload }, { call }) {
      const data = yield call(api.getQrcode, payload);
      return data.data;
    },
    *getImgList({ payload }, { call }) {
      const data = yield call(api.getImgList, payload);
      return data.data;
    },
    *uploadImgList({ payload }, { call }) {
      const data = yield call(api.uploadImgList, payload);
      return data.data;
    },
    *previewReport({ payload }, { call }) {
      yield call(api.previewReport, payload);
    },
    *downloadsettle({ payload }, { call }) {
      yield call(api.downloadsettle, payload);
    },
    *downloadresult({ payload }, { call }) {
      yield call(api.downloadresult, payload);
    },
    *exportsample({ payload }, { call }) {
      yield call(api.exportsample, payload);
    },
    *exportsampleall({ payload }, { call }) {
      yield call(api.exportsampleall, payload);
    },
    *batchPreviewReport({ payload }, { call }) {
      yield call(api.batchPreviewReport, payload);
    },
  },
};
