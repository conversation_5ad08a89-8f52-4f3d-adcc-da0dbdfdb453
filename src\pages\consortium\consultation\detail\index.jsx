import React, { Component } from 'react';
import { connect } from 'dva';
import { Transfer, Select, Row, Col, Button, Table, Steps, DatePicker, Input, message, Radio, Tooltip, Modal, Upload, Timeline, Collapse } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';
import querystring from 'query-string';
import { getDownload as download } from '@/utils/utils';
import * as Api from './api';
import styles from './index.less';

const { Panel } = Collapse;
const { TextArea } = Input;
const { Step } = Steps;

@connect()
@Form.create()
class Index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      type: '',
      previewVisible: false,
      previewImage: '',
      previewTitle: '',
      timeLine: [],
      auditInput: '', //审核详情
      placeholderText: '',
      auditText: '',
      fileList: [],
      applyTime: '',
      consultationId: '',
      doctorHospitalId: '',
      doctorHospitalName: '',
      existDiagnosis: '',
      historyIssue: '',
      mainIssue: '',
      nowIssue: '',
      familyIssue: '',
      personalIssue: '',
      marriageIssue: '',
      periodIssue: '',
      patientNameInfo: '',
      patientPidInfo: '',
      purpose: '',
      reason: '',
      relationPatientId: '',
      patientId: '',
      status: '',
      timeLine: [],
      patientMobile: '',
      addConsultation: false,
      consulationArr: [],
      patientPid: '', //关联病友id
      patientName: '',
      talkPatient: '0', //是否邀请病友
      allUser: [], //所有用户
      rightKey: [], //已选择医生
      orderNo: '', //会诊单号
      plannedTime: '', //计划会诊时间
      selectDoctorList: [], //已选择医生
      mainDoctorId: '', //主诊医生id
      mainDoctorName: '', //主诊医生姓名
      filter: '',
      hospitalArr: [],
      doctorArr: [],
      opinionList: [],
      doctorNameList: [],
      reportDetail: [],
      chatGroupId: '',
      remarks: '',
      shouldEnter: false,
      consultationStatus: false,
      patientSex: '',
      patientAge: '',
    };
  }

  componentDidMount() {
    const {
      query: { consultationId, type = '' },
    } = this.props.location;
    this.getConsultationInfo(consultationId);
    this.getStatusLine(consultationId);
    this.getAllResult(consultationId);
    this.getAllDoctor();
    this.setState({
      type,
    });
  }

  //获取所有用户
  getAllDoctor = async () => {
    const { code, data = [] } = await Api.getAllUsersGroup();
    if (code == 0) {
      this.setState({
        doctorArr: data.length > 0 ? data[0].userList : [],
      });
    }
  };

  getStatusLine = async id => {
    const { code, data } = await Api.getStatusLine({ id });
    if (code == 0) {
      this.setState({
        timeLine: data,
      });
    }
  };

  //获取所有账户
  getAllUsrs = async () => {
    const { code, data = [] } = await Api.getAllUser();
    if (code == 0) {
      const allUser = [];
      data.forEach((item, key) => {
        (item.userList || []).map(user => {
          allUser.push({
            label: `${item.identityName}-${user.name}`,
            value: `${user.name}___${user.account}`,
          });
        });
      });
      //对象数组去重
      let result = {};
      for (let i = 0; i < allUser.length; i++) {
        result[allUser[i]['value']] = allUser[i];
      }
      let finalArr = [];
      for (let item in result) {
        finalArr.push(result[item]);
      }
      this.setState({ allUser: finalArr });
    }
  };

  getAllResult = async consultationId => {
    const { code, data, msg } = await Api.getAllResult({ consultationId });
    if (code == 0) {
      const { opinionList, doctorNameList, reportDetail } = data;
      this.setState({ opinionList, doctorNameList, reportDetail });
    } else {
      if (code != ********) {
        message.error({ content: msg, duration: 1 });
      }
    }
  };

  //获取会诊详情
  getConsultationInfo = async id => {
    const { code, data } = await Api.getApplyInfoAndFile({ id });
    if (code == 0) {
      const {
        chatGroupId,
        fileList,
        applyTime,
        consultationId,
        doctorHospitalId,
        doctorHospitalName,
        existDiagnosis,
        historyIssue,
        mainIssue,
        nowIssue,
        familyIssue,
        personalIssue,
        marriageIssue,
        periodIssue,
        orderNo,
        shouldEnter,
        patientName,
        doctorName,
        patientPid,
        purpose,
        reason,
        relationPatientId,
        status,
        patientVo: { patientMobile, patientSex, patientAge, patientId },
        auditStatusLog = {},
      } = data;
      const { remarks = '' } = auditStatusLog;
      let imgArr = [];
      if (fileList && fileList.length > 0) {
        fileList.map((item, index) => {
          imgArr.push({ uid: index, name: item.name, url: item.url, status: 'done' });
        });
      }
      this.setState({
        patientNameInfo: patientName,
        patientPidInfo: patientPid,
        fileList: imgArr,
        doctorHospitalName,
        relationPatientId,
        doctorHospitalId,
        consultationId,
        existDiagnosis,
        patientMobile,
        historyIssue,
        familyIssue,
        personalIssue,
        marriageIssue,
        periodIssue,
        chatGroupId,
        shouldEnter,
        patientSex,
        patientAge,
        doctorName,
        patientPid,
        applyTime,
        patientId,
        mainIssue,
        nowIssue,
        orderNo,
        remarks,
        purpose,
        reason,
        status,
      });
    }
  };

  handlePreview = async file => {
    if (!file.url && !file.preview) {
      file.preview = await this.getBase64(file.originFileObj);
    }

    this.setState({
      previewImage: file.url || file.preview,
      previewVisible: true,
      previewTitle: file.name || file.url.substring(file.url.lastIndexOf('/') + 1),
    });
  };

  getBase64 = file => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };

  handleChange = ({ fileList }) => this.setState({ fileList });

  downloadPic = async file => {
    download(file.url, file.url);
  };

  handleCancel = () => this.setState({ previewVisible: false });

  actionButton = (status, len) => {
    console.log(this.props);
    const { patientPid, shouldEnter } = this.state;
    const {
      query: { role = '' },
    } = this.props.location;
    switch (status) {
      case 0:
        if (len == 1) {
          if (role === 'manage') {
            return (
              <div className={styles.uButton}>
                <Button className={styles.timeLineLeftButton} type="primary" size="small" onClick={() => this.audit(true)}>
                  通过
                </Button>
                <Button className={styles.timeLineRightButton} size="small" onClick={() => this.audit(false)}>
                  不通过
                </Button>
              </div>
            );
          }
          return (
            <>
              <Button className={styles.timeLineLeftButton} type="primary" size="small" onClick={this.cancelApply}>
                取消申请
              </Button>
            </>
          );
        }
        return null;
      case 1:
        if (len == 2) {
          return (
            <>
              <Button className={styles.timeLineLeftButton} type="primary" size="small" onClick={this.createConsultationRoom}>
                创建会诊间
              </Button>
            </>
          );
        } else {
          return (
            <>
              <Button className={styles.timeLineLeftButton} type="primary" size="small" onClick={() => this.lookDetails(true)}>
                查看审核详情
              </Button>
            </>
          );
        }
      case 2:
        if (shouldEnter) {
          return (
            <>
              <Button className={styles.timeLineLeftButton} type="primary" size="small" onClick={() => this.toChat(patientPid)}>
                进入会诊间
              </Button>
            </>
          );
        }
        return null;
      case 4:
        return (
          <>
            <Button className={styles.timeLineLeftButton} type="primary" size="small" onClick={() => this.lookDetails(false)}>
              查看审核详情
            </Button>
          </>
        );
      default:
        return null;
    }
  };

  audit = val => {
    const { placeholderText, auditText } = this.state;
    if (val) {
      this.setState({
        placeholderText: '您可输入相关会诊安排...',
        auditText: '确认审核通过？审核通过后系统将推送成功通知给申请医生，请及时创建会诊。',
      });
    } else {
      this.setState({
        placeholderText: '您可输入不通过的缘由...',
        auditText: '确认审核不通过？系统将及时把审核结果通知申请医生。',
      });
    }
  };

  downloadAll = () => {
    const { fileList } = this.state;
    fileList.forEach(Item => {
      download(Item.url);
    });
  };

  setOrderNo = e => {
    this.setState({ orderNo: e.target.value });
  };

  addPatient = e => {
    this.setState({ talkPatient: e.target.value });
  };

  consulationTime = plannedTime => {
    this.setState({ plannedTime });
  };

  searchContent = val => {
    const { filter = '' } = this.state;
    if (filter !== val) {
      this.setState({ filter: val });
    }
  };

  auditCancel = async val => {
    const { placeholderText, auditInput, consultationId } = this.state;
    let param = {
      consultationId,
      reviewResult: placeholderText.indexOf('不通过') > -1 ? 0 : 1,
      remark: auditInput,
    };
    if (val) {
      const { code, data } = await Api.auditingAction(param);
      if (code == 0) {
        this.getStatusLine(consultationId);
        this.getConsultationInfo(consultationId);
        message.success({ content: '会诊申请已通过', duration: 1 });
      }
    }
    this.setState({ auditInput: '', placeholderText: '' });
  };

  inputAudit = e => {
    this.setState({ auditInput: e.target.value });
  };

  consultationSubmit = async () => {
    const { consultationId } = this.state;
    const { orderNo, patientPid, patientName, plannedTime, mainDoctorId, mainDoctorName, talkPatient, selectDoctorList } = this.state;
    let param = {
      orderNo,
      patientPid,
      patientName,
      plannedTime,
      mainDoctorId,
      mainDoctorName,
      isInvitePat: talkPatient,
      list: selectDoctorList,
    };
    const { code, msg = '' } = await Api.creatConsultationChat(param);
    if (code == 0) {
      this.getStatusLine(consultationId);
      this.getConsultationInfo(consultationId);
      message.success({ content: '创建会诊间成功！', duration: 2 });
      this.setState({ addConsultation: false });
    }
  };

  createConsultationRoom = () => {
    const { patientNameInfo } = this.state;
    this.getAllUsrs();
    this.setState({
      addConsultation: true,
      patientName: patientNameInfo,
    });
  };

  lookDetails = val => {
    let detailsView;
    const { remarks } = this.state;
    if (val) {
      detailsView = (
        <div>
          <p>审核结果：通过 </p>
          <p>备注：{remarks} </p>
        </div>
      );
    } else {
      detailsView = (
        <div>
          <p>审核结果：不通过 </p>
          <p>备注：{remarks} </p>
        </div>
      );
    }
    Modal.info({
      title: '审核详情',
      content: detailsView,
      onOk() {},
      okText: '确定',
    });
  };

  toChat = pid => {
    const { chatGroupId, status } = this.state;
    if (window.location.hostname == 'localhost' || window.location.hostname == '127.0.0.1') {
      window.open(
        `http://${window.location.hostname}:8001/#/inquery/doctor?${querystring.stringify({
          pid,
          type: 'consultation',
          chatGroupId,
          status,
        })}`,
        '_blank',
      );
      return;
    }
    window.open(
      `${window.location.origin}/merchant/umi/index.html#/inquery/doctor?${querystring.stringify({
        pid,
        type: 'consultation',
        chatGroupId,
        status,
      })}`,
      '_blank',
    );
    // router.push({
    //   pathname: '/inquery/select',
    //   query: { pid, type: 'consultation', chatGroupId, status },
    // });
  };

  setTransfer = rightKey => {
    const selectDoctorList = rightKey.map(item => {
      let arr = [];
      arr = item.split('___');
      return {
        account: arr[1],
        name: arr[0],
      };
    });
    this.setState({ rightKey, selectDoctorList });
  };

  getTransferItem = item => {
    const { filter = '' } = this.state;
    let filterStr;
    if (!filter && !filter.length) {
      filterStr = item.label;
    } else {
      filterStr = `${item.label}`.replace(new RegExp(filter, 'g'), `<span style="color: ${PRIMARY_COLOR}">${filter}</span>`);
    }
    const customLabel = (
      <span
        dangerouslySetInnerHTML={{
          __html: filterStr,
        }}
      />
    );
    return {
      label: customLabel, // for displayed item
      value: item.key, // for title and filter matching
    };
  };

  selectAttendingDoctor = (value, option, judge) => {
    const {
      props: { id = '' },
    } = option;
    let param = {};
    if (judge == 'mainDoctor') {
      param.mainDoctorId = id;
      param.mainDoctorName = value;
    }
    if (judge == 'doctorHospitalId') {
      param.doctorHospitalId = id;
    }
    this.setState(param);
  };

  printEvent = () => {
    const title = window.document.title;
    window.document.title = '';
    const el = document.getElementById('consultationReport');
    const iframe = document.createElement('IFRAME');
    let doc = null;
    document.body.appendChild(iframe);
    doc = iframe.contentWindow.document;
    doc.write(el.innerHTML);
    doc.close();
    iframe.contentWindow.focus();
    iframe.contentWindow.print();
    // if (navigator.userAgent.indexOf("MSIE") > 0) {
    document.body.removeChild(iframe);
    // }
    window.document.title = title;
  };

  getFindSelect(label, consulationOptions, judge, span) {
    const {
      form: { getFieldDecorator },
    } = this.props;
    return (
      <Col span={span}>
        <Form.Item {...this.formItemLayout} label={label}>
          {getFieldDecorator(judge)(
            <Select
              style={{ width: '100%' }}
              placeholder="请选择"
              onChange={(value, option) => this.selectAttendingDoctor(value, option, judge)}
              showSearch={true}
              filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
            >
              {judge != 'mainDoctor' && (
                <Select.Option id="" value="">
                  全部
                </Select.Option>
              )}
              {consulationOptions}
            </Select>,
          )}
        </Form.Item>
      </Col>
    );
  }

  back = () => {
    this.props.history.goBack();
  };

  collapseHeader = item => {
    return (
      <div className={styles.collapseTitle}>
        <div>
          {item.hospitalName ? item.hospitalName + '-' : ''}
          {item.doctorName}
        </div>
        <div className={styles.collapseTime}>{item.createTime}</div>
      </div>
    );
  };

  render() {
    const {
      consultationStatus,
      patientId,
      patientAge,
      patientSex,
      statusGroup,
      stageStatus = [],
      disList = [],
      tableData = {},
      visitTagList = [],
      userTagList = [],
      batchOperId = [],
      operMsgIds = [],
      showNoticeList,
      pageSize,
      moveId = [],
      detailsVal,
      fileList,
      previewVisible,
      previewImage,
      previewTitle,
      auditInput,
      placeholderText,
      auditText,
      applyTime,
      consultationId,
      doctorHospitalId,
      doctorHospitalName,
      doctorName,
      existDiagnosis,
      historyIssue,
      mainIssue,
      patientNameInfo,
      patientPidInfo,
      doctorArr,
      hospitalArr,
      opinionList,
      doctorNameList,
      reportDetail,
      consulationArr,
      addConsultation,
      talkPatient,
      allUser,
      filter,
      rightKey,
      nowIssue,
      orderNo,
      patientName,
      patientPid,
      purpose,
      reason,
      relationPatientId,
      status,
      timeLine,
      patientMobile,
      type,
      familyIssue,
      personalIssue,
      marriageIssue,
      periodIssue,
    } = this.state;

    const showData = allUser.filter(item => {
      return (!filter && !filter.length) || (item.label || '').indexOf(filter) > -1;
    });

    const rightShow = rightKey.length ? rightKey : [];

    const doctorOptions = doctorArr.map(d => (
      <Select.Option id={d.id} key={d.id}>
        {d.name}
      </Select.Option>
    ));

    const upLoadprops = {
      showUploadList: {
        // downloadIcon: 'download ',
        showRemoveIcon: false,
        showDownloadIcon: true,
      },
    };

    return (
      <div className={`${styles.uPage} g-page`}>
        {/* <div className={styles.mBack}>
          <div className={styles.hoverModule}>
            <div className={styles.iconLeft}></div>
            <div className={styles.backText} onClick={this.back}>
              返回
            </div>
          </div>
          <div className={styles.consultationTitle}>会诊详情</div>
        </div> */}
        <div className={styles.detialsModule}>
          <div className={styles.leftModule}>
            <div className={styles.moduleItemTitle}>会诊申请详情</div>
            <div className={styles.contentModule}>
              <div className={styles.mTitle}>
                <div className={styles.borderView}></div>
                <div className={styles.title}>患者基本信息</div>
              </div>
              <div className={styles.infoModule}>
                <div className={styles.detailsTitle}>
                  患者姓名：<span>{patientNameInfo}</span>
                </div>
                <div className={styles.detailsTitle}>
                  患者PID：<span>{patientPidInfo}</span>
                </div>
                <div className={styles.detailsTitle}>
                  联系方式：<span>{patientMobile}</span>
                </div>
                {/* <div className={styles.detailsTitle}>阶段状态：<span>{this.switchStatus(status)}</span></div> */}
              </div>
            </div>
            <div className={styles.contentModule}>
              <div className={styles.mTitle}>
                <div className={styles.borderView}></div>
                <div className={styles.title}>申请信息</div>
              </div>
              <div className={styles.infoModule}>
                <div className={styles.detailsTitle}>
                  申请单号：
                  <span>{orderNo}</span>
                </div>
                <div className={styles.detailsTitle}>
                  申请时间：
                  <span>{applyTime}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p style={{ letterSpacing: '0.3em' }}>申请方：</p>
                  <span>{doctorHospitalName + '-' + doctorName}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p style={{ letterSpacing: '1em' }}>主诉：</p>
                  <span>{mainIssue}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p style={{ letterSpacing: '0.3em' }}>现病史：</p>
                  <span>{nowIssue}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p style={{ letterSpacing: '0.3em' }}>既往史：</p>
                  <span>{historyIssue}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p style={{ letterSpacing: '0.3em' }}>家族史：</p>
                  <span>{familyIssue}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p style={{ letterSpacing: '0.3em' }}>个人史：</p>
                  <span>{personalIssue}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p style={{ letterSpacing: '0.3em' }}>婚育史：</p>
                  <span>{marriageIssue}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p style={{ letterSpacing: '0.3em' }}>月经史：</p>
                  <span>{periodIssue}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p>现有诊断：</p>
                  <span>{existDiagnosis}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p>会诊目的：</p>
                  <span>{purpose}</span>
                </div>
                <div className={styles.applyInfo}>
                  <p>会诊原因：</p>
                  <span>{reason}</span>
                </div>
              </div>
            </div>
            <div className={styles.contentModule}>
              <div className={styles.downloadPic}>
                <div className={styles.mTitle}>
                  <div className={styles.borderView}></div>
                  <div className={styles.title}>附件</div>
                </div>
                {fileList.length > 0 && (
                  <span onClick={this.downloadAll} className={styles.downloadAll}>
                    下载全部
                  </span>
                )}
              </div>
              {fileList.length > 0 ? (
                <Upload {...upLoadprops} listType="picture-card" fileList={fileList} onPreview={this.handlePreview} onChange={this.handleChange} onDownload={this.downloadPic} />
              ) : (
                <div className={styles.mNull}>暂无</div>
              )}
              <Modal visible={previewVisible} title={previewTitle} footer={null} onCancel={this.handleCancel}>
                <img alt="example" style={{ width: '100%' }} src={previewImage} />
              </Modal>
            </div>
          </div>
          <div className={styles.rightModule}>
            <div className={styles.moduleItemTitle}>会诊执行状态</div>
            <div className={styles.contentModule}>
              <div className={styles.mTitle}>
                <div className={styles.borderView}></div>
                <div className={styles.title}>会诊时间线</div>
              </div>
              <Steps labelPlacement="vertical" className={styles.timeStep} current={timeLine.length}>
                {type === 'hospital'
                  ? timeLine.map(item => {
                      return (
                        <Step
                          icon={<div className={styles.statusIcon1} />}
                          title={item.description}
                          description={this.actionButton(item.status, timeLine.length)}
                          key={item.time}
                          subTitle={item.time}
                        />
                      );
                    })
                  : timeLine.map(item => {
                      return (
                        <Step
                          icon={<div className={styles.statusIcon1} />}
                          title={item.description}
                          description={this.actionButton(item.status, timeLine.length)}
                          key={item.time}
                          subTitle={item.time}
                        />
                      );
                    })}
              </Steps>
            </div>
            <div className={styles.contentModule}>
              <div className={styles.mTitle}>
                <div className={styles.borderView}></div>
                <div className={styles.title}>会诊内容</div>
              </div>
              <div className={styles.detailsTitle}>
                <div className={styles.contentLayout}>
                  <div>会诊医师：</div>
                  {doctorNameList && doctorNameList.length > 0 ? (
                    <div>
                      {doctorNameList.map((item, index) => {
                        return (
                          <p key={index} className={styles.suggestion}>
                            {item}
                          </p>
                        );
                      })}
                    </div>
                  ) : (
                    <p>暂无</p>
                  )}
                </div>
                <div
                  className={styles.contentLayout}
                  style={{
                    alignItems: opinionList && opinionList.length > 0 ? 'inherit' : 'baseline',
                  }}
                >
                  <div style={{ paddingTop: '13px' }}>会诊意见：</div>
                  {opinionList && opinionList.length > 0 ? (
                    <Collapse ghost className={styles.collapse} defaultActiveKey={1} expandIconPosition={'right'}>
                      {opinionList.map((item, index) => {
                        return (
                          <Panel header={this.collapseHeader(item)} key={index}>
                            <p>{item.report}</p>
                          </Panel>
                        );
                      })}
                    </Collapse>
                  ) : (
                    <p>暂无</p>
                  )}
                </div>
                <div className={styles.contentLayout}>
                  <div className={styles.consultationReport}>会诊报告：</div>
                  <div style={{ flex: 1 }}>
                    {reportDetail && reportDetail != '' ? (
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div style={{ display: 'flex' }}>
                          <p style={{ marginRight: '15px' }}>{`${reportDetail.hospitalName ? `${reportDetail.hospitalName}-` : ''}${reportDetail.doctorName}`}</p>
                          <Button type="primary" size="small" onClick={() => this.setState({ consultationStatus: true })}>
                            <div style={{ color: '#fff' }}>查看报告</div>
                          </Button>
                        </div>
                        <p style={{ marginRight: '50px', fontWeight: 500 }}>{reportDetail.createTime}</p>
                      </div>
                    ) : (
                      <p>暂无</p>
                    )}
                    <p className={styles.reportContent}>{(reportDetail && reportDetail.report) || null}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Modal title="创建会诊" visible={addConsultation} onCancel={() => this.setState({ addConsultation: false })} onOk={this.consultationSubmit} width={'50%'}>
          <div className={styles.consultationRow}>
            <div style={{ letterSpacing: '0.4em' }}>
              <text>*</text>会诊单号：
            </div>
            <div style={{ flex: 1 }}>
              <Input value={orderNo} onChange={this.setOrderNo} />
            </div>
          </div>
          <div className={styles.consultationRow}>
            <div style={{ letterSpacing: '0.4em' }}>
              <text>*</text>关联病友：
            </div>
            <div style={{ flex: 1 }}>
              <Input disabled value={patientName} />
            </div>
          </div>
          {/* <div className={styles.consultationRow}>
            <div>&nbsp;&nbsp;是否邀请病友：</div>
            <div style={{ flex: 1 }}>
              <Radio.Group onChange={this.addPatient} value={talkPatient}>
                <Radio value={'0'}>否</Radio>
                <Radio value={'1'}>是</Radio>
              </Radio.Group>
            </div>
          </div> */}
          <div className={styles.consultationRow}>
            <div>&nbsp;&nbsp;计划会诊时间：</div>
            <div>
              <DatePicker onChange={(date, dateString) => this.consulationTime(dateString)} className={styles.date} showTime />
            </div>
          </div>
          <div style={{ marginBottom: 10, display: 'flex' }}>
            <div style={{ marginBottom: '10px' }}>
              <text style={{ color: 'red' }}>*</text>添加会诊医生：
            </div>
            <div style={{ flex: 1 }}>
              <Input.Search className={styles['search-box']} placeholder="搜索" onSearch={this.searchContent} style={{ width: 260, marginBottom: 4 }} />
              <Transfer
                rowKey={record => record.value}
                dataSource={showData}
                targetKeys={rightShow}
                listStyle={{
                  width: '43%',
                  height: 180,
                }}
                render={this.getTransferItem}
                onChange={this.setTransfer}
                titles={['可选', '已选']}
                notFoundContent="暂无数据"
              />
            </div>
          </div>
          <div className={styles.consultationRow} style={{ height: '41px' }}>
            <div>
              <text>*</text>选择主诊医生：
            </div>
            <div style={{ width: 389, paddingTop: '24px' }}>{this.getFindSelect('', doctorOptions, 'mainDoctor', 16)}</div>
          </div>
        </Modal>
        <Modal title="审核确认" onOk={() => this.auditCancel(true)} onCancel={() => this.auditCancel(false)} visible={placeholderText != ''} okText="确认" cancelText="取消">
          <div>
            <p>{auditText}</p>
            <div>
              <p style={{ padding: '5px 0' }}>审核备注：</p>
              <TextArea value={auditInput} placeholder={placeholderText} autoSize={{ minRows: 2 }} maxLength={30} onChange={this.inputAudit} />
            </div>
            <div className={styles.textNum}>{auditInput.length}/30</div>
          </div>
        </Modal>
        <Modal
          title=""
          visible={consultationStatus}
          onCancel={() => this.setState({ consultationStatus: false })}
          width={'50%'}
          footer={
            <>
              <Button
                type="primary"
                onClick={() => {
                  this.printEvent();
                }}
              >
                打印
              </Button>
              <Button onClick={() => this.setState({ consultationStatus: false })}>关闭</Button>
            </>
          }
          style={{ padding: '15px 40px' }}
        >
          <div id="consultationReport">
            <h2 style={{ textAlign: 'center' }}>会诊结果单</h2>
            <div
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                borderTop: '1px solid #000',
                borderBottom: '1px solid #000',
                margin: '17px 0',
                padding: '10px 0',
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', flex: 1, margin: '10px 0' }}>
                <span style={{ whiteSpace: 'nowrap' }}>姓名：</span>
                <div style={{ width: '120px', borderBottom: '1px solid #000', textAlign: 'center' }}>{patientNameInfo}</div>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', flex: 1, margin: '10px 0' }} className={styles.baseItem}>
                <span style={{ whiteSpace: 'nowrap' }}>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;性别：</span>
                <div style={{ width: '120px', borderBottom: '1px solid #000', textAlign: 'center' }} className={styles.content}>
                  {patientSex == 'M' ? '男' : '女'}
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  flex: 1,
                  margin: '10px 0',
                  justifyContent: 'flex-end',
                }}
                className={styles.baseItem}
              >
                <span style={{ whiteSpace: 'nowrap' }}>年龄：</span>
                <div style={{ width: '120px', borderBottom: '1px solid #000', textAlign: 'center' }} className={styles.content}>
                  {patientAge}
                </div>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', flex: 1, margin: '10px 0' }} className={styles.baseItem}>
                <span style={{ letterSpacing: '0.12em' }}>PID：</span>
                <div style={{ width: '120px', borderBottom: '1px solid #000', textAlign: 'center' }} className={styles.content}>
                  {patientId}
                </div>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', flex: 2, margin: '10px 0' }} className={styles.baseItem}>
                <span style={{ whiteSpace: 'nowrap' }}>会诊单号：</span>
                <div style={{ width: '120px', borderBottom: '1px solid #000', textAlign: 'center' }} className={styles.content}>
                  {orderNo}
                </div>
              </div>
            </div>
            <div>
              <div style={{ paddingBottom: '10px' }}>临床信息：</div>
              <div className={styles.clinicInfo}>
                <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                  <span style={{ letterSpacing: '0.88em' }}>主诉：</span>
                  {mainIssue}
                </div>
                <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                  <span style={{ letterSpacing: '0.26em' }}>现病史：</span>
                  {nowIssue}
                </div>
                <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                  <span style={{ letterSpacing: '0.26em' }}>既往史：</span>
                  {historyIssue}
                </div>
                <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                  <span style={{ letterSpacing: '0.26em' }}>家族史：</span>
                  {familyIssue}
                </div>
                <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                  <span style={{ letterSpacing: '0.26em' }}>个人史：</span>
                  {personalIssue}
                </div>
                <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                  <span style={{ letterSpacing: '0.26em' }}>婚育史：</span>
                  {marriageIssue}
                </div>
                <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                  <span style={{ letterSpacing: '0.26em' }}>月经史：</span>
                  {periodIssue}
                </div>
                <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                  <span>现有诊断：</span>
                  {existDiagnosis}
                </div>
              </div>
              <div>
                <div style={{ padding: '15px 0 10px 0' }}>会诊报告：</div>
                <div className={styles.clinicInfo} style={{ padding: '10px 15px' }}>
                  {reportDetail ? reportDetail.report : '无'}
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  borderTop: '1px solid #000',
                  border: 0,
                }}
                className={styles.baseInfo}
              >
                <div style={{ display: 'flex', alignItems: 'center', flex: 1, margin: '10px 0' }} className={styles.baseItem}>
                  <span>报告医师：</span>
                  <div
                    style={{
                      width: '120px',
                      borderBottom: '1px solid #000',
                      textAlign: 'center',
                      flex: 0.8,
                    }}
                    className={styles.content}
                  >
                    {doctorHospitalName + '-' + doctorName}
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', flex: 1, margin: '10px 0' }} className={styles.baseItem}>
                  <span>报告时间：</span>
                  <div
                    style={{
                      width: '120px',
                      borderBottom: '1px solid #000',
                      textAlign: 'center',
                      flex: 0.8,
                    }}
                    className={styles.content}
                  >
                    {reportDetail ? reportDetail.createTime : ''}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Modal>
      </div>
    );
  }
}

export default Index;
