# umi project

- [技术选型](#技术选型)
- [拉取项目](#拉取项目)
- [安装依赖](#安装依赖)
- [本地运行](#本地运行)
- [编译打包](#编译打包)
- [项目结构](#项目结构)
- [代码规范](#代码规范)
- [注意事项](#注意事项)
- [链接](#链接)

## 技术选型

```
react@16 
基于umiv3.x
less
```

### 安装依赖

```
$ npm install
```
_也可以使用  `yarn`_


## 本地运行

```
npm run start
```

## 编译打包

```
npm run build
```


## 开发环境部署（sftp）

```
ip：**************
账号：oper
密码：oper#gzhc2015
部署目录：/app/oper/static/websites/followup/operate/
```


## 测试&&生产环境部署（sftp）

```
部署目录： /app/stsweb/websites/followup/operate/
```


## 在线访问

```
开发环境：https://scfs.med.gzhc365.com/operate/#/login
测试环境：https://ucfs.med.gzhc365.com/operate/#/login
生产环境：https://cfs.med.gzhc365.com/operate/#/login
```


### 项目结构

1. 目录结构

```
├── config                   // 配置文件
│   ├── config.js
│   ├── config.local.js
│   ├── config.prod.js
│   └── config.uat.js
├── mock                     // mock数据目录
│   └── demo.js
├── src
│   ├── assets               // 静态文件目录
│   ├── components           // 公共组件目录
│   ├── layouts              // 公共结构组件目录
│   ├── models               // 公共dva数据目录
│   ├── pages                // 页面目录
│   ├── utils                // 公共工具目录
│   ├── app.ts               // 入口文件
│   ├── config.ts            // 业务配置文件
│   └── global.less          // 全局样式
├── README.md
├── package.json
├── commitlint.config.js     // gitCommit 规则
├── typings.d.ts             // 全局ts声明文件
├── jsconfig.json
├── .editorconfig
├── .eslintignore            // eslint白名单
├── .eslintrc.js             // eslint规则
├── .gitignore               // git白名单
├── .prettierignore          // prettier白名单
└── .prettierrc              // prettier配置
```
2. [详细目录介绍](http://sfex.med.gzhc365.com/lib/docs/1-template)

3. 文件及文件夹命名 
   文件夹和文件命名都采用 **小写** 命，如:userlist

## 代码规范

1. 用eslint 规则参照：[eslint-airbnb](https://github.com/airbnb/javascript)
2. 其他内部规范：[JS代码风格及通用规范](http://**************/pages/viewpage.action?pageId=13534304)

## 注意事项

### 1、git commit 注释规范(冒号后有空格)

* upd：更新某功能（不是 feat, 不是 fix）
* feat：新功能（feature）
* fix：修补bug
* docs：文档（documentation）
* style： 格式（不影响代码运行的变动）
* refactor：重构（即不是新增功能，也不是修改bug的代码变动）
* test：增加测试
* chore：构建过程或辅助工具的


### 链接
- [React官方中文文档](https://react.docschina.org/)
- [UmiJs官方文档](https://umijs.org/)
- [antd官方文档](https://ant.design/docs/react/getting-started-cn)
- [Git工程&工程组创建规范](http://**************/pages/viewpage.action?pageId=13534284)





