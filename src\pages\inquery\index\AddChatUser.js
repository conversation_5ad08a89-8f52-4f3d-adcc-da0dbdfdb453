import React, { Component, Fragment } from 'react';
import { connect } from 'dva';
import { Modal, Transfer, message, Input } from 'antd';
import * as Api from './api';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      allUser: [],
      rightKey: [],
      filter: '',
    };
  }

  componentDidMount() {
    this.getAllUsrs();
  }

  getAllUsrs = async () => {
    const { code, data = [] } = await Api.getAllUser();
    if (code == 0) {
      const allUser = [];
      data.forEach((item, key) => {
        (item.userList || []).map(user => {
          allUser.push({
            label: `${item.identityName}-${user.name}`,
            value: `${user.name}___${user.account}`,
          });
        });
      });
      //对象数组去重
      let result = {};
      for (let i = 0; i < allUser.length; i++) {
        result[allUser[i]['value']] = allUser[i];
      }
      let finalArr = [];
      for (let item in result) {
        finalArr.push(result[item]);
      }
      this.setState({ allUser: finalArr });
    }
  };

  getTransferItem = item => {
    const { filter = '' } = this.state;
    let filterStr;
    if (!filter && !filter.length) {
      filterStr = item.label;
    } else {
      filterStr = `${item.label}`.replace(new RegExp(filter, 'g'), `<span style="color: ${PRIMARY_COLOR}">${filter}</span>`);
    }
    const customLabel = (
      <span
        dangerouslySetInnerHTML={{
          __html: filterStr,
        }}
      />
    );
    return {
      label: customLabel, // for displayed item
      value: item.key, // for title and filter matching
    };
  };

  setTransfer = rightKey => {
    this.setState({ rightKey });
  };

  handleSubmit = async () => {
    const { rightKey } = this.state;
    const { chatInfo, onCancel, getChatList } = this.props;
    const { code } = await Api.addMemberToGroup({
      groupId: chatInfo.id,
      list: rightKey.map(item => {
        const arr = item.split('___');
        return { account: arr[1], name: arr[0] };
      }),
    });
    if (code == 0) {
      message.success('添加成功');
      onCancel();
      getChatList();
    }
  };

  searchContent = val => {
    const { filter = '' } = this.state;
    if (filter !== val) {
      this.setState({ filter: val }, this.getData);
    }
  };

  render() {
    const { allUser, rightKey = [], filter } = this.state;
    const { targetKeys = [], onCancel, show } = this.props;

    const rightShow = rightKey.length ? rightKey : targetKeys;

    const showData = allUser.filter(item => {
      return (!filter && !filter.length) || (item.label || '').indexOf(filter) > -1;
    });

    return (
      <Modal
        title="添加成员"
        visible={show}
        onOk={this.handleSubmit}
        onCancel={() => {
          onCancel();
          this.setState({ rightKey: [] });
        }}
      >
        <Input.Search placeholder="搜索" onSearch={this.searchContent} style={{ width: 203, marginBottom: 4 }} />
        <Transfer
          rowKey={record => record.value}
          dataSource={showData}
          targetKeys={rightShow}
          listStyle={{
            width: '43%',
            height: 400,
          }}
          render={this.getTransferItem}
          onChange={this.setTransfer}
          titles={['可选', '已选']}
          notFoundContent="暂无数据"
        />
      </Modal>
    );
  }
}

export default connect()(Index);
