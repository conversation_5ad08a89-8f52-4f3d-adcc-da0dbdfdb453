@charset "utf-8";
@import '../../resources/styles/mixins';

.page-resetPwd {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 540px;
  background: url('../../resources//images/common/login-bg.png');
  background-size: 100% 100%;

  .login-body {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 380px;
    background: linear-gradient(135deg, rgba(45, 102, 111, 0.92) 0%, rgba(48, 161, 166, 0.13) 100%);
    position: relative;
    transform: 0;

    .login-form {
      .login-form-box {
        padding: 40px 80px;
        background: #fff;

        .login-form-header {
          text-align: center;
          img {
            width: 229px;
            height: 26px;
          }
          .head-text {
            margin-top: 16px;
            color: #000;
            font-family: PingFang SC;
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: 36px;
          }
        }

        .ant-legacy-form {
          width: 319px;
        }
        .login-input {
          padding: 12px;
          background: rgba(0, 0, 0, 0.04);
          border: none;
        }

        .login-button {
          width: 319px;
          height: 57px;
          font-family: PingFang SC;
          font-size: 18px;
          font-style: normal;
          font-weight: 600;
          background-color: #3f969d !important;
          border-radius: 4px;
        }
      }
    }
  }
}
