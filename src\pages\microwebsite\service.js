import * as utils from '../../utils/utils';

/**
 * 加载静态JSON数据文件
 * @param {*} param
 */
export function loadStaticJSONData({ url }) {
  return utils.loadStaticJSONData(url, {
    method: 'POST',
  });
}

/**
 * 文章列表
 * @param {*} param
 */
export function articleList(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/article/list', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 文章类型列表
 * @param {*} param
 */
export function articleTypeList(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/article/type/list', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除文章类型
 * @param {*} param
 */
export function articleTypeDelete(param = {}) {
  param.showMode = 'microSite';
  delete param.listQueryParam;
  return utils.request('/api/article/type/delete', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 修改文章
 * @param {*} param
 */
export function submitArticleTypeDetail(param = {}) {
  param.showMode = 'microSite';
  delete param.listQueryParam;
  const { type } = param;
  if (type != 'new') {
    delete param.createTime;
    delete param.sortNum;
    delete param.updateTime;
    delete param.listQueryParam;
    return utils.request('/api/article/type/update', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  } else {
    delete param.typeId;
    return utils.request('/api/article/type/add', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  }
}

/**
 * 提交文章类型排序
 * @param {*} param
 */
export function submitArticleTypeSortable(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/article/type/sort', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 文章详情
 * @param {*} param
 */
export function articleDetail(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/article/get', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 修改文章
 * @param {*} param
 */
export function submitArticleDetail(param = {}) {
  param.showMode = 'microSite';
  const { articleId } = param;
  // delete param.createTime;
  delete param.publishTime;
  delete param.sortNum;
  delete param.statusDesc;
  delete param.updateTime;
  if (articleId) {
    return utils.request('/api/article/update', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  } else {
    delete param.articleId;
    return utils.request('/api/article/add', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  }
}

/**
 * 医院建筑物（楼栋）列表
 * @param {*} param
 */
export function edificeList(param = {}) {
  param.showMode = 'microSite';
  param.parentId = -1; // 查询建筑传1,其余的传对应的父建筑ID
  return utils.request('/api/build/getbuild', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除文章
 * @param {*} param
 */
export function articleDelete(param = {}) {
  param.showMode = 'microSite';
  delete param.listQueryParam;
  return utils.request('/api/article/delete', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除医院建筑物（楼栋）
 * @param {*} param
 */
export function upFloorImg(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/build/upfloorimg', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除医院建筑物（楼栋）
 * @param {*} param
 */
export function edificeDelete(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/build/removebuild', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 楼层列表
 * @param {*} param
 */
export function floorList(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/build/getbuild', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除楼层
 * @param {*} param
 */
export function floorDelete(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/build/removebuild', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 编辑科室分布（某建筑楼楼层有哪些科室）
 * @param {*} param
 */
export function submitDeptCoordinate(param = {}) {
  param.showMode = 'microSite';
  const { id } = param;
  if (id) {
    return utils.request('/api/build/editbuild', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  } else {
    delete param.id;
    return utils.request('/api/build/addbuild', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  }
}

/**
 * 上传医院楼群分布的图片
 * @param {*} param
 */
export function submitEdificeCoordinate(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/userinfo/finduser', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 科室列表
 * @param {*} param
 */
export function departmentList(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/deptinfo/getdeptlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 科室结构树
 * @param {*} param
 */
export function departmentTree(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/deptinfo/getdepttree', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 科室信息
 * @param {*} param
 */
export function departmentDetail(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/deptinfo/getdeptinfo', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除科室
 * @param {*} param
 */
export function departmentDelete(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/deptinfo/deletedept', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 编辑科室
 * @param {*} param
 */
export function submitDepartmentDetail(param = {}) {
  param.showMode = 'microSite';
  const { method } = param;
  if (method == 'modify') {
    delete param.method;
    return utils.request('/api/deptinfo/modifydept', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  } else {
    delete param.method;
    return utils.request('/api/deptinfo/savedept', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  }
}

/**
 * 医生列表
 * @param {*} param
 */
export function doctorList(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/doctorinfo/getdoctorlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 医生详细信息
 * @param {*} param
 */
export function doctorDetail(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/doctorinfo/getdoctorinfo', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除医生
 * @param {*} param
 */
export function doctorDelete(param = {}) {
  param.showMode = 'microSite';
  delete param.listQueryParam;
  return utils.request('/api/doctorinfo/deletedoctor', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 批量删除医生
 * @param {*} param
 */
export function batchDelDoctor(param = {}) {
  return utils.request('/api/doctorinfo/batchdeletedoctors', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function submitDoctorDetail(param = {}) {
  param.showMode = 'microSite';
  delete param.listQueryParam;
  const { method } = param;
  if (method == 'modify') {
    delete param.method;
    return utils.request('/api/doctorinfo/modifydoctor', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  } else {
    delete param.method;
    param.deptNo = param.newDeptNo;
    return utils.request('/api/doctorinfo/savedoctor', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  }
}

/**
 * 医院列表
 * @param {*} param
 */
export function hospitalList(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/userinfo/gethislist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 医院详情
 * @param {*} param
 */
export function hospitalDetail(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/his/getinfo', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 编辑医院信息
 * @param {*} param
 */
export function submitHospitalDetail(param = {}) {
  return utils.request('/api/his/edithisinfo', {
    method: 'POST',
    body: utils.jsonToQueryString({
      showMode: 'microSite',
      id: param.id,
      hisId: param.hisId,
      address: param.address,
      imgPath: param.imgPath,
      introduction: param.introduction,
      levelName: param.levelName,
      name: param.name,
      platformId: param.platformId,
      telNo: param.telNo,
    }),
  });
}

/**
 * 科室排序列表
 * @param {*} param
 */
export function deptSortable(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/deptinfo/getdeptlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 提交科室排序
 * @param {*} param
 */
export function submitDeparmentSortable(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/deptinfo/deptsort', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 医生排序列表
 * @param {*} param
 */
export function doctorSortable(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/doctorinfo/getalldoctorlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 提交医生排序
 * @param {*} param
 */
export function submitDoctorSortable(param = {}) {
  param.showMode = 'microSite';
  return utils.request('/api/doctorinfo/doctorsort', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 批量上传医生头像
 */
export function syncDocImg(param) {
  return utils.request('/api/doctorinfo/autoormanualsynchisdocimg', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 批量删除科室
 */
export function batchDeleteDepts(param) {
  return utils.request('/api/deptinfo/batchdeletedepts', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
