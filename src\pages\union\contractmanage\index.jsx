/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useRef } from 'react';
import { connect, history } from 'umi';
import { Form, Row, Col, Select, DatePicker, Input, Button, Table, Divider, Popconfirm, message, Tooltip } from 'antd';
import queryString from 'query-string';
import { HZMS, HTLX, HTZT, FKFS, JSFS, JSZQ } from './_data';
import { provinceData } from '../province';
import './index.less';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;
const MIN_TABLE_HEIGHT = 218;

const Index = props => {
  const { dispatch, products = [], subproducts = [], allPersonnel = [], allGroup = [], listData = {}, btns = {}, menuType = '' } = props;
  const [form] = Form.useForm();
  const { recordList = [], currentPage = 1, numPerPage = 10, totalCount = 0 } = listData;
  const [cityOption, setCityOption] = useState([]);
  const [queryParam, setQueryParam] = useState({});
  const [isFold, setIsFold] = useState(true);
  const [tableHeight, setTableHeight] = useState(MIN_TABLE_HEIGHT);
  const tableContent = useRef(null);

  useEffect(() => {
    if (tableContent.current) {
      setTableHeight(Math.max(tableContent.current.offsetHeight, MIN_TABLE_HEIGHT));
    }
  }, [tableContent.current, isFold]);

  const getAllCity = value => {
    let allCity = [];
    if (value) {
      provinceData
        .filter(province => province.label == value)
        .forEach(v => {
          allCity.push(...(v.children || []));
        });
    } else {
      provinceData.forEach(v => {
        allCity.push(...(v.children || []));
      });
    }
    setCityOption(allCity);
  };

  const onSearch = value => {
    setQueryParam({ ...queryParam, keyword: value });
    query(1, value);
  };
  const query = (pageNum, inputData) => {
    const values = form.getFieldsValue();
    if (values.signingDate) {
      values.signingDateStart = values.signingDate[0].format('YYYY-MM-DD');
      values.signingDateEnd = values.signingDate[1].format('YYYY-MM-DD');
    }
    if (menuType) {
      values.menuType = menuType;
    }
    setQueryParam({ ...values, keyword: inputData ?? queryParam.keyword });
    dispatch({
      type: 'contract/getcontractbypage',
      payload: { ...values, pageNum, keyword: inputData ?? queryParam.keyword },
    });
  };
  const exportcontract = () => {
    const values = form.getFieldsValue();
    if (values.signingDate) {
      values.signingDateStart = values.signingDate[0].format('YYYY-MM-DD');
      values.signingDateEnd = values.signingDate[1].format('YYYY-MM-DD');
    }
    if (menuType) {
      values.menuType = menuType;
    }
    dispatch({
      type: 'contract/exportcontract',
      payload: { ...values },
    });
  };

  const getproducts = parentId => {
    dispatch({ type: 'contract/getproducts', payload: { parentId, menuType } });
  };

  useEffect(() => {
    dispatch({ type: 'contract/getAllUser', payload: { menuType } });
    dispatch({ type: 'contract/getAllGroup', payload: { numPerPage: 9999, menuType } });
    getproducts();
    getAllCity();
    query(1);

    // 添加resize监听
    const handleResize = () => {
      if (tableContent.current) {
        setTableHeight(Math.max(tableContent.current.offsetHeight, MIN_TABLE_HEIGHT));
      }
    };

    window.addEventListener('resize', handleResize); // 添加事件监听器

    return () => {
      window.removeEventListener('resize', handleResize); // 移除事件监听器
    };
  }, []);
  const deletecontract = async id => {
    dispatch({
      type: 'contract/deletecontract',
      payload: { id },
    }).then(res => {
      if (res) {
        message.success('删除成功！', 2, () => {
          const { currentPage = 1, recordList = [] } = listData;
          let pageNum = currentPage;
          if (status === 0 && recordList.length == 1 && currentPage > 1) {
            pageNum -= 1;
          }
          query(pageNum);
        });
      }
    });
  };

  let columns = [
    {
      title: '序号',
      dataIndex: 'index',
      render: (text, record, index) => totalCount - ((currentPage - 1) * numPerPage + index),
      fixed: 'left',
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      fixed: 'left',
    },
    {
      title: '合同单位',
      dataIndex: 'assignedUnitName',
      fixed: 'left',
      render: v => (
        <Tooltip title={v}>
          <div className="ellipsis">{v}</div>
        </Tooltip>
      ),
    },
    {
      title: '客户编号',
      dataIndex: 'assignedUnitCode',
      fixed: 'left',
    },
    {
      title: '客户省份',
      dataIndex: 'assignedUnitProvinceName',
    },
    {
      title: '客户城市',
      dataIndex: 'assignedUnitCityName',
    },
    {
      title: '销售人员',
      dataIndex: 'salesPersonnelName',
    },
    {
      title: '销售渠道',
      dataIndex: 'cooperationMode',
      render: v => HZMS[v],
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
      render: v => HTLX[v],
    },
    {
      title: '签约日期',
      dataIndex: 'signingDate',
    },
    {
      title: '开始日期',
      dataIndex: 'startDate',
    },
    {
      title: '截止日期',
      dataIndex: 'endDate',
    },
    {
      title: '合同状态',
      dataIndex: 'contractStatus',
      render: v => HTZT[v],
    },
    {
      title: '签约产品线',
      dataIndex: 'relProducts',
      render: item => (
        <Tooltip title={item.map(v => v.productName).join('；')}>
          <div className="ellipsis">{item.map(v => v.productName).join('；')}</div>
        </Tooltip>
      ),
    },
    {
      title: '押金状态',
      dataIndex: 'cxgMarginPayStatus',
      render: v => (v == 1 ? '已支付' : v == '0' ? '未支付' : '-'),
    },
    {
      title: '押金金额',
      dataIndex: 'cxgMargin',
    },
    {
      title: '押金余额',
      dataIndex: 'cxgMarginFree',
    },
    {
      title: '付款方式',
      dataIndex: 'payType',
      render: v => FKFS[v],
    },
    {
      title: '结算方式',
      dataIndex: 'settlementType',
      render: v => JSFS[v],
    },
    {
      title: '结算周期',
      dataIndex: 'payCycle',
      render: v => JSZQ[v],
    },
    {
      title: '录入人员',
      dataIndex: 'inputPersonName',
    },
    {
      title: '最后更新人',
      dataIndex: 'updateUserName',
    },
    {
      title: '最后更新日期',
      dataIndex: 'updateTime',
    },
    {
      title: '操作',
      fixed: 'right',
      render: record => {
        return (
          <>
            <a
              onClick={() => {
                if (menuType && menuType !== 'team') {
                  history.push({
                    pathname: '/union/contractmanage/detail',
                    search: queryString.stringify({ id: record.id, menuType }),
                  });
                } else {
                  window.open(`${window.location.href.split('#')[0]}#/union/contractmanage/detail?id=${record.id}&menuType=${menuType}`, '_blank');
                }
              }}
            >
              详情
            </a>
            {btns['/union/contractmanage/delete'] && (!menuType || menuType === 'team') ? (
              <>
                <Divider type="vertical" />
                <Popconfirm title="确定要删除当前合同吗？" onConfirm={() => deletecontract(record.id)}>
                  <a>删除</a>
                </Popconfirm>
              </>
            ) : (
              ''
            )}
          </>
        );
      },
    },
  ];

  // if (menuType == 'team') {
  //   columns = columns.filter(item => {
  //     return !['updateUserName', 'updateTime'].includes(item.dataIndex);
  //   });
  // }

  return (
    <div className="g-page page-contractmanage">
      <Form className="g-query-box" form={form}>
        <Row gutter={[16, 24]}>
          <Col span={8} className="col-item">
            <FormItem label="销售渠道" name="cooperationMode">
              <Select placeholder="请选择">
                <Option value="">全部</Option>
                {(Object.keys(HZMS) || []).map(key => (
                  <Option key={key} value={key}>
                    {HZMS[key]}
                  </Option>
                ))}
              </Select>
            </FormItem>
          </Col>
          <Col span={8} className="col-item">
            <FormItem label="合同类型" name="contractType">
              <Select placeholder="请选择">
                <Option value="">全部</Option>
                {(Object.keys(HTLX) || []).map(key => (
                  <Option key={key} value={key}>
                    {HTLX[key]}
                  </Option>
                ))}
              </Select>
            </FormItem>
          </Col>
          {isFold ? (
            <Col span={8} style={{ textAlign: 'right', display: 'block' }}>
              <Button type="primary" onClick={() => query(1)}>
                查询
              </Button>
              <Button
                onClick={() => {
                  form.resetFields();
                  getAllCity();
                  query(1);
                }}
                style={{ marginLeft: 10 }}
              >
                重置
              </Button>
              <Button type="link" onClick={() => setIsFold(false)}>
                展开
              </Button>
            </Col>
          ) : (
            <>
              <Col span={8} className="col-item">
                <FormItem label="客户省份" name="province">
                  <Select
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                    onChange={v => {
                      getAllCity(v);
                      form.setFieldsValue({ city: null });
                    }}
                  >
                    <Option value="">全部</Option>
                    {provinceData.map((v, i) => (
                      <Option value={v.label} key={i}>
                        {v.label}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
              <Col span={8} className="col-item">
                <FormItem label="客户城市" name="city">
                  <Select
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                  >
                    <Option value="">全部</Option>
                    {cityOption.map((v, i) => (
                      <Option value={v.label} key={i}>
                        {v.label}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
              <Col span={8} className="col-item">
                <FormItem label="签约产品线" name="productId">
                  <Select
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                    onChange={v => {
                      getproducts(v);
                      form.setFieldsValue({ sonProductId: null });
                    }}
                  >
                    <Option value="">全部</Option>
                    {products.map((v, i) => (
                      <Option value={v.id} key={i}>
                        {v.productName}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
              <Col span={8} className="col-item">
                <FormItem label="签约产品" name="sonProductId">
                  <Select
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                  >
                    <Option value="">全部</Option>
                    {subproducts.map((v, i) => (
                      <Option value={v.id} key={i}>
                        {v.productName}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
              <Col span={8} className="col-item">
                <FormItem label="签约日期" name="signingDate">
                  <RangePicker format="YYYY-MM-DD" />
                </FormItem>
              </Col>
              <Col span={8} className="col-item">
                <FormItem label="合同状态" name="contractStatus">
                  <Select placeholder="请选择">
                    <Option value="">全部</Option>
                    {(Object.keys(HTZT) || []).map(key => (
                      <Option key={key} value={key}>
                        {HTZT[key]}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
              <Col span={8} className="col-item">
                <FormItem label="付款方式" name="payType">
                  <Select placeholder="请选择">
                    <Option value="">全部</Option>
                    {(Object.keys(FKFS) || []).map(key => (
                      <Option key={key} value={key}>
                        {FKFS[key]}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
              <Col span={8} className="col-item">
                <FormItem label="结算方式" name="settlementType">
                  <Select placeholder="请选择">
                    <Option value="">全部</Option>
                    {(Object.keys(JSFS) || []).map(key => (
                      <Option key={key} value={key}>
                        {JSFS[key]}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
              {menuType !== 'person' && (
                <Col span={8} className="col-item">
                  <FormItem label="录入人员" name="inputPerson">
                    <Select
                      placeholder="请选择"
                      showSearch
                      filterOption={(input, option) => {
                        return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }}
                    >
                      <Option value="">全部</Option>
                      {(allPersonnel || []).map(v => (
                        <Option value={v.id} key={v.id}>
                          {v.name}
                        </Option>
                      ))}
                    </Select>
                  </FormItem>
                </Col>
              )}
              {menuType !== 'person' && (
                <Col span={8} className="col-item">
                  <FormItem label="销售人员" name="salesPersonId">
                    <Select
                      placeholder="请选择"
                      showSearch
                      filterOption={(input, option) => {
                        return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }}
                    >
                      <Option value="">全部</Option>
                      {(allPersonnel || []).map(v => (
                        <Option value={v.id} key={v.id}>
                          {v.name}
                        </Option>
                      ))}
                    </Select>
                  </FormItem>
                </Col>
              )}
              {!menuType ? (
                <Col span={8} className="col-item">
                  <FormItem label="销售团队" name="belongTeamId">
                    <Select
                      placeholder="请选择"
                      showSearch
                      filterOption={(input, option) => {
                        return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }}
                    >
                      <Option value="">全部</Option>
                      {allGroup.map((v, i) => (
                        <Option value={v.id} key={i}>
                          {v.teamName}
                        </Option>
                      ))}
                    </Select>
                  </FormItem>
                </Col>
              ) : (
                ''
              )}
              <Col span={menuType === 'team' ? 24 : menuType === 'person' ? 16 : 16} style={{ textAlign: 'right', display: 'block' }}>
                <Button type="primary" onClick={() => query(1)}>
                  查询
                </Button>
                <Button
                  onClick={() => {
                    form.resetFields();
                    getAllCity();
                    query(1);
                  }}
                  style={{ marginLeft: 10 }}
                >
                  重置
                </Button>
                <Button type="link" onClick={() => setIsFold(true)}>
                  收起
                </Button>
              </Col>
            </>
          )}
        </Row>
      </Form>
      <div className="container">
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Search
            placeholder="请输入客户名称/合同编号"
            allowClear
            onSearch={onSearch}
            style={{
              width: 320,
              marginBottom: 16,
            }}
          />
          {!menuType || menuType === 'team' ? (
            <div className="g-table-opt-cell">
              {btns['/union/contractmanage/add'] ? (
                <Button
                  type="primary"
                  onClick={() => {
                    dispatch({
                      type: 'contract/save',
                      payload: { detail: {} },
                    });
                    // history.push('/union/contractmanage/detail');
                    window.open(`${window.location.href.split('#')[0]}#/union/contractmanage/detail?menuType=${menuType}`, '_blank');
                  }}
                >
                  添加合同
                </Button>
              ) : null}
              {btns['/union/contractmanage/import'] ? <Button>数据导入</Button> : null}
              {btns['/union/contractmanage/export'] ? <Button onClick={exportcontract}>数据导出</Button> : null}
            </div>
          ) : null}
        </div>
        <div className="table-box" style={{ minHeight: MIN_TABLE_HEIGHT }}>
          <div className="table-content" ref={tableContent}>
            <Table
              columns={columns}
              dataSource={recordList}
              rowKey="id"
              scroll={{ x: 'max-content', y: tableHeight - 55 - 48 }}
              pagination={{
                showSizeChanger: false,
                showQuickJumper: true,
                current: currentPage,
                pageSize: numPerPage,
                total: totalCount,
                showTotal: total => `共 ${total} 条`,
                onChange: page => {
                  query(page);
                },
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.contract,
  };
})(Index);
