import React from 'react';
import { connect } from 'dva';
import { Input, Row, Col, Button, DatePicker, Select, Cascader, message, Table, Modal } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';
import { Decimal } from 'decimal.js';

import * as utils from '../../../utils/utils';
import Drawer from '../../../components/drawer/Drawer';
import OrderInfo from '../components/OrderInfo';
import * as SearchItem from '../components/SearchItem';
import { Link } from 'react-router-dom';

import '../styles.less';
import './style.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const Option = Select.Option;
let isInitRequest = true;
const today = moment().add(1, 'd');

class OrderExport extends React.Component {
  constructor(props) {
    super(props);
    this.pageNum = 1;
    this.isOrderBack = false; /* 是否 是退款 */
    this.rebackStep = 0;
    this.selectItem = {}; /* 退款或者更新选中的item */
    this.tableHeaderTime = {};
    this.updateOrderStat = (busType, id) => {
      const item = {
        busType,
        id,
      };
      const { dispatch } = this.props;
      this.isOrderBack = true;
      this.selectItem = item;
      SearchItem.updateOrderStat(
        () => {
          dispatch({
            type: 'order/refreshOrderInfo',
            payload: {
              ...item,
              isSuccStatus: 1,
            },
          }).then(res => {
            if (res && res.code == 0) {
              const confirm = Modal.confirm({
                title: '是否需要对该笔订单进行退款？',
                content: <i onClick={() => confirm.destroy()} className="merchanticon anticon-close order-confirm-close" />,
                iconType: 'exclamationcircle',
                cancelText: '不需要',
                okText: '退款',
                maskClosable: false,
                onOk: () => {
                  this.selectItem = item;
                  this.promiseUpdate(item);
                },
                onCancel: () => {
                  this.handleSubmit(this.pageNum);
                },
              });
            }
          });
        },
        () => {
          dispatch({
            type: 'order/refreshOrderInfo',
            payload: {
              ...item,
              isSuccStatus: 0,
            },
          }).then(res => {
            if (res && res.code == 0) {
              const confirm = Modal.confirm({
                title: '是否需要对该笔订单进行退款？',
                content: <i onClick={() => confirm.destroy()} className="merchanticon anticon-close order-confirm-close" />,
                iconType: 'exclamationcircle',
                cancelText: '不需要',
                okText: '退款',
                maskClosable: false,
                onOk: () => {
                  this.promiseUpdate(item);
                },
                onCancel: () => {
                  this.handleSubmit(this.pageNum);
                },
              });
            }
          });
        },
      );
    };
    this.state = {
      footType: '1',
    };
  }
  componentDidMount() {
    this.props.dispatch({ type: 'order/fetchHisLst' });
    this.props.dispatch({ type: 'order/fetchBusChannelLst' });
  }
  // eslint-disable-next-line react/no-deprecated
  componentWillReceiveProps(nextProps) {
    // 等拿到业务办理渠道和业务类型 后再查询列表
    const { order } = nextProps;
    if (isInitRequest && order.busChannelLst.code == 0 && order.busType.code == 0 && order.hisList.code == 0) {
      const reqData = {
        busType: order.busType.data && order.busType.data.length ? order.busType.data[0].dictKey : '',
        // busChannel: order.busChannelLst.data && order.busChannelLst.data.length ?
        // order.busChannelLst.data[0].dictKey : '',
        busChannel: '',
        hisId: order.hisList.data && order.hisList.data.length ? order.hisList.data[0].hisId : '',
        startTime: moment(moment().add(-1, 'M')).format('YYYY-MM-DD'),
        endTime: moment(moment()).format('YYYY-MM-DD'),
      };
      this.queryData = reqData;
      this.tableHeaderTime = {
        startTime: reqData.startTime,
        endTime: reqData.endTime,
      };
      this.props.dispatch({
        type: 'order/fetchOrderStatus',
        payload: {
          busType: order.busType.data && order.busType.data.length ? order.busType.data[0].dictKey : '',
        },
      });
      // this.props.dispatch({
      //   type: 'order/fetchOrderLst',
      //   payload: reqData,
      // });
      isInitRequest = false;
    }
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'order/clear',
    });
    isInitRequest = true;
  }
  /* 更新状态 */
  promiseUpdate(item) {
    const { dispatch } = this.props;
    this.handleSubmit(this.pageNum);
    dispatch({
      type: 'order/fetchOrderInfo',
      payload: {
        ...item,
      },
    });
    dispatch({
      /* 查询操作列表 */
      type: 'order/fetchOrderbyorderId',
      payload: item.id,
    });
    dispatch({
      type: 'root/openDrawer',
    });
  }
  /* 查看订单详情 */
  orderDetail(item, type, index) {
    /* refundStatus 1: 退款中 2：退款完结 0 无退款  type 退款按钮点击*/
    if (!item) {
      return false;
    }
    const { busType, bizOrderId, refundFlag } = item;
    this.selectItem = {
      busType,
      id: bizOrderId,
      index,
      type,
    };
    this.isOrderBack = type ? 1 : 0;
    if (refundFlag) {
      this.rebackStep = refundFlag;
    } else {
      this.rebackStep = 0;
    }
    this.props.dispatch({
      type: 'order/fetchOrderInfo',
      payload: {
        busType,
        id: bizOrderId,
      },
    });
    this.props.dispatch({
      /* 查询操作列表 */
      type: 'order/fetchOrderbyorderId',
      payload: bizOrderId,
    });
    this.props.dispatch({
      type: 'root/openDrawer',
    });
  }
  dateValidateHandler = (rule, value, callback) => {
    const beginDate = moment(value[0]);
    const endDate = moment(value[1]);
    if (endDate.valueOf() > beginDate.month(beginDate.month() + 3).valueOf()) {
      callback('选择周期不能超过三个月！');
    } else {
      callback();
    }
  };
  handleSubmit(pageNo) {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const pageNum = pageNo || 1;
        const reqData = { ...values, pageNum };
        this.pageNum = pageNo;
        reqData.payChannel = values.payChannel ? values.payChannel[0] : '';
        reqData.payWay = values.payChannel ? values.payChannel[1] : '';
        reqData.startTime = moment(values.time[0]).format('YYYY-MM-DD');
        reqData.endTime = moment(values.time[1]).format('YYYY-MM-DD');
        reqData.refundState = values.refundState == -1 ? '' : values.refundState;
        this.selectItem.hisId = values.hisId;
        this.queryData = reqData; // 退款后 查询列表用
        this.tableHeaderTime = {
          startTime: reqData.startTime,
          endTime: reqData.endTime,
        };
        this.props.dispatch({
          type: 'order/fetchOrderLst',
          payload: reqData,
        });
      }
    });
  }
  handleReset() {
    this.props.dispatch({
      type: 'order/clearBusType',
    });
    this.props.form.resetFields();
    // this.busChanneChange(this.props.order.busChannelLst.data[0].dictKey);
    this.props.dispatch({ type: 'order/fetchBusChannelLst' });
    this.handleSubmit();
    isInitRequest = true;
  }
  orderReBack(backReason, refundFee, rundPwd) {
    if (!/^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(refundFee) || refundFee * 1 <= 0) {
      message.warn('请输入最多两位小数且大于0的退款金额');
      return false;
    }
    const orderData = this.props.order.order.orderInfo.data.data;
    refundFee = new Decimal(refundFee).mul(new Decimal(100)).toNumber();
    let maxRefundFee = orderData.totalRealFee || 0;
    if (orderData.totalRefundFee > 0) {
      maxRefundFee = new Decimal(maxRefundFee).sub(orderData.totalRefundFee).toNumber();
    }
    if (refundFee > maxRefundFee) {
      message.warn(`本次最多可退费金额${new Decimal(maxRefundFee).div(100).toNumber()}元`);
      return false;
    }

    if (!backReason) {
      message.warn('请输入退款原因');
      return;
    }
    if (!rundPwd) {
      message.warn('请输入退款密码');
      return;
    }
    const { dispatch } = this.props;
    const confirm = SearchItem.updateOrderStat(
      () => {
        dispatch({
          type: 'order/reBackOrder',
          payload: {
            rebackData: {
              ...this.selectItem,
              reason: backReason,
              rundPwd,
              refundFee,
            },
            queryData: this.queryData,
            queryType: 'order',
          },
          next: this.orderDetail.bind(this),
        });
      },
      () => {
        confirm.destroy();
      },
      '是否需要对该笔订单进行退款？',
      '不需要',
      '退款',
    );
  }
  busTypeChange(value) {
    this.props.dispatch({
      type: 'order/fetchOrderStatus',
      payload: { busType: value },
    });
    this.props.form.resetFields(['status']);
  }
  busChanneChange(value) {
    const { hisId } = this.props.form.getFieldsValue(['hisId']);
    this.props.dispatch({
      type: 'order/fetchBusTypeGroup',
      payload: { type: value, hisId },
    });
    this.props.dispatch({
      type: 'order/fetchPayChannelLst',
      payload: { busChannel: value },
    });
    this.props.form.resetFields(['status', 'busType', 'payChannel']);
  }
  itemRender = (current, type, originalElement) => {
    if (type === 'prev') {
      return <a>上一页</a>;
    }
    if (type === 'next') {
      return <a>下一页</a>;
    }
    if (type === 'page') {
      return <a>{current}</a>;
    }
    return originalElement;
  };
  render() {
    const { drawerStatus, order, btns = {} } = this.props;
    const { getFieldDecorator } = this.props.form;
    const formItemLayout = {
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    };
    const busType =
      order.busType && order.busType.data && order.busType.data.length ? (
        order.busType.data.map(item => (
          <Option key={item.dictKey} value={item.dictKey}>
            {item.dictValue}
          </Option>
        ))
      ) : (
        <Option value="">暂无数据</Option>
      );

    const orderStatus =
      order.order.orderStatus && order.order.orderStatus.data && order.order.orderStatus.data.length ? (
        order.order.orderStatus.data.map(item => (
          <Option key={`${item.dictKey}`} value={item.dictKey}>
            {item.dictValue}
          </Option>
        ))
      ) : (
        <Option value="">暂无数据</Option>
      );

    const tableData = order.order.orderLst && order.order.orderLst.data && order.order.orderLst.data.recordList ? order.order.orderLst.data.recordList : [];

    const columns = [
      {
        title: '序号',
        key: 'hisOrderNo',
        render: (text, record, index) => {
          return <span>{index + 1}</span>;
        },
      },
      {
        title: '交易时间',
        render: record => {
          return (
            <div>
              <p>{record.tradeTime ? record.tradeTime.split(' ')[0] : ''}</p>
              <p>{record.tradeTime ? record.tradeTime.split(' ')[1] : ''}</p>
            </div>
          );
        },
      },
      {
        title: '姓名丨卡号',
        render: record => {
          return (
            <div>
              <span>{record.patientName}</span>
              <p>{record.patCardNo || record.admissionNum}</p>
            </div>
          );
        },
      },
      {
        title: '类型丨平台订单号',
        render: record => {
          return (
            <div>
              <span>{record.busTypeDesc}</span>
              <p>{record.bizOrderId}</p>
            </div>
          );
        },
      },
      {
        title: '支付方式 | 支付流水号',
        render: record => {
          return (
            <div>
              <span>{record.payMode}</span>
              <p>{record.agtOrdNum}</p>
            </div>
          );
        },
      },
      {
        title: '状态',
        render: record => {
          return <span>{SearchItem.getStatusName(record.busType, record.status) || record.statusDesc}</span>;
        },
      },
      {
        title: '有无退款',
        dataIndex: 'refundStateDesc',
      },
      {
        title: '金额（元）',
        render: record => {
          return <p>{utils.comma(record.totalRealFee || 0)}</p>;
        },
      },
      {
        title: '操作' /* 更新 退款不能同时存在 */,
        render: (text, record, index) => {
          return (
            <div className="opt-cloum">
              <p>
                {btns['/transaction/order/detail'] ? <a onClick={() => this.orderDetail(record)}>查看</a> : null}
                {record.canRefund && record.refundFlag != 2 && btns['/transaction/order/reback'] && !(record.status == 'HZ' || record.status == 'A' || record.status == 'Z' || record.status == 'H') ? (
                  <span>
                    <a>丨</a>
                    <a onClick={() => this.orderDetail(record, 'back', index)}>退款</a>
                  </span>
                ) : (
                  false
                )}
                {(record.status == 'HZ' || record.status == 'A' || record.status == 'Z' || record.status == 'H') && btns['/transaction/order/update'] ? (
                  <span>
                    <a>丨</a>
                    <a onClick={() => this.updateOrderStat(record.busType, record.bizOrderId)}>更新状态</a>
                  </span>
                ) : (
                  false
                )}
              </p>
            </div>
          );
        },
      },
    ];
    return (
      <div className="page-transaction page-transaction-order order-all">
        <div className="tsc-top-panle">
          <Form
            hideRequiredMark={true}
            form={this.props.form}
            onSubmit={e => {
              e.preventDefault();
              this.handleSubmit();
            }}
          >
            <Row type="flex" justify="start">
              {/* <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="医院">
                  {getFieldDecorator('hisId', {
                    initialValue: order.hisList.data ? `${order.hisList.data[0].hisId}` : '',
                  })(<Select placeholder="请选择">{SearchItem.hisListItem(order)}</Select>)}
                </FormItem>
              </Col> */}

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="查询时间">
                  {getFieldDecorator('time', {
                    initialValue:
                      this.state.footType === '1'
                        ? [moment().add(-1, 'M'), moment()]
                        : [
                            moment()
                              .add(-4, 'M')
                              .startOf('month'),
                            moment()
                              .add(-4, 'M')
                              .endOf('month'),
                          ],
                    rules: [
                      {
                        required: 'Y',
                        message: '请选择查询时间',
                      },
                      {
                        validator: this.dateValidateHandler,
                      },
                    ],
                  })(
                    <RangePicker
                      format="YYYY-MM-DD"
                      allowClear={false}
                      renderExtraFooter={() => (
                        <span className="foot-btn">
                          <text>请选择时间：</text>
                          <span className={`${this.state.footType === '1' ? 'active' : ''}`} onClick={() => this.setState({ footType: '1' })}>
                            {moment()
                              .add(-3, 'M')
                              .startOf('month')
                              .format('YYYY-MM-DD')}
                            之后
                          </span>
                          <span className={`${this.state.footType === '2' ? 'active' : ''}`} onClick={() => this.setState({ footType: '2' })}>
                            {moment()
                              .add(-3, 'M')
                              .startOf('month')
                              .format('YYYY-MM-DD')}
                            之前
                          </span>
                        </span>
                      )}
                      disabledDate={date => {
                        const { footType } = this.state;
                        let range =
                          date.valueOf() <=
                            moment()
                              .add(-3, 'M')
                              .startOf('month')
                              .valueOf() || date.valueOf() >= today.valueOf();
                        if (footType === '2') {
                          range =
                            date.valueOf() >=
                            moment()
                              .add(-3, 'M')
                              .startOf('month')
                              .valueOf();
                        } else {
                          range =
                            date.valueOf() <=
                              moment()
                                .add(-3, 'M')
                                .startOf('month')
                                .valueOf() || date.valueOf() >= today.valueOf();
                        }
                        return range;
                      }}
                    />,
                  )}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="业务办理渠道">
                  {getFieldDecorator('busChannel', {
                    initialValue: '',
                  })(
                    <Select placeholder="请选择业务办理渠道" onChange={value => this.busChanneChange(value)}>
                      {SearchItem.busChannelLstItem(order)}
                    </Select>,
                  )}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="支付方式">
                  {getFieldDecorator('payChannel', {
                    initialValue: [''],
                  })(<Cascader options={SearchItem.payWasItem(order)} placeholder="请选择支付方式" allowClear={false} />)}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="业务类型">
                  {getFieldDecorator('busType', {
                    initialValue: order.busType.data && order.busType.data.length ? order.busType.data[0].dictKey : '',
                    rules: [
                      {
                        required: 'Y',
                        message: '请选择业务类型',
                      },
                    ],
                  })(
                    <Select placeholder="请选择业务类型" onChange={value => this.busTypeChange(value)}>
                      {/* <Option value="">全部</Option> */}
                      {busType}
                    </Select>,
                  )}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="订单状态">
                  {getFieldDecorator('status', {
                    initialValue: order.order.orderStatus && order.order.orderStatus.data && order.order.orderStatus.data.length ? order.order.orderStatus.data[0].dictKey : '',
                    rules: [
                      {
                        message: '请选择订单状态',
                      },
                    ],
                  })(<Select placeholder="请选择订单状态">{orderStatus}</Select>)}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="退款情况">
                  {getFieldDecorator('refundState', {
                    initialValue: '-1',
                    rules: [
                      {
                        required: 'Y',
                        message: '请选择退款情况',
                      },
                    ],
                  })(
                    <Select>
                      <Option value="-1">全部</Option>
                      <Option value="1">有退款</Option>
                      <Option value="0">无退款</Option>
                    </Select>,
                  )}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="平台订单号">
                  {getFieldDecorator('bizOrderId', {
                    rules: [
                      {
                        message: '请输入平台订单号',
                        max: 25,
                      },
                    ],
                  })(<Input maxLength="25" placeholder="请输入平台订单号" />)}
                </FormItem>
              </Col>
              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="诊疗卡">
                  {getFieldDecorator('other', {
                    rules: [
                      {
                        message: '请输入姓名／诊疗卡',
                      },
                    ],
                  })(<Input maxLength="50" placeholder="请输入姓名／诊疗卡" />)}
                </FormItem>
              </Col>
              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="支付流水号">
                  {getFieldDecorator('agtOrdNum', {
                    initialValue: '',
                    rules: [
                      {
                        message: '请输入正确的支付流水号',
                        pattern: /^[a-zA-Z0-9]*$/,
                      },
                    ],
                  })(<Input maxLength="64" placeholder="请输入支付流水号" />)}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="退款流水号">
                  {getFieldDecorator('refundSerialNo', {
                    initialValue: '',
                    rules: [
                      {
                        message: '请输入正确的退款流水号',
                        pattern: /^[0-9]*$/,
                      },
                    ],
                  })(<Input maxLength="64" placeholder="请输入退款流水号" />)}
                </FormItem>
              </Col>

              <Col span={16} className="btn-bar">
                <Button type="primary" htmlType="submit">
                  查询
                </Button>
                <Button className="ant-col-offset-1" onClick={() => this.handleReset()}>
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
        </div>

        <div className="tsc-main-panle" style={{ padding: '0 24px' }}>
          <Table
            columns={columns}
            dataSource={tableData}
            title={() =>
              `${this.tableHeaderTime.startTime || ''}至
            ${this.tableHeaderTime.endTime || ''}`
            }
            rowKey="bizOrderId"
            pagination={{
              showSizeChanger: false,
              showQuickJumper: true,
              current: order.order.orderLst && order.order.orderLst.data ? order.order.orderLst.data.currentPage : 0,
              pageSize: order.order.orderLst && order.order.orderLst.data ? order.order.orderLst.data.numPerPage : 10,
              total: order.order.orderLst && order.order.orderLst.data ? order.order.orderLst.data.totalCount : 1,
              onChange: pageNum => {
                this.handleSubmit(pageNum);
              },
              // itemRender: this.itemRender,
              // showLessItems: true,
            }}
          />
        </div>

        <Drawer open={drawerStatus} className="transaction-drawer">
          <OrderInfo
            data={order.order.orderInfo && order.order.orderInfo.data ? order.order.orderInfo.data.data : {}}
            isOrderBack={this.isOrderBack}
            funcReBack={(backReason, refundFee, rundPwd) => this.orderReBack(backReason, refundFee, rundPwd)}
            rebackStep={this.rebackStep}
            operData={order.process.orderInfo && order.process.orderInfo.data ? order.process.orderInfo.data : []}
          />
        </Drawer>
      </div>
    );
  }
}

export default connect(state => {
  return {
    order: state.order,
    process: state.process,
    drawerStatus: state.root.drawerStatus,
    btns: state.root.permissionData.btns,
  };
})(Form.create()(OrderExport));
