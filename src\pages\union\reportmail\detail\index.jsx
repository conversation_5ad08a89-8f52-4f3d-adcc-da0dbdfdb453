// src/pages/detail/index.jsx
import React from 'react';
import { connect } from 'dva';
import { Modal, Table, Pagination } from 'antd';
import moment from 'moment';
import styles from './index.less';

const columns = [
  {
    title: '送检医院',
    dataIndex: 'submitHospitalName',
    width: 150,
    render: text => text || '-',
  },
  {
    title: '客户名称',
    dataIndex: 'institutionName',
    width: 150,
    render: text => text || '-',
  },
  {
    title: '样本编号',
    dataIndex: 'sampleNumber',
    width: 120,
  },
  {
    title: '受检者',
    dataIndex: 'sjzName',
    width: 150,
    render: text => text || '-',
  },
  {
    title: '采样日期',
    dataIndex: 'sampleTime',
    width: 120,
    render: text => (text ? moment(text).format('YYYY-MM-DD HH:mm') : '-'),
  },
  {
    title: '报告日期',
    dataIndex: 'reportTime',
    width: 120,
    render: text => (text ? moment(text).format('YYYY-MM-DD HH:mm') : '-'),
  },
  {
    title: '报告结果',
    dataIndex: 'resultType',
    width: 100,
    render: text => text || '-',
  },
];

@connect(({ reportmail }) => ({
  visible: reportmail.detailVisible,
  pagination: reportmail.pagination,
  queryParams: reportmail.queryParams,
  currentRecord: reportmail.currentRecord || {},
  listData: reportmail.listData || [],
}))
class DetailModal extends React.Component {
  getTableData = (pageNum = 1) => {
    var payload = {
      pageNum,
      numPerPage: this.props.pagination.pageSize,
      id: this.props.currentRecord?.id, // 列表ID
      institutionId: this.props.currentRecord?.institutionId, // 客户ID
      groupedProductId: this.props.currentRecord?.groupedProductId, // 产品ID
    };
    // 如果有查询参数，则添加到payload中
    if (this.props.queryParams.reportStartDate) {
      payload.reportStartDate = this.props.queryParams.reportStartDate;
    }
    if (this.props.queryParams.reportEndDate) {
      payload.reportEndDate = this.props.queryParams.reportEndDate;
    }
    this.props.dispatch({
      type: 'reportmail/fetchDetail', // 确保调用正确的action
      payload: payload,
    });
  };

  // 表格每页行数改变
  onShowSizeChange = (c, size) => {
    this.props.pagination.pageSize = size; // 更新pagination的pageSize
    this.getTableData();
  };

  handleClose = () => {
    this.props.dispatch({
      type: 'reportmail/closeDetail',
      payload: {
        detailVisible: false,
      },
    });
  };

  render() {
    const { visible, listData, pagination } = this.props;

    return (
      <Modal title="报告详情" open={visible} onCancel={this.handleClose} width={1200} footer={null} destroyOnClose className={styles.modal}>
        <Table
          columns={columns}
          dataSource={listData || []}
          rowKey="id"
          pagination={{
            total: pagination.total || 0,
            showTotal: total => `共 ${total} 条`,
            onChange: pageNum => {
              this.getTableData(pageNum);
            },
            current: pagination.current || 0,
            pageSize: pagination.pageSize,
            showQuickJumper: true,
            showSizeChanger: true,
            onShowSizeChange: this.onShowSizeChange,
          }}
          bordered
          size="small"
          scroll={{ x: 'max-content' }}
          className={styles.table}
        />
        {/* <br />
        <div className={styles.footer}>
          <span className={styles.remark}>备注：{this.props.data.remark}</span>

        </div> */}
      </Modal>
    );
  }
}

export default DetailModal;
