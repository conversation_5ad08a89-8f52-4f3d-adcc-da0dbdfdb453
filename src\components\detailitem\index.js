import React from 'react';
import { Row, Col, Divider, Button } from 'antd';
import styles from './index.less';
/**
 * 详情组件
 * @param {*} props
 */
const DetailItem = props => {
  const { title = '', detailList = [], children } = props;
  return (
    <div className={styles.comDetailItem}>
      {title ? <div className={styles.title}>{title}</div> : null}
      <Row justify="start" type="flex">
        {detailList.map(item => {
          const visible = item.visible === undefined ? true : item.visible;
          if (!visible || !item.content) {
            return null;
          }
          return (
            <Col xs={{ span: 24 }} sm={{ span: 24 }} md={{ span: 12 }} lg={{ span: 12 }} xl={{ span: 8 }} xxl={{ span: 8 }} style={{ marginBottom: 16 }}>
              <div className={styles.label}>{item.label}：</div>
              <div className={styles.detailItemContent}>{item.content || '暂无'}</div>
              {item.showNewButton ? (
                <Button type="primary" className={styles.resOrder} onClick={item.onClickEvent}>
                  重新下单
                </Button>
              ) : null}
            </Col>
          );
        })}
      </Row>
      {children || null}
      <Divider dashed />
    </div>
  );
};

export default DetailItem;
