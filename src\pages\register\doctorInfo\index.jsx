import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Tabs, Cascader, Input, Button, Table, Modal, message } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';

import Drawer from '../../../components/drawer/Drawer';

import IIHOCDoctorDrawer from './components/IIHOCDoctorDrawer';
import DoctorEdit from './components/DoctorEdit';
import DoctorDetail from './components/DoctorDetail';

import '../../microwebsite/style.less';

import * as utils from '../../../utils/utils';

const TabPane = Tabs.TabPane;
const FormItem = Form.Item;
const confirm = Modal.confirm;

export default connect(state => {
  return {
    drawerStatus: state.root.drawerStatus,
    hisList: state.register.hospital.list,
    deptTree: state.register.department.tree,
    ...state.register.doctor,
    permissionData: state.root.permissionData,
  };
})(
  Form.create()(
    class Doctor extends React.Component {
      state = {
        drawerType: 'detail',
        drawer: null,
        rowKeys: [], //选中项id
      };

      componentDidMount() {
        const { dispatch, location } = this.props;
        const hisId = utils.queryStringToJson(location.search).hisId;
        dispatch({
          type: 'register/hospitalList',
          payload: {
            hisIdStr: hisId,
          },
          next: ['doctorList', 'departmentTree'],
        });
      }

      openDrawer = () => {
        const { dispatch } = this.props;
        dispatch({
          type: 'root/openDrawer',
        });
      };

      getDoctorDrawer = hisId => {
        const { drawerType } = this.state;
        const { form, list = {} } = this.props;
        const param = form.getFieldsValue();
        const listQueryParam = {
          hisId,
          ...param,
          pageNum: list.currentPage || 1,
          deptNo: param.deptNo && param.deptNo[param.deptNo.length - 1],
        };
        if (drawerType == 'detail') {
          const { detail } = this.props;
          return IIHOCDoctorDrawer({
            WrappedComponent: DoctorDetail,
            detail,
            type: drawerType,
            hisId,
            listQueryParam,
          });
        } else if (drawerType == 'modify') {
          const { detail } = this.props;
          return IIHOCDoctorDrawer({ WrappedComponent: DoctorEdit, detail, type: drawerType, hisId, listQueryParam });
        } else if (drawerType == 'new') {
          return IIHOCDoctorDrawer({ WrappedComponent: DoctorEdit, detail: {}, type: drawerType, hisId, listQueryParam });
        } else {
          return null;
        }
      };

      departmentTree = payload => {
        const { dispatch, form } = this.props;
        const { setFieldsValue } = form;
        setFieldsValue({
          deptNo: [],
        });
        dispatch({
          type: 'register/departmentTree',
          payload,
        });
      };

      doctorList = payload => {
        const { dispatch } = this.props;
        dispatch({
          type: 'register/doctorList',
          payload,
        });
      };

      doctorDetail = payload => {
        const { dispatch } = this.props;
        dispatch({
          type: 'register/doctorDetail',
          payload,
        });
      };

      editDoctor = (type, payload) => {
        if (type != 'new') {
          this.doctorDetail(payload);
        }
        this.setState({
          drawerType: type,
        });
        this.openDrawer();
      };

      deleteDoctor = payload => {
        const { dispatch, list = {} } = this.props;
        const param = this.props.form.getFieldsValue();
        const listQueryParam = {
          hisId: payload.hisId,
          ...param,
          pageNum: list.currentPage || 1,
          deptNo: param.deptNo && param.deptNo[param.deptNo.length - 1],
        };
        dispatch({
          type: 'register/doctorDelete',
          payload: {
            ...payload,
            listQueryParam,
          },
        });
      };

      syncDoctorToMicro = hisId => {
        const { dispatch } = this.props;
        dispatch({
          type: 'register/syncDoctorToMicro',
          payload: {
            hisId,
          },
        });
      };

      batchDel(hisId) {
        const { rowKeys = [] } = this.state;
        const { dispatch, list = {} } = this.props;
        const param = this.props.form.getFieldsValue();
        if (rowKeys.length === 0) {
          message.warn('请勾选要删除的医生！');
          return false;
        }
        confirm({
          title: <div>确定要删除所选医生吗？</div>,
          content: '删除后无法再恢复，请确认是否删除该医生。',
          onOk() {
            const ids = [];
            rowKeys.map(item => {
              ids.push(item.no);
            });
            const listQueryParam = {
              ...param,
              hisId,
              pageNum: list.currentPage || 1,
              deptNo: param.deptNo && param.deptNo[param.deptNo.length - 1],
            };
            dispatch({
              type: 'register/batchDelDoctor',
              payload: {
                hisId,
                ids: ids.join(','),
                type: 'register',
                listQueryParam,
              },
            });
          },
        });
      }

      render() {
        const { hisList = [], deptTree = [], list = [], drawerStatus, form, location, permissionData = {} } = this.props;
        if (!hisList || hisList.length == 0) return null;
        const { btns = {} } = permissionData;
        const { recordList } = list;
        const { getFieldDecorator, getFieldsValue, resetFields } = form;
        const hisId = utils.queryStringToJson(location.search).hisId || (hisList && hisList.length > 0 && hisList[0].hisId);
        const DoctorDrawer = this.getDoctorDrawer(hisId);
        const doctorTable = {
          style: { marginBottom: 14 },
          columns: [
            {
              title: '序号',
              key: 'seqNo',
              render: (value, row, num) => {
                const obj = {
                  children: num + 1,
                };
                return obj;
              },
            },
            {
              title: '姓名',
              dataIndex: 'name',
            },
            {
              title: '所属科室',
              dataIndex: 'deptName',
            },
            {
              title: '级别',
              dataIndex: 'title',
            },
            {
              title: '类型',
              dataIndex: 'levelDesc',
            },
            {
              title: '医生编号',
              dataIndex: 'no',
            },
            {
              title: '操作',
              width: 260,
              render: record => {
                return (
                  <span>
                    {btns['/register/doctorInfo/modify'] ? (
                      <a
                        onClick={() => {
                          this.editDoctor('modify', {
                            hisId: record.hisId,
                            deptNo: record.deptNo,
                            no: record.no,
                          });
                        }}
                      >
                        修改
                      </a>
                    ) : null}
                    <span style={{ color: '#4C9CDF' }}>丨</span>
                    {btns['/register/doctorInfo/detail'] ? (
                      <a
                        onClick={() => {
                          this.editDoctor('detail', {
                            hisId: record.hisId,
                            deptNo: record.deptNo,
                            no: record.no,
                          });
                        }}
                      >
                        查看
                      </a>
                    ) : null}
                    <span style={{ color: '#4C9CDF' }}>丨</span>
                    {btns['/register/doctorInfo/delete'] ? (
                      <a
                        onClick={() => {
                          const $this = this;
                          confirm({
                            title: (
                              <div>
                                确定删除医生
                                <span style={{ color: '#f57f17' }}>{record.name}</span>
                                吗？
                              </div>
                            ),
                            content: '删除后无法再恢复，请确认是否删除该医生。',
                            onOk() {
                              $this.deleteDoctor({
                                hisId: record.hisId,
                                deptNo: record.deptNo,
                                no: record.no,
                              });
                            },
                          });
                        }}
                      >
                        删除
                      </a>
                    ) : null}
                  </span>
                );
              },
            },
          ],
        };

        return (
          <div className="page-doctor">
            <Drawer open={drawerStatus}>
              <DoctorDrawer />
            </Drawer>
            <Tabs
              animated={false}
              defaultActiveKey={`${hisId}`}
              style={{ display: 'flex', flex: 'auto', flexDirection: 'column' }}
              onChange={id => {
                this.departmentTree({
                  hisId: id,
                });
                this.doctorList({
                  hisId: id,
                });
                resetFields();
                history.push(`/register/doctorInfo?hisId=${id}`);
              }}
            >
              {hisList.map(item => {
                return (
                  <TabPane tab={item.hisName} key={`${item.hisId}`}>
                    <div className="doctor-body">
                      <div className="doctor-option">
                        <Form
                          layout="inline"
                          onSubmit={e => {
                            e.preventDefault();
                            const param = getFieldsValue();
                            this.doctorList({
                              hisId: item.hisId,
                              ...param,
                              deptNo: param.deptNo && param.deptNo[param.deptNo.length - 1],
                            });
                          }}
                        >
                          <FormItem>
                            {getFieldDecorator(
                              'deptNo',
                              {},
                            )(<Cascader style={{ width: 211 }} placeholder="请选择科室" options={deptTree} displayRender={label => label[label.length - 1]} changeOnSelect showSearch />)}
                          </FormItem>
                          <FormItem>{getFieldDecorator('name', {})(<Input style={{ width: 125 }} placeholder="医生姓名" />)}</FormItem>
                          <FormItem>{getFieldDecorator('no', {})(<Input style={{ width: 125 }} placeholder="医生编号" />)}</FormItem>
                          <FormItem>
                            <Button type="primary" size="default" htmlType="submit">
                              查询
                            </Button>
                          </FormItem>
                          <FormItem>
                            <Button
                              onClick={() => {
                                this.props.form.resetFields();
                                this.doctorList({ hisId });
                              }}
                              size="default"
                            >
                              重置
                            </Button>
                          </FormItem>
                          <FormItem style={{ float: 'right', margin: 0 }}>
                            {btns['/register/doctorInfo/upload'] ? (
                              <Button
                                onClick={() => {
                                  history.push(`/register/doctorInfo/upload?hisId=${item.hisId}`);
                                }}
                                size="default"
                              >
                                批量添加
                              </Button>
                            ) : null}
                            {btns['/register/doctorInfo/sync'] ? (
                              <a
                                style={{ margin: '0 0 0 24px', fontSize: 14, fontWeight: 400 }}
                                onClick={() => {
                                  const $this = this;
                                  confirm({
                                    title: <div>确定将所有医生信息同步到微网站吗？</div>,
                                    content: '同步后将覆盖微网站已有医生的信息，请仔细确认是否同步。',
                                    onOk() {
                                      $this.syncDoctorToMicro(item.hisId);
                                    },
                                  });
                                }}
                              >
                                同步到微网站
                              </a>
                            ) : null}
                          </FormItem>
                        </Form>
                      </div>
                      <div className="doctor-list">
                        <div style={{ paddingBottom: 8 }}>
                          {btns['/register/doctorInfo/new'] ? (
                            <Button
                              type="primary"
                              onClick={() => {
                                this.editDoctor('new');
                              }}
                            >
                              添加医生
                            </Button>
                          ) : null}
                          {btns['/register/doctorInfo/batchdelete'] ? (
                            <Button
                              onClick={() => {
                                this.batchDel(hisId);
                              }}
                              style={{ marginLeft: '30px' }}
                              disabled={recordList && recordList.length === 0}
                            >
                              批量删除
                            </Button>
                          ) : null}
                        </div>
                        {recordList && recordList.length > 0 ? (
                          <Table
                            style={doctorTable.style}
                            columns={doctorTable.columns}
                            dataSource={recordList}
                            rowKey={row => `${row.deptNo}-${row.no}`}
                            pagination={{
                              showQuickJumper: true,
                              defaultCurrent: list.currentPage || 1,
                              current: list.currentPage,
                              total: list.totalCount,
                              showTotal: () => {
                                return `共${list.totalCount}条`;
                              },
                              onChange: pageNum => {
                                const param = getFieldsValue();
                                this.doctorList({
                                  hisId: item.hisId,
                                  ...param,
                                  deptNo: param.deptNo && param.deptNo[param.deptNo.length - 1],
                                  pageNum,
                                });
                              },
                            }}
                            rowSelection={{
                              onChange: (selectedRowKeys, selectedRows) => {
                                const rowKeys = selectedRows.map(obj => {
                                  return {
                                    deptNo: obj.deptNo,
                                    no: obj.no,
                                  };
                                });
                                this.setState({ rowKeys });
                              },
                            }}
                          />
                        ) : (
                          <Table style={doctorTable.style} columns={doctorTable.columns} rowKey={row => `${row.deptNo}-${row.no}`} pagination={false} />
                        )}
                      </div>
                    </div>
                  </TabPane>
                );
              })}
            </Tabs>
          </div>
        );
      }
    },
  ),
);
