/* eslint-disable react/jsx-no-duplicate-props */
import React, { useEffect, useState } from 'react';
import { Form, Row, Col, Descriptions, Space, Button, Table, DatePicker, Empty, Card } from 'antd';
import moment from 'moment';
import queryString from 'query-string';
import * as Api from '../service';
import { md5Encrypt } from '@/utils/utils';

const { RangePicker } = DatePicker;

const Index = props => {
  const { patIdNo, patName, startTime = '' } = queryString.parse(props.location.search);

  const [recordData, setRecordData] = useState([]);
  const [range, setRange] = useState([moment().subtract(3, 'months'), moment()]);

  useEffect(() => {
    fetchList();
  }, []);

  const fetchList = async () => {
    try {
      const params = {
        patIdNo,
        patName,
        msgKey: md5Encrypt(patIdNo + '2023queryChargeList'),
        beginDate: (range && range[0].format('YYYY-MM-DD')) || '',
        endDate: (range && range[1].format('YYYY-MM-DD')) || '',
        // "beginDate":"2023-09-01",
        // "endDate":"2023-09-11",
        // "patName":"朱珊",
        // "patIdNo":"******************",
        // msgKey: md5Encrypt("******************" + '2023queryChargeList'),
      };

      const { data: res } = await Api.queryChargeList(params);
      const { data = {} } = res;
      setRecordData(data.order || []);
    } catch (error) {
      console.log(error);
    }
  };

  const columns = [
    {
      title: '项目订单号',
      dataIndex: 'hisOrdNum',
    },
    {
      title: '项目名称',
      dataIndex: 'hisItemName',
    },
    {
      title: '单位',
      dataIndex: 'unit',
      // render: v => USER_STATUS_MAP[v]?.label,
    },
    {
      title: '单价（元）',
      dataIndex: 'price',
    },
    {
      title: '规格',
      dataIndex: 'speci',
    },
    {
      title: '折扣',
      dataIndex: 'discountAmt',
    },

    {
      title: '总金额',
      dataIndex: 'money',
    },
  ];

  return (
    <div className="g-page p-product-list">
      <div className="g-query-box">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="time" label="查询范围" style={{ marginBottom: 0 }}>
              <RangePicker defaultValue={range} onChange={v => setRange(v)} />
            </Form.Item>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <Space>
              <Button type="primary" onClick={fetchList}>
                查询
              </Button>
              {/* <Button onClick={reset} disabled={loading}>
                重置
              </Button> */}
            </Space>
          </Col>
        </Row>
      </div>
      <Space style={{ padding: '24px' }} direction="vertical">
        {recordData.length > 0 ? (
          recordData.map((item, index) => {
            return (
              <Card key={index}>
                <Descriptions column={4}>
                  <Descriptions.Item label="就诊时间">{item.regDate}</Descriptions.Item>
                  <Descriptions.Item label="总金额（元）">{item.totalFee}</Descriptions.Item>
                  <Descriptions.Item label="就诊科室">{item.deptName}</Descriptions.Item>
                  <Descriptions.Item label="就诊医生">{item.doctorName}</Descriptions.Item>
                  <Descriptions.Item label="姓名">{item.patName}</Descriptions.Item>
                  <Descriptions.Item label="身份证号码">{item.patIdNo}</Descriptions.Item>
                  <Descriptions.Item label="卡号">{item.patCardNo}</Descriptions.Item>
                </Descriptions>
                <Table rowKey="id" columns={columns} dataSource={item.item || []} scroll={{ x: 'max-content' }} pagination={false} />
              </Card>
            );
          })
        ) : (
          <Empty />
        )}
      </Space>
    </div>
  );
};

export default Index;
