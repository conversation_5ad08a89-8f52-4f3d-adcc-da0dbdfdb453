import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Select, 
  Space, 
  Card, 
  Modal, 
  message,
  Form,
  Row,
  Col,
  Input,
  Checkbox,
  Badge,
  Tooltip,
  DatePicker
} from 'antd';
import { SearchOutlined, DownloadOutlined, RedoOutlined } from '@ant-design/icons';
import moment from 'moment';
import { 
  getBillByPage, 
  execBill, 
  getBillDetail,
  exportBillDetail,
  updateBillStatus,
  getAllUser
} from './api';
// 在文件顶部添加导入
import { HZMS, HTLX, FKFS, JSFS, JSZQ } from '../contractmanage/_data';
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;  // 修复：正确导入Search组件
// DetailModal 组件修改如下
const DetailModal = ({ visible, onCancel, detailData, form, pagination, fetchBillData }) => {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalAmount, setTotalAmount] = useState(0);

  useEffect(() => {
    if (visible && detailData?.billNo) {
      setLoading(true);
      getBillDetail(detailData.billNo)
        .then(response => {
          if (response?.code === 0) {
            const detailList = response.data?.list || [];
            const formattedData = detailList.map((item, index) => ({
              ...item,
              key: index, // 使用索引作为key
              serialNumber: index + 1, // 添加序号字段
              submitHospitalName: item.submitHospital || '-',
              settlementAmount: item.settlementAmount || 0,
              check: item.check || '0'
            }));
            
            console.log('格式化后的详情数据:', formattedData);
            setDataSource(formattedData);
            
            const total = response.data?.totalAmount || 
              formattedData.reduce((sum, item) => sum + (item.settlementAmount || 0), 0);
            setTotalAmount(total);
          } else {
            message.error(response?.msg || '获取详情失败');
          }
        })
        .catch(err => {
          console.error('获取对账详情失败:', err);
          message.error('获取详情失败');
        })
        .finally(() => setLoading(false));
    }
  }, [visible, detailData]);

  const handleExportDetail = () => {
    if (!detailData?.billNo) {
      message.warning('缺少对账编号');
      return;
    }
    exportBillDetail(detailData.billNo)
      .then(() => message.success('导出任务已开始'))
      .catch(err => message.error('导出失败: ' + (err.message || err)));
  };

const handleMarkPayment = () => {
  Modal.confirm({
    title: '确认标记核款',
    content: '确定要将所有记录标记为已核款吗？',
    onOk: () => {
      const params = {
        id: detailData.id,
        check: '1'
      };

      return updateBillStatus(params)
        .then(response => {
          if (response?.code === 0) {
            message.success('标记核款成功');
            
            // 刷新详情数据
            getBillDetail(detailData.billNo)
              .then(res => {
                if (res?.code === 0) {
                  const detailList = res.data?.list || [];
                  const formattedData = detailList.map((item, index) => ({
                    ...item,
                    key: index,
                    serialNumber: index + 1,
                    submitHospitalName: item.submitHospital || '-',
                    settlementAmount: item.settlementAmount || 0
                  }));
                  setDataSource(formattedData);
                }
              });
            
            // 刷新主列表数据
            if (form && pagination && fetchBillData) {
              form.validateFields().then(values => {
                fetchBillData({
                  ...values,
                  pageNum: pagination.current,
                  numPerPage: pagination.pageSize,
                });
              });
            }
            
          } else {
            message.error(response?.msg || '标记核款失败');
          }
        })
        .catch(err => {
          console.error('标记核款失败:', err);
          message.error('标记核款失败');
        });
    }
  });
};

  const detailColumns = [
    {
      title: '序号',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 60,
      fixed: 'left'
    },
    {
      title: '产品线',
      dataIndex: 'productName',
      key: 'productName',
      width: 120,
      render: (text, record) => (
        <Tooltip title={`${text}${record.sonProductName ? `/${record.sonProductName}` : ''}`}>
          <span className="ellipsis">
            {text}{record.sonProductName ? `/${record.sonProductName}` : ''}
          </span>
        </Tooltip>
      )
    },
    {
      title: '样本编号',
      dataIndex: 'sampleNumber',
      key: 'sampleNumber',
      width: 120
    },
    {
      title: '受检者姓名',
      dataIndex: 'sjzName',
      key: 'sjzName',
      width: 100
    },
    {
      title: '采样医院',
      dataIndex: 'submitHospitalName',
      key: 'submitHospitalName',
      width: 150,
      render: text => text || '-'
    },
    {
      title: '采样时间',
      dataIndex: 'sampleTime',
      key: 'sampleTime',
      width: 150,
      render: text => text ? moment(text).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '样本类型',
      dataIndex: 'submitMaterial',
      key: 'submitMaterial',
      width: 120
    },
    {
      title: '应结算金额',
      dataIndex: 'settlementAmount',
      key: 'settlementAmount',
      width: 120,
      render: amount => amount ? `¥${amount.toFixed(2)}` : '-'
    },
    {
      title: '是否核款',
      dataIndex: 'check',
      key: 'check',
      width: 100,
      render: check => (
        <span style={{ 
          color: check === '1' ? '#52c41a' : '#ff4d4f',
          fontWeight: check === '1' ? 'bold' : 'normal'
        }}>
          {check === '1' ? '是' : '否'}
        </span>
      )
    }
  ];

  return (
     <Modal
      title={`对账详情 - ${detailData?.billNo || ''}`}
      visible={visible}
      onCancel={onCancel}
      footer={null}
      width={1300}
      bodyStyle={{ padding: 0 }}
      confirmLoading={loading}
    >
      <div style={{ 
        padding: '16px 24px', 
        borderBottom: '1px solid #f0f0f0', 
        display: 'flex', 
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ fontWeight: 'bold' }}>
          合计: ¥{totalAmount.toFixed(2)}
        </div>
        <Space>
          <Button 
            type="primary" 
            onClick={handleMarkPayment}
          >
            标记核款
          </Button>
          <Button 
            type="primary" 
            icon={<DownloadOutlined />}
            onClick={handleExportDetail}
          >
            导出详情
          </Button>
        </Space>
      </div>

      <div style={{ padding: '0 24px 24px' }}>
        <Table
          columns={detailColumns}
          dataSource={dataSource}
          bordered
          size="middle"
          scroll={{ x: 'max-content', y: 500 }}
          pagination={false}
          loading={loading}
          rowClassName={(record) => record.check === '1' ? 'row-checked' : ''}
        />
      </div>
    </Modal>
  );
};
const ReconciliationManagement = () => {
  const [form] = Form.useForm();
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailData, setDetailData] = useState(null);
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [salesPersons, setSalesPersons] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

const fetchSalesPersons = async () => {
  try {
    const response = await getAllUser({});
    if (response?.code === 0) {
      setSalesPersons(response.data || []);
    } else {
      message.error(response?.msg || '获取销售人员失败');
    }
  } catch (error) {
    console.error('获取销售人员失败:', error);
    message.error('获取销售人员失败');
  }
};
const fetchBillData = async (params = {}) => {
  setLoading(true);
  try {
    const requestParams = {
      month: params.month ? moment(params.month).format('YYYY-MM') : '',
      status: params.status || '',
      salesPersonnel: params.salesPersonnel || '',
      search: params.search || '', // 合同编号/合同名称查询参数
      pageNum: params.pageNum || 1,
      numPerPage: params.numPerPage || 10
    };
    
    const response = await getBillByPage(requestParams);
    
    if (response && response.code === 0) {
      const { recordList = [], totalCount = 0 } = response.data;
      setDataSource(recordList.map((item, index) => ({
        ...item,
        key: item.id || index,
        serialNumber: index + 1,
        billNo: `${item.billNo}`,
        assignedUnitNa: item.assignedUnitName,
        signingUnitNam: item.signingUnit,
        salesPersonnel: `${item.salesPersonnel}`,
        settlementAmou: item.settlementAmou || 0,
        billTime: item.status === 'S' ? item.updateTime : null,
      })));
      setPagination({
        ...pagination,
        current: requestParams.pageNum,
        pageSize: requestParams.numPerPage,
        total: totalCount,
      });
    } else {
      message.error(response?.msg || '获取数据失败');
    }
  } catch (error) {
    message.error('请求失败，请稍后重试');
    console.error('获取对账数据失败:', error);
  } finally {
    setLoading(false);
  }
};
  const handleSearch = () => {
    form.validateFields().then(values => {
      fetchBillData({
        ...values,
        pageNum: 1,
        numPerPage: pagination.pageSize,
      });
    });
  };

  const handleReset = () => {
    form.resetFields();
    form.setFieldsValue({
      month: undefined,
      status: '',
      salesPersonnel: '',
      search: ''
    });
    fetchBillData({
      month: '',
      status: '',
      salesPersonnel: '',
      search: '',
      pageNum: 1,
      numPerPage: 10,
    });
  };

  const handleTableChange = (pagination) => {
    form.validateFields().then(values => {
      fetchBillData({
        ...values,
        pageNum: pagination.current,
        numPerPage: pagination.pageSize,
      });
    });
    setPagination(pagination);
  };

  const handleExecBill = (id) => {
    Modal.confirm({
      title: '确认执行对账',
      content: '确定要执行对账操作吗？',
      onOk: () => {
        return execBill(id)
          .then(response => {
            if (response?.code === 0) {
              message.success('对账执行成功');
              handleSearch();
            } else {
              message.error(response?.msg || '对账执行失败');
            }
          })
          .catch(err => {
            console.error('执行对账失败:', err);
            message.error('对账执行失败');
          });
      }
    });
  };

  const handleUpdateStatus = (record, field, value) => {
    const params = {
      id: record.id,
      [field]: value
    };
    
    updateBillStatus(params)
      .then(response => {
        if (response?.code === 0) {
          message.success('状态更新成功');
          handleSearch();
        } else {
          message.error(response?.msg || '状态更新失败');
        }
      })
      .catch(err => {
        console.error('更新状态失败:', err);
        message.error('状态更新失败');
      });
  };

  const handleViewDetail = (record) => {
    setDetailData(record);
    setDetailVisible(true);
  };
  const handleConfirmInvoice = (record) => {
  Modal.confirm({
    title: '确认核开票',
    content: '确定要将此对账单标记为已核开票吗？',
    onOk: () => {
      return updateBillStatus({
        id: record.id,
        invoiceFlg: '1'
      })
      .then(response => {
        if (response?.code === 0) {
          message.success('核开票状态更新成功');
          handleSearch();
        } else {
          message.error(response?.msg || '状态更新失败');
        }
      })
      .catch(err => {
        console.error('更新状态失败:', err);
        message.error('状态更新失败');
      });
    }
  });
};

  const handleExportAll = () => {
    const values = form.getFieldsValue();
    const month = values.month ? moment(values.month).format('YYYY-MM') : moment().format('YYYY-MM');
    const params = {
      ...values,
      month,
      pageNum: 1,
      numPerPage: pagination.total
    };
    
    message.loading('正在准备导出数据...', 0);
    getBillByPage(params)
      .then(response => {
        if (response?.code === 0) {
          message.success('数据准备完成，开始导出');
          // 实际导出逻辑
          console.log('导出数据:', response.data.recordList);
        } else {
          message.error(response?.msg || '数据准备失败');
        }
      })
      .catch(() => {
        message.error('数据准备失败');
      })
      .finally(() => message.destroy());
  };

  useEffect(() => {

     fetchSalesPersons(); // 获取销售人员
  form.setFieldsValue({
    month: undefined,
    status: '',
    salesPersonnel: '',
    search: ''
  });

    fetchBillData({
      month:  '',
      pageNum: 1,
      numPerPage: 10,
    });
  }, []);

  const columns = [
    {
      title: '对账编号',
      dataIndex: 'billNo',
      key: 'billNo',
      width: 150,
      fixed: 'left'
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      key: 'contractNumber',
      width: 150
    },
    {
      title: '合同单位名称',
      dataIndex: 'assignedUnitNa',
      key: 'assignedUnitNa',
      width: 180,
      render: (text) => (
        <Tooltip title={text}>
          <span className="ellipsis">{text}</span>
        </Tooltip>
      )
    },
    {
      title: '签定单位编码',
      dataIndex: 'signingUnit',
      key: 'signingUnit',
      width: 180,
      render: (text) => (
        <Tooltip title={text}>
          <span className="ellipsis">{text}</span>
        </Tooltip>
      )
    },
    {
      title: '销售人员',
      dataIndex: 'salesPersonnel',
      key: 'salesPersonnel',
      width: 120
    },
      {
    title: '销售渠道',
    dataIndex: 'cooperationMode',
    key: 'cooperationMode',
    width: 120,
    render: mode => HZMS[mode] || mode
  },
  {
    title: '合同类型',
    dataIndex: 'contractType',
    key: 'contractType',
    width: 120,
    render: type => HTLX[type] || type
  },
  {
    title: '付款方式',
    dataIndex: 'payType',
    key: 'payType',
    width: 120,
    render: type => FKFS[type] || type
  },
  {
    title: '结算方式',
    dataIndex: 'settlementType',
    key: 'settlementType',
    width: 120,
    render: type => JSFS[type] || type
  },
  {
    title: '结算周期',
    dataIndex: 'payCycle',
    key: 'payCycle',
    width: 120,
    render: cycle => JSZQ[cycle] || cycle
  },
     {
      title: '对账月份',
      dataIndex: 'month',
      key: 'month',
      width: 120
    },
    {
      title: '结算金额',
      dataIndex: 'settlementAmou',
      key: 'settlementAmou',
      width: 120,
      render: amount => amount ? `¥${amount.toFixed(2)}` : '-'
    },
   {
  title: '对账状态',
  dataIndex: 'status',
  key: 'status',
  width: 100,
  render: status => (
    <span style={{ color: status === 'S' ? '#52c41a' : '#ff4d4f' }}>
      {status === 'S' ? '已对账' : '未对账'}
    </span>
  )
},
    {
      title: '对账执行时间',
      dataIndex: 'billTime',
      key: 'billTime',
      width: 150,
      render: text => text ? moment(text).format('YYYY-MM-DD HH:mm') : '-'
    },
 {
    title: '是否核款',
    dataIndex: 'check',
    key: 'check',
    width: 100,
    render: check => (
      <span style={{ color: check === '1' ? '#52c41a' : '#ff4d4f' }}>
        {check === '1' ? '是' : '否'}
      </span>
    )
  },
  {
    title: '是否开票',
    dataIndex: 'invoiceFlg',
    key: 'invoiceFlg',
    width: 100,
    render: flg => (
      <span style={{ color: flg === '1' ? '#52c41a' : '#ff4d4f' }}>
        {flg === '1' ? '是' : '否'}
      </span>
    )
  },
   {
  title: '操作',
  key: 'action',
  width: 200,
  fixed: 'right',
  render: (_, record) => (
    <Space direction="vertical" size={8}>
      <Space>
        <Button 
          type="link" 
          size="small"
          style={{ padding: 0 }}
          disabled={record.status === 'S'}
          onClick={() => handleExecBill(record.id)}
        >
          {record.status === 'S' ? '已执行对账' : '执行对账'}
        </Button>

        <Button 
          type="link" 
          size="small"
          style={{ padding: 0 }}
          disabled={record.invoiceFlg === '1'}
          onClick={() => handleConfirmInvoice(record)}
        >
          {record.invoiceFlg === '1' ? '已核开票' : '已核开票'}
        </Button>
      </Space>
      <Space>
        <Button 
          type="link" 
          size="small" 
          style={{ padding: 0 }}
          onClick={() => handleViewDetail(record)}
        >
          查看详情
        </Button>
        <Button 
          type="link" 
          size="small" 
          style={{ padding: 0 }}
          onClick={() => exportBillDetail(record.billNo)}
        >
          导出详情
        </Button>
      </Space>
    </Space> 
  ),
},
  ];

  return (
    <div className="g-page page-manage-sample-send">
      <Card bordered={false} style={{ marginBottom: 16, background: '#fff' }}>
        <Form form={form}>
          <Row gutter={16} style={{ marginTop: 0 }}>
            <Col span={8}>
              <Form.Item label="对账月份" name="month">
                <DatePicker 
                  picker="month" 
                  style={{ width: '100%' }}
                  disabledDate={(current) => current && current > moment().endOf('day')}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="对账状态" name="status">
                <Select style={{ width: '100%' }}>
                  <Option value="">全部</Option>
                  <Option value="U">未对账</Option>
                  <Option value="S">已对账</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
             <Form.Item label="销售人员" name="salesPersonnel">
      <Select 
        style={{ width: '100%' }} 
        showSearch
        optionFilterProp="children"
        filterOption={(input, option) =>
          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
        }
      >
        <Option value="">全部</Option>
        {salesPersons.map(person => (
          <Option key={person.id} value={person.id.toString()}>
            {person.name || `销售员${person.id}`}
          </Option>
        ))}
      </Select>
    </Form.Item>
            </Col>

            <Col>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  查询
                </Button>
                <Button icon={<RedoOutlined />} onClick={handleReset}>
                  重置
                </Button>
               
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

      <div style={{ background: '#fff', padding: 16, margin:16, borderRadius: 2, boxShadow: '0 1px 2px 0 rgba(0,0,0,0.03)' }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
           <Search
  placeholder="请输入合同编号/合同名称"
  allowClear
  enterButton="搜索"
  style={{ width: '100%', maxWidth: 500, marginBottom: 16 }}
  onSearch={(value) => {
    form.setFieldsValue({ search: value });
    fetchBillData({
      ...form.getFieldsValue(),
      search: value,
      pageNum: 1,
      numPerPage: pagination.pageSize,
    });
  }}
/>
          </Col>

          <Col flex="right">
           <Button 
                  type="primary" 
                  icon={<DownloadOutlined />}
                  onClick={handleExportAll}
                >
                  导出全部
                </Button>
          </Col>
        </Row>
        
        <Table
          columns={columns}
          dataSource={dataSource}
          bordered
          size="middle"
          scroll={{ x: 1800 }}
          loading={loading}
          pagination={{
            ...pagination,
            position: ['bottomRight'],
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50'],
            showTotal: total => `共 ${total} 条`,
            showQuickJumper: true
          }}
          onChange={handleTableChange}
          style={{ margin: 0 }}
        />
      </div>

   <DetailModal 
  visible={detailVisible} 
  onCancel={() => setDetailVisible(false)}
  detailData={detailData}
  form={form}
  pagination={pagination}
  fetchBillData={fetchBillData}
/>
    </div>
  );
};

export default ReconciliationManagement;