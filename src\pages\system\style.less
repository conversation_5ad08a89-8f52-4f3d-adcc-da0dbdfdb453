@charset "utf-8";
@import '../../resources/styles/mixins';

.custom-ewm-popover {
  .ant-popover-inner-content {
    padding: 0;
  }
  .custom-ewm-popover-content {
    padding: 12px 16px;
    > div {
      cursor: pointer;
      &:hover {
        color: #3f969d;
      }
      &:not(:last-child) {
        margin-bottom: 8px;
      }
    }
  }
}
.page-account-list {
  display: flex;
  flex-direction: column;
  flex: auto;

  .option-panel {
    padding: 12px 24px;
    background: #fff;
  }

  .account-list {
    flex: 1;
    margin: 24px;
    padding: 16px 21px;
    border-radius: 4px;
    background: #fff;
  }
}

.page-account-edit {
  display: flex;
  padding: 20px 20px 126px 20px;
  flex: auto;
  color: @title-color;
  font-size: 12px;
  border-radius: 4px;
  margin: 24px;
  background: #fff;
  justify-content: center;

  .edit-panel {
    width: 943px;

    .edit-board {
      margin-top: 30px;

      .edit-tuber {
        padding: 36px 0 10px 0;
        border-radius: 4px;
        background: #f9f9f9;
      }

      .edit-title {
        font-weight: 500;
        margin-bottom: 5px;
        font-size: 14px;
      }

      .edit-permission-table {
        border: 1px solid @border-color;
        border-radius: 4px;

        > .permission-table-node:first-child {
          border-right: 0;
        }

        .permission-table-node {
          display: flex;
          align-items: center;
          padding: 18.5px 20px;
          min-width: 156px;
          border-right: 1px solid @border-color;
          border-bottom: 1px solid @border-color;
        }

        .permission-table-cell {
          display: flex;
          flex: auto;
          border-bottom: 1px solid @border-color;

          &:last-child {
            border-bottom: 0;
          }

          .permission-table-board {
            display: flex;
            flex: auto;
            flex-wrap: wrap;

            > .permission-table-cell {
              width: 100%;
              border-right: 0;

              &:last-child {
                border: 0;
              }
            }

            > .permission-table-node {
              border: 0;
            }
          }

          > .permission-table-node {
            border-bottom: 0;
          }
        }
      }
    }
  }

  .m-cover {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    opacity: 0;
  }
}

.page-modify-password {
  flex: auto;
  margin: 24px;
  border-radius: 4px;
  background: #fff;
}
