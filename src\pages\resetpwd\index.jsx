import React from 'react';
import { connect } from 'dva';
import { Input, Button } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import * as utils from '../../utils/utils';
import './style.less';

import loginLogo from '../../resources/images/common/login-logo.png';

const FormItem = Form.Item;

function Resetpwd({ dispatch, data: loginData, captcha, form, location }) {
  const { getFieldDecorator, getFieldsValue, getFieldValue, validateFields } = form;

  function changeCaptcha() {
    dispatch({
      type: 'resetPwd/changeCaptcha',
    });
  }

  function resetPwd(oldPwd, newPwd, validateCode) {
    dispatch({
      type: 'resetPwd/ressetpassword',
      payload: { oldPwd, newPwd, validateCode },
    });
  }

  function handleSubmit(e) {
    e.preventDefault();
    form.validateFields((err, values) => {
      if (!err) {
        resetPwd(values.oldPwd, values.newPwd, values.validateCode);
      }
    });
  }

  function validateHandler(rule, value, callback) {
    const { field } = rule;
    switch (field) {
      case 'newPwd':
        if (!value || value == '') {
          callback('请输入新密码!');
        } else if (!/^(?=.*?[A-Za-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,18}$/.test(value)) {
          callback('长度为8-18位字符,需包含数字+字母+符号，空格除外!');
        } else if (value == getFieldValue('oldPwd')) {
          callback('新密码和原密码不能相同!');
        } else {
          callback();
        }
        break;
      case 'confirmNewPwd':
        if (!value || value == '') {
          callback('请再次输入新密码!');
        } else if (!/^(?=.*?[A-Za-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,18}$/.test(value)) {
          callback('长度为8-18位字符,需包含数字+字母+符号，空格除外!');
        } else if (value != getFieldValue('newPwd')) {
          callback('密码不一致!');
        } else {
          callback();
        }
        break;
      default:
        callback();
        break;
    }
  }

  function resetText() {
    const resetCode = utils.queryStringToJson(location.search).resetCode;
    let resetText = '';
    switch (resetCode) {
      case '1':
        resetText = (
          <span>
            <Icon type="exclamation-circle-o" style={{ marginRight: 5 }} />
            首次登录，请修改密码！
          </span>
        );
        break;
      case '2':
        resetText = (
          <span>
            <Icon type="exclamation-circle-o" style={{ marginRight: 5 }} />
            当前密码不符合规范，格式须为8-18位字符，包含数字&字母&符号（空格除外），请修改密码！
          </span>
        );
        break;
      case '3':
        resetText = (
          <span>
            <Icon type="exclamation-circle-o" style={{ marginRight: 5 }} />
            当前密码已过期，请修改密码！
          </span>
        );
        break;
      default:
        resetText = '';
        break;
    }
    return resetText;
  }

  function modifyPassword() {
    // console.log('modifyPassword');
  }

  return (
    <div className="page-resetPwd">
      <div className="login-body">
        <div className="login-form">
          <div className="login-form-box">
            <div className="login-form-header">
              <img src={loginLogo} alt="" />
              <div className="head-text">互联网医院后台管理系统</div>
            </div>
            <div style={{ padding: '20px 0', width: 319 }}>
              <p style={{ fontSize: 13, color: '#f04134' }}>{resetText()}</p>
            </div>
            <Form>
              <FormItem>
                {getFieldDecorator('oldPwd', {
                  rules: [{ required: true, whitespace: true, message: '请输入原密码' }],
                })(<Input className="login-input" placeholder="请输入原密码" type="password" />)}
              </FormItem>
              <FormItem>
                {getFieldDecorator('newPwd', {
                  rules: [{ required: true, whitespace: true, validator: validateHandler }],
                })(<Input className="login-input" placeholder="请输入新密码" type="password" />)}
              </FormItem>
              <FormItem>
                {getFieldDecorator('confirmNewPwd', {
                  rules: [{ required: true, whitespace: true, validator: validateHandler }],
                })(<Input className="login-input" type="password" placeholder="请再次输入新密码" />)}
              </FormItem>
              <FormItem>
                <div style={{ display: 'flex' }}>
                  {getFieldDecorator('validateCode', {
                    rules: [{ required: true, message: '请输入验证码!' }],
                  })(<Input className="login-input" placeholder="请输入右侧验证码" />)}
                  <img alt="" src={captcha} onClick={changeCaptcha} style={{ arginLeft: 16, height: 44, width: 110 }} />
                </div>
              </FormItem>
              <Button htmlType="submit" className="login-button" ghost onClick={handleSubmit}>
                确认修改
              </Button>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
}

export default connect(state => state.resetPwd)(Form.create()(Resetpwd));
