@charset "utf-8";
@import '../../../../resources/styles/mixins';

.sort-article-type-card {
  width: 372px;
  height: 130px;
  margin: 12px 12px;
  border: 1px solid @border-color;
  border-radius: 4px;
  cursor: move;

  &:hover {
    color: @primary-5;
    border: 1px solid @primary-5;
  }

  .card-content {
    height: 90px;
    padding: 21px 23px;
    border-bottom: 1px solid @border-color;
  }

  .card-bottom {
    height: 39px;
    padding: 11px 21px;
    text-align: right;
  }

  .card-title {
    font-size: 16px;
    font-weight: 500;
    color: @title-color;
  }

  .card-desc {
    color: @desc-color;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
