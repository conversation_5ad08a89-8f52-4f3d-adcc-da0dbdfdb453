import React, { useRef } from 'react';
import { Input, message, Modal, Result } from 'antd';
import { connect } from 'dva';
import * as Api from './api';

class Index extends React.Component {
  constructor(props) {
    super(props);
    const {
      location: { query = {} },
    } = this.props;
    this.query = query;
    this.state = {
      passwd: this.query.passwd || '',
      verificationCode: '',
      visible: true,
    };

    this.inputRef = React.createRef();

    // 监听hash变化，刷新页面
    window.onhashchange = () => {
      location.reload();
    };
  }

  //组件挂载
  componentDidMount() {
    window.document.title = '下载报告';
    if (this.inputRef.current) {
      this.inputRef.current.focus({
        cursor: 'start',
      });
    }
  }

  //处理取消
  handleCandel = () => {
    this.setState({ visible: false });
  };

  //处理下载
  handleOk = async () => {
    const { verificationCode } = this.state;
    if (!verificationCode) {
      message.error('请输入下载验证码');
      return;
    }
    const params = {
      passwd: this.state.passwd,
      verificationCode,
    };
    const res = await Api.downloadReport(params);
    if (res.data.code === 0) {
      this.setState({ visible: false });
      message.success('下载成功');
    } else {
      message.error(res.data.msg || '下载失败');
    }
  };

  //处理输入验证码
  handleInput = e => {
    const { value } = e.target;
    this.setState({ verificationCode: value });
  };

  //渲染
  render() {
    const { passwd, visible } = this.state;
    return (
      <div className="g-page">
        <div className="container">
          {passwd ? (
            <Modal title="下载文件" open={visible} destroyOnClose={true} onOk={this.handleOk} onCancel={this.handleCandel}>
              <Input ref={this.inputRef} style={{ width: '100%' }} placeholder="请输入下载验证码" onInput={this.handleInput} />
            </Modal>
          ) : (
            <Result status="404" title="404" subTitle="抱歉，当前文件不存在！" />
          )}
        </div>
      </div>
    );
  }
}

export default connect()(Index);
