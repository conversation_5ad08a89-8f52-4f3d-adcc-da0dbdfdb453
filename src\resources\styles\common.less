@charset "utf-8";
@import 'mixins';

html,
body,
#root {
  min-width: 1340px;
  height: 100vh;
  background: #fff;
}

* {
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;
  margin-block-start: 0;
  margin-block-end: 0;
}
p {
  margin: 0;
}

a {
  color: #3f969d;
}
pre {
  font-family: inherit;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  margin-bottom: 0;
}

.f-clear:after,
.clearfix:after {
  content: ' ';
  visibility: hidden;
  display: block;
  font-size: 0;
  clear: both;
  height: 0;
}

.f-fl {
  float: left;
}

.f-fr {
  float: right;
}

.f-tac {
  text-align: center;
}

.f-none {
  display: none;
}
.f-toe {
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
}

th.column-right,
td.column-right {
  text-align: right !important;
}

th.column-center,
td.column-center {
  text-align: center !important;
}

.table-header-split {
  margin: 0 5px;
}

.merchant-pet-panel {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;

  .merchant-pet {
    width: 145px;
    height: 145px;
    background-image: url(../images/common/pet.png);
    background-repeat: no-repeat;
    background-size: 100%;
  }
}

.phone {
  width: 375px;
  height: 667px;
  font-size: 14px;
  box-shadow: 0 7px 17px 0 rgba(190, 190, 190, 0.5);

  .phone-head {
    height: 64px;
    color: #fff;
    font-size: 16px;
    text-align: center;
    padding-top: 30px;
    background-size: cover;
    background-image: url(../images/common/phone.png);
    background-repeat: no-repeat;
  }

  .phone-body {
    overflow-x: hidden;
    overflow-y: scroll;
    position: relative;
    height: 603px;

    .phone-list {
      display: flex;
      height: 54px;
      margin: 0 15px;
      border-bottom: 1px solid @border-color;

      .list-left,
      .list-right {
        margin: 8px 0;
        overflow: hidden;
        max-width: 250px;
        display: flex;
        flex: auto;
        flex-direction: column;
        justify-content: center;
      }

      .list-right {
        text-align: right;
        color: #4db6ac;
      }
    }

    .phone-panel {
      margin: 16px 15px 0;
      text-align: justify;
      word-wrap: break-word;
    }
  }
}

.page-usermng {
  .default-userheader {
    display: block;
    border-radius: 4px;
    width: 42px;
    height: 42px;
    background-image: url(../images/common/userHeader.png);
    background-size: 100% 100%;
  }
}

// drawer
#drawer {
  .drawer-header {
    padding: 0 25px;
    height: 54px;
    line-height: 54px;
    font-size: 16px;
    border-bottom: 1px #e9e9e9 solid;

    .close-icon {
      float: right;
      cursor: pointer;
    }
  }
  .drawer-main {
    padding: 0 25px;
  }
}
.f-wrap {
  word-wrap: break-word;
  word-break: normal;
}
