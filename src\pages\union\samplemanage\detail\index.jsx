/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useRef } from 'react';
import { connect, history } from 'umi';
import { Collapse, Form, Row, Col, Input, InputNumber, Select, DatePicker, Cascader, Button, message, Empty, Image, Modal, Upload } from 'antd';
import { PlusOutlined, ZoomInOutlined, ZoomOutOutlined, RotateRightOutlined, RotateLeftOutlined, LeftOutlined, RightOutlined, ReloadOutlined, CloseCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import dayjs from 'dayjs';
import queryString from 'query-string';
import * as CONSTANT from '@/config/constant/constant';
import { validateIdNo } from '@/utils/utils';
import { provinceData } from '../../province';
import { SJCL, JCFZYP, NATION } from '../_data';
import { HTLX } from '../../contractmanage/_data';
import CNV from './components/cnv';
import JHCAH from './components/jhcah';
import JHDP from './components/jhdp';
import JHYS from './components/jhys';
import NIPT from './components/nipt';
import PLUS from './components/plus';
import PE from './components/pe';
import JSXJWSZ from './components/jsxjwsz';
import FXS from './components/fxs';
import SMA from './components/sma';
import QWXZZCX from './components/qwxzzcx';
import DJYJWDJC from './components/djyjwdjc';
import RSTFISH from './components/rstfish';
import YCXER from './components/ycxer';
import PANEL from './components/panel';
import XNXGJKPG from './components/XNXGJKPG';
import XB from './components/xb';
import KZMZBZY from './components/KZMZBZY';
import { getinfobysampling as checksampling } from '../api';
// 在组件中使用
import './index.less';
import FormItem from 'antd/es/form/FormItem';

const { Option } = Select;
const { TextArea } = Input;
const { Search } = Input;
let sampleImgInterval = null;
let moveTimer = null; // 自定义图片增加定时器

const Index = props => {
  const { location, dispatch, institutionList = [], institutionContract = [], contractproducts = [], contractsubproducts = [], detail = {}, btns = {} } = props;
  const { id = '', menuType } = queryString.parse(location.search);
  const [form] = Form.useForm();

  const sampleNumber = Form.useWatch('sampleNumber', form); // 采样管编号
  const templateType = Form.useWatch('informedConsentCode', form); // 同意书模板类型
  const sjzIdNum = Form.useWatch('sjzIdNum', form);
  const productIdValue = Form.useWatch('productId', form);
  const payStatus = Form.useWatch('payStatus', form);

  const [cityOption, setCityOption] = useState([]); // 城市options
  const [canEdit, setCanEdit] = useState(!!!id);
  const [qrCode, setQrcode] = useState({});
  const [fileList, setFileList] = useState([]);
  const [qrCodeModal, setQrcodeModal] = useState(false);
  const [samplingInfo, setSamplingInfo] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const rightBox = useRef(null);
  /**  自定义图片预览 start **/
  const [preZoom, setPreZoom] = useState(1);
  const [preRotate, setPreRotate] = useState(0);
  const [preMoveX, setPreMoveX] = useState(0);
  const [preMoveY, setPreMoveY] = useState(0);
  const [defaultDownPosition, setDefaultDownPosition] = useState({ x: 0, y: 0 });
  const [tempPosition, setTempPosition] = useState({ x: 0, y: 0 });
  const [isMoving, setIsMoving] = useState(false);
  const [currentImg, setCurrentImg] = useState(0);

  const resetPre = () => {
    setPreZoom(1);
    setPreRotate(0);
    setPreMoveX(0);
    setPreMoveY(0);
    setDefaultDownPosition({ x: 0, y: 0 });
    setTempPosition({ x: 0, y: 0 });
    setIsMoving(false);
  };

  const zoomImg = n => {
    let newZoom = preZoom + n * 0.1;
    if (newZoom <= 1) {
      newZoom = 1;
    }
    setPreZoom(newZoom);
  };

  const rotateImg = rotateAng => {
    const newRotate = preRotate + rotateAng;
    setPreRotate(newRotate);
  };

  const wheelImg = e => {
    if (e.nativeEvent.deltaY > 0) {
      zoomImg(-1);
    } else {
      zoomImg(1);
    }
  };

  const onMouseDown = e => {
    e.stopPropagation();
    e.preventDefault();
    defaultDownPosition.x = e.clientX;
    defaultDownPosition.y = e.clientY;
    tempPosition.x = preMoveX;
    tempPosition.y = preMoveY;
    setDefaultDownPosition(defaultDownPosition);
    setTempPosition(tempPosition);
    setIsMoving(true);
  };

  const onMouseMove = e => {
    e.stopPropagation();
    if (isMoving) {
      const preMoveX = e.clientX - defaultDownPosition.x + tempPosition.x;
      const preMoveY = e.clientY - defaultDownPosition.y + tempPosition.y;
      setPreMoveX(preMoveX);
      setPreMoveY(preMoveY);
    }
  };

  const onMouseUpOrOut = e => {
    e.stopPropagation();
    e.preventDefault();
    moveTimer = setTimeout(() => {
      clearTimeout(moveTimer);
      setIsMoving(false);
    }, 50);
  };
  /**  自定义图片预览 end **/

  const saveDetail = (values, noClean) => {
    if (values.hasOwnProperty('institutionId') && !noClean) {
      values.contractId = null;
      values.productId = null;
      values.sonProductId = null;
      values.informedConsentCode = null;
    }
    if (values.hasOwnProperty('contractId') && !noClean) {
      values.productId = null;
      values.sonProductId = null;
      values.informedConsentCode = null;
    }
    if (values.hasOwnProperty('productId') && !noClean) {
      values.sonProductId = null;
    }
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, ...values },
      },
    });
  };

  const getcontractbyinstitution = id => {
    dispatch({ type: 'sample/getcontractbyinstitution', payload: { id } });
  };
  const getproducts = (contractId, productId) => {
    dispatch({ type: 'sample/getproductsbycontract', payload: { contractId, productId } });
  };

  const getinfobysampling = () => {
    if (!canEdit || (!!id && detail?.extfiled1 != 2)) return;
    if (sampleNumber) {
      getQrcode(sampleNumber);
      dispatch({
        type: 'sample/checksample',
        payload: { id, sampleNumber: sampleNumber },
      }).then(res => {
        if (res) {
          dispatch({
            type: 'sample/getinfobysampling',
            payload: { samplingNumber: sampleNumber },
          }).then(res => {
            if (res) {
              const { institutionId, contractId, productId } = res;
              const params = { institutionId, contractId: null, productId: null, sonProductId: null };
              dispatch({ type: 'sample/getcontractbyinstitution', payload: { id: institutionId } }).then(res => {
                if (res.filter(v => v.id == contractId)?.length > 0) {
                  params.contractId = contractId;
                }
                dispatch({ type: 'sample/getproductsbycontract', payload: { contractId } }).then(res => {
                  const filterResult = res.filter(v => v.id == productId);
                  if (filterResult?.length > 0) {
                    params.productId = productId;
                    params.informedConsentCode = filterResult[0].templateType;
                  }
                  setSamplingInfo(params);
                  saveDetail(params, true);
                  form.setFieldsValue(params);
                });
              });
            } else {
              setSamplingInfo({});
              saveDetail({ institutionId: null, contractId: null, productId: null, sonProductId: null }, true);
              form.setFieldsValue({ institutionId: null, contractId: null, productId: null, sonProductId: null });
              dispatch({
                type: 'sample/save',
                payload: {
                  institutionContract: [], // 客户有效合同
                  contractproducts: [], // 合同内产品线
                  contractsubproducts: [], // 合同内子产品
                },
              });
            }
          });
        }
      });
    }
  };

  const getQrcode = (no = sampleNumber) => {
    dispatch({
      type: 'sample/getQrcode',
      payload: { platformId: 242, platformSource: 3, subSource: 2, gotoUrl: 'pages/sample-upload/index', scene: no },
    }).then(res => {
      if (res) {
        setQrcode({ sampleNumber: no, url: res });
      }
      getImgListInterval(no);
    });
  };

  const getImgListInterval = no => {
    if (sampleImgInterval) {
      clearInterval(sampleImgInterval);
      sampleImgInterval = null;
    }
    getImgList(no);
    sampleImgInterval = setInterval(() => getImgList(no), 5000);
  };
  const getImgList = no => {
    dispatch({
      type: 'sample/getImgList',
      payload: { sampleNumber: no },
    }).then(res => {
      if (res) {
        setFileList(res.replace(/;/g, ',').split(','));
      }
    });
  };

  const renderZQTYS = key => {
    switch (key) {
      case 'CNV':
        return <CNV />;
      case 'JHCAH':
        return <JHCAH />;
      case 'JHDPSDDP':
        return <JHDP />;
      case 'JHYSYS':
        return <JHYS />;
      case 'NIPTJHBB':
        return <NIPT />;
      case 'PLUSJHBB':
        return <PLUS />;
      case 'PEJHBB':
        return <PE />;
      case 'JSXJWSZ':
        return <JSXJWSZ />;
      case 'FXS':
        return <FXS />;
      case 'SMA':
        return <SMA />;
      case 'QWXZZCX':
        return <QWXZZCX />;
      case 'DJYJWDJC':
        return <DJYJWDJC />;
      case 'RSTFISH':
        return <RSTFISH />;
      case 'YCXER':
        return <YCXER />;
      case 'PANEL':
        return <PANEL />;
      case 'XNXGJKPG':
        return <XNXGJKPG />;
      case 'XB':
        return <XB />;
      case 'KZMZBZY':
        return <KZMZBZY />;
      default:
        return null;
    }
  };

  useEffect(() => {
    if (menuType === 'sub') {
      dispatch({ type: 'sample/getinstitutions' });
    } else {
      dispatch({ type: 'sample/getinstitutionbylist', payload: { menuType } });
    }
    return () => {
      if (sampleImgInterval) {
        clearInterval(sampleImgInterval);
        sampleImgInterval = null;
      }
    };
  }, []);
  useEffect(() => {
    if (payStatus !== '已支付') {
      form.setFieldsValue({ payDate: null });
    }
  }, [payStatus]);
  useEffect(() => {
    if (detail.sjzProvinceId) {
      const newCityOption = provinceData.filter(province => province.label == detail.sjzProvinceId)[0]?.children;
      setCityOption(newCityOption || []);
    }
  }, [detail.sjzProvinceId]);
  useEffect(() => {
    if (detail.institutionId) {
      getcontractbyinstitution(detail.institutionId);
    }
  }, [detail.institutionId]);
  useEffect(() => {
    if (detail.contractId) {
      getproducts(detail.contractId);
    }
  }, [detail.contractId]);
  useEffect(() => {
    if (detail.contractId) {
      getproducts(detail.contractId, detail.productId);
    }
  }, [detail.productId]);
  useEffect(() => {
    if (id) {
      dispatch({
        type: 'sample/getDetail',
        payload: { id },
      }).then(res => {
        if (res) {
          form.resetFields();
          form.setFieldsValue(res);
          if (!res.contractId) {
            if (res.productName) {
              form.setFieldsValue({ productId: res.productName });
            }
            if (res.sonProductName) {
              form.setFieldsValue({ sonProductId: res.sonProductName });
            }
          }
          getQrcode(res.sampleNumber);
        }
      });
    }
  }, [id]);
  useEffect(() => {
    setSamplingInfo({});
    if (qrCode.sampleNumber && qrCode.sampleNumber !== sampleNumber) {
      if (sampleImgInterval) {
        clearInterval(sampleImgInterval);
        sampleImgInterval = null;
      }
      setFileList([]);
      setQrcode({});
    }
  }, [sampleNumber]);

  useEffect(() => {
    if (sjzIdNum) {
      form
        .validateFields(['sjzIdNum', 'sjzAge'])
        .then(res => {
          const idNumber = res.sjzIdNum;
          if (idNumber && !res.sjzAge) {
            const birthday = dayjs(idNumber.substring(6, 14), 'YYYYMMDD');
            const age = dayjs().diff(birthday, 'year');
            form.setFieldValue('sjzAge', age);
          }
        })
        .catch(error => {
          console.error(error);
        });
    }
  }, [sjzIdNum]);

  const submit = isContinue => {
    form
      .validateFields()
      .then(values => {
        if (!values.upd) values.upd = '空';
        if (id) {
          submitHandle(values, isContinue);
          return;
        }
        console.log('values', values);
        dispatch({
          type: 'sample/checksample',
          payload: { id, sampleNumber: values.sampleNumber },
        }).then(res => {
          if (res) {
            submitHandle(values, isContinue);
          }
        });
      })
      .catch(errorInfo => {
        console.log('errorInfo', errorInfo);
        try {
          if (errorInfo.errorFields?.length > 0) {
            if (errorInfo.errorFields[0].errors?.length > 0) {
              message.error(errorInfo.errorFields[0].errors[0]);
            }
          }
        } catch (error) {}
      });
  };

  const submitHandle = (values, isContinue) => {
    if (values.applyTime) {
      values.applyTime = values.applyTime.format('YYYY-MM-DD');
    }
    if (values.sampleTime) {
      values.sampleTime = values.sampleTime.format('YYYY-MM-DD');
    }
    if (values.extfiled8) {
      values.extfiled8 = values.extfiled8.format('YYYY-MM-DD HH:mm');
    }
    if (values.extfiled9) {
      values.extfiled9 = values.extfiled9.format('YYYY-MM-DD');
    }
    if (values.payDate) {
      values.payDate = values.payDate.format('YYYY-MM-DD');
    }
    if (!values.payStatus) {
      values.payStatus = '未知';
    }
    if (values.sjzNative) {
      values.sjzNativeProvinceId = values.sjzNative[0];
      values.sjzNativeCityId = values.sjzNative[1];
      values.sjzNativeAreaId = values.sjzNative[2];
      delete values.sjzNative;
    }
    if (values.birth) {
      values.birth = values.birth.format('YYYY-MM-DD');
    }
    const {
      reportPath = '',
      reportName = '',
      cnvTemplate = {},
      jhcahTemplate = {},
      jhdpsddpTemplate = {},
      jhysysTemplate = {},
      niptjhbbTemplate = {},
      plusjhbbTemplate = {},
      pejhbbTemplate = {},
      jsxjwsztemplate = {},
      fxstemplate = {},
      smatemplate = {},
      qwxzzcxtemplate = {},
      djyjwdjctemplate = {},
      rstfishtemplate = {},
      ycxertemplate = {},
      paneltemplate = {},
      xnxgjkpgtemplate = {},
      xbtemplate = {},
      kzmzbzytemplate = {},
    } = detail;
    switch (values.informedConsentCode) {
      case 'CNV':
        values.cnvTemplateJson = JSON.stringify(cnvTemplate);
        break;
      case 'JHCAH':
        values.jhcahTemplateJson = JSON.stringify(jhcahTemplate);
        break;
      case 'JHDPSDDP':
        values.jhdpsddpTemplateJson = JSON.stringify(jhdpsddpTemplate);
        break;
      case 'JHYSYS':
        values.jhysysTemplateJson = JSON.stringify(jhysysTemplate);
        break;
      case 'NIPTJHBB':
        values.niptjhbbTemplateJson = JSON.stringify(niptjhbbTemplate);
        break;
      case 'PLUSJHBB':
        values.plusjhbbTemplateJson = JSON.stringify(plusjhbbTemplate);
        break;
      case 'PEJHBB':
        values.pejhbbTemplateJson = JSON.stringify(pejhbbTemplate);
        break;
      case 'JSXJWSZ':
        values.jsxjwsztemplateJson = JSON.stringify(jsxjwsztemplate);
        break;
      case 'FXS':
        values.fxstemplateJson = JSON.stringify(fxstemplate);
        break;
      case 'SMA':
        values.smatemplateJson = JSON.stringify(smatemplate);
        break;
      case 'QWXZZCX':
        values.qwxzzcxtemplateJson = JSON.stringify(qwxzzcxtemplate);
        break;
      case 'DJYJWDJC':
        values.djyjwdjctemplateJson = JSON.stringify(djyjwdjctemplate);
        break;
      case 'RSTFISH':
        values.rstfishtemplateJson = JSON.stringify(rstfishtemplate);
        break;
      case 'YCXER':
        values.ycxertemplateJson = JSON.stringify(ycxertemplate);
        break;
      case 'PANEL':
        values.paneltemplateJson = JSON.stringify(paneltemplate);
        break;
      case 'XNXGJKPG':
        values.xnxgjkpgtemplateJson = JSON.stringify(xnxgjkpgtemplate);
        break;
      case 'XB':
        values.xbtemplateJson = JSON.stringify(xbtemplate);
      case 'KZMZBZY':
        values.kzmzbzytemplateJson = JSON.stringify(kzmzbzytemplate);
        break;
      default:
        break;
    }
    values.files = fileList.join(',');
    if (id) {
      values.id = id;
    }

    values.reportPath = reportPath;
    values.reportName = reportName;

    dispatch({
      type: id ? 'sample/updatesample' : 'sample/addsample',
      payload: values,
    }).then(res => {
      if (res) {
        if (sampleImgInterval) {
          clearInterval(sampleImgInterval);
          sampleImgInterval = null;
        }
        message.success('操作成功！', 1, () => {
          if (isContinue && !id) {
            dispatch({
              type: 'sample/save',
              payload: { detail: {} },
            });
            setTimeout(() => {
              form.resetFields();
              if (rightBox.current) {
                rightBox.current.scrollTo(0, 0);
              }
            }, 0);
          } else {
            setCanEdit(false);
            history.goBack();
          }
        });
      }
    });
  };

  const onSearch = keyword => {
    if (!keyword) return;
    dispatch({
      type: 'sample/getsamplebypageOfNo',
      payload: { keyword },
    }).then(res => {
      if (res.recordList?.length > 0) {
        history.replace({
          pathname: '/union/samplemanage/detail',
          search: queryString.stringify({ id: res.recordList[0].id, menuType }),
        });
      } else {
        message.warning('无该编号的样本，请重新核对');
      }
    });
  };

  const beforeUpload = file => {
    const size = file.size / 1024 / 1024 <= 50;
    if (!size) {
      message.error('文件大小不能超过50MB!');
    }
    return size;
  };
  const uploadOnChange = e => {
    if (!isLoading && e.file.status === 'uploading') {
      message.loading('加载中', 0);
      setIsLoading(true);
    }
    const { response } = e.file;
    if (response) {
      message.destroy();
      setIsLoading(false);
      let img = '';
      if (response.code != 0) {
        message.error('上传文件失败');
        return false;
      } else {
        name = (response.data || {}).fileName;
        img = (response.data || {}).url;
        saveDetail({ reportPath: img, reportName: name });
      }
    }
  };
  const deleteImg = () => {
    saveDetail({ reportPath: '', reportName: '' });
  };

  return (
    <Form
      className="g-page page-samplemanage-detail"
      layout="vertical"
      form={form}
      scrollToFirstError
      onValuesChange={changedValues => saveDetail(changedValues)}
      initialValues={{ ...detail, extfiled9: detail.extfiled9 || moment() }}
      disabled={!canEdit}
    >
      <div className="top-box">
        <Search placeholder="请输入样本编号搜索" style={{ width: 320 }} allowClear disabled={false} onSearch={onSearch} />
        {id ? (
          <Button
            type="link"
            style={{ textAlign: 'right', padding: '0 24px' }}
            disabled={false}
            onClick={() => history.push(`/union/changerecord?moduleCode=com.anyi.his.vo.SampleRespVo&moduleId=${id}`)}
          >
            变更记录
          </Button>
        ) : null}
      </div>
      <div className="container">
        {/* <div className="left">
          {qrCode.url ? (
            <div className="samplemanage-detail-qrcode-box">
              <img src={qrCode.url} alt="" />
              <div>样本编号：{sampleNumber}</div>
              <div>微信扫描二维码上传同意书</div>
            </div>
          ) : (
            <Empty description="点击检索样本编号生成上传二维码" />
          )}
          <div id="imgPreview" style={{ display: fileList?.length > 0 ? 'block' : 'none' }}>
            <Image.PreviewGroup
              preview={{
                visible: true,
                onVisibleChange: () => setQrcodeModal(true),
                getContainer: document.getElementById('imgPreview'),
              }}
            >
              {fileList.map((v, i) => (
                <Image src={v} key={i} />
              ))}
            </Image.PreviewGroup>
          </div>
          <Modal title="样本图片上传" open={qrCodeModal} onCancel={() => setQrcodeModal(false)} footer={null}>
            <div className="samplemanage-detail-qrcode-box">
              <img src={qrCode.url} alt="" />
              <div>样本编号：{sampleNumber}</div>
              <div>微信扫描二维码上传同意书</div>
            </div>
          </Modal>
        </div> */}
        <div className="left">
          {qrCode.url ? (
            <div className="samplemanage-detail-qrcode-box">
              <img src={qrCode.url} alt="" />
              <div>样本编号：{sampleNumber}</div>
              <div>微信扫描二维码上传同意书</div>
            </div>
          ) : (
            <Empty description="点击检索样本编号生成上传二维码" />
          )}
          <div id="imgPreview" style={{ display: fileList?.length > 0 ? 'block' : 'none' }}>
            <div className="preview-root">
              <div className="preview-mask" />
              <div className="preview-wrap">
                <div className="image-preview">
                  <div className="image-preview-content">
                    <div className="image-preview-body">
                      <div className="preview-img-wrapper" onWheel={wheelImg} onMouseMove={onMouseMove} onMouseUp={onMouseUpOrOut}>
                        <img
                          onClick={e => e.stopPropagation()}
                          src={fileList[currentImg]}
                          alt=""
                          style={{
                            transform: `scale(${preZoom}) translate(${preMoveX}px, ${preMoveY}px) rotate(${preRotate}deg)`,
                          }}
                          onMouseDown={onMouseDown}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="preview-operations-wrapper">
              <div className="preview-switch-left" onClick={() => setCurrentImg(Math.max(0, currentImg - 1))}>
                <LeftOutlined />
              </div>
              <div className="preview-switch-right" onClick={() => setCurrentImg(Math.min(fileList.length - 1, currentImg + 1))}>
                <RightOutlined />
              </div>
              <ul className="preview-operations">
                <li className="preview-operations-progress">
                  {currentImg + 1} / {fileList.length}
                </li>
                <li className="preview-operations-operation" onClick={() => setQrcodeModal(true)}>
                  <PlusOutlined />
                </li>
                <li className="preview-operations-operation" onClick={() => resetPre()}>
                  <ReloadOutlined />
                </li>
                <li className="preview-operations-operation" onClick={() => zoomImg(1)}>
                  <ZoomInOutlined />
                </li>
                <li className="preview-operations-operation" onClick={() => zoomImg(-1)}>
                  <ZoomOutOutlined />
                </li>
                <li className="preview-operations-operation" onClick={() => rotateImg(90)}>
                  <RotateRightOutlined />
                </li>
                <li className="preview-operations-operation" onClick={() => rotateImg(-90)}>
                  <RotateLeftOutlined />
                </li>
              </ul>
            </div>
          </div>
          <Modal title="样本图片上传" open={qrCodeModal} onCancel={() => setQrcodeModal(false)} footer={null}>
            <div className="samplemanage-detail-qrcode-box">
              <img src={qrCode.url} alt="" />
              <div>样本编号：{sampleNumber}</div>
              <div>微信扫描二维码上传同意书</div>
            </div>
          </Modal>
        </div>
        <div className="right">
          <div className="right-box" ref={rightBox}>
            <Collapse defaultActiveKey="1" expandIconPosition="end">
              <Collapse.Panel header="样本基本信息" key="1">
                <Row gutter={[16, 0]}>
                  <Col span={8}>
                    <Form.Item label="样本编号" name="sampleNumber" rules={[{ required: true }]}>
                      <Input
                        placeholder="请输入"
                        disabled={!!id}
                        addonAfter={
                          <span style={{ cursor: !canEdit || (!!id && detail?.extfiled1 != 2) ? 'not-allowed' : 'pointer' }} onClick={getinfobysampling}>
                            检索
                          </span>
                        }
                        onBlur={async e => {
                          if (e.target.value) {
                            const data = await checksampling({ samplingNumber: e.target.value });
                            if (data.code === -10000) {
                              form.setFields([
                                // { name: '表单字段name', value: '需要设置的值', errors: ['错误信息'] }, 当 errors 为非空数组时，表单项呈现红色，
                                { name: 'sampleNumber', errors: ['该编号采样包未发放，不能录入，请先发放采样包'] },
                              ]);
                            }
                          }
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="合作客户" name="institutionId" rules={[{ required: true }]}>
                      <Select
                        placeholder="请选择"
                        // disabled={!canEdit || (!!id && detail?.extfiled1 != 2) || !!samplingInfo.institutionId}
                        showSearch
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                        onChange={() => {
                          form.setFieldsValue({ contractId: null, productId: null, sonProductId: null, informedConsentCode: null });
                        }}
                      >
                        {(institutionList || []).map(v => (
                          <Option value={String(v.id)} key={v.id}>
                            {v.institutionName}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="合同类型" name="contractId" rules={[{ required: true }]}>
                      <Select
                        placeholder="请选择"
                        // disabled={!canEdit || (!!id && detail?.extfiled1 != 2) || !!samplingInfo.contractId}
                        showSearch
                        filterOption={(input, option) => {
                          return option.contractType.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                        options={institutionContract.map(v => ({ ...v, contractType: HTLX[v.contractType] }))}
                        fieldNames={{ label: 'contractType', value: 'id' }}
                        onChange={() => {
                          form.setFieldsValue({ productId: null, sonProductId: null, informedConsentCode: null });
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="送检医院" name="submitHospital" rules={[{ required: true }]}>
                      <Select
                        placeholder="请选择"
                        showSearch
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                      >
                        {(institutionList || []).map(v => (
                          <Option value={String(v.id)} key={v.id}>
                            {v.institutionName}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="产品线" name="productId" rules={[{ required: true }]}>
                      <Select
                        placeholder="请选择"
                        // disabled={!canEdit || (!!id && detail?.extfiled1 != 2) || !!samplingInfo.productId}
                        showSearch
                        filterOption={(input, option) => {
                          return option.productName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                        options={contractproducts}
                        fieldNames={{ label: 'productName', value: 'id' }}
                        onChange={(v, o) => {
                          form.setFieldsValue({ informedConsentCode: o.templateType, sonProductId: null });
                          setTimeout(() => {
                            saveDetail({ informedConsentCode: o.templateType });
                          }, 500);
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="子产品线" name="sonProductId" rules={[{ required: false }]}>
                      <Select
                        placeholder="请选择"
                        // disabled={!canEdit || (!!id && detail?.extfiled1 != 2)}
                        showSearch
                        filterOption={(input, option) => {
                          return option.productName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                        options={contractsubproducts}
                        fieldNames={{ label: 'productName', value: 'id' }}
                        onChange={(v, o) => {
                          form.setFieldsValue({ informedConsentCode: o.templateType });
                          setTimeout(() => {
                            saveDetail({ informedConsentCode: o.templateType });
                          }, 500);
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Form.Item label="" name="informedConsentCode">
                    <Input type="hidden" />
                  </Form.Item>
                  <Col span={8}>
                    <Form.Item label="送检材料" name="submitMaterial" rules={[{ required: templateType == 'CNV' }]}>
                      <Select
                        placeholder="请选择"
                        showSearch
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                      >
                        {templateType == 'CNV'
                          ? (Object.keys(SJCL) || [])
                              .filter(v => ['S17', 'S16', 'S13', 'S14', 'S15', 'S6', 'S7', 'S3', 'S2'].includes(v))
                              .map(key => (
                                <Option key={key} value={key}>
                                  {SJCL[key]}
                                </Option>
                              ))
                          : (Object.keys(SJCL) || []).map(key => (
                              <Option key={key} value={key}>
                                {SJCL[key]}
                              </Option>
                            ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="检测辅助样品" name="testingAuxiliarySamples">
                      <Select
                        placeholder="请选择"
                        showSearch
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                      >
                        {(Object.keys(JCFZYP) || []).map(key => (
                          <Option key={key} value={key}>
                            {JCFZYP[key]}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="送检科室" name="submitDept">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="送检医生" name="submitDoctor">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="医生电话" name="doctorPhone">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="住院号/门诊号" name="hisNo">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="申请日期" name="applyTime">
                      <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="采样日期" name="sampleTime" rules={[{ required: true }]}>
                      <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="谈话时间" name="extfiled8">
                      <DatePicker showTime={{ format: 'HH:mm' }} format="YYYY-MM-DD HH:mm" style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="是否重采样" name="resamplingFlag">
                      <Select placeholder="请选择">
                        <Option value="1">是</Option>
                        <Option value="0">否</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="重采样类型" name="resamplingType">
                      <Select placeholder="请选择">
                        <Option value="R1">普通</Option>
                        <Option value="R2">阳性</Option>
                        <Option value="R3">其他</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="原样本编号" name="originalSampleNumber">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="收样日期" name="extfiled9" rules={[{ required: true }]}>
                      <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  {productIdValue == 1118 || contractproducts.filter(v => v.id == productIdValue)[0]?.productName == 'CNV-seq' ? (
                    <Col span={8}>
                      <Form.Item label="样本类型" name="extfiled11" rules={[{ required: true }]}>
                        <Select placeholder="请选择">
                          <Option value="患者">患者</Option>
                          <Option value="胎儿">胎儿</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  ) : (
                    ''
                  )}
                  <Col span={8}>
                    <Form.Item label="快递单号" name="expressnumber">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="是否UPD" name="upd">
                      <Select placeholder="请选择" allowClear>
                        <Option value="否">否</Option>
                        <Option value="是">是</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="二级合作客户" name="secendInstitutionId">
                      <Select
                        placeholder="请选择"
                        showSearch
                        allowClear
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                        onChange={(v, o) => {
                          form.setFieldsValue({ secendInstitutionName: o ? o.children : '' });
                        }}
                      >
                        {(institutionList || []).map(v => (
                          <Option value={String(v.id)} key={v.id}>
                            {v.institutionName}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="保险购买"
                      name="insure"
                      initialValue="是" // 直接设置initialValue即可
                    >
                      <Select placeholder="请选择">
                        <Option value="是">是</Option>
                        <Option value="否">否</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="外部样本编号" name="outSampleNumber">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>

                  {templateType == 'CNV' || templateType == 'NIPTJHBB' || templateType == 'PLUSJHBB' ? (
                    <Col span={8}>
                      <Form.Item label="平台" name="platform" rules={[{ required: templateType == 'CNV' || templateType == 'NIPTJHBB' || templateType == 'PLUSJHBB' }]}>
                        <Select placeholder="请选择">
                          <Option value="华大">华大</Option>
                          <Option value="贝瑞">贝瑞</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  ) : (
                    ''
                  )}
                  <Form.Item label="" name="secendInstitutionName">
                    <Input type="hidden" />
                  </Form.Item>
                </Row>
              </Collapse.Panel>
            </Collapse>
            <Collapse defaultActiveKey="1" expandIconPosition="end">
              <Collapse.Panel header="受检者基本信息" key="1">
                <Row gutter={[16, 0]}>
                  <Col span={8}>
                    <Form.Item label="姓名" name="sjzName">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="拼音（大写）" name="sjzNamePinyin">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="性别" name="sjzSex">
                      <Select placeholder="请选择">
                        <Option value="1">男</Option>
                        <Option value="2">女</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="证件类型" name="sjzIdType">
                      <Select placeholder="请选择">
                        <Option value="1">身份证</Option>
                        <Option value="2">其他</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="证件号码"
                      name="sjzIdNum"
                      rules={[
                        // {
                        //   pattern: /^([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}(\d|X|x))$/,
                        //   message: '请输入正确的证件号码',
                        // },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            if (value && !validateIdNo(value) && form.getFieldValue('sjzIdType') === '1') {
                              return Promise.reject(new Error('请输入正确的证件号码'));
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                    >
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  {templateType == 'CNV' ? (
                    <Col span={8}>
                      <Form.Item>
                        <div className="flex-box">
                          <Form.Item label="年龄(岁)" name="sjzAge" rules={[{ required: templateType == 'CNV' }]}>
                            <InputNumber placeholder="请输入岁" style={{ width: '100%' }} min={0} />
                          </Form.Item>

                          <Form.Item label="月" name="sjzMonth">
                            <InputNumber placeholder="请输入月" style={{ width: '100%' }} min={0} defaultValue={0} />
                          </Form.Item>

                          <Form.Item label="天" name="sjzDay">
                            <InputNumber placeholder="请输入天" style={{ width: '100%' }} min={0} defaultValue={0} />
                          </Form.Item>
                        </div>
                      </Form.Item>
                    </Col>
                  ) : (
                    <Col span={8}>
                      <Form.Item label="年龄" name="sjzAge">
                        <InputNumber placeholder="请输入" style={{ width: '100%' }} min={0} />
                      </Form.Item>
                    </Col>
                  )}

                  <Col span={8}>
                    <Form.Item label="身高cm" name="sjzHeight">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="体重kg" name="sjzWeight">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      label="手机号码"
                      name="sjzPhone"
                      rules={[
                        {
                          pattern: /^1\d{10}$/,
                          message: '手机号码格式不正确',
                        },
                      ]}
                    >
                      <Input placeholder="请输入" maxLength={11} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="紧急联系电话" name="sjzEmergencyPhone">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="籍贯" name="sjzNative">
                      <Cascader placeholder="请选择" options={provinceData} fieldNames={{ label: 'label', value: 'label', children: 'children' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="民族" name="sjzNation">
                      <Select
                        placeholder="请选择"
                        showSearch
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                      >
                        {(Object.keys(NATION) || []).map(key => (
                          <Option key={key} value={key}>
                            {NATION[key]}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="邮编" name="sjzZipCode">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="省份" name="sjzProvinceId">
                      <Select
                        placeholder="请选择"
                        options={provinceData}
                        showSearch
                        filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                        fieldNames={{ label: 'label', value: 'label' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="城市" name="sjzCityId">
                      <Select
                        placeholder="请选择"
                        options={cityOption}
                        showSearch
                        filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                        fieldNames={{ label: 'label', value: 'label' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="详细地址" name="address">
                      <Input placeholder="请输入" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="出生日期" name="birth">
                      <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              </Collapse.Panel>
            </Collapse>
            {renderZQTYS(templateType, canEdit)}
            <Collapse defaultActiveKey="1" expandIconPosition="end">
              <Collapse.Panel header="备注" key="1">
                <Row gutter={[16, 0]}>
                  <Col span={12}>
                    <Form.Item label="支付状态" name="payStatus">
                      <Select placeholder="请选择" allowClear>
                        <Option value="已支付">已支付</Option>
                        <Option value="未支付">未支付</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="支付日期" name="payDate">
                      <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} allowClear />
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item label="" name="notes">
                  <TextArea placeholder="请输入备注内容" rows={5} style={{ marginBottom: '24px' }} />
                </Form.Item>
              </Collapse.Panel>
            </Collapse>
            <Collapse defaultActiveKey="1" expandIconPosition="end">
              <Collapse.Panel header="样本报告：（选择本地PDF报告上传）" key="1">
                <div className="report-list">
                  {detail.reportPath ? (
                    <div className="file-box">
                      <a href={detail.reportPath} target="_blank" rel="noreferrer">
                        {detail.reportName || detail.reportPath}
                      </a>
                      {canEdit ? <CloseCircleOutlined title="删除" onClick={() => deleteImg()} /> : null}
                    </div>
                  ) : null}
                  {canEdit ? (
                    <Upload
                      name="upfile"
                      accept=".pdf"
                      listType="picture-card"
                      showUploadList={false}
                      action={`${CONSTANT.DOMAIN}/api/files/uploadpic`}
                      data={{
                        partnerId: 'merchant',
                        serviceType: 'test',
                      }}
                      beforeUpload={beforeUpload}
                      onChange={uploadOnChange}
                    >
                      <div>
                        <PlusOutlined />
                        <div style={{ marginTop: 8 }}>上传</div>
                      </div>
                    </Upload>
                  ) : null}
                </div>
              </Collapse.Panel>
            </Collapse>
          </div>
        </div>
      </div>
      {!canEdit ? (
        btns['/union/samplemanage/edit'] ? (
          <div className="footer">
            <Button type="primary" onClick={() => setCanEdit(true)} disabled={false}>
              编辑
            </Button>
          </div>
        ) : null
      ) : (
        <div className="footer">
          {/* {!id ? (
            <Button type="primary" htmlType="submit" onClick={() => submit(true)}>
              提交并继续添加
            </Button>
          ) : null} */}
          <Button type="primary" onClick={() => submit(true)}>
            提交
          </Button>
          <Button
            onClick={() => {
              dispatch({
                type: 'sample/save',
                payload: { detail: {} },
              });
              if (window.history.length > 1) {
                history.goBack();
              } else {
                window.opener = null;
                window.close();
              }
            }}
          >
            取消
          </Button>
        </div>
      )}
    </Form>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
