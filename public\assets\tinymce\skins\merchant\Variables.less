// Variables
// Syntax: <control>-(<sub control>)-<bg|border|text>-(<state>)-(<extra>);
// Example: @btn-primary-bg-hover-hlight;

@prefix: mce;

// Default font
@font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
@font-size: 14px;
@line-height: 20px;
@has-gradients: true;
@has-radius: true;
@has-boxshadow: false;
@has-button-borders: true;

// Text colors
@text: #595959;
@text-inverse: #ffffff;
@text-disabled: #bfbfbf;
@text-shadow: 0 1px 1px hsla(hue(@text-inverse), saturation(@text-inverse), lightness(@text-inverse), 0.75);
@text-error: #b94a48;
@text-warning: #c09853;
@text-success: #468847;
@text-link: #2980b9;

// Button
@btn-text: #595959;
@btn-text-shadow: #ffffff;
@btn-border-top: #e9e9e9;
@btn-border-right: rgba(233, 233, 233, 1);
@btn-border-bottom: #e9e9e9;
@btn-border-left: rgba(233, 233, 233, 1);
@btn-caret-border: @btn-text;
@btn-text-disabled: @text-disabled;
@btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
@btn-box-shadow-active: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 1px 2px rgba(0, 0, 0, 0.05);
@btn-box-disabled-opacity: 0.4;
@btn-bg: #ffffff;
@btn-bg-hlight: #ffffff;
@btn-bg-hover: darken(@btn-bg, 5%);
@btn-bg-hlight-hover: darken(@btn-bg-hlight, 5%);
@btn-border-hover: darken(@btn-bg, 20%);
@btn-border-active: darken(@btn-bg, 20%);
@btn-padding: 4px 10px;

@btn-primary-bg: #3f969d;
@btn-primary-bg-hlight: #9fd8d2;
@btn-primary-bg-hover: darken(@btn-primary-bg, 5%);
@btn-primary-bg-hover-hlight: darken(@btn-primary-bg-hlight, 5%);
@btn-primary-text: #ffffff;
@btn-primary-text-shadow: #ffffff;
@btn-primary-border-top: mix(@btn-border-top, @btn-primary-bg, 50%);
@btn-primary-border-right: mix(@btn-border-right, @btn-primary-bg, 50%);
@btn-primary-border-bottom: mix(@btn-border-bottom, @btn-primary-bg, 50%);
@btn-primary-border-left: mix(@btn-border-left, @btn-primary-bg, 50%);
@btn-primary-border: transparent;
@btn-primary-border-hover: transparent;

// Button group
@btn-group-border-width: 1px;

// Menu
@menuitem-text: #595959;
@menu-bg: #ffffff;
@menu-margin: -1px 0 0;
@menu-border: rgba(0, 0, 0, 0.2);
@menubar-border: mix(@panel-border, @panel-bg, 60%);
@menuitem-text-inverse: #595959;
@menubar-bg-active: darken(@btn-bg, 10%);
@menuitem-bg-hover: #0081c2;
@menuitem-bg-selected: #f0fbf9;
@menuitem-bg-selected-hlight: #f0fbf9;
@menuitem-bg-disabled: #ccc;
@menuitem-caret: @menuitem-text;
@menuitem-caret-selected: @menuitem-text-inverse;
@menuitem-separator-top: #cbcbcb;
@menuitem-separator-bottom: #ffffff;
@menuitem-bg-active: #f0fbf9;
@menuitem-text-active: #595959;
@menuitem-preview-border-active: #3f969d;
@menubar-menubtn-text: #595959;

// Panel
@panel-border: #e9e9e9;
@panel-bg: #ffffff;
@panel-bg-hlight: #ffffff;

// Tabs
@tab-border: #e9e9e9;
@tab-bg: #fdfdfd;
@tab-bg-hover: #f0fbf9;
@tab-bg-active: #f0fbf9;
@tabs-bg: #ffffff;

// Tooltip
@tooltip-bg: #000;
@tooltip-text: white;
@tooltip-font-size: 11px;

// Notification
@notification-font-size: 14px;
@notification-bg: #f0f0f0;
@notification-border: #cccccc;
@notification-text: #595959;
@notification-success-bg: #dff0d8;
@notification-success-border: #d6e9c6;
@notification-success-text: #3c763d;
@notification-info-bg: #d9edf7;
@notification-info-border: #779ecb;
@notification-info-text: #31708f;
@notification-warning-bg: #fcf8e3;
@notification-warning-border: #faebcc;
@notification-warning-text: #8a6d3b;
@notification-error-bg: #f2dede;
@notification-error-border: #ebccd1;
@notification-error-text: #a94442;

// Infobox
@infobox-bg: @notification-bg;
@infobox-border: @notification-border;
@infobox-text: @notification-text;
@infobox-success-bg: @notification-success-bg;
@infobox-success-border: @notification-success-border;
@infobox-success-text: @notification-success-text;
@infobox-info-bg: @notification-info-bg;
@infobox-info-border: @notification-info-border;
@infobox-info-text: @notification-info-text;
@infobox-warning-bg: @notification-warning-bg;
@infobox-warning-border: @notification-warning-border;
@infobox-warning-text: @notification-warning-text;
@infobox-error-bg: @notification-error-bg;
@infobox-error-border: @notification-error-border;
@infobox-error-text: @notification-error-text;

// Window
@window-border: #e9e9e9;
@window-head-border: @window-border;
@window-head-close: mix(@text, @window-bg, 60%);
@window-head-close-hover: mix(@text, @window-bg, 40%);
@window-foot-border: @window-border;
@window-foot-bg: @window-bg;
@window-fullscreen-bg: #fff;
@window-modalblock-bg: #000;
@window-modalblock-opacity: 0.3;
@window-box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
@window-bg: #ffffff;
@window-title-font-size: 20px;

// Popover
@popover-bg: @window-bg;
@popover-arrow-width: 10px;
@popover-arrow: @window-bg;
@popover-arrow-outer-width: @popover-arrow-width + 1;
@popover-arrow-outer: rgba(0, 0, 0, 0.25);

// Floatpanel
@floatpanel-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);

// Checkbox
@checkbox-bg: @btn-bg;
@checkbox-bg-hlight: @btn-bg-hlight;
@checkbox-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
@checkbox-border: #e9e9e9;
@checkbox-border-focus: #bdeee6;

// Path
@path-text: @text;
@path-bg-focus: #666;
@path-text-focus: #fff;

// Textbox
@textbox-text-placeholder: #aaa;
@textbox-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
@textbox-bg: #fff;
@textbox-border: #e9e9e9;
@textbox-border-focus: #3f969d;

// Selectbox
@selectbox-bg: @textbox-bg;
@selectbox-border: @textbox-border;

// Throbber
@throbber-bg: #fff url('img/loader.gif') no-repeat center center;

// Combobox
@combobox-border: @textbox-border;
@combobox-error-text: @text-error;
@combobox-warning-text: @text-warning;
@combobox-success-text: @text-success;

// Colorpicker
@colorpicker-border: @textbox-border;
@colorpicker-hue-bg: #fff;
@colorpicker-hue-border: #333;

// Grid
@grid-bg-active: @menuitem-bg-active;
@grid-border-active: #a1a1a1;
@grid-border: #d6d6d6;

// Misc
@colorbtn-backcolor-bg: #bbbbbb;
@iframe-border: @panel-border;

// Slider
@slider-border: #aaaaaa;
@slider-bg: #eeeeee;
@slider-handle-border: #bbbbbb;
@slider-handle-bg: #dddddd;
@slider-handle-bg-focus: #bbb;

// Progress
@progress-border: #e2e9e8;
@progress-bar-bg: #9fd8d2;
@progress-bar-bg-hlight: #f7f7f7;
@progress-text: #595959;

// Flow layout
@flow-layout-spacing: 2px;

// Table

@table-row-even: #fafafa;
@table-row-hover: darken(@table-row-even, 10%);
