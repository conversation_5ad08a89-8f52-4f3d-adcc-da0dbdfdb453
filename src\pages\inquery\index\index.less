@import '~antd/lib/style/themes/default.less';

.contentBox {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  // overflow-x: hidden;

  &.nouser {
    &:before {
      content: '';
      position: absolute;
      left: 340px;
      right: 0;
      top: 0;
      bottom: 0;
      opacity: 0;
      z-index: 10;
    }
  }

  .left {
    background: linear-gradient(180deg, rgba(62, 206, 182, 1) 0%, rgba(0, 193, 226, 1) 100%);
  }
  .center {
    // margin: 24px;
  }
  .end {
  }
}

.chart {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #fff;
  .chartHeadtimes {
    display: flex;
    padding: 3px 5px;
    align-items: center;
    border-bottom: 1px solid #f0f2f5;
    background: #fff7ec;
    .chartName {
      flex: 1;
      color: #ff8a00;
      font-weight: 500;
      font-size: 16px;
      padding-left: 5px;
    }
  }
  .chartHead {
    display: flex;
    padding: 12px 16px;
    align-items: center;
    border-bottom: 1px solid #f0f2f5;
    background: #fff;
    .chartName {
      flex: 1;
      color: #000;
      font-weight: 500;
      font-size: 16px;
    }
  }
  .chartBody {
    flex: 1;
    margin-top: 1px;
    position: relative;
    background: #fff;
  }
  .chartFoot {
    height: 200px;
    padding: 0;
    background: #fff;
    .editOper {
      .leftOper {
      }
      .rightOper {
      }
    }
  }
}

.chartInfo {
  cursor: pointer;
  padding: 6px 10px;
  background-color: #fff7ec;
  color: #ff8a00;
  border-radius: 10%;
  border: 1px solid #ff8a00;
}

.msg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow-x: hidden;
  overflow-y: auto;
  padding-bottom: 16px;

  &::-webkit-scrollbar {
    width: 0.5rem;
    height: 0.5rem;
    background: hsla(0, 0%, 100%, 0.6);
  }

  &::-webkit-scrollbar-track {
    border-radius: 0;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 0;
    background-color: rgba(95, 95, 95, 0.4);
    transition: all 0.2s;
    border-radius: 0.5rem;

    &:hover {
      background-color: rgba(95, 95, 95, 0.7);
    }
  }

  .msgDate {
    margin-top: 16px;
    text-align: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }
}

.msgBox {
  margin-top: 16px;
  display: flex;

  .msgHeader {
    padding-left: 24px;
  }
  .msgContent {
    flex: 1;
    padding-left: 40px;
    position: relative;

    .userInfo {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      padding-bottom: 4px;
    }
    .chartMsg {
      max-width: 300px;
      background-color: rgba(30, 54, 86, 0.04);
      color: #000;
      border-radius: 4px;
      position: relative;
      padding: 6px 10px;
      font-size: 14px;
      display: inline-block;
      word-break: break-all;
      text-align: justify;
      white-space: pre-wrap;
      img {
        max-width: 100%;
        max-height: 200px;
        cursor: pointer;
      }

      &:before {
        content: '';
        position: absolute;
        left: -16px;
        top: 6px;
        width: 16px;
        border-top: 8px solid rgba(30, 54, 86, 0.04);
        border-right: 8px solid rgba(30, 54, 86, 0.04);
        border-bottom: 8px solid transparent;
        border-left: 8px solid transparent;
        transform: skewY(15deg);
      }
    }
  }
  &.msgRight {
    .msgContent {
      text-align: right;
      .chartMsg {
        text-align: justify;
        background-color: @primary-color;
        color: #fff;
        white-space: pre-wrap;

        &:before {
          content: '';
          position: absolute;
          right: -16px;
          left: auto;
          top: 6px;
          width: 16px;
          border-top: 8px solid @primary-color;
          border-left: 8px solid @primary-color;
          border-bottom: 8px solid transparent;
          border-right: 8px solid transparent;
          transform: skewY(-15deg);
        }
      }
    }

    .msgHeader {
      padding-right: 24px;
      padding-left: 24px;
    }
  }
}

.contextMenu {
  position: fixed;
  z-index: 1;
  cursor: pointer;
}

.visitModal {
  .radioBtn {
    margin-right: 8px;
    border-radius: 4px;
  }
}
.feeChatHead {
  font-size: 14px;
  color: #000;
  font-weight: normal;
  font-weight: 400;
}
.feeChatStartTip {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  padding: 30px 0;
  text-align: center;
}

.previewImg {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    cursor: pointer;
  }
  .rotateOper {
    position: absolute;
    right: 30px;
    bottom: 30px;
    width: 60px;
    height: 60px;
    z-index: 101;
    background: url('../../../assets/video-rotate.png') no-repeat center center / 80% auto;
    box-shadow: 0 0 10px 10px rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    cursor: pointer;
  }
}

.consultationSuggest {
  position: absolute;
  z-index: 99;
  right: -10px;
  font-size: 12px;
  top: 50%;
  height: 30px;
  width: 80px;
  background-color: #439dfd;
  border-radius: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  cursor: pointer;
}

.consultationInfo {
  font-size: 12px;
  font-weight: 400;
  color: #666666;
}

.chartHradBtn {
  display: inline-block;
  width: 30px;
  height: 30px;
  margin-left: 8px;
  background-position: 0 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  cursor: pointer;
}
.chbtn2 {
  background-image: url(../../../assets/icon-record.png);
}

.list {
  height: 400px;
  overflow: auto;
  white-space: pre-wrap;
  text-align: justify;

  &::-webkit-scrollbar {
    display: none;
  }

  .item {
    padding-right: 24px;
    padding-left: 24px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;

    .meta {
      width: 100%;
    }

    .avatar {
      margin-top: 4px;
      background: #fff;
    }

    .iconElement {
      font-size: 32px;
    }

    &.read {
      opacity: 0.4;
    }

    &:last-child {
      border-bottom: 0;
    }

    &:hover {
      background: @primary-1;
    }

    .title {
      margin-bottom: 8px;
      font-weight: normal;
    }

    .descriptionModule {
      display: flex;
    }

    .description {
      font-size: 12px;
      line-height: @line-height-base;
    }

    .datetime {
      margin-top: 4px;
      font-size: 12px;
      line-height: @line-height-base;
    }

    .extra {
      float: right;
      margin-top: -1.5px;
      margin-right: 0;
      color: @text-color-secondary;
      font-weight: normal;
    }
  }

  .loadMore {
    padding: 8px 0;
    color: @primary-6;
    text-align: center;
    cursor: pointer;

    &.loadedAll {
      color: rgba(0, 0, 0, 0.25);
      cursor: unset;
    }
  }
}
