import React, { useEffect, useMemo, useState } from 'react';
import { useAntdTable } from 'ahooks';
import { merge, set } from 'lodash';
import moment from 'moment';
import { connect, history } from 'umi';
import { Form, Row, Col, Input, Select, Space, Button, Table, DatePicker, Image, Modal } from 'antd';
import { provinceData } from '../province';

import * as Api from './service';

const { RangePicker } = DatePicker;
const { Search, TextArea } = Input;
const { Option } = Select;
const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 17 },
};

const titleList = [
  { value: '', label: '全部' },
  { value: '医士', label: '医士' },
  { value: '医师', label: '医师' },
  { value: '住院医师', label: '住院医师' },
  { value: '主治医师', label: '主治医师' },
  { value: '副主任医师', label: '副主任医师' },
  { value: '主任医师', label: '主任医师' },
];

const typeList = [
  { label: '全部', value: '' },
  { label: '待审核', value: 1 },
  { label: '已通过', value: 2 },
  { label: '已驳回', value: 3 },
];

const typeMap = {
  1: '待审核',
  2: '已通过',
  3: '已驳回',
};

const provinceMap = provinceData.map(item => {
  return { label: item.label, value: item.label, children: item.children };
});

const Index = props => {
  const [form] = Form.useForm();
  const [modalForm] = Form.useForm();
  const [cancelForm] = Form.useForm();

  const { permissionData = {}, menuType = '' } = props;
  const { btns = {} } = permissionData;
  const [cityList, setCityList] = useState([]);
  const [rolesList, setRolesList] = useState([]);
  const [allUserList, setAllUserList] = useState([]);
  const [institutionList, setInstitutionList] = useState([]);
  const [provinceId, setProvinceId] = useState('');
  const [queryParam, setQueryParam] = useState({});
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [cancelModal, setCancelModal] = useState(false);
  const [info, setInfo] = useState({});

  useEffect(() => {
    getInstitution();
    getrolesbypage();
    findAllUser();
  }, []);

  useEffect(() => {
    if (provinceId) {
      const list = provinceMap.filter(item => item.value === provinceId);
      setCityList(
        list[0]?.children?.map(item => {
          return { label: item.label, value: item.label, children: item.children };
        }),
      );
    }
  }, [provinceId]);

  const onSearch = value => {
    setQueryParam({ ...queryParam, keyword: value });
    submit();
  };

  const findAllUser = async () => {
    const { code, data } = await Api.findAllUser({ menuType });
    if (code === 0) {
      setAllUserList(data || []);
    }
  };
  const getrolesbypage = async () => {
    const { code, data } = await Api.getrolesbypage({
      pageNum: 1,
      numPerPage: 999,
    });
    if (code === 0) {
      setRolesList(data.recordList || []);
    }
  };

  const getInstitution = async () => {
    const data = await Api.getInstitution({ menuType });
    if (data.code === 0) {
      setInstitutionList(data.data || []);
    }
  };

  const fetchList = async ({ current = 1, pageSize = 10 }) => {
    try {
      const { auditDate = [], createTime = [], identityId, institutionId, ...rest } = form.getFieldsValue();
      const params = {
        pageNum: current,
        numPerPage: pageSize,
        registerStart: createTime[0] && moment(createTime[0]).format('YYYY-MM-DD'),
        registerEnd: createTime[0] && moment(createTime[1]).format('YYYY-MM-DD'),
        auditStart: auditDate[0] && moment(auditDate[0]).format('YYYY-MM-DD'),
        auditEnd: auditDate[0] && moment(auditDate[1]).format('YYYY-MM-DD'),
        identityId,
        institutionId,
        ...(rest || {}),
        identityName: rolesList.filter(item => item.id === identityId)[0]?.identityName,
        institutionName: institutionList.filter(item => item.id === institutionId)[0]?.institutionName,
        inputDate: queryParam.keyword,
        menuType,
      };

      const data = await Api.fetchList({ ...params });

      return {
        total: Number(data.data?.totalCount) || 0,
        list: data?.data?.recordList || [],
      };
    } catch (error) {
      console.log(error);
    }
  };

  const { loading, tableProps, search } = useAntdTable(fetchList, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
  });

  const tableRealProps = useMemo(
    () =>
      merge(tableProps, {
        pagination: {
          // showQuickJumper: true,
          // showSizeChanger: true,
          showTotal: total => `共 ${total} 条`,
        },
      }),
    [tableProps],
  );

  const { submit, reset } = search;

  const onOk = () => {
    modalForm.submit();
  };

  const onCancel = () => {
    setIsModalOpen(false);
    setCancelModal(false);
    modalForm.resetFields();
    cancelForm.resetFields();
  };

  const modalFinish = async values => {
    console.log(values);
    let salesItem = {};
    if (values.salesId) {
      salesItem = allUserList.filter(item => item.id === values.salesId)[0] || {};
    }
    const { code } = await Api.audit({
      ...values,
      salesName: salesItem.name ? `${salesItem.name}(${(salesItem.phone || '').slice(-4)})` : '',
      identityName: rolesList.filter(item => item.id === values.identityId)[0]?.identityName,
      institutionName: institutionList.filter(item => item.id === values.institutionId)[0]?.institutionName,
      id: info.id,
    });
    if (code === 0) {
      setIsModalOpen(false);
      submit();
    }
  };

  const cancelFinish = async values => {
    console.log(values);
    const { code } = await Api.reject({
      ...values,
      id: info.id,
    });
    if (code === 0) {
      setCancelModal(false);
      submit();
    }
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      fixed: 'left',
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
    },
    {
      title: '姓名',
      dataIndex: 'name',
    },
    {
      title: '手机号码',
      dataIndex: 'phone',
    },
    {
      title: '身份证号码',
      dataIndex: 'idNo',
    },
    {
      title: '职称',
      dataIndex: 'title',
    },
    {
      title: '所属单位名称',
      dataIndex: 'unitName',
    },
    {
      title: '所属科室或部门',
      dataIndex: 'dept',
    },
    {
      title: '医师资格证书',
      dataIndex: 'yszzzs',
      render: text => text && <Image height={80} src={text} />,
    },
    {
      title: '医师执业证书',
      dataIndex: 'yszyzs',
      render: text => text && <Image height={80} src={text} />,
    },
    {
      title: '个人头像',
      dataIndex: 'accountImg',
      render: text => text && <Image height={80} src={text} />,
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      render: text => typeMap[text],
    },
    {
      title: '审核时间',
      dataIndex: 'auditDate',
    },
    {
      title: '审核人',
      dataIndex: 'auditUser',
    },
    {
      title: '客户名称',
      dataIndex: 'institutionName',
    },
    {
      title: '关联销售',
      dataIndex: 'salesName',
    },
    {
      title: '角色',
      dataIndex: 'identityName',
    },
    {
      title: '咨询设置',
      dataIndex: 'zxsz',
      render: text => (text == 1 ? '允许咨询' : '不允许咨询'),
    },
    {
      title: '开单设置',
      dataIndex: 'kdsz',
      render: text => (text == 1 ? '允许开单' : '不允许开单'),
    },
    {
      title: '描述',
      dataIndex: 'reason',
    },
    {
      title: '操作',
      dataIndex: 'institutionId',
      fixed: 'right',
      render: (id, record) => {
        return (
          record.status === 1 && (
            <div className="edit-btn">
              <span
                onClick={() => {
                  setIsModalOpen(true);
                  modalForm.setFieldsValue({ name: record.name, unitName: record.unitName, salesId: record.salesId });
                  setInfo(record);
                }}
              >
                通过
              </span>
              <span
                style={{ color: 'red' }}
                onClick={() => {
                  setCancelModal(true);
                  cancelForm.setFieldsValue({ name: record.name, unitName: record.unitName });
                  setInfo(record);
                }}
              >
                驳回
              </span>
            </div>
          )
        );
      },
    },
  ];

  return (
    <div className="g-page p-product-list">
      <div className="g-query-box">
        <Form form={form} {...formItemLayout}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="createTime" label="申请日期">
                <RangePicker />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="title" label="职称">
                <Select placeholder="请选择" options={titleList} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="auditDate" label="审核日期">
                <RangePicker />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="status" label="审核状态">
                <Select placeholder="请选择" options={typeList} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionId" label="所属客户">
                <Select
                  fieldNames={{ label: 'institutionName', value: 'id' }}
                  options={[{ id: '', institutionName: '全部' }, ...institutionList]}
                  showSearch
                  filterOption={(input, option) => (option?.institutionName ?? '').toLowerCase().includes(input.toLowerCase())}
                  placeholder="请选择"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="identityId" label="角色">
                <Select
                  placeholder="请选择"
                  fieldNames={{ label: 'identityName', value: 'id' }}
                  options={[{ id: '', identityName: '全部' }, ...rolesList]}
                  showSearch
                  filterOption={(input, option) => (option?.identityName ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="salesId" label="关联销售">
                <Select
                  placeholder="请选择"
                  showSearch
                  filterOption={(input, option) => {
                    return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  <Option value="">全部</Option>
                  {(allUserList || []).map(item => (
                    <Option key={item.id} value={item.id}>{`${item.name}(${(item.phone || '').slice(-4)})`}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={16} style={{ textAlign: 'right', display: 'block' }}>
              <Space>
                <Button type="primary" onClick={submit} disabled={loading}>
                  查询
                </Button>
                <Button onClick={reset} disabled={loading}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="p-product-table">
        <Space direction="vertical" style={{ display: 'flex' }} size="middle">
          <Row>
            <Col span={8}>
              <Search
                placeholder="请输入姓名/手机号搜索"
                allowClear
                onSearch={onSearch}
                style={{
                  width: 260,
                }}
              />
            </Col>
          </Row>
          <Table rowKey="id" {...tableRealProps} loading={loading} columns={columns} scroll={{ x: 'max-content' }} />
        </Space>
      </div>
      <Modal open={isModalOpen} onOk={onOk} onCancel={onCancel} className="add-sampling-modal" title="通过申请" okText="通过并创建账号">
        <Form form={modalForm} onFinish={modalFinish} {...{ labelCol: { span: 6 }, wrapperCol: { span: 12 } }}>
          <Form.Item name="name" label="姓名">
            <Input disabled />
          </Form.Item>
          <Form.Item name="unitName" label="所属单位名称">
            <Input disabled />
          </Form.Item>
          <Form.Item name="institutionId" label="所属客户" rules={[{ required: true }]}>
            <Select
              fieldNames={{ label: 'institutionName', value: 'id' }}
              options={[...institutionList]}
              showSearch
              filterOption={(input, option) => (option?.institutionName ?? '').toLowerCase().includes(input.toLowerCase())}
              placeholder="请选择"
            />
          </Form.Item>
          <Form.Item name="identityId" label="角色" rules={[{ required: true }]}>
            <Select
              placeholder="请选择"
              fieldNames={{ label: 'identityName', value: 'id' }}
              options={[...rolesList]}
              showSearch
              filterOption={(input, option) => (option?.identityName ?? '').toLowerCase().includes(input.toLowerCase())}
            />
          </Form.Item>
          <Form.Item name="zxsz" label="咨询设置" rules={[{ required: true }]} initialValue={1}>
            <Select
              placeholder="请选择"
              options={[
                { label: '允许咨询', value: 1 },
                { label: '不允许咨询', value: 0 },
              ]}
            />
          </Form.Item>
          <Form.Item name="kdsz" label="开单设置" rules={[{ required: true }]} initialValue={1}>
            <Select
              placeholder="请选择"
              options={[
                { label: '允许开单', value: 1 },
                { label: '不允许开单', value: 0 },
              ]}
            />
          </Form.Item>
          <Form.Item name="salesId" label="关联销售">
            <Select
              placeholder="请选择"
              allowClear
              showSearch
              filterOption={(input, option) => {
                return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }}
            >
              {(allUserList || []).map(item => (
                <Option key={item.id} value={item.id}>{`${item.name}(${(item.phone || '').slice(-4)})`}</Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
        <div className="tip" style={{ color: '#3F969D' }} onClick={() => window.open(`${window.location.href.split('#')[0]}#/union/customer/detail`, '_blank')}>
          快捷创建客户
        </div>
      </Modal>
      <Modal
        open={cancelModal}
        onOk={() => {
          cancelForm.submit();
        }}
        onCancel={onCancel}
        className="add-sampling-modal"
        title="驳回申请"
      >
        <Form form={cancelForm} onFinish={cancelFinish} {...{ labelCol: { span: 6 }, wrapperCol: { span: 12 } }}>
          <Form.Item name="name" label="姓名">
            <Input disabled />
          </Form.Item>
          <Form.Item name="unitName" label="所属单位名称">
            <Input disabled />
          </Form.Item>
          <Form.Item name="reason" label="驳回原因">
            <TextArea placeholder="请输入驳回原因，方便用户重新申请" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Index);
