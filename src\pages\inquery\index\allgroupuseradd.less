@import '~antd/lib/style/themes/default.less';

.list {
  height: 400px;
  overflow: auto;
  margin-top: 12px;
  &::-webkit-scrollbar {
    display: none;
  }
  .item {
    // padding-right: 24px;
    // padding-left: 24px;
    padding: 9px 0;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;

    .meta {
      width: 100%;
    }

    .avatar {
      margin-top: 4px;
      background: #fff;
    }
    .iconElement {
      font-size: 32px;
    }

    &.read {
      opacity: 0.4;
    }
    &:last-child {
      border-bottom: 0;
    }
    &:hover {
      background: @primary-1;
    }
    .title {
      color: #999;
      margin-bottom: 0;
      font-weight: normal;
      margin-right: 10px;
      padding-left: 25px;
      background: url(../../../assets/group-header.png) no-repeat left center / 19px auto;
    }
    .description {
      font-size: 12px;
      line-height: @line-height-base;
      display: flex;
      .extra {
        flex: 1;
        text-align: right;
        padding-left: 10px;
      }
    }
    .datetime {
      margin-top: 4px;
      font-size: 12px;
      line-height: @line-height-base;
      display: flex;

      .timelt {
        flex: auto;
        padding-right: 10px;
      }
    }
  }
  .loadMore {
    padding: 8px 0;
    color: @primary-6;
    text-align: center;
    cursor: pointer;
    &.loadedAll {
      color: rgba(0, 0, 0, 0.25);
      cursor: unset;
    }
  }
}

.extra {
  float: right;
  margin-top: -1.5px;
  margin-right: 0;
  color: @text-color-secondary;
  font-weight: normal;
}

.notFound {
  padding: 73px 0 88px 0;
  color: @text-color-secondary;
  text-align: center;
  img {
    display: inline-block;
    height: 76px;
    margin-bottom: 16px;
  }
}

.bottomBar {
  height: 46px;
  color: @text-color;
  line-height: 46px;
  text-align: center;
  border-top: 1px solid @border-color-split;
  border-radius: 0 0 @border-radius-base @border-radius-base;
  transition: all 0.3s;
  div {
    display: inline-block;
    width: 50%;
    cursor: pointer;
    transition: all 0.3s;
    user-select: none;
    &:hover {
      color: @heading-color;
    }
    &:only-child {
      width: 100%;
    }
    &:not(:only-child):last-child {
      border-left: 1px solid @border-color-split;
    }
  }
}
