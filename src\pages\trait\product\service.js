import * as utils from '../../../utils/utils';

export function fetchList(param) {
  return utils.request('/api/product/get-by-page', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function fetchDetail(param) {
  return utils.request('/api/product/get-by-id', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function updateProduct(param) {
  return utils.request('/api/product/update', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function addProduct(param) {
  return utils.request('/api/product/add-product', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function deleteProduct(param) {
  return utils.request('/api/product/delete', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function getProduct(param) {
  return utils.request('/api/product/get-products', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function dataExport(param) {
  return utils.request('/api/product/export', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function getDetailList(param) {
  return utils.request('/api/product/product-sign-list', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function findAllUser(param) {
  return utils.request('/api/userinfo/findAllUser', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
