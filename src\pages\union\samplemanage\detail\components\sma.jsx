/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select, DatePicker } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { smatemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, smatemplate: { ...smatemplate, ...payload } },
      },
    });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="受检者信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="关联样本">
                <Radio.Group value={smatemplate.field1} onChange={e => changeData({ field1: e.target.value })}>
                  <Radio value="无">无</Radio>
                  <Radio value="有">有</Radio>
                </Radio.Group>
                {smatemplate.field1 === '有' ? (
                  <>
                    <div className="flex-box" style={{ margin: '16px 0' }}>
                      <div className="flex-box">
                        <div className="flex-shrink">关联样本编号1</div>
                        <Input placeholder="请输入" value={smatemplate.field2} onChange={e => changeData({ field2: e.target.value })} />
                      </div>
                      <div className="flex-box">
                        <div className="flex-shrink">与本样本关系</div>
                        <Input placeholder="请输入" value={smatemplate.field3} onChange={e => changeData({ field3: e.target.value })} />
                      </div>
                    </div>
                    <div className="flex-box">
                      <div className="flex-box">
                        <div className="flex-shrink">关联样本编号2</div>
                        <Input placeholder="请输入" value={smatemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
                      </div>
                      <div className="flex-box">
                        <div className="flex-shrink">与本样本关系</div>
                        <Input placeholder="请输入" value={smatemplate.field5} onChange={e => changeData({ field5: e.target.value })} />
                      </div>
                    </div>
                  </>
                ) : null}
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="目前是否怀孕">
                <div className="flex-box">
                  <Radio.Group value={smatemplate.field6} onChange={e => changeData({ field6: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">现孕周</div>
                    <Input placeholder="请输入" value={smatemplate.field7} onChange={e => changeData({ field7: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="不良孕产史">
                <div className="flex-box">
                  <Radio.Group value={smatemplate.field8} onChange={e => changeData({ field8: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">描述</div>
                    <Input placeholder="请输入" value={smatemplate.field9} onChange={e => changeData({ field9: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="送检样本信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="样本类型">
                <Checkbox.Group value={smatemplate.field10 ? smatemplate.field10.split(',') : []} onChange={v => changeData({ field10: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={8}>
                      <Checkbox value="血液（推荐）">血液（推荐）</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="基因组 DNA">基因组 DNA</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="肌肉组织">肌肉组织</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="流产组织">流产组织</Checkbox>
                    </Col>
                    <Col span={16}>
                      <div className="flex-box">
                        <Checkbox value="其它">其它</Checkbox>
                        <div className="flex-box">
                          <Input placeholder="请输入" value={smatemplate.field11} onChange={e => changeData({ field11: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="样本寄送日期">
                <DatePicker format="YYYY-MM-DD" value={smatemplate.field12 ? moment(smatemplate.field12) : null} onChange={(date, dateString) => changeData({ field12: dateString })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="样本寄送人">
                <Input placeholder="请输入" value={smatemplate.field13} onChange={e => changeData({ field13: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="联系电话">
                <Input placeholder="请输入" value={smatemplate.field14} onChange={e => changeData({ field14: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="样本临床信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="受检者类型">
                <Checkbox.Group value={smatemplate.field15 ? smatemplate.field15.split(',') : []} onChange={v => changeData({ field15: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={8}>
                      <Checkbox value="表型正常者">表型正常者</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="确诊患者">确诊患者</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="疑似患者">疑似患者</Checkbox>
                    </Col>
                    <Col span={16}>
                      <div className="flex-box">
                        <Checkbox value="其它">其它</Checkbox>
                        <div className="flex-box">
                          <Input placeholder="请输入" value={smatemplate.field24} onChange={e => changeData({ field24: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="检测目的">
                <Checkbox.Group value={smatemplate.field16 ? smatemplate.field16.split(',') : []} onChange={v => changeData({ field16: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={8}>
                      <Checkbox value="携带者检测">携带者检测</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="查找病因">查找病因</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="辅助诊断">辅助诊断</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="指导生育">指导生育</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="家系验证">家系验证</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="受检者疾病史">
                <TextArea placeholder="请输入" defaultValue={smatemplate.field17} onChange={e => changeData({ field17: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="临床症状">
                <TextArea placeholder="请输入" defaultValue={smatemplate.field18} onChange={e => changeData({ field18: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="父母是否近亲结婚">
                <div className="flex-box">
                  <Radio.Group value={smatemplate.field19} onChange={e => changeData({ field19: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="是否有疾病家族史">
                <div className="flex-box">
                  <Radio.Group value={smatemplate.field20} onChange={e => changeData({ field20: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="是否有附加检查材料">
                <div className="flex-box">
                  <Radio.Group value={smatemplate.field21} onChange={e => changeData({ field21: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="是否曾做过相关疾病的基因检测">
                <div className="flex-box">
                  <Radio.Group value={smatemplate.field22} onChange={e => changeData({ field22: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="基因检测报告结果">
                <TextArea placeholder="请输入" defaultValue={smatemplate.field23} onChange={e => changeData({ field23: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
