import * as request from '@/utils/request';

export function getDeptlist(param) {
  return request.post('/api/ehis/health/api/dept/treeDepts', { data: param }) || {};
}

export function getNBDoctor(param) {
  return request.post('/api/ehis/health/api/doctor/recommendDoctors', { data: param }) || {};
}

export function getDoctorByDept(param) {
  return request.post('/api/ehis/health/api/doctor/page', { data: param }) || {};
}

export function queryChatList(param) {
  return request.post('/api/docChat/queryChatList', { data: param }) || {};
}

export function queryRelationPatients(param) {
  return request.post('/api/docChat/queryChatListByPid', { data: param }) || {};
}

export function sendMsg(param) {
  return request.post('/api/docChat/sendMessage', { data: param }) || {};
}

export function queryChatInfo(param) {
  return request.post('/api/docChat/queryChatInfo', { data: param }) || {};
}

export function uploadFile(param) {
  return request.postjson('/api/files/uploadpic', { data: param }) || {};
}

export function queryHealthRecord(param) {
  return request.post('/api/healthRecord/queryHealthRecord', { data: param }) || {};
}

export function queryDiseaseList(param) {
  return request.post('/api/healthRecord/queryDiseaseList', { data: param }) || {};
}

export function queryMedicalRecords(param) {
  return request.post('/api/medical/queryMedicalRecords', { data: param }) || {};
}

export function queryUserInfo(param) {
  return request.post('/api/receive/queryUserInfo', { data: param }) || {};
}

export function getRtcSig(param) {
  return request.post('/api/doctor/liveStream/sign', { data: param }) || {};
}

export function startVideo(param) {
  return request.post('/api/doctor/liveStream/startLive', { data: param }) || {};
}

export function getVideoStatus(param) {
  return request.post('/api/doctor/liveStream/getStatus', { data: param }) || {};
}

export function endVideo(param) {
  return request.post('/api/doctor/liveStream/endLive', { data: param }) || {};
}

export function getFastList(param) {
  return request.post('/api/receive/queryFastList', { data: param }) || {};
}
export function addMedicalRecord(param) {
  return request.post('/api/medical/addMedicalRecord', { data: param }) || {};
}

export function updateMedicalRecord(param) {
  return request.post('/api/medical/updateMedicalRecord', { data: param }) || {};
}

export function queryReportOutHospital(param) {
  // 院外检查报告列表查询
  return request.post('/api/reportOut/queryReportOutHospital', { data: param }) || {};
}

export function queryReportOutHospitalDetail(param) {
  // 院外检查报告详细查询(异常/结果)
  return request.post('/api/reportOut/queryReportOutHospitalDetail', { data: param }) || {};
}

export function deleteReportOutHospital(param) {
  // 院外检查报告删除
  return request.post('/api/reportOut/deleteReportOutHospital', { data: param }) || {};
}

export function queryReportById(param) {
  // 院外检查报告详细查询(异常/结果)
  return request.post('/api/reportOut/queryReportById', { data: param }) || {};
}

export function getBusType(param) {
  return request.post('/api/receive/getBusType', { data: param }) || {};
}

export function handMove(param) {
  return request.post('/api/receive/divertConfirm', { data: param }) || {};
}

export function queryTagList(param) {
  return request.post('/api/receive/queryTagList', { data: param }) || {};
}

export function queryTagHistory(param) {
  return request.post('/api/medical/queryMedicalRecords', { data: param }) || {};
}

export function addTag(param) {
  return request.post('/api/medical/addMedicalRecord', { data: param }) || {};
}

export function queryUserTagList(param) {
  return request.post('/api/receive/queryUserTagList', { data: param }) || {};
}

export function addUserTag(param) {
  return request.postjson('/api/receive/addUserTag', { data: param }) || {};
}

export function queryTagForUserList(param) {
  return request.post('/api/receive/queryTagForUserList', { data: param }) || {};
}

export function deleteUserTag(param) {
  return request.post('/api/receive/deleteUserTag', { data: param }) || {};
}

export function getReportTypes(param) {
  return request.post('/api/reportOut/queryItemTypeList', { data: param }) || {};
}

export function getReportTypesItemList(param) {
  return request.post('/api/reportOut/queryItemListByType', { data: param }) || {};
}

export function sortReport(param) {
  return request.postjson('/api/reportOut/sortReport', { data: param }) || {};
}

export function addReportOutHospital(param) {
  return request.post('/api/reportOut/addReportOutHospital', { data: param }) || {};
}

export function revokeMessage(param) {
  return request.post('/api/docChat/revokeMessage', { data: param }) || {};
}

export function unReadMessage(param) {
  return request.post('/api/docChat/unReadMessage', { data: param }) || {};
}

export function creatChat(param) {
  return request.post('/api/docChat/creatChat', { data: param }) || {};
}

export function queryTimeAxis(param) {
  return request.post('/api/medical/queryTimeAxis', { data: param }) || {};
}

export function getInHosReportList(param) {
  return request.post('/api/customize/getReportList?_route=h242&k=1', { data: param }) || {};
}

export function getInHosReportDetail(param) {
  return request.post('/api/customize/getReportDetail?_route=h242&k=1', { data: param }) || {};
}

export function queryQYStatusByPid(param) {
  return request.post('/api/userManage/queryPatientByPid', { data: param }) || {};
}

export function queryQYStatusAll(param) {
  return request.post('/api/healthRecord/queryDiseaseList', { data: param }) || {};
}

export function updateQYStatusByPid(param) {
  return request.post('/api/userManage/updateConsultationStatus', { data: param }) || {};
}

export function getAllUser(param) {
  return request.post('/api/team/getAllUsersGroup', { data: param }) || {};
}

export function queryGroupMember(param) {
  return request.post('/api/docChat/queryGroupMember', { data: param }) || {};
}

export function addMemberToGroup(param) {
  return request.postjson('/api/docChat/addMemberToGroup', { data: param }) || {};
}

export function getFreeChatByPid(param) {
  return request.post('/api/docChat/getFreeChatByPid', { data: param }) || {};
}
export function queryChatHistoryRecord(param) {
  return request.post('/api/docChat/queryChatHistoryRecord', { data: param }) || {};
}

export function searchPatientGroupAllUser(param) {
  return request.post('/api/docChat/searchPatientGroup', { data: param }) || {};
}

export function addAllUserToPatientGroup(param) {
  return request.post('/api/docChat/addToPatientGroup', { data: param }) || {};
}

export function queryFeeChatListForDoc(param) {
  return request.post('/api/docChat/queryFeeChatListForDoc', { data: param }) || {};
}

export function creatFeeChatForDoc(param) {
  return request.post('/api/docChat/creatFeeChatForDoc', { data: param }) || {};
}

export function endFeeChat(param) {
  return request.post('/api/docChat/endFeeChat', { data: param }) || {};
}

export function readAllUserMessage(param) {
  return request.post('/api/docChat/readAllUserMessage', { data: param }) || {};
}

export function readMessage(param) {
  return request.post('/api/docChat/readMessage', { data: param }) || {};
}

export function getPeriod(param) {
  return request.postjson('/api/customize/getPeriod?_route=h242&k=1', { data: param }) || {};
}

export function queryMyFastQuestion(param) {
  return request.post('/api/receive/question/queryAccountFastInfo', { data: param }) || {};
}

export function addFastQuestion(param) {
  return request.post('/api/receive/question/addAccountFastQuestion', { data: param }) || {};
}

export function deleteFastQuestion(param) {
  return request.post('/api/receive/question/deleteAccountFastQuestion', { data: param }) || {};
}

export function loopMessage(param) {
  return request.post('/api/docChat/queryChatListForNewMessage', { data: param }, false) || {};
}

export function refreshPatGroup(param) {
  return request.post('/api/docChat/refreshPatGroup', { data: param }) || {};
}

export function getTruePhoneNum(param) {
  return request.post('/api/customize/getTruePhoneNum?_route=h242', { data: param }) || {};
}

export function getAllRecordList(param) {
  return request.post('/api/consultation/getAllRecordList', { data: param }) || {};
}

export function creatConsultationChat(param) {
  return request.postjson('/api/consultation/chat/creatConsultationChat', { data: param }) || {};
}

export function getApplyInfoAndFile(param) {
  return request.post('/api/consultation/getApplyInfoAndFile', { data: param }) || {};
}

export function queryReport(param) {
  return request.post('/api/consultation/chat/queryReport', { data: param }) || {};
}

export function addReport(param) {
  return request.post('/api/consultation/chat/addReport', { data: param }) || {};
}

export function cancelChat(param) {
  return request.post('/api/consultation/chat/cancelChat', { data: param }) || {};
}

export function getButtonShow(param) {
  return (
    request.post('/api/consultation/chat/isMainDoctorOrHasConsultationManagement', {
      data: param,
    }) || {}
  );
}

export function controlChat(param) {
  return request.post('/api/docChat/controlChat', { data: param }) || {};
}

export function getChatGroupInfo(param) {
  return request.post('/api/docChat/getChatGroupInfo', { data: param }) || {};
}

export function getCall(param) {
  // return request.post('/api/docChat/getCall', { data: param }) || {};
  return request.post('/api/customize/getCall?_route=h242', { data: param }) || {};
}

export function deleteChatMessage(param) {
  return request.post('/api/docChat/deleteChatMessage', { data: param }) || {};
}

export function exitMemberToGroup(param) {
  return request.postjson('/api/docChat/exitMemberToGroup', { data: param }) || {};
}

//获取当前用户
export function queryCurrent() {
  return request.post('/api/userinfo/merchantuser');
}

//删除就诊记录

export function DeleteMedicalRecord(param) {
  return request.post('/api/medical/deleteMedicalRecord', { data: param }) || {};
}
