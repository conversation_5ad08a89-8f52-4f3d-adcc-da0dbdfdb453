import React, { Component, Fragment } from 'react';
import { Tooltip, Modal, message, Tag } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import * as Api from './api';

import styles from './groupoper.less';
import { relative } from 'path';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      visible: false,
      tagList: [],
      activeTag: [],
      userTags: [],
    };

    this.formItemLayout = {
      labelCol: {
        span: 0,
      },
      wrapperCol: {
        span: 24,
      },
    };
  }

  componentDidMount() {}

  queryTagList = async () => {
    const { chatInfo } = this.props;
    const { code, data = [] } = await Api.queryUserTagList({
      tagType: 'user',
      groupId: chatInfo.id,
      pid: chatInfo.pid,
      numPerPage: 100,
    });
    if (code == 0) {
      this.setState({ userTags: data });
    }
  };

  queryTagForUserList = async () => {
    const { chatInfo } = this.props;
    const { code, data = [] } = await Api.queryTagForUserList({
      tagType: 'user',
      groupId: chatInfo.id,
      pid: chatInfo.pid,
    });
    if (code == 0) {
      const avalTag = data.filter(item => {
        return item.userTagFlg == 0;
      });
      this.setState({ tagList: avalTag });
    }
  };

  handleOk = async () => {
    const { activeTag, tagList = [] } = this.state;
    if (!activeTag.length) {
      message.error('请选择可标记标签');
      return false;
    }
    const list = activeTag.map(item => {
      const tag = tagList[item];
      return { tagId: tag.id, tagName: tag.tagName };
    });
    const { chatInfo } = this.props;
    const { code } = await Api.addUserTag({
      groupId: chatInfo.id,
      list,
      pid: chatInfo.pid,
    });
    if (code == 0) {
      message.success('提交成功');
      this.handleCancel();
    }
  };

  visilist = () => {
    this.setState({
      visible: true,
    });
    this.queryTagList();
    this.queryTagForUserList();
  };

  handleCancel = () => {
    this.setState({
      visible: false,
      activeTag: [],
      tagList: [],
      userTags: [],
    });
  };

  handleTagClick = tagIndex => {
    const { activeTag } = this.state;
    const idx = activeTag.indexOf(tagIndex);
    if (idx > -1) {
      activeTag.splice(idx, 1);
    } else {
      activeTag.push(tagIndex);
    }
    this.setState({ activeTag });
  };

  preRemoveTag = tag => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除标签 ${tag.tagName} 吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.removeTag(tag);
      },
    });
  };

  removeTag = async tag => {
    const { code } = await Api.deleteUserTag({ userTagId: tag.userTagId });
    if (code == 0) {
      this.queryTagList();
      this.queryTagForUserList();
    }
  };

  render() {
    const { visible, confirmLoading, tagList = [], activeTag = [], userTags = [] } = this.state;
    const { chatInfo = {} } = this.props;
    return (
      <Fragment>
        {chatInfo.chatFilterType != '11' && (
          <Tooltip title="患者标签">
            <div className={`${styles.chartHradBtn} ${styles.chbtn3}`} onClick={this.visilist} />
          </Tooltip>
        )}
        <Modal title="患者标签" visible={visible} destroyOnClose onOk={this.handleOk} confirmLoading={confirmLoading} onCancel={this.handleCancel}>
          <Form>
            {userTags.length ? (
              <div style={{ marginBottom: 10 }}>
                <div>已标记</div>
                <div>
                  {userTags.map((item, key) => {
                    return (
                      <Tag style={{ marginTop: 6, position: 'relative' }} key={key} closable color={PRIMARY_COLOR}>
                        <div className={styles.tagCloseBox} onClick={() => this.preRemoveTag(item)} />
                        {item.tagName}
                      </Tag>
                    );
                  })}
                </div>
              </div>
            ) : null}
            {tagList.length ? (
              <div>
                <div>可标记</div>
                <div>
                  {tagList.map((item, key) => {
                    return (
                      <Tag style={{ cursor: 'pointer', marginTop: 6 }} key={key} color={activeTag.indexOf(key) > -1 ? PRIMARY_COLOR : ''} onClick={() => this.handleTagClick(key)}>
                        {item.tagName}
                      </Tag>
                    );
                  })}
                </div>
              </div>
            ) : null}
          </Form>
        </Modal>
      </Fragment>
    );
  }
}

export default Index;
