@charset "utf-8";

// ant-design 渐变色板
@primary-color: #3f969d;
@primary-1: #f0fbf9;
@primary-2: #dbf6f1;
@primary-3: #bdeee6;
@primary-4: #97e5d8;
@primary-5: #6cdac7;
@primary-6: @primary-color;
@primary-7: #35b4a2;
@primary-8: #2b998d;
@primary-9: #227e77;
@primary-10: #186262;

@warn-color: #f57f17; // 醒目提示等
@danger-color: #f00; // 警告、错误等
@fail-color: @danger-color; // 失败等
@title-color: #404040; // 标题等
@text-color: #595959; // 文本
@desc-color: #919191; // 描述
@link-color: #4c9cdf; // 链接
@disable-color: #bfbfbf; // 禁用
@border-color: #e9e9e9; // 边框
@body-color: #f2f2f2; // 底色

// 单行省略号
.ellipsis() {
  overflow: hidden;
  word-wrap: normal;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 多行省略号
.ellipsisLn(@line) {
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: @line;
}

// 文字换行
.textBreak() {
  word-wrap: break-word;
  word-break: break-all;
}
