import React from 'react';
import { history } from 'umi';
import { Menu } from 'antd';

import '../style.less';

const { SubMenu } = Menu;

export default class MerchantMenu extends React.Component {
  constructor(props) {
    super(props);
    const { location } = props;
    const pathSnippets = location.pathname.split('/').filter(i => i);
    const selectedMenus = pathSnippets.map((_, index) => {
      return `/${pathSnippets.slice(0, index + 1).join('/')}`;
    });
    this.state = {
      current: selectedMenus[1] || selectedMenus[0],
      openKeys: [selectedMenus[0]],
    };
  }

  onOpenChange = openKeys => {
    const state = this.state;
    const latestOpenKey = openKeys.find(key => !(state.openKeys.indexOf(key) > -1));
    const latestCloseKey = state.openKeys.find(key => !(openKeys.indexOf(key) > -1));

    let nextOpenKeys = [];
    if (latestOpenKey) {
      nextOpenKeys = this.getAncestorKeys(latestOpenKey).concat(latestOpenKey);
    }
    if (latestCloseKey) {
      nextOpenKeys = this.getAncestorKeys(latestCloseKey);
    }
    if (openKeys.length && openKeys.length > 1 && openKeys[1].indexOf('http') == 0) {
      window.location.href = openKeys[1]; // eslint-disable-line
    }
    if (openKeys.length && openKeys.length == 1 && openKeys[0].indexOf('http') == 0) {
      window.location.href = openKeys[0]; // eslint-disable-line
    }
    this.setState({ openKeys: nextOpenKeys });
  };

  getAncestorKeys = key => {
    const map = {
      sub3: ['sub2'],
    };
    return map[key] || [];
  };

  render() {
    const { data = [], dispatch } = this.props;
    return (
      <Menu
        className="g-menu"
        mode="inline"
        openKeys={this.state.openKeys}
        selectedKeys={[this.state.current]}
        onOpenChange={this.onOpenChange}
        onClick={menu => {
          dispatch({ type: 'sample/save', payload: { queryParams: null } });
          this.setState({ current: menu.key });
          if (menu.key && menu.key.indexOf('http') == 0) {
            // window.open(menu.key, '_blank'); // eslint-disable-line
            window.location.href = menu.key;
          } else if (menu.key && menu.key.indexOf('#') > -1) {
            window.open(`${window.location.origin}${menu.key}`, '_blank');
          } else {
            history.push({ pathname: menu.key });
          }
        }}
      >
        {data.map(menu => {
          if (!menu.children || menu.children.length == 0) {
            return (
              <Menu.Item key={menu.url}>
                {/* <span className={`iconfont iconfont-${menu.icon}`} /> */}
                {menu.icon ? <img src={`./images/menu/${menu.icon}.png`} alt="" /> : ''}
                <span>{menu.name}</span>
              </Menu.Item>
            );
          } else {
            return (
              <SubMenu
                key={menu.url}
                title={
                  <span>
                    {/* <span className={`iconfont iconfont-${menu.icon}`} /> */}
                    {menu.icon ? <img src={`./images/menu/${menu.icon}.png`} alt="" /> : ''}
                    <span>{menu.name}</span>
                  </span>
                }
              >
                {menu.children.map(subMenu => {
                  return (
                    <Menu.Item key={subMenu.url}>
                      {/* <span className={`iconfont iconfont-${subMenu.icon}`} /> */}
                      {subMenu.icon ? <img src={`./images/menu/${subMenu.icon}.png`} alt="" /> : ''}
                      <span>{subMenu.name}</span>
                    </Menu.Item>
                  );
                })}
              </SubMenu>
            );
          }
        })}
      </Menu>
    );
  }
}
