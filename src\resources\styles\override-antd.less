@charset "utf-8";

@ant-prefix: ant;
@menu-bg: #2d666f;
@warn-color: #f57f17;

// 覆盖左侧菜单导航栏样式
@menu-prefix-cls: ~'@{ant-prefix}-menu';
@layout-prefix-cls: ~'@{ant-prefix}-layout';
@progress-prefix-cls: ~'@{ant-prefix}-progress';
@steps-prefix-cls: ~'@{ant-prefix}-steps';
@btn-prefix-cls: ~'@{ant-prefix}-btn';
@table-prefix-cls: ~'@{ant-prefix}-table';
@breadcrumb-prefix-cls: ~'@{ant-prefix}-breadcrumb';
@tab-prefix-cls: ~'@{ant-prefix}-tabs';
@dialog-prefix-cls: ~'@{ant-prefix}-modal';
@cascader-prefix-cls: ~'@{ant-prefix}-cascader';

@menu-border-right: @menu-bg;

//覆盖Modal confirm 样式
@modal-prefix-cls: ~'@{ant-prefix}-modal-body';

.@{menu-prefix-cls} {
  color: #f2f4f4;
  background: @menu-bg;

  &-item:active,
  &-submenu-title:active {
    color: #f2f4f4;
    background: rgba(255, 255, 255, 0.1);
  }
  &-item-active {
    &:hover {
      color: #f2f4f4 !important;
      background: rgba(255, 255, 255, 0.1);
    }
  }
  &-submenu-title {
    color: #f2f4f4;
    &:hover {
      color: #f2f4f4;
    }
  }
  &-item-selected {
    color: #f2f4f4;
    > a,
    > a:hover {
      color: #f2f4f4;
    }
  }

  &:not(&-horizontal) &-item-selected {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &-submenu {
    > .@{menu-prefix-cls} {
      background: @menu-bg;
    }
  }

  &-item-selected:hover {
    color: #fff;
  }

  &-inline {
    border-right: 1px solid @menu-border-right;
  }

  &-inline,
  &-vertical {
    .@{menu-prefix-cls}-submenu-title,
    .@{menu-prefix-cls}-item {
      font-size: 14px;
    }
  }

  &-inline&-sub {
    .@{menu-prefix-cls}-item {
      font-size: 12px;
    }
  }
}

.@{layout-prefix-cls} {
  .ant-layout-sider {
    background: @menu-bg;
  }
  &-content {
    display: flex;
    flex-direction: column;
  }
}

.@{progress-prefix-cls} {
  &-status-exception {
    .@{progress-prefix-cls}-bg {
      background-color: @warn-color;
    }
    .@{progress-prefix-cls}-text {
      color: @warn-color;
    }
    .@{progress-prefix-cls}-circle-path {
      stroke: @warn-color;
    }
  }
}

.@{steps-prefix-cls} {
  &-head-inner,
  &-title {
    font-size: 14px;
  }
}

// .@{btn-prefix-cls} {
//   height: 28px;
// }

.@{breadcrumb-prefix-cls} {
  font-size: 14px;
}

.@{modal-prefix-cls} {
  .ant-confirm-title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.75);
  }
}

.@{tab-prefix-cls} {
  &-bar {
    padding: 14px 4px 0;
    background: #fff;
    border: 0;
    margin: 0;
  }

  &-nav {
    .@{tab-prefix-cls}-tab {
      padding: 0;
      margin: 8px 20px;
    }
  }

  // &-content {
  //   display: flex;
  //   flex: auto;
  //   flex-direction: column;

  //   .@{tab-prefix-cls}-tabpane {
  //     display: flex;
  //     flex: auto;
  //     flex-direction: column;
  //   }
  // }
}

.@{dialog-prefix-cls} {
  top: 0;

  &-wrap {
    display: flex;
    flex-direction: column;
    justify-content: center;
    .ant-modal {
      top: 0;
    }
  }
}

.@{table-prefix-cls} {
  &-thead > tr > th {
    background: #f9f9f9;
  }
}

.@{cascader-prefix-cls} {
  &-menu-item-expand {
    padding: 7px 28px 7px 8px;
  }
}

// .@{ant-prefix}-input {
//   line-height: 1.5;
// }
