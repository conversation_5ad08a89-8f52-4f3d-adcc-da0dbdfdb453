@import '~antd/lib/style/themes/default.less';

.page-record-container {
  width: 100%;
  height: 100%;
  padding: 16px;
  position: relative;

  .page-content {
    background: #f8f8f8;
    height: 100%;

    .page-right {
      height: 100%;
      display: flex;
      display: -webkit-flex;
      flex-direction: column;
      -ms-flex-direction: column;
      position: relative;

      .chat-header {
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e9e9e9;
        padding: 0 30px;
      }

      .chat-content {
        flex: 1;
        padding: 30px 30px;
        overflow: auto;
        // max-height: 500px;

        .loadmore-container {
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 30px;

          .tips-nomore {
            color: grey;
          }
        }

        .spin-container {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .chat-content-notice {
          text-align: center;
          width: 100%;
          margin-bottom: 30px;
          color: rgba(0, 0, 0, 0.45);
          height: 20px;
          font-size: 14px;
        }

        .chat-content-item {
          display: flex;
          align-items: center;

          .msg-box {
            max-width: 60%;
            margin: 0 30px 30px;
            background: #ffffff;
            box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            word-break: break-all;
            position: relative;
          }

          .msg-container {
            max-width: 60%;
            // margin: 0 30px 30px;
            background: #ffffff;
            box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            word-break: break-all;
            position: relative;

            .msg-text {
              padding: 10px 20px;
            }

            .msg-audio {
              position: relative;
              padding: 10px 20px;
              display: flex;
              align-items: center;

              .audio-icon {
                height: auto;
                width: 10px;
              }

              .audio-duration {
                margin: 0 10px;
              }

              .audio-loading {
                position: absolute;
                right: -20px;
                top: 50%;
                transform: translateY(-50%);
              }
            }

            .msg-image {
              width: 300px;
              height: 200px;
              border-radius: 10px;
              display: flex;
              align-items: center;
              justify-content: center;

              img {
                height: 100%;
                display: inline-block;
                max-width: 280px;
                max-height: 180px;
              }
            }

            .msg-prescribe {
              width: 100%;
              font-size: 16px;
              color: #fff;
              display: flex;
              align-items: center;
              overflow: hidden;
              border-radius: 10px;

              .prescribe-text {
                div {
                  font-size: 14px;
                  overflow: hidden;
                  display: -webkit-box;
                  text-overflow: ellipsis;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                }

                .pre-title {
                  background: #3eceb6;
                  height: 40px;
                  display: flex;
                  align-items: center;
                  padding: 0 30px 0 20px;
                  justify-content: space-between;

                  .left-title {
                    display: flex;
                    align-items: center;
                  }

                  img {
                    width: 24px;
                    height: 24px;
                    margin-right: 8px;
                  }
                }

                .orange {
                  background: #ff8d00;
                }

                .dark {
                  background: #b2b2b2;
                }

                .pre-content {
                  display: flex;
                  flex-direction: column;
                  background: white;
                  color: #2d2d2d;
                  padding-bottom: 10px;

                  div {
                    width: 300px;
                    padding: 10px 40px 0 20px;
                  }
                }
              }
            }
          }

          &.left {
            flex-direction: row-reverse;
            justify-content: flex-end;

            .msg-container .arrow {
              content: ' ';
              position: absolute;
              top: 8px;
              left: -10px;
              width: 0;
              height: 0;
              border-top: 8px solid transparent;
              border-bottom: 8px solid transparent;
              border-right: 10px solid #ffffff;
            }

            .orange-arrow {
              border-right: 10px solid #ff8d00 !important;
            }

            .dark-arrow {
              border-right: 10px solid #b2b2b2 !important;
            }

            .msg-container {
              // .msg-prescribe {
              //   color: #3eceb6;
              // }
            }

            .msg-box {
              .rest-prescribe {
                color: white;
              }

              .arrow {
                position: absolute;
                top: 8px;
                left: -10px;
                width: 0;
                height: 0;
                border-top: 8px solid transparent;
                border-bottom: 8px solid transparent;
                border-right: 10px solid #3eceb6;
              }
            }
          }

          &.right {
            justify-content: flex-end;

            .msg-container {
              background: #3eceb6;
              color: #ffffff;

              .arrow {
                content: ' ';
                position: absolute;
                top: 8px;
                right: -10px;
                width: 0;
                height: 0;
                border-top: 8px solid transparent;
                border-bottom: 8px solid transparent;
                border-left: 10px solid #3eceb6;
              }

              .orange-arrow {
                border-left: 10px solid #ff8d00;
              }

              .dark-arrow {
                border-left: 10px solid #b2b2b2;
              }

              .msg-audio {
                flex-direction: row-reverse;

                .audio-loading {
                  left: -20px;
                }
              }
            }

            .msg-box {
              background: white;
            }
          }

          &.image-triangle {
            .msg-container {
              background: #fff;
            }

            .msg-container::after {
              border-right-color: #ffffff;
              border-left-color: #ffffff;
            }
          }
        }
      }

      .chat-bottom {
        height: 200px;
        border-top: 1px solid #e9e9e9;
        padding: 0 30px;

        .opt-group {
          height: 50px;
          display: flex;
          align-items: center;
          cursor: pointer;

          .opt-group-item {
            margin-right: 30px;
            display: flex;
            align-items: center;

            .icon {
              margin-right: 4px;
            }
          }
        }

        .send-container {
          margin-top: 10px;
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }

  .video-content {
    background: #f8f8f8;
    min-height: 650px;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .title {
      font-size: 30px;
      padding-bottom: 30px;
    }

    .video-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .report {
    position: relative;
    background: #3eceb6;
    border-radius: 8px;
    font-size: 15px;
    width: 300px;
    box-sizing: border-box;
    color: #989898;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;

    .pre-inqury-report {
      .report-head {
        padding: 12px;
        color: #fff;
        font-size: 17px;

        .pat-info {
          font-size: 13px;
          margin-top: 9px;
        }
      }

      .report-body {
        background-color: #fff;
        padding: 1px 12px 23px;

        .type {
          color: #2d2d2d;
          font-weight: 600;
          margin-bottom: 5px;
          margin-top: 14px;
          padding-left: 10px;
          position: relative;

          &:before {
            content: ' ';
            height: 15px;
            width: 2px;
            background-color: #3eceb6;
            position: absolute;
            left: 0;
            top: 4px;
          }
        }

        .des {
          margin-bottom: 6px;
        }
      }
    }
  }
}

.workstation-preview-box {
  position: fixed;
  z-index: 999999;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(233, 233, 233, 1);
  border-radius: 12px;
  box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.05);
  padding: 40px 20px;
  box-sizing: border-box;

  .btn-close {
    position: absolute;
    top: 8px;
    left: 8px;
    font-size: 16px;
  }

  .preview-img {
    width: 50vw;
    height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;

    & > img {
      max-width: 100%;
      max-height: 100%;
    }

    .btn-rotate {
      margin-top: 10px;
      font-size: 16px;
      display: block;
      position: absolute;
      bottom: 8px;
      z-index: 999;
    }
  }
}

.msgFooter {
  margin-top: 50px;
  text-align: center;
}
