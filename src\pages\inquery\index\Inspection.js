import React, { Component, Fragment } from 'react';
import { Radio, Input, Table, Empty, Button } from 'antd';
import * as Api from './api';

import styles from './right.less';

const { Search } = Input;

const AnasysisReport = props => {
  const { list } = props;
  const abnormalConfig = {
    0: { text: '', style: { color: 'green' } },
    1: { text: '偏低', style: { color: 'orange' } },
    2: { text: '偏高', style: { color: 'orange' } },
    3: { text: '危急低', style: { color: 'red' } },
    4: { text: '危急高', style: { color: 'red' } },
    5: { text: '异常', style: { color: 'red' } },
  };
  const tableColumns = [
    {
      title: '项目名称',
      dataIndex: 'itemName',
    },
    {
      title: '状态',
      dataIndex: 'abnormal',
      width: 75,
      key: 'abnormal',
      render: text => {
        const abn = abnormalConfig[text] || {};
        return <span style={abn.style || {}}>{abn.text}</span>;
      },
    },
    {
      title: '检验值(结果)',
      dataIndex: 'result',
    },
    {
      title: '单位',
      dataIndex: 'itemUnit',
    },
    {
      title: '参考区间',
      dataIndex: 'refRange',
    },
  ];
  return <Table columns={tableColumns} rowKey="reportId" dataSource={list} pagination={false} size="middle" />;
};

const CheckReport = props => {
  const { info = {} } = props;
  return (
    <div style={{ padding: '15px', background: '#f8f8f8' }}>
      {info.deptName || info.doctorName ? (
        <div style={{ color: '#000' }}>
          {info.deptName ? (
            <span>
              开方科室：<span style={{ color: '#888' }}>{info.deptName}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </span>
          ) : null}
          {info.deptName ? (
            <span>
              开方医生：<span style={{ color: '#888' }}>{info.doctorName}</span>
            </span>
          ) : null}
        </div>
      ) : null}
      <div style={{ color: '#000', marginTop: '5px' }}>
        检查部位：<span style={{ color: '#888' }}>{info.checkPart}</span>
      </div>
      <div style={{ color: '#000', marginTop: '5px' }}>
        检查所见：
        <span style={{ color: '#888' }} dangerouslySetInnerHTML={{ __html: info.checkSituation }} />
      </div>
      <div style={{ color: '#000', marginTop: '5px' }}>
        诊断意见：<span style={{ color: '#888' }}>{info.option}</span>
      </div>
    </div>
  );
};

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      list: [],
      sortBy: 1, // 1报告时间(默认) 2样本
      filterStr: '',
      allOpen: false, // 全部展开
    };
    this.isGetAll = false;
  }

  componentDidMount() {
    this.getInHosReportList();
  }

  getInHosReportList = async (detailFlag = 0, callback) => {
    const { userInfo, familyMemberCode } = this.props;
    const user = userInfo.filter(i => i.familyMemberCode === familyMemberCode);
    const { sortBy } = this.state;
    const { grid, pid } = user[0] || {};
    const { code, data = {} } = await Api.getInHosReportList({ pid, sortBy, grid, detailFlag });
    if (code == 0) {
      // 异常报告列表
      const excpList = [];
      const list = data.items || [];
      list.map(item => {
        if (!item.isExcption && item.type == 2 && item.inspectReportMpVo && item.inspectReportMpVo.items) {
          item.reportInfo = item.inspectReportMpVo.items;
          item.inspectReportMpVo.items.forEach(row => {
            if (row.abnormal != 0) {
              excpList.push(row);
            }
          });
        }
        return item;
      });
      list.unshift({ reportInfo: excpList, reportName: '异常报告', type: 2, open: false, reportId: '异常报告', isExcption: true });
      this.setState({ list: list || [] }, () => {
        if (typeof callback === 'function') {
          callback();
        }
      });
    }
  };

  onChange = e => {
    this.setState({ sortBy: e.target.value }, this.getInHosReportList);
  };

  setFilterStr = val => {
    this.setState({ filterStr: val });
  };

  getShowList = () => {
    const { list = [], filterStr = '' } = this.state;
    const showList = list.filter(item => {
      return !filterStr || (item.reportName || '').indexOf(filterStr) > -1;
    });
    return showList;
  };

  openLine = async idx => {
    const showList = this.getShowList();
    showList[idx].open = !showList[idx].open;
    const item = showList[idx];
    if (showList[idx].open && !item.reportInfo) {
      const { userInfo, familyMemberCode } = this.props;
      const user = userInfo.filter(i => i.familyMemberCode === familyMemberCode);
      const { pid, grid } = user[0] || {};
      const { code, data = {} } = await Api.getInHosReportDetail({ grid, pid, fyjg: item.fyjg, reportId: item.reportId, type: item.type });
      if (code == 0) {
        if (item.type == 2) {
          const { items = [] } = data;
          item.reportInfo = items;
        }
        if (item.type == 1) {
          item.reportInfo = data;
        }
      }
    }
    const { list = [] } = this.state;
    this.setState({ list });
  };

  getReportInfo = item => {
    if (!item.open) {
      return null;
    }
    if (item.type == 2) {
      return <AnasysisReport list={item.reportInfo || item.checkReportMpVo} />;
    }
    if (item.type == 1) {
      return <CheckReport info={item.reportInfo || item.checkReportMpVo} />;
    }
  };

  // 全部展开
  switchOpenAll = async () => {
    if (!this.isGetAll) {
      this.isGetAll = true;
      this.getInHosReportList(1, this.openAll);
    } else {
      this.openAll();
    }
  };

  openAll = () => {
    const { list = [], allOpen = false, isGetAll } = this.state;
    const showList = this.getShowList();
    for (let i = 0; i < showList.length; i++) {
      const item = showList[i];
      item.open = !allOpen;
    }
    this.setState({ list, allOpen: !allOpen });
  };

  render() {
    const showList = this.getShowList();
    const { allOpen = false } = this.state;
    return (
      <div className={styles.inspection}>
        <div className={styles.title}>
          <div>
            <span>排序：</span>
            <span>
              <Radio.Group onChange={this.onChange} defaultValue="1">
                <Radio.Button value="1">按报告时间</Radio.Button>
                <Radio.Button value="2">按样本</Radio.Button>
              </Radio.Group>
            </span>
            <span style={{ marginLeft: '30px' }}>
              <Button onClick={this.switchOpenAll}>全部{allOpen ? '收起' : '展开'}</Button>
            </span>
          </div>
          <div>
            <Search placeholder="输入项目名称或指标名称进行搜索" onSearch={this.setFilterStr} style={{ width: 280 }} />
          </div>
        </div>
        {showList.map((item, key) => {
          return (
            <Fragment key={item.reportId}>
              <div className={styles.title} onClick={() => this.openLine(key)}>
                <div className={styles.titleInfo}>
                  <span className={styles.titleName}>{item.reportName}</span>
                  {item.reportTime && (
                    <span style={{ color: '#888', fontSize: '12px' }}>
                      {item.type == '2' ? '报告' : '检查'}时间：{item.reportTime || ''}
                    </span>
                  )}
                </div>
                <div className={styles.listRight}>
                  {item.type == 3 ? (
                    <a href={item.reportUrl} target="_blank">
                      查看
                    </a>
                  ) : (
                    <span style={{ whiteSpace: 'nowrap' }}>{item.open ? '收起' : '展开'}</span>
                  )}
                  {/* <Icon type="down" /> */}
                </div>
              </div>
              {this.getReportInfo(item)}
            </Fragment>
          );
        })}
        {!showList.length ? <Empty description="暂无报告数据" /> : null}
      </div>
    );
  }
}

export default Index;
