import React, { Component } from 'react';
import { connect } from 'dva';
import { Select, Row, Modal, Alert, Col, Button, Table, DatePicker, Input, message, Radio, Tooltip, Cascader } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { history as router } from 'umi';
import moment from 'moment';
import * as Api from './api';
import styles from './index.less';
import { YBZT } from './_data';
import DetailModal from './detail/index';
const { Search, TextArea } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;
// 在文件顶部导入语句区域添加
import { provinceData } from './province'; // 确保路径正确

const FormItem = Form.Item;
// 在组件中使用前进行数据结构转换
const transformData = originalData => {
  return originalData.map(province => ({
    label: province.label,
    value: province.value,
    children: province.children.map(city => ({
      label: city.label,
      value: city.value,
      // 保留第三级数据但不展示（按需选择）
      // children: city.children
    })),
  }));
};
// DetailModal的connect
@connect(({ sampleDetail }) => ({
  visible: sampleDetail.detailVisible,
  data: sampleDetail.listData, // 使用正确数据源
  pagination: sampleDetail.pagination,
  products: sampleDetail.products || [], // 添加默认值
  subproducts: sampleDetail.subproducts || [], // 添加默认值
}))
@Form.create()
class Index extends Component {
  constructor(props) {
    super(props);
    //const { dispatch, institutionList = [], allPersonnel = [], allGroup = [], products = [], subproducts = [], listData = {}, btns = {}, menuType = '', menuTypeValue = '', queryParams } = props;
    this.state = {
      tableData: {}, // 病友数据
      pageSize: 10,
      institutionList: [],
      menuType: props.menuType || '', // 添加 menuType 状态
      cityOptions: [], // 城市列表初始为空
    };
    //获取Form表单数据

    // 表格表头和每列数据
    this.tableColumns = [
      {
        title: '包裹状态',
        dataIndex: 'status',
        render: text => {
          const expressMap = {
            S: '寄件成功',
            C: '取消寄件',
            P: '寄件异常',
          };
          return expressMap[text] || text; // 显示映射值或原值
        },
      },
      {
        title: '物流公司',
        dataIndex: 'expressType',
        render: text => {
          const expressMap = {
            1: '顺丰特快',
            2: '顺丰标快',
          };
          return expressMap[text] || text; // 显示映射值或原值
        },
      },
      {
        title: '​​物流单号',
        dataIndex: 'mailNo',
      },
      {
        title: '付款方式',
        dataIndex: 'payMethod',
        render: text => {
          const expressMap = {
            1: '寄付现结',
            2: '到付',
            3: '寄付月结',
          };
          return expressMap[text] || text; // 显示映射值或原值
        },
      },
      {
        title: '​​寄件人名字',
        dataIndex: 'senderName',
      },
      {
        title: '寄件人手机​',
        dataIndex: 'senderMobile',
      },
      {
        title: '寄件人省​',
        dataIndex: 'senderProvinceName',
      },
      {
        title: '寄件人市​',
        dataIndex: 'senderCityName',
      },
      {
        title: '寄件人详细地址​​',
        dataIndex: 'senderAddressDetail',
      },
      {
        title: '寄件下单时间​',
        dataIndex: 'createTime',
      },
      // {
      //   title: '寄件签收时间​',
      //   dataIndex: 'receiptTime',
      // },
      {
        title: '样本数量',
        dataIndex: 'num',
        // render: (list) => list ? list.length : 0
      },

      // {
      //       title: '​产品线​',
      //       dataIndex: 'productName',
      //     },
      //     {
      //       title: '子​产品线​',
      //       dataIndex: 'SubproductName',
      //     },
      {
        title: '操作',
        fixed: 'right',
        render: record => {
          return (
            <>
              {
                <a
                  onClick={() => {
                    // const values = form.getFieldsValue();
                    // dispatch({
                    //   type: 'sample/save',
                    //   payload: {
                    //     queryParams: {
                    //       ...values,
                    //       pageNum: currentPage,
                    //       inputData: queryParam.keyword,
                    //       numPerPage,
                    //     },
                    //   },
                    // });
                    // history.push({
                    //   pathname: '/union/samplemanage/detail',
                    //   search: queryString.stringify({ id: record.id, menuType }),
                    // });
                    // 在Index组件的详情点击事件中
                    this.props.dispatch({
                      type: 'sampleDetail/openDetail',
                      payload: {
                        record: {
                          ...record, // 传递整个记录对象
                          list: record.list || [], // 确保传递样本列表,
                        },
                      },
                    });
                    // 触发数据加载
                    // 触发数据加载
                    this.props.dispatch({
                      type: 'sampleDetail/fetchDetail',
                      payload: {
                        ...record, // 传递整个记录对象
                        list: record.list || [], // 确保传递样本列表
                      },
                    });
                    // window.open(`${window.location.href.split('#')[0]}#/union/samplemanage/detail?id=${record.id}&menuType=${menuType}`, '_blank');
                  }}
                >
                  详情
                </a>
              }
            </>
          );
        },
      },
    ];
  }

  componentDidMount() {
    this.getInstitutionList();
    this.getTableData();
    this.getproducts();
  }

  // 显示弹窗方法
  // showDetail = (record) => {
  //   this.props.dispatch({
  //     type: 'logistics/openDetail',
  //     payload: {
  //       record,
  //       page: 1,
  //       pageSize: 10
  //     }
  //   });
  // };
  getproducts = parentId => {
    this.props
      .dispatch({
        type: 'sampleDetail/getproducts',
        payload: {
          parentId,
          menuType: 'sub',
        },
      })
      .then(() => {
        // 异步获取后打印最新数据
      });
  };

  onSearch = value => {
    this.setState({ searchKeyword: value }, this.handleSearch);
  };
  getQueryParam = (pageNum = 1) => {
    const { form } = this.props;
    const values = form.getFieldsValue();
    const { searchKeyword } = this.state;

    const params = {
      ...values,
      //payMethod: values.payMethod ? parseInt(values.payMethod) : undefined,
      pageNum,
      numPerPage: this.state.pageSize,
      sampleNumber: searchKeyword,
    };

    // 处理日期范围参数
    if (values.createTime && values.createTime.length === 2) {
      params.mailStartTime = values.createTime[0]?.format('YYYY-MM-DD');
      params.mailEndTime = values.createTime[1]?.format('YYYY-MM-DD');
    }

    return params;
  };
  handleSearch = () => {
    this.getTableData(1);
  };

  handleReset = () => {
    this.props.form.resetFields();
    this.setState(
      {
        cityOptions: [],
        searchKeyword: '',
      },
      this.handleSearch,
    );
  };

  //  query = (pageNum, inputData, numPerPage = 10) => {
  //   const values = Form.getFieldsValue();

  //   // if (values.payDate) {
  //   //   values.payDateStart = values.payDate[0].format('YYYY-MM-DD');
  //   //   values.payDateEnd = values.payDate[1].format('YYYY-MM-DD');
  //   // }
  //   // if (values.sampleDate) {
  //   //   values.sampleDateStart = values.sampleDate[0].format('YYYY-MM-DD');
  //   //   values.sampleDateEnd = values.sampleDate[1].format('YYYY-MM-DD');
  //   // }
  //   // if (values.extfiled9) {
  //   //   values.extfiled9Start = values.extfiled9[0].format('YYYY-MM-DD');
  //   //   values.extfiled9End = values.extfiled9[1].format('YYYY-MM-DD');
  //   // }
  //   // if (values.createDate) {
  //   //   values.createDateStart = values.createDate[0].format('YYYY-MM-DD');
  //   //   values.createDateEnd = values.createDate[1].format('YYYY-MM-DD');
  //   // }
  //   // if (values.reportDate) {
  //   //   values.reportDateStart = values.reportDate[0].format('YYYY-MM-DD HH:mm:ss');
  //   //   values.reportDateEnd = values.reportDate[1].format('YYYY-MM-DD HH:mm:ss');
  //   // }
  //   // console.log('menutype', menuType)
  //   // if (menuType) {
  //   //   values.menuType = menuTypeValue || menuType;
  //   // }
  //   // setQueryParam({ ...values, keyword: inputData ?? queryParam.keyword });
  //   // dispatch({
  //   //   type: `sample/${menuType === 'sub' ? 'getsubsamplebypage' : 'getsamplebypage'}`,
  //   //   payload: { ...values, pageNum, numPerPage, keyword: inputData ?? queryParam.keyword },
  //   // });
  // };

  // 获取查询参数
  // getQueryParam = (pageNum = '',inputData) => {
  //   console.log('input',inputData)
  //   const { tableData = {}, pageSize = 10 } = this.state;
  //   const {
  //     form: { getFieldsValue },
  //   } = this.props;
  //   const value = getFieldsValue();
  //   const { sendTime = ['', ''] } = value;
  //   const {orderTime = ['', '']} = value;
  //   //下单时间
  //   if (orderTime[0] && orderTime[1]) {
  //     param.orderStartTime = orderTime[0] && moment(orderTime[0]).format('YYYY-MM-DD');
  //     param.orderEndTime = orderTime[1] && moment(orderTime[1]).format('YYYY-MM-DD');
  //     delete param.orderTime;}

  //     //签收时间
  //     const {receiptTime = ['', '']} = value;
  //     if (receiptTime[0] && receiptTime[1]) {
  //       param.receiptStartTime = receiptTime[0] && moment(receiptTime[0]).format('YYYY-MM-DD');
  //       param.receiptEndTime = receiptTime[1] && moment(receiptTime[1]).format('YYYY-MM-DD');
  //       delete param.receiptTime;
  //     }

  //   const param = {
  //     ...value,
  //     pageNum: pageNum || tableData.currentPage || 1,
  //     numPerPage: pageNum ? pageSize : tableData.totalCount,
  //     queryType: 2,
  //     orderBy: 'create_time',
  //     sort: 'desc',
  //     hisId: 242,
  //     keyword: inputData,
  //   };
  //   // 处理寄样时间
  //   // if (sendTime[0] && sendTime[1]) {
  //   //   param.mailStartTime = sendTime[0] && moment(sendTime[0]).format('YYYY-MM-DD');
  //   //   param.mailEndTime = sendTime[1] && moment(sendTime[1]).format('YYYY-MM-DD');
  //   //   delete param.sendTime;
  //   // }
  //   return param;
  // };

  // 获取样本寄送列表
  // getTableData = async (pageNum = 1, inputData) => {
  //   const param = this.getQueryParam(pageNum,inputData);

  //   console.log('param', param);
  //   const { code, data = {} } = await Api.queryMailOrderList(param);
  //   if (code == 0) {
  //     console.log('tableData', data);
  //     this.setState({ tableData: data });
  //   }
  // };

  getTableData = async (pageNum = 1) => {
    const { pageSize } = this.state;

    // console.log('pageNum', pageNum)
    // console.log('pageSize', pageSize)
    const params = {
      ...this.getQueryParam(pageNum),
      numPerPage: pageSize, // 确保传递当前pageSize
      pageNum,
    };
    console.log('params', params);
    try {
      const { code, data } = await Api.queryMailOrderList(params);
      if (code === 0) {
        this.setState({
          tableData: data,
          pagination: {
            current: data.currentPage,
            pageSize: data.numPerPage,
            total: data.totalCount,
          },
        });
      }
    } catch (error) {
      message.error('数据获取失败');
    }
  };

  // 获取样本寄送列表
  getInstitutionList = async () => {
    const { code, data = {} } = await Api.getinstitutionbylist();
    if (code == 0) {
      this.setState({ institutionList: data });
    }
  };

  // 表格每页行数改变
  onShowSizeChange = (c, size) => {
    this.setState(
      {
        pageSize: size,
      },
      () => {
        this.getTableData(1); // 重置到第一页
      },
    );
  };

  // 大于当前日期不能选
  disabledDate = time => {
    if (!time) {
      return false;
    }
    return time > moment();
  };

  render() {
    const { tableData = {}, pageSize, institutionList } = this.state;
    const {
      form,
      form: { getFieldDecorator },
    } = this.props;
    // const onSearch = value => {
    //   // setQueryParam({ ...queryParam, keyword: value });
    //   // query(1, value);
    // };
    return (
      <div className="g-page page-manage-sample-send">
        <Form className="g-query-box">
          <Row gutter={[16, 24]}>
            {/* 第一行 */}
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="下单时间" name="createTime">
                {getFieldDecorator('createTime')(<RangePicker style={{ width: '100%' }} format="YYYY-MM-DD" placeholder={['开始日期', '结束日期']} />)}
              </Form.Item>
            </Col>
            {/* <Col span={8} className={styles['col-item']}>
      <Form.Item label="签收时间">
        {getFieldDecorator('receiptTime')(
          <RangePicker 
            style={{ width: '100%' }}
            format="YYYY-MM-DD"
            placeholder={['开始日期', '结束日期']}
          />
        )}
      </Form.Item>
    </Col> */}

            {/* 第二行 */}
            {/* <Col span={8} className={styles['col-item']}>
    <Form.Item label="产品线" name="productId">
    {getFieldDecorator('productId', {
      initialValue: '' // 新增默认值设置
    })(
    <Select
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                    onChange={v => {
                      console.log(v);
                      this.getproducts(v);
                      form.setFieldsValue({ sonProductId: null });
                    }}
                  >
                    <Option value="">全部</Option>
                    {this.props.products.map(item => (
        <Option key={item.id} value={item.id}>{item.productName}</Option>
      ))}
                  </Select>
    )}
  </Form.Item>
    </Col>

    <Col span={8} className={styles['col-item']}>
    <Form.Item label="子产品线" name="sonProductId">
    {getFieldDecorator('sonProductId', {
      initialValue: '' // 新增默认值设置
    })(
    <Select
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                    // onChange={v => {
                    //   this.getproducts(v);
                    //   form.setFieldsValue({ sonProductId: null });
                    // }}
                  >
                    <Option value="">全部</Option>
                    {this.props.subproducts.map(item => (
                        <Option key={item.id} value={item.id}>{item.productName}</Option>
                    ))}
                  </Select>
    )}
  </Form.Item>
    </Col> */}

            {/* <Col span={8} className={styles['col-item']}>
   
<Form.Item label="寄件人省" name='senderProvince'>
  {getFieldDecorator('senderProvince')(
    <Select
                       placeholder="请选择省"
                       showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                       onChange={value => {
                         //this.getAllCity(v);

                         // 根据省份获取对应城市
                         const selectedProvince = provinceData.find(p => p.label === value);
                         this.setState({ 
                           cityOptions: selectedProvince ? selectedProvince.children : [] 
                         });
                         form.setFieldsValue({ senderCity: null });
                       }}
                     >
                       <Option value="">全部</Option>
                       {provinceData.map((v, i) => (
                         <Option value={v.label} key={i}>
                           {v.label}
                         </Option>
                       ))}
                     </Select>
  )}
</Form.Item>
    </Col> */}

            {/* <Col span={8} className={styles['col-item']}>
  <Form.Item label="寄件人市" name="senderCity">
    {getFieldDecorator('senderCity')(
      <Select
        placeholder="请先选择市"
        showSearch
        filterOption={(input, option) =>
          option.props.children.toLowerCase().includes(input.toLowerCase())
        }
      >
        <Option value="">全部</Option>
        {this.state.cityOptions.map(city => (
          <Option key={city.value} value={city.value}>
            {city.label}
          </Option>
        ))}
      </Select>
    )}
  </Form.Item>
</Col> */}

            <Col span={8} className={styles['col-item']}>
              <Form.Item label="付款方式" name="payMethod">
                {getFieldDecorator('payMethod', {
                  initialValue: '', // 新增默认值设置
                })(
                  <Select placeholder="请选择">
                    <Option value="">全部</Option>
                    <Option value="1">寄付现结</Option>
                    <Option value="2">到付</Option>
                    <Option value="3">寄付月结</Option>
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="包裹状态">
                {getFieldDecorator('status', {
                  initialValue: '', // 新增默认值设置
                })(
                  <Select placeholder="请选择">
                    <Option value="">全部</Option>
                    <Option value="S">寄件成功</Option>
                    <Option value="C">取消寄件</Option>
                    <Option value="P">寄件异常</Option>
                  </Select>,
                )}
              </Form.Item>

              <Form.Item label="样本状态">
                {getFieldDecorator('receiveStatus', {
                  initialValue: '', // 新增默认值设置
                })(
                  <Select placeholder="请选择">
                    <Option value="">全部</Option>
                    <Option value="1">已收样</Option>
                    <Option value="0">未收样</Option>
                  </Select>,
                )}
              </Form.Item>
            </Col>

            {/* 操作按钮 */}
            <Col span={8}>
              <Button type="primary" onClick={this.handleSearch}>
                查询
              </Button>
              <Button
                onClick={() => {
                  form.resetFields();
                  this.handleReset();
                }}
                style={{ marginLeft: 8 }}
              >
                重置
              </Button>
            </Col>
          </Row>
        </Form>
        <div className="container samplemanage-table">
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Search
              placeholder="请输入物流单号/寄件人名字/手机号码"
              allowClear
              onSearch={this.onSearch}
              enterButton
              value={this.state.searchKeyword}
              onChange={e => this.setState({ searchKeyword: e.target.value })}
              style={{ width: 380, marginBottom: 16 }}
            />
          </div>
          <div className="table-box">
            <div className="table-content">
              <Table
                key={tableData.currentPage}
                rowKey="id"
                dataSource={tableData.recordList || []}
                columns={this.tableColumns}
                scroll={{ x: 'max-content' }} // 新增的滚动配置
                pagination={{
                  total: tableData.totalCount || 0,
                  showTotal: total => `共 ${total} 条`,
                  onChange: pageNum => {
                    this.getTableData(pageNum);
                  },
                  current: tableData.currentPage || 0,
                  pageSize,
                  showQuickJumper: true,
                  showSizeChanger: true,
                  onShowSizeChange: this.onShowSizeChange,
                }}
              />
            </div>
          </div>
        </div>
        <DetailModal />
      </div>
    );
  }
}

export default Index;
