import React, { Component } from 'react';
import { connect } from 'dva';
import { Select, Row, Col, Button, Table, DatePicker, Input, message, Radio, Tooltip, Cascader } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { history as router } from 'umi';
import moment from 'moment';
import { download } from '@/utils/utils';

import { provinceData } from '../../union/province';
import * as Api from './api';
import styles from './index.less';

@connect()
@Form.create()
class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      tableData: {}, // 病友数据
      pageSize: 10,
    };
    // 表格表头和每列数据
    this.tableColumns = [
      {
        title: '家庭号',
        dataIndex: 'patHisNo',
      },
      {
        title: '姓名',
        dataIndex: 'name',
      },
      {
        title: '成员关系',
        dataIndex: 'consumeType',
      },
      {
        title: '就诊卡号',
        dataIndex: 'patCardNo',
      },
      {
        title: '联系方式',
        dataIndex: 'mobile',
      },
      // {
      //   title: '注册日期',
      //   dataIndex: 'createTime',
      // },
      {
        title: '省市区',
        render: record => `${record.pro}-${record.city}-${record.area}`,
      },
      {
        title: '家庭住址',
        dataIndex: 'address',
      },
      // {
      //   title: '登录状态',
      //   dataIndex: 'loginStatus',
      //   render: (text, { roleList: list = [] }) => {
      //     return list.map((item, key) => {
      //       return (
      //         <div key={key} className={styles.tableCol}>
      //           {item.loginStatus == 0 ? '未登录' : '已登录'}
      //         </div>
      //       );
      //     });
      //   },
      // },
      // {
      //   title: '婚姻状况',
      //   dataIndex: 'merrde',
      //   render: (text, { roleList: list = [] }) => {
      //     return list.map((item, key) => {
      //       return (
      //         <div key={key} className={styles.tableCol}>
      //           {item.merrde == 0 ? '未婚' : '已婚'}
      //         </div>
      //       );
      //     });
      //   },
      // },
    ];
  }

  componentDidMount() {
    this.getTableData();
  }

  // 获取查询参数
  getQueryParam = (pageNum = '') => {
    const { tableData = {}, pageSize = 10 } = this.state;
    const {
      form: { getFieldsValue },
    } = this.props;
    const value = getFieldsValue();
    const { createTime = ['', ''] } = value;
    const param = {
      ...value,
      pageNum: pageNum || tableData.currentPage || 1,
      numPerPage: pageNum ? pageSize : tableData.totalCount,
      queryType: 2,
      orderBy: 'create_time',
      sort: 'desc',
      hisId: 242,
    };
    // 处理注册时间和初诊时间
    if (createTime[0] && createTime[1]) {
      param.createTimeStart = createTime[0] && moment(createTime[0]).format('YYYY-MM-DD');
      param.createTimeEnd = createTime[1] && moment(createTime[1]).format('YYYY-MM-DD');
      delete param.createTime;
    }
    if (param.ssq && param.ssq[0]) {
      param.pro = param.ssq[0];
    }
    if (param.ssq && param.ssq[1]) {
      param.city = param.ssq[1];
    }
    if (param.ssq && param.ssq[2]) {
      param.area = param.ssq[2];
    }
    delete param.ssq;
    return param;
  };

  // 获取我的所有病友
  getTableData = async (pageNum = 1) => {
    const param = this.getQueryParam(pageNum);
    const { code, data = {} } = await Api.queryPatientList(param);
    if (code == 0) {
      this.setState({ tableData: data });
    }
  };

  // 表格每页行数改变
  onShowSizeChange = (c, size) => {
    this.setState({ pageSize: size }, this.getTableData);
  };

  // post下载
  dataExport = async () => {
    const param = this.getQueryParam();
    const { code, data = {}, msg } = await Api.dataExport(param);
    if (code == 0) {
      const { reportUrl = '' } = data;
      const a = document.createElement('a');
      a.href = reportUrl;
      a.target = '_blank';
      document.body.appendChild(a);
      a.click();
    }
  };

  // 大于当前日期不能选
  disabledDate = time => {
    if (!time) {
      return false;
    }
    return time > moment();
  };

  render() {
    const { tableData = {}, pageSize } = this.state;
    const {
      form,
      form: { getFieldDecorator },
    } = this.props;

    return (
      <div className="g-page page-">
        <Form className="g-query-box">
          <Row gutter={[16, 24]}>
            {/* <Col span={8} className={styles['col-item']}>
              <Form.Item label="注册时间">
                {getFieldDecorator('createTime')(<DatePicker.RangePicker allowClear disabledDate={this.disabledDate} format="YYYY-MM-DD" style={{ width: '100%' }} />)}
              </Form.Item>
            </Col>
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="登录状态">
                {getFieldDecorator('loginStatus')(
                  <Select placeholder="请选择">
                    <Select.Option value="">全部</Select.Option>
                    <Select.Option value="0">未登录</Select.Option>
                    <Select.Option value="1">已登录</Select.Option>
                  </Select>,
                )}
              </Form.Item>
            </Col> */}
            {/* <Col span={8} className={styles['col-item']}>
              <Form.Item label="婚姻状况">
                {getFieldDecorator('merry')(
                  <Select placeholder="请选择">
                    <Select.Option value="">全部</Select.Option>
                    <Select.Option value="0">未婚</Select.Option>
                    <Select.Option value="1">已婚</Select.Option>
                  </Select>,
                )}
              </Form.Item>
            </Col> */}
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="搜索病友">{getFieldDecorator('pidOrName')(<Input placeholder="请输入 家庭号/姓名" />)}</Form.Item>
            </Col>
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="省市区">
                {getFieldDecorator('ssq')(<Cascader placeholder="请选择" options={provinceData} fieldNames={{ label: 'label', value: 'label', children: 'children' }} changeOnSelect />)}
              </Form.Item>
            </Col>
            <Col span={16} className="text-right">
              <Button type="primary" onClick={() => this.getTableData()}>
                查询
              </Button>
              <Button
                onClick={() => {
                  form.resetFields();
                  this.getTableData();
                }}
              >
                重置
              </Button>
            </Col>
          </Row>
        </Form>
        <div className="container">
          <div className="g-table-opt-cell" style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={() => this.dataExport()}>
              数据导出
            </Button>
          </div>
          <Table
            key={tableData.currentPage}
            dataSource={tableData.recordList || []}
            columns={this.tableColumns}
            pagination={{
              total: tableData.totalCount || 0,
              showTotal: total => `共 ${total} 条`,
              onChange: pageNum => {
                this.getTableData(pageNum);
              },
              current: tableData.currentPage || 0,
              pageSize,
              showQuickJumper: true,
              showSizeChanger: true,
              onShowSizeChange: this.onShowSizeChange,
            }}
          />
        </div>
      </div>
    );
  }
}

export default Index;
