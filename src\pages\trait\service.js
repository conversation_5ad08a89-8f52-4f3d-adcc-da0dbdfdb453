import * as utils from '../../utils/utils';
/**
 * 问卷题目类型查询
 * @param {object} param
 */
export function getQuestionType(param) {
  return utils.request('/api/question/questiontype', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 问卷列表查询
 * @param {object} param
 */
export function getQuestionList(param) {
  return utils.request('/api/question/getquestionlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除某个问卷
 * @param {object} param
 */
export function deleteQuestionForid(param) {
  return utils.request('/api/question/deletequestionforid', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 问卷评分维度获取
 * @param {object} param
 */
export function getDimensionList(param) {
  return utils.request('/api/question/getdimensionlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 保存问卷评分维度
 * @param {object} param
 */
export function saveDimension(param) {
  return utils.request('/api/question/savedimension', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 删除问卷评分维度
 * @param {object} param
 */
export function deleteDimension(param) {
  return utils.request('/api/question/deletedimension', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 问卷参与列表查询
 * @param {object} param
 */
export function getQuestionCondition(param) {
  return utils.request('/api/question/getquestioncondition', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 问卷信息查询
 * @param {object} param
 */
export function getQuestionInfo(param) {
  return utils.request('/api/question/getquestionforid', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 问卷统计信息
 * @param {object} param
 */
export function getQuestionStatistic(param) {
  return utils.request('/api/question/getquestionstatistic', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 问卷用户信息查询
 * @param {object} param
 */
export function getUserInfo(param) {
  return utils.request('/api/question/getquestionuserinfo', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 问卷信息查询 -- 根据条件查询列表
 * @param {object} param
 */
export function getPatientList(param) {
  return utils.request('/api/question/getquestionuserinfolist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 新建或者编辑问卷
 * @param {object} param
 */
export function saveQuestion(param) {
  return utils.request('/api/question/savequestion', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 新建或者编辑问卷题目
 * @param {object} param
 */
export function saveTitleAndOption(param) {
  // 确保所有布尔值和数字参数都转换为字符串类型
  const processedParam = { ...param };
  
  // 处理常见的需要转换为字符串的字段
  const fieldsToStringify = ['fileFlg', 'required', 'has_attachment', 'optionType'];
  
  fieldsToStringify.forEach(field => {
    if (processedParam[field] !== undefined) {
      processedParam[field] = String(processedParam[field]);
    }
  });
  
  // 处理选项列表中的字段
  if (processedParam.jsonToOption && Array.isArray(processedParam.jsonToOption)) {
    processedParam.jsonToOption = processedParam.jsonToOption.map(option => {
      const newOption = { ...option };
      
      // 处理选项中的布尔值和数字字段
      // 不传递 enableSecondLevel 参数
      if (newOption.enableSecondLevel !== undefined) {
        delete newOption.enableSecondLevel;
      }
      if (newOption.optionType !== undefined) {
        newOption.optionType = String(newOption.optionType);
      }
      if (newOption.haveRemarkFrame !== undefined) {
        newOption.haveRemarkFrame = String(newOption.haveRemarkFrame);
      }
      
      return newOption;
    });
  }
  
  return utils.request('/api/question/savetitleandoption', {
    method: 'POST',
    body: utils.jsonToQueryString(processedParam),
  });
}

/**
 * 复制到草稿箱
 * @param {object} param
 */
export function copyQuestion(param) {
  return utils.request('/api/question/copyquestion', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除问卷题目
 * @param {object} param
 */
export function deleteTitleForid(param) {
  return utils.request('/api/question/deletetitleforid', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 发布问卷
 * @param {object} param
 */
export function releaseQuestion(param) {
  return utils.request('/api/question/releasequestion', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 根据问卷编号和题目编号查询所有就诊人答题记录
 * @param {object} param
 */
export function getQuestionAnsweruserList(param) {
  return utils.request('/api/question/getquestionansweruserlistbyid', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 批量导出问卷
 * @param {object} param
 */
export function exportSurvey(param = {}) {
  return utils.request('/api/question/getquestionexport', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 *  保存到草稿箱
 * @param {object} param
 */
export function saveQuestionToDrafts(param) {
  return utils.request('/api/question/savequestiontodrafts', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

// 投诉部分接口
/**
 * 首页-投诉类型列表查询
 * @param {object} param
 */
export function getComplainTypeList(param) {
  return utils.request('/api/complain/typepage', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 首页-新增时保存投诉类型
 * @param {object} param
 */
export function saveComplainType(param) {
  return utils.request('/api/complain/savetype', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 首页-修改投诉类型上下架状态
 * @param {object} param
 */
export function manageType(param) {
  return utils.request('/api/complain/managetype', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 类型详情列表页-查询投诉类型列表 (用户搜索下拉框)
 * @param {object} param
 */
export function getSelectTypeList(param) {
  return utils.request('/api/complain/typelist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 类型详情列表页-分页查询投诉记录
 * @param {object} param
 */
export function getComplainRecordPage(param) {
  return utils.request('/api/complain/recordpage', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 类型详情列表页-回复投诉
 * @param {object} param
 */
export function reply(param) {
  return utils.request('/api/complain/reply', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 具体投诉详情页-获取投诉详情
 * @param {object} param
 */
export function getComplainDetail(param) {
  return utils.request('/api/complain/details', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 科室结构树
 * @param {*} param
 */
export function departmentTree(param = {}) {
  // param.showMode = 'register';
  return utils.request('/api/deptinfo/getdepttree', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
// // 获取科室列表
// export function getDeptList(param) {
//   // param.showMode = 'register';
//   return utils.request('/api/deptinfo/getdeptlist', {
//     method: 'POST',
//     body: utils.jsonToQueryString({ ...param }),
//   });
// }
// 获取医生列表
export function getDoctorList(param) {
  // param.showMode = 'register';
  return utils.request('/api/doctorinfo/getdoctorlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 省中医满意度调查报表下载
 * @param {object} param
 */
export function exportAnwsersExcel(param) {
  return utils.request('/api/specialquestionnaire/exportanwsersexcel', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 省中医满意度调查报表下载
 * @param {object} param
 */
export function getQuestionExport(param) {
  return utils.request('/api/question/getQuestionExport', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 查询全部课程内容
 * @param {object} param
 */
export function courseList(param) {
  return utils.request('/api/course/getcourselist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 查询单个课程内容
 * @param {object} param
 */
export function courseDetail(param) {
  return utils.request('/api/course/getcourseforid', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 编辑课程
 * @param {object} param
 */
export function submitCourseDetail(param) {
  delete param.classTime;
  delete param.createTime;
  delete param.updateTime;
  delete param.minTime;
  delete param.maxTime;
  delete param.idSeq;
  return utils.request('/api/course/savecourse', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除课程
 * @param {object} param
 */
export function courseDelete(param) {
  return utils.request('/api/course/deletecourse', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 课程预约情况
 * @param {object} param
 */
export function courseReserveState(param) {
  return utils.request('/api/course/getcoursewomanlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取第三方平台url
 * @param {object} param
 */
export function getExtraMenu(param) {
  return utils.request('/api/hiond/getssourl', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 撤销进行中的问卷
 * @param {object} param
 */
export function revertPublish(param) {
  return utils.request('/api/question/revertpublish', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 新增财务缴费类型
 * @param {object} param
 */
export function addPayType(param) {
  return utils.request('/api/ext/financialpay/add', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取财务缴费类型列表
 * @param {object} param
 */
export function getPayList(param) {
  return utils.request('/api/ext/financialpay/page', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取财务缴费类型列表详情
 * @param {object} param
 */
export function getPayListDetail(param) {
  return utils.request('/api/ext/financialpay/detail', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取财务缴费数据汇总
 * @param {object} param
 */
export function getTotalData(param) {
  return utils.request('/api/ext/financialpay/statistics', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 财务缴费上下架
 * @param {object} param
 */
export function upOrDown(param) {
  return utils.request('/api/ext/financialpay/update', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 财务缴费编辑
 * @param {object} param
 */
export function editType(param) {
  return utils.request('/api/ext/financialpay/update', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 报表下载
 */
export function downloadFile(param) {
  return utils.request('/api/ext/downloadfinancialstatistics', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 缴费类型详情
 */
export function getTypeDetail(param) {
  return utils.request('/api/ext/financialpay/detail', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取住院预约当前商户后台账户能看到的科室列表
 */
export function getReserveDeptList(param) {
  return utils.request('/api/hospitalappointment/getdeptlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取住院预约该医院所有科室列表
 */
export function getReserveAllDeptList(param) {
  return utils.request('/api/hospitalappointment/getalldeptlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 获取住院预约申请记录列表
 */
export function getInhospitalList(param) {
  return utils.request('/api/hospitalappointment/getrecordbypage', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 处理住院预约申请
 */
export function handleInhospitalReserve(param) {
  return utils.request('/api/hospitalappointment/handlerecord', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 查询住院预约申请详情
 */
export function getInhospitalDetail(param) {
  return utils.request('/api/hospitalappointment/getrecorddetail', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 查询住院预约自动取消相关配置
 */
export function getAutoCancelConfig(param) {
  return utils.request('/api/hospitalappointment/getautocancelconfig', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 添加或修改自动取消预约的时限
 */
export function defineAutoCancelRule(param) {
  return utils.request('/api/hospitalappointment/defineautocancelrule', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 查询商户后台账户与住院科室权限管理关系
 */
export function getAccountDept(param) {
  return utils.request('/api/hospitalappointment/getaccountdept', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 编辑商户后台账户与住院科室权限管理关系
 */
export function editAccountDept(param) {
  return utils.request('/api/hospitalappointment/editaccountdept', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 查询该用户权限树
 */
export function permissionList(param) {
  return utils.request('/api/userpatient/userpermission', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 *  商户后台新建问卷时页面上是否展示给用户问卷所属项目组的选择
 */
export function showBelongTeamSelector(param) {
  return utils.request('/api/question/showbelongteamselector', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 *  保存问卷调查的处理意见
 */
export function onSaveHandleOpinion(param) {
  return utils.request('/api/question/modifyuseranswer', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
