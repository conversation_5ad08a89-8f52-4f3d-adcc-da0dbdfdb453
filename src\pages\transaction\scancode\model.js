import * as Api from './service';

export default {
  namespace: 'scanOrder',
  state: {
    hisBusTypeList: [],
    tableData: {},
    productList: [],
    doctorList: [],
    customerList: [],
    orderInfo: {},
  },
  reducers: {
    saveHisBusTypeList(state, { payload }) {
      return {
        ...state,
        hisBusTypeList: payload,
      };
    },
    saveTableData(state, { payload }) {
      return {
        ...state,
        tableData: payload,
      };
    },
    saveProductList(state, { payload }) {
      return {
        ...state,
        productList: payload,
      };
    },
    saveDoctorList(state, { payload }) {
      return {
        ...state,
        doctorList: payload,
      };
    },
    saveCustomerList(state, { payload }) {
      return {
        ...state,
        customerList: payload,
      };
    },
    saveOrderInfo(state, { payload }) {
      console.log(payload, '=====47');
      return {
        ...state,
        orderInfo: payload,
      };
    },
  },
  effects: {
    *getHisBusTypeList({}, { call, put }) {
      const params = { type: 'dna_gene_pay' };
      const { data } = yield call(Api.getHisBusTypeList, params);
      const ret = data.data;
      if (data.code == 0 && ret) {
        yield put({
          type: 'saveHisBusTypeList',
          payload: ret,
        });
      }
    },
    *getOrdersByPage({ payload }, { call, put }) {
      const { data } = yield call(Api.getOrdersByPage, payload);
      const ret = data.data;
      if (data.code == 0 && ret) {
        yield put({
          type: 'saveTableData',
          payload: ret,
        });
      }
    },
    *getByPage({ payload }, { call, put }) {
      const { data } = yield call(Api.getByPage, payload);
      const ret = data.data;
      if (data.code == 0 && ret) {
        yield put({
          type: 'saveProductList',
          payload: ret,
        });
      }
    },
    *findAllUser({ payload }, { call, put }) {
      const { data } = yield call(Api.findAllUser, payload);
      const ret = data.data;
      if (data.code == 0 && ret) {
        yield put({
          type: 'saveDoctorList',
          payload: ret,
        });
      }
    },
    *getByList({ payload }, { call, put }) {
      const { data } = yield call(Api.getByList, payload);
      const ret = data.data;
      if (data.code == 0 && ret) {
        yield put({
          type: 'saveCustomerList',
          payload: ret,
        });
      }
    },
    /* 查询订单详情 */
    *getOrderDetail({ payload: reqData }, { call, put }) {
      const data = yield call(Api.getOrderDetail, reqData);
      const ret = data.data.data;
      if (data.data.code == 0 && ret) {
        yield put({
          type: 'saveOrderInfo',
          payload: ret,
        });
      }
    },
    /* 退款 */
    *refund({ payload: reqData }, { call, put }) {
      const data = yield call(Api.refund, reqData);
      return data;
    },
    *exportOrder({ payload }, { call }) {
      yield call(Api.exportOrder, payload);
    },
  },
};
