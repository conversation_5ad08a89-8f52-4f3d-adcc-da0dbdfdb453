/* eslint-disable react/jsx-no-duplicate-props */
import React, { useEffect, useMemo, useState } from 'react';
import { history, connect } from 'umi';
import { Form, Row, Col, Input, Select, Space, Button, Table, DatePicker } from 'antd';
import { useAntdTable } from 'ahooks';
import { merge } from 'lodash';
import moment from 'moment';
import { filterObj, getDownload } from '@/utils/utils';
import queryString from 'query-string';
import * as Api from '../service';
import { NEED_SIGN, USER_STATUS, NEED_SIGN_MAP, USER_STATUS_MAP, SHOW_STATUS } from '../_data';
import './style.less';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const { RangePicker } = DatePicker;

const Index = props => {
  const [form] = Form.useForm();
  const { id = '' } = queryString.parse(props.location.search);
  const { permissionData = {} } = props;
  const { btns = {} } = permissionData;

  const [institutionList, setInstitutionList] = useState([]);

  useEffect(() => {
    getInstitution();
  }, []);

  const getInstitution = async () => {
    const data = await Api.getInstitution();
    if (data.code === 0) {
      setInstitutionList(data.data || []);
    }
  };

  const dataExport = async () => {
    // const url = '/merchant/api/institution/export';
    // const paramStr = queryString.stringify({
    //   ...filterObj(queryParam),
    // });
    // getDownload(`${url}?${paramStr}`);
  };

  const fetchList = async ({ current = 1, pageSize = 10 }) => {
    try {
      const { time = [], ...rest } = form.getFieldsValue();
      const params = {
        pageNum: current,
        numPerPage: pageSize,
        trainId: id,
        signDateStart: time[0] && moment(time[0]).format('YYYY-MM-DD'),
        signDateEnd: time[0] && moment(time[1]).format('YYYY-MM-DD'),
        ...(rest || {}),
      };

      const data = await Api.fetchDetailList({ ...params });

      return {
        total: Number(data.data.totalCount) || 0,
        list: data?.data?.recordList || [],
      };
    } catch (error) {
      console.log(error);
    }
  };

  const { loading, tableProps, search } = useAntdTable(fetchList, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
  });

  const tableRealProps = useMemo(
    () =>
      merge(tableProps, {
        pagination: {
          // showQuickJumper: true,
          // showSizeChanger: true,
          showTotal: total => `共 ${total} 条`,
        },
      }),
    [tableProps],
  );

  const { submit, reset } = search;

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
    },
    {
      title: '报名月数',
      dataIndex: 'amount',
    },
    {
      title: '缴费金额',
      dataIndex: 'totalFee',
      render: v => (v > 0 ? `${(v / 100).toFixed(2)}元` : ''),
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: v => USER_STATUS_MAP[v]?.label,
    },
    {
      title: '所在单位',
      dataIndex: 'organization',
    },
    {
      title: '所在科室',
      dataIndex: 'deptName',
    },
    {
      title: '职称',
      dataIndex: 'jobTitle',
      // render: v => SHOW_STATUS[v]
    },

    {
      title: '手机号码',
      dataIndex: 'phone',
    },
    {
      title: '身份证号码',
      dataIndex: 'idNo',
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
    },
    {
      title: '操作',
      dataIndex: 'id',
      fixed: 'right',
      render: (id, record) => (
        <div className="edit-btn">
          <span
            onClick={() => {
              history.push(`/trait/train/applydetail?id=${id}`);
            }}
          >
            详情
          </span>
        </div>
      ),
    },
  ];

  return (
    <div className="g-page p-product-list">
      <div className="g-query-box">
        <Form form={form} {...formItemLayout} labelWrap>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="time" label="报名日期">
                <RangePicker />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="status" label="状态" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...USER_STATUS]} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionId" label="所属客户" initialValue="">
                <Select
                  fieldNames={{ label: 'institutionName', value: 'id' }}
                  options={[{ institutionName: '全部', id: '' }, ...institutionList]}
                  showSearch
                  filterOption={(input, option) => (option?.institutionName ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="inputData" label="姓名" initialValue="">
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col span={16} style={{ textAlign: 'right' }}>
              <Space>
                <Button type="primary" onClick={submit} disabled={loading}>
                  查询
                </Button>
                <Button onClick={reset} disabled={loading}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="p-product-table">
        <div className="flex-box">
          {btns['/trait/train/export'] && (
            <Button type="primary" disabled={loading} onClick={dataExport}>
              批量导出
            </Button>
          )}
        </div>
        <Table rowKey="id" {...tableRealProps} loading={loading} columns={columns} scroll={{ x: 'max-content' }} />
      </div>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Index);
