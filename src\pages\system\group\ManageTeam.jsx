import React, { Component } from 'react';
import { Table, Button, Modal, Input, message, Radio } from 'antd';
import { history as router } from 'umi';
import * as Api from './api';

import styles from './index.less';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      q: '',
      teamList: [],
      currentPage: 1,
      totalCount: 0,
    };
    this.tableColumns = [
      {
        title: '管理团队',
        dataIndex: 'teamName',
        fixed: 'left',
        render: v => <div style={{ maxWidth: '200px' }}>{v}</div>,
      },
      {
        title: '管理员',
        dataIndex: 'adminsStr',
        fixed: 'left',
        render: v => <div style={{ maxWidth: '200px' }}>{v}</div>,
      },
      {
        title: '下属基础团队',
        dataIndex: 'childTeamsStr',
        render: v => <div style={{ maxWidth: '200px' }}>{v}</div>,
      },
      {
        title: '团队介绍',
        dataIndex: 'description',
        render: v => <div style={{ maxWidth: '200px' }}>{v}</div>,
      },
      {
        title: '操作',
        fixed: 'right',
        render: record => {
          return (
            <>
              <span
                className={styles.tableOperText}
                onClick={() => {
                  router.push({
                    pathname: '/system/group/editmanage',
                    query: { teamId: record.id },
                  });
                }}
              >
                编辑
              </span>
              <span className={styles.tableOperText} onClick={() => this.preDelete(record)}>
                删除
              </span>
            </>
          );
        },
      },
    ];
  }

  componentDidMount() {
    this.getTeamList();
  }

  getTeamList = async (pageNum = 1) => {
    const { q } = this.state;
    const param = {
      type: 2,
      numPerPage: 10,
      pageNum,
      name: q,
    };
    const { code, data = [] } = await Api.getTeamList(param);
    if (code == 0) {
      const { recordList = [], currentPage = 1, totalCount = 0 } = data;
      const teamList = recordList.map(item => {
        // 逗号拼接团队成员，管理员和附属团队
        const membersStr = (item.members || [])
          .map(memberItem => {
            return memberItem.name;
          })
          .join(',');
        const adminsStr = (item.admins || [])
          .map(managerItem => {
            return managerItem.name;
          })
          .join(',');
        const childTeamsStr = (item.childTeams || [])
          .map(childTeamItem => {
            return childTeamItem.teamName;
          })
          .join(',');
        return { ...item, membersStr, adminsStr, childTeamsStr };
      });
      this.setState({ teamList, currentPage, totalCount });
    }
  };

  setQ = e => {
    const q = e.target.value;
    this.setState({ q: q.trim() });
  };

  preDelete = record => {
    Modal.confirm({
      title: '确认删除',
      content: `是否确认删除团队：${record.teamName}`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.doDelete(record);
      },
    });
  };

  doDelete = async record => {
    const { code } = await Api.deleteBaseTeam({ teamId: record.id, type: '2' });
    if (code == 0) {
      this.getTeamList();
      message.success('删除成功');
    }
  };

  render() {
    const { teamList, currentPage, totalCount } = this.state;
    return (
      <>
        <div className={styles.flexLine} style={{ marginTop: 30 }}>
          <div className={styles.flexItem}>
            <Input style={{ width: 300 }} placeholder="请输入团队名称" onChange={e => this.setQ(e)} />
            <Button style={{ marginLeft: 30 }} type="primary" onClick={() => this.getTeamList()}>
              搜索
            </Button>
          </div>
          <div>
            <Button type="primary" onClick={() => router.push({ pathname: '/system/group/editmanage' })}>
              添加管理团队
            </Button>
          </div>
        </div>
        <Table
          style={{ marginTop: 16, backgroundColor: '#fff' }}
          dataSource={teamList}
          columns={this.tableColumns}
          rowKey="id"
          locale={{
            emptyText: '暂无数据',
          }}
          scroll={{ x: 'max-content' }}
          pagination={{
            total: totalCount || 0,
            showTotal: total => `共 ${total} 条`,
            onChange: pageNum => {
              this.getTeamList(pageNum);
            },
            current: currentPage,
          }}
        />
      </>
    );
  }
}

export default Index;
