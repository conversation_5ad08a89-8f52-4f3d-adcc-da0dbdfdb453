## models - 全局model

### model 规则
````
1. src/models/**/*.js 为 global model 
2. src/pages/**/models/**/*.js 为 page model 
3. global model 全量载入，page model 在 production 时按需载入，在 development 时全量载入 
4. page model 为 page js 所在路径下 models/**/*.js 的文件 
5. page model 会向上查找，比如 page js 为 pages/a/b.js，他的 page model 为 pages/a/b/models/**/*.js + pages/a/models/**/*.js，依次类推 
6. 约定 model.js 为单文件 model，解决只有一个 model 时不需要建 models 目录的问题，有 model.js 则不去找 models/**/*.js 
````

### 全局 model 规则 
- 约定全局公共 model 为 src/model/global.ts
- 其他需要模块化的全局默认加载的model，文件名与model内namespace保持一致，如：src/model/userInfo.js 对应为 namespace: 'userInfo'
