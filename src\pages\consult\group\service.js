import { post } from '@/utils/request';

export const fetchList = (param = {}) => post('/api/consult/area/group/list', { data: param });

export const addGroup = (param = {}) => post('/api/consult/area/group/add', { data: param });

export const updateGroup = (param = {}) => post('/api/consult/area/group/update', { data: param });

export const deleteGroup = (param = {}) => post('/api/consult/area/group/delete', { data: param });
