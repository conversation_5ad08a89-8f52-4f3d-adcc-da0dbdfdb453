/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useRef } from 'react';
import { connect, history } from 'umi';
import { Form, Row, Col, Select, DatePicker, Input, Button, Table, Divider, Popconfirm, message, Modal, Upload, Badge, Tooltip } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import moment from 'moment';
import queryString from 'query-string';
import loading from '@/components/loading/Loading';
import * as CONSTANT from '@/config/constant/constant';
import { HZMS, HTLX, HTZT, FKFS, JSFS, JSZQ } from '../contractmanage/_data';
import { YBZT } from './_data';
import { provinceData } from '../province';
import './index.less';

const FormItem = Form.Item;
const { Option } = Select;
const { RangePicker } = DatePicker;
const Dragger = Upload.Dragger;
const { Search, TextArea } = Input;
const MIN_TABLE_HEIGHT = 218;

const Index = props => {
  const { dispatch, institutionList = [], allPersonnel = [], allGroup = [], products = [], subproducts = [], listData = {}, btns = {}, menuType = '', menuTypeValue = '', queryParams } = props;
  const [form] = Form.useForm();
  const { recordList = [], currentPage = 1, numPerPage = 10, totalCount = 0 } = listData;
  const [cityOption, setCityOption] = useState([]);
  const [reportList, setReportList] = useState([]);
  const [settleShow, setSettleShow] = useState(false);
  const [settleUuid, setSettleUuid] = useState('');
  const [queryParam, setQueryParam] = useState({});

  const [importShow, setImportShow] = useState(false);
  const [importCode, setImportCode] = useState('');
  const [importUuid, setImportUuid] = useState('');

  const [showPay, setShowPay] = useState(false);
  const [payDetail, setPayDetail] = useState({});

  const [isFold, setIsFold] = useState(true);
  const [tableHeight, setTableHeight] = useState(MIN_TABLE_HEIGHT);
  const tableContent = useRef(null);

  useEffect(() => {
    if (tableContent.current) {
      setTableHeight(Math.max(tableContent.current.offsetHeight, MIN_TABLE_HEIGHT));
    }
  }, [tableContent.current, isFold]);

  const getAllCity = value => {
    let allCity = [];
    if (value) {
      provinceData
        .filter(province => province.label == value)
        .forEach(v => {
          allCity.push(...(v.children || []));
        });
    } else {
      provinceData.forEach(v => {
        allCity.push(...(v.children || []));
      });
    }
    setCityOption(allCity);
  };

  const query = (pageNum, inputData, numPerPage = 10) => {
    const values = form.getFieldsValue();
    if (values.payDate) {
      values.payDateStart = values.payDate[0].format('YYYY-MM-DD');
      values.payDateEnd = values.payDate[1].format('YYYY-MM-DD');
    }
    if (values.sampleDate) {
      values.sampleDateStart = values.sampleDate[0].format('YYYY-MM-DD');
      values.sampleDateEnd = values.sampleDate[1].format('YYYY-MM-DD');
    }
    if (values.extfiled9) {
      values.extfiled9Start = values.extfiled9[0].format('YYYY-MM-DD');
      values.extfiled9End = values.extfiled9[1].format('YYYY-MM-DD');
    }
    if (values.createDate) {
      values.createDateStart = values.createDate[0].format('YYYY-MM-DD');
      values.createDateEnd = values.createDate[1].format('YYYY-MM-DD');
    }
    if (values.reportDate) {
      values.reportDateStart = values.reportDate[0].format('YYYY-MM-DD HH:mm:ss');
      values.reportDateEnd = values.reportDate[1].format('YYYY-MM-DD HH:mm:ss');
    }
    if (menuType) {
      values.menuType = menuTypeValue || menuType;
    }
    
    setQueryParam({ ...values, keyword: inputData ?? queryParam.keyword });

    dispatch({
      type: `sample/${menuType === 'sub' ? 'getsubsamplebypage' : 'getsamplebypage'}`,
      payload: { ...values, pageNum, numPerPage, keyword: inputData ?? queryParam.keyword },
    });
  };
  const exportsample = () => {
    const values = form.getFieldsValue();
    if (values.payDate) {
      values.payDateStart = values.payDate[0].format('YYYY-MM-DD');
      values.payDateEnd = values.payDate[1].format('YYYY-MM-DD');
    }
    if (values.sampleDate) {
      values.sampleDateStart = values.sampleDate[0].format('YYYY-MM-DD');
      values.sampleDateEnd = values.sampleDate[1].format('YYYY-MM-DD');
    }
    if (values.extfiled9) {
      values.extfiled9Start = values.extfiled9[0].format('YYYY-MM-DD');
      values.extfiled9End = values.extfiled9[1].format('YYYY-MM-DD');
    }
    if (values.createDate) {
      values.createDateStart = values.createDate[0].format('YYYY-MM-DD');
      values.createDateEnd = values.createDate[1].format('YYYY-MM-DD');
    }
    if (values.reportDate) {
      values.reportDateStart = values.reportDate[0].format('YYYY-MM-DD HH:mm:ss');
      values.reportDateEnd = values.reportDate[1].format('YYYY-MM-DD HH:mm:ss');
    }
    dispatch({
      type: 'sample/exportsample',
      payload: { ...values, menuType: menuTypeValue || menuType },
    });
  };
  //导出唐筛数据
  const exporttssample = () => {
    const values = form.getFieldsValue();
    if (values.payDate) {
      values.payDateStart = values.payDate[0].format('YYYY-MM-DD');
      values.payDateEnd = values.payDate[1].format('YYYY-MM-DD');
    }
    if (values.sampleDate) {
      values.sampleDateStart = values.sampleDate[0].format('YYYY-MM-DD');
      values.sampleDateEnd = values.sampleDate[1].format('YYYY-MM-DD');
    }
    if (values.extfiled9) {
      values.extfiled9Start = values.extfiled9[0].format('YYYY-MM-DD');
      values.extfiled9End = values.extfiled9[1].format('YYYY-MM-DD');
    }
    if (values.createDate) {
      values.createDateStart = values.createDate[0].format('YYYY-MM-DD');
      values.createDateEnd = values.createDate[1].format('YYYY-MM-DD');
    }
    if (values.reportDate) {
      values.reportDateStart = values.reportDate[0].format('YYYY-MM-DD HH:mm:ss');
      values.reportDateEnd = values.reportDate[1].format('YYYY-MM-DD HH:mm:ss');
    }
    dispatch({
      type: 'sample/exportsample',
      payload: { ...values, menuType: menuTypeValue || menuType, tsFlg: 1 },
    });
  };
  const exportsampleall = () => {
    const values = form.getFieldsValue();
    if (values.payDate) {
      values.payDateStart = values.payDate[0].format('YYYY-MM-DD');
      values.payDateEnd = values.payDate[1].format('YYYY-MM-DD');
    }
    if (values.sampleDate) {
      values.sampleDateStart = values.sampleDate[0].format('YYYY-MM-DD');
      values.sampleDateEnd = values.sampleDate[1].format('YYYY-MM-DD');
    }
    if (values.extfiled9) {
      values.extfiled9Start = values.extfiled9[0].format('YYYY-MM-DD');
      values.extfiled9End = values.extfiled9[1].format('YYYY-MM-DD');
    }
    if (values.createDate) {
      values.createDateStart = values.createDate[0].format('YYYY-MM-DD');
      values.createDateEnd = values.createDate[1].format('YYYY-MM-DD');
    }
    if (values.reportDate) {
      values.reportDateStart = values.reportDate[0].format('YYYY-MM-DD HH:mm:ss');
      values.reportDateEnd = values.reportDate[1].format('YYYY-MM-DD HH:mm:ss');
    }
    dispatch({
      type: 'sample/exportsampleall',
      payload: { ...values, menuType: menuTypeValue || menuType },
    });
  };
  const batchPreviewReport = () => {
    const values = form.getFieldsValue();
    if (values.payDate) {
      values.payDateStart = values.payDate[0].format('YYYY-MM-DD');
      values.payDateEnd = values.payDate[1].format('YYYY-MM-DD');
    }
    if (values.sampleDate) {
      values.sampleDateStart = values.sampleDate[0].format('YYYY-MM-DD');
      values.sampleDateEnd = values.sampleDate[1].format('YYYY-MM-DD');
    }
    if (values.extfiled9) {
      values.extfiled9Start = values.extfiled9[0].format('YYYY-MM-DD');
      values.extfiled9End = values.extfiled9[1].format('YYYY-MM-DD');
    }
    if (values.createDate) {
      values.createDateStart = values.createDate[0].format('YYYY-MM-DD');
      values.createDateEnd = values.createDate[1].format('YYYY-MM-DD');
    }
    if (values.reportDate) {
      values.reportDateStart = values.reportDate[0].format('YYYY-MM-DD HH:mm:ss');
      values.reportDateEnd = values.reportDate[1].format('YYYY-MM-DD HH:mm:ss');
    }
    dispatch({
      type: 'sample/batchPreviewReport',
      payload: { ...values, menuType: menuTypeValue || menuType, keyword: queryParam.keyword },
    });
  };

 const getproducts = (parentId, isInit = false) => {
  dispatch({ 
    type: 'sample/getproducts', 
    payload: { parentId, menuType: menuTypeValue || menuType } 
  }).then(() => {
    if(isInit) {
      form.setFieldsValue({ sonProductId: undefined }); // 明确设置为非必填
    }
  });
};

  const getSampleReports = id => {
    dispatch({ type: 'sample/getSampleReports', payload: { id } }).then(res => {
      if (res?.length > 0) {
        if (res?.length == 1) {
          previewReport(res[0].reportName, res[0].reportUrl);
        } else {
          setReportList(res);
        }
      } else {
        message.warning('该样本暂无报告！');
      }
    });
  };

  const previewReport = (fileName, url) => {
    // dispatch({ type: 'sample/previewReport', payload: { fileName, url } });
    console.log(window.location.origin);
    window.open(`${window.location.origin}${CONSTANT.DOMAIN}/api/sample/previewReport?${queryString.stringify({ fileName, url })}`, '_blank');
  };

  const downloadsettle = () => {
    dispatch({ type: 'sample/downloadsettle', payload: { id: settleUuid } });
  };
  const downloadresult = () => {
    dispatch({ type: 'sample/downloadresult', payload: { id: importUuid } });
  };

  useEffect(() => {
    console.log('menuType', menuType);
   
    dispatch({ type: 'sample/getAllUser', payload: { menuType } });
    dispatch({ type: 'sample/getAllGroup', payload: { numPerPage: 9999, menuType } });
    // getproducts();
    getproducts(null, true); // 添加第二个参数表示初始化
    getAllCity();
    /**
     * Retrieves and sets up the list data based on query parameters
     * Sets form values if queryParams exist, otherwise resets the form
     * Extracts pagination parameters and triggers the query
     * @function getList
     * @private
     */
    const getList = () => {
      if (queryParams) {
        form.setFieldsValue(queryParams);
      } else {
        form.resetFields();
      }
      const { pageNum = 1, inputData = '', numPerPage = 10 } = queryParams || {};
      query(pageNum, inputData, numPerPage);
    };

    if (menuType === 'sub') {
      dispatch({ type: 'sample/getinstitutions' }).then(() => {
        getList(1);
      });
    } else {
      dispatch({ type: 'sample/getinstitutionbylist', payload: { menuType } }).then(() => {
        getList(1);
      });
    }

    // 添加resize监听
    const handleResize = () => {
      if (tableContent.current) {
        setTableHeight(Math.max(tableContent.current.offsetHeight, MIN_TABLE_HEIGHT));
      }
    };

    window.addEventListener('resize', handleResize); // 添加事件监听器

    return () => {
      window.removeEventListener('resize', handleResize); // 移除事件监听器
    };
  }, []);

  const updatesample = () => {
    dispatch({
      type: 'sample/updatePayInfo',
      payload: { ...payDetail, payStatus: payDetail.payStatus || '未知' },
    }).then(res => {
      if (res) {
        message.success('操作成功！', 1, () => {
          const { currentPage = 1 } = listData;
          query(listData.currentPage || 1);
          setShowPay(false);
          setPayDetail({});
        });
      }
    });
  };

  const deletesample = async id => {
    dispatch({
      type: 'sample/deletesample',
      payload: { id },
    }).then(res => {
      if (res) {
        message.success('删除成功！', 2, () => {
          const { currentPage = 1, recordList = [] } = listData;
          let pageNum = currentPage;
          if (status === 0 && recordList.length == 1 && currentPage > 1) {
            pageNum -= 1;
          }
          query(pageNum);
        });
      }
    });
  };

  const onSearch = value => {
    setQueryParam({ ...queryParam, keyword: value });
    query(1, value);
  };

  let columns = [
    {
      title: '样本编号',
      dataIndex: 'sampleNumber',
      fixed: 'left',
    },
    {
      title: '受检者姓名',
      dataIndex: 'sjzName',
      fixed: 'left',
    },
    {
      title: '送检医院',
      dataIndex: 'submitHospitalName',
      fixed: 'left',
    },
    {
      title: '送检模式',
      dataIndex: 'relContractName',
    },
    {
      title: '合作客户',
      dataIndex: 'institutionName',
    },
    {
      title: '二级合作客户',
      dataIndex: 'secendInstitutionName',
    },
    {
      title: '采样时间',
      dataIndex: 'sampleTime',
    },
    {
      title: '快递单号',
      dataIndex: 'expressnumber',
    },
    // {
    //   title: '合同编号',
    //   dataIndex: 'contractNo',
    //   fixed: 'left',
    // },
    {
      title: '合作模式',
      dataIndex: 'cooperationMode',
      render: v => HZMS[v],
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
      render: v => HTLX[v],
    },
    {
      title: '样本状态',
      dataIndex: 'extfiled1',
      render: v => YBZT[v],
    },
    {
      title: '身份证号码',
      dataIndex: 'sjzIdNum',
    },
    {
      title: '收样日期',
      dataIndex: 'extfiled9',
    },
    {
      title: '产品线',
      dataIndex: 'productName',
    },
    {
      title: '子产品线',
      dataIndex: 'sonProductName',
    },
    {
      title: '支付状态',
      dataIndex: 'payStatus',
    },
    {
      title: '支付日期',
      dataIndex: 'payDate',
    },
    {
      title: '备注',
      dataIndex: 'payRemark',
      render: v => (
        <Tooltip title={v}>
          <div className="ellipsis">{v}</div>
        </Tooltip>
      ),
    },
    {
      title: '报告状态',
      dataIndex: 'reportStatus',
      render: item => <Badge status={item == 1 ? 'success' : 'default'} text={item == 1 ? '已出' : '未出'} />,
    },
    {
      title: '报告发布时间',
      dataIndex: 'reportTime',
    },
    {
      title: '结算状态',
      dataIndex: 'settleStatus',
      render: item => (item == 1 ? '已结算' : '未结算'),
    },
    // {
    //   title: '结算金额',
    //   dataIndex: 'settleAmount',
    // },
    {
      title: '省份',
      dataIndex: 'sjzProvinceId',
    },
    {
      title: '城市',
      dataIndex: 'sjzCityId',
    },
    {
      title: '重采样',
      dataIndex: 'resamplingFlag',
      render: item => (item == 1 ? '是' : item == '0' ? '否' : '-'),
    },
    {
      title: '销售人员',
      dataIndex: 'salesPersonnelName',
    },
    {
      title: '录入人员',
      dataIndex: 'extfiled3',
    },
    {
      title: '录入时间',
      dataIndex: 'createTime',
    },
    {
      title: '所属分中心',
      dataIndex: 'dataOwner',
    },
    {
      title: '结算日期',
      dataIndex: 'settleDate',
    },
    {
      title: '最后更新人',
      dataIndex: 'extfiled7',
    },
    {
      title: '最后更新日期',
      dataIndex: 'updateTime',
    },
    {
      title: '操作',
      fixed: 'right',
      render: record => {
        return (
          <>
            {menuType !== 'custom' && (
              <a
                onClick={() => {
                  const values = form.getFieldsValue();
                  dispatch({
                    type: 'sample/save',
                    payload: {
                      queryParams: {
                        ...values,
                        pageNum: currentPage,
                        inputData: queryParam.keyword,
                        numPerPage,
                      },
                    },
                  });
                  history.push({
                    pathname: '/union/samplemanage/detail',
                    search: queryString.stringify({ id: record.id, menuType }),
                  });
                  // window.open(`${window.location.href.split('#')[0]}#/union/samplemanage/detail?id=${record.id}&menuType=${menuType}`, '_blank');
                }}
              >
                详情
              </a>
            )}
            {btns['/union/samplemanage/paystatus'] && (!menuType || menuType === 'team') && (
              <>
                <Divider type="vertical" />
                <a
                  onClick={() => {
                    setShowPay(true);
                    setPayDetail({
                      id: record.id,
                      payStatus: record.payStatus,
                      payDate: record.payDate,
                      payRemark: record.payRemark,
                    });
                  }}
                >
                  支付状态
                </a>
              </>
            )}
            {btns['/union/samplemanage/delete'] && menuType !== 'custom' && (
              <>
                <Divider type="vertical" />
                <Popconfirm title="确定要删除当前样本吗？" onConfirm={() => deletesample(record.id)}>
                  <a>删除</a>
                </Popconfirm>
              </>
            )}
            {record.reportStatus == 1 && btns['/union/samplemanage/exportreport'] ? (
              <>
                {menuType !== 'custom' && <Divider type="vertical" />}
                <a onClick={() => getSampleReports(record.id)}>下载报告</a>
              </>
            ) : null}
          </>
        );
      },
    },
  ];
  if (menuType && menuType !== 'team') {
    columns = columns.filter(item => {
      return !['dataOwner', 'secendInstitutionName'].includes(item.dataIndex);
    });
  }

  if (menuType && !['custom', 'sub', 'team'].includes(menuType)) {
    columns.pop();
  }

  if (menuType === 'custom') {
    columns = columns.filter(item => {
      return ![
        'relContractName',
        'institutionName',
        'contractNo',
        'cooperationMode',
        'contractType',
        'extfiled1',
        'settleStatus',
        'settleAmount',
        'sjzProvinceId',
        'sjzCityId',
        'resamplingFlag',
        'salesPersonnelName',
        'extfiled3',
        'createTime',
        'settleDate',
        'extfiled7',
      ].includes(item.dataIndex);
    });
  }
  if (menuType === 'sub') {
    columns = columns.filter(item => {
      return !['contractNo', 'cooperationMode', 'contractType', 'settleStatus', 'settleAmount', 'salesPersonnelName', 'settleDate'].includes(item.dataIndex);
    });
  }

  return (
    <div className="g-page page-samplemanage">
      <Form className="g-query-box" form={form}>
        <Row gutter={[16, 24]} className={isFold ? 'g-fold-box' : ''}>
          {menuType !== 'custom' && (
            <>
              <Col span={8} className="col-item">
                <FormItem label="合作客户" name="institutionId">
                  <Select
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                  >
                    <Option value="">全部</Option>
                    {(institutionList || []).map(v => (
                      <Option value={v.id} key={v.id}>
                        {v.institutionName}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
              {menuType !== 'sub' && (
                <>
                  <Col span={8} className="col-item">
                    <FormItem label="合作模式" name="cooperationMode">
                      <Select placeholder="请选择">
                        <Option value="">全部</Option>
                        {(Object.keys(HZMS) || []).map(key => (
                          <Option key={key} value={key}>
                            {HZMS[key]}
                          </Option>
                        ))}
                      </Select>
                    </FormItem>
                  </Col>
                  <Col span={8} className="col-item">
                    <FormItem label="合同类型" name="contractType">
                      <Select placeholder="请选择">
                        <Option value="">全部</Option>
                        {(Object.keys(HTLX) || []).map(key => (
                          <Option key={key} value={key}>
                            {HTLX[key]}
                          </Option>
                        ))}
                      </Select>
                    </FormItem>
                  </Col>
                </>
              )}
            </>
          )}
          {menuType !== 'person' && menuType !== 'custom' && menuType !== 'sub' && (
            <>
              <Col span={8} className="col-item">
                <FormItem label="销售人员" name="salesPersonId">
                  <Select
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                  >
                    <Option value="">全部</Option>
                    {(allPersonnel || []).map(v => (
                      <Option value={v.id} key={v.id}>
                        {v.name}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
              {menuType !== 'team' && (
                <Col span={8} className="col-item">
                  <FormItem label="所属团队" name="belongTeamId">
                    <Select
                      placeholder="请选择"
                      showSearch
                      filterOption={(input, option) => {
                        return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }}
                    >
                      <Option value="">全部</Option>
                      {allGroup.map((v, i) => (
                        <Option value={v.id} key={i}>
                          {v.teamName}
                        </Option>
                      ))}
                    </Select>
                  </FormItem>
                </Col>
              )}
            </>
          )}
          <Col span={8} className="col-item">
            <FormItem label="采样日期" name="sampleDate">
              <RangePicker format="YYYY-MM-DD" />
            </FormItem>
          </Col>
          <Col span={8} className="col-item">
            <FormItem label="收样日期" name="extfiled9">
              <RangePicker format="YYYY-MM-DD" />
            </FormItem>
          </Col>
          {menuType !== 'sub' && menuType !== 'custom' && (
            <Col span={8} className="col-item">
              <FormItem label="录入人员" name="extfiled2">
                <Select
                  placeholder="请选择"
                  showSearch
                  filterOption={(input, option) => {
                    return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  <Option value="">全部</Option>
                  {(allPersonnel || []).map(v => (
                    <Option value={v.id} key={v.id}>
                      {v.name}
                    </Option>
                  ))}
                </Select>
              </FormItem>
            </Col>
          )}
          {menuType !== 'custom' && (
            <Col span={8} className="col-item">
              <FormItem label="录入日期" name="createDate">
                <RangePicker format="YYYY-MM-DD" />
              </FormItem>
            </Col>
          )}
          {menuType !== 'sub' && menuType !== 'custom' && (
            <Col span={8} className="col-item">
              <FormItem label="送检医院" name="submitHospitalId">
                <Select
                  placeholder="请选择"
                  showSearch
                  filterOption={(input, option) => {
                    return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  <Option value="">全部</Option>
                  {(institutionList || []).map(v => (
                    <Option value={v.id} key={v.id}>
                      {v.institutionName}
                    </Option>
                  ))}
                </Select>
              </FormItem>
            </Col>
          )}
          {menuType !== 'sub' && menuType !== 'custom' && (
            <>
              <Col span={8} className="col-item">
                <FormItem label="采样省份" name="submitHospitalProvinceId">
                  <Select
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                    onChange={v => {
                      console.log(v);
                      getAllCity(v);
                      form.setFieldsValue({ city: null });
                    }}
                  >
                    <Option value="">全部</Option>
                    {provinceData.map((v, i) => (
                      <Option value={v.label} key={i}>
                        {v.label}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
              <Col span={8} className="col-item">
                <FormItem label="采样城市" name="submitHospitalCityId">
                  <Select
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => {
                      return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    }}
                  >
                    <Option value="">全部</Option>
                    {cityOption.map((v, i) => (
                      <Option value={v.label} key={i}>
                        {v.label}
                      </Option>
                    ))}
                  </Select>
                </FormItem>
              </Col>
            </>
          )}
          {!menuType ? (
            <Col span={8} className="col-item">
              <FormItem label="所属分中心" name="dataOwner" initialValue="PARENT">
                <Select placeholder="请选择">
                  <Option value="ALL">全部</Option>
                  <Option value="PARENT">总部</Option>
                  <Option value="XJ">新疆</Option>
                </Select>
              </FormItem>
            </Col>
          ) : null}
          <Col span={8} className="col-item">
            <FormItem label="产品线" name="productId">
              <Select
                placeholder="请选择"
                showSearch
                filterOption={(input, option) => {
                  return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                }}
                onChange={v => {
                  getproducts(v);
                  form.setFieldsValue({ sonProductId: null });
                }}
              >
                <Option value="">全部</Option>
                {products.map((v, i) => (
                  <Option value={v.id} key={i}>
                    {v.productName}
                  </Option>
                ))}
              </Select>
            </FormItem>
          </Col>
          <Col span={8} className="col-item">
            <FormItem label="子产品线" name="sonProductId" rules={[{ required: false }]}>
              
              <Select
                placeholder="请选择"
                showSearch
                filterOption={(input, option) => {
                  return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                }}
              >
                <Option value="">全部</Option>
                {subproducts.map((v, i) => (
                  <Option value={v.id} key={i}>
                    {v.productName}
                  </Option>
                ))}
              </Select>
            </FormItem>
          </Col>
          {menuType !== 'custom' && (
            <Col span={8} className="col-item">
              <FormItem label="重采样" name="resamplingFlag">
                <Select placeholder="请选择">
                  <Option value="">全部</Option>
                  <Option value="1">是</Option>
                  <Option value="0">否</Option>
                </Select>
              </FormItem>
            </Col>
          )}
          {menuType !== 'sub' && menuType !== 'custom' && (
            <Col span={8} className="col-item">
              <FormItem label="结算状态" name="settleStatus">
                <Select placeholder="请选择">
                  <Option value="">全部</Option>
                  <Option value="1">已结算</Option>
                  <Option value="0">未结算</Option>
                </Select>
              </FormItem>
            </Col>
          )}
          {menuType !== 'sub' && (
            <Col span={8} className="col-item">
              <FormItem label="报告发布时间" name="reportDate">
                <RangePicker format="YYYY-MM-DD HH:mm:ss" showTime={{ defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')] }} />
              </FormItem>
            </Col>
          )}
          <Col span={8} className="col-item">
            <FormItem label="报告状态" name="reportStatus">
              <Select placeholder="请选择">
                <Option value="">全部</Option>
                <Option value="1">已出</Option>
                <Option value="0">未出</Option>
              </Select>
            </FormItem>
          </Col>
          {menuType !== 'custom' && (
            <Col span={8} className="col-item">
              <FormItem label="样本状态" name="extfiled1">
                <Select placeholder="请选择">
                  <Option value="">全部</Option>
                  {(Object.keys(YBZT) || []).map(key => (
                    <Option key={key} value={key}>
                      {YBZT[key]}
                    </Option>
                  ))}
                </Select>
              </FormItem>
            </Col>
          )}
          <Col span={8} className="col-item">
            <FormItem label="支付状态" name="payStatus">
              <Select placeholder="请选择">
                <Option value="">全部</Option>
                <Option value="已支付">已支付</Option>
                <Option value="未支付">未支付</Option>
                {/* <Option value="未知">未知</Option> */}
              </Select>
            </FormItem>
          </Col>
          <Col span={8} className="col-item">
            <FormItem label="支付日期" name="payDate">
              <RangePicker format="YYYY-MM-DD" />
            </FormItem>
          </Col>
          <Col span={8} className="col-item">
            <FormItem label="平台" name="platform">
              <Select placeholder="请选择">
                <Option value="华大">华大</Option>
                <Option value="贝瑞">贝瑞</Option>
                <Option value="其他">其他</Option>
              </Select>
            </FormItem>
          </Col>
          {/* <Col span={8} className="col-item">
            <FormItem label="" name="keyword">
              <Input placeholder="请输入样本编号/受检者姓名/合同编号" />
            </FormItem>
          </Col> */}
          <Col span={isFold ? 8 : menuType === 'team' ? 8 : menuType === 'person' || !menuType ? 16 : 8} style={{ textAlign: 'right', display: 'block' }}>
            <Button
              onClick={() => {
                form.resetFields();
                getAllCity();
                query(1);
              }}
              style={{ marginLeft: 10 }}
            >
              重置
            </Button>
            <Button type="primary" onClick={() => query(1)} style={{ marginLeft: 10 }}>
              查询
            </Button>
            <Button type="link" onClick={() => setIsFold(!isFold)}>
              {isFold ? '展开' : '收起'}
            </Button>
          </Col>
        </Row>
      </Form>
      <div className="container samplemanage-table">
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Search
            placeholder={menuType !== 'custom' ? `请输入样本编号/受检者姓名${menuType ? '' : '/合同编号'}/快递单号` : '请输入样本编号/受检者姓名'}
            allowClear
            onSearch={onSearch}
            style={{
              width: 380,
              marginBottom: 16,
            }}
          />
          {!menuType || menuType === 'sub' || menuType === 'team' ? (
            <div className="g-table-opt-cell">
              {btns['/union/samplemanage/add'] ? (
                <Button
                  type="primary"
                  onClick={() => {
                    window.open(`${window.location.href.split('#')[0]}#/union/samplemanage/detail?menuType=${menuType}`, '_blank');
                    // dispatch({
                    //   type: 'sample/save',
                    //   payload: { detail: {} },
                    // });
                    // history.push('/union/samplemanage/detail');
                  }}
                >
                  添加样本
                </Button>
              ) : null}
              {/* <Button>刷新报告</Button> */}
              {btns['/union/samplemanage/batchexportreport'] ? <Button onClick={batchPreviewReport}>报告批量下载</Button> : null}
              <Button
                onClick={() => {
                  setImportShow(true);
                  setImportCode('');
                  setImportUuid('');
                }}
              >
                导入样本信息
              </Button>
              {btns['/union/samplemanage/settle'] && (!menuType || menuType === 'team') ? <Button onClick={() => setSettleShow(true)}>样本结算</Button> : null}
              {btns['/union/samplemanage/export'] ? <Button onClick={exportsample}>导出系统数据</Button> : null}
              {btns['/union/samplemanage/exportall'] ? <Button onClick={exportsampleall}>导出全部数据</Button> : null}
              {btns['/union/samplemanage/exportts'] ? <Button onClick={exporttssample}>导出唐筛数据</Button> : null}
            </div>
          ) : (
            <div className="g-table-opt-cell">{btns['/union/samplemanage/batchexportreport'] ? <Button onClick={batchPreviewReport}>报告批量下载</Button> : null}</div>
          )}
        </div>
        <div className="table-box" style={{ minHeight: MIN_TABLE_HEIGHT }}>
          <div className="table-content" ref={tableContent}>
            <Table
              columns={columns}
              dataSource={recordList}
              rowKey="id"
              scroll={{ x: 'max-content', y: tableHeight - 55 - 48 }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                current: currentPage,
                pageSize: numPerPage,
                total: totalCount,
                pageSizeOptions: [10, 25, 50, 100, 300, 500],
                showTotal: total => `共 ${total} 条`,
                onChange: (page, pageSize) => {
                  query(page, '', pageSize);
                },
              }}
            />
          </div>
        </div>
      </div>
      <Modal title="该样本编号下有多份报告，请选择" open={reportList.length > 0} onCancel={() => setReportList([])} footer={null}>
        <div className="report-pdf-list">
          {reportList.map((v, i) => (
            <div className="report-pdf-item" key={i} onClick={() => previewReport(v.reportName, v.reportUrl)}>
              {v.reportName}
            </div>
          ))}
        </div>
      </Modal>
      <Modal
        title="导入样本信息"
        open={importShow}
        onCancel={() => {
          setImportShow(false);
          setImportCode('');
          setImportUuid('');
        }}
        maskClosable={false}
        footer={null}
      >
        <div className="report-pdf-list">
          {importCode ? (
            <>
              {/* <div>
              请导入样本信息EXCEL表格，请准保数据的准确性。如果您还没有模板，请<a href="/merchant/api/sample/downloadTemplate">点击下载</a>
            </div> */}
              <div style={{ width: '380px', height: '194px' }}>
                <Dragger
                  name="file"
                  showUploadList={false}
                  action={`${CONSTANT.DOMAIN}/api/sample/upload-sample?code=${importCode}`}
                  onChange={info => {
                    loading.show();
                    const { response } = info.file;
                    if (response) {
                      loading.destroy();
                      if (response.code == 0) {
                        message.success('结算成功！', 1.5, () => {
                          setImportShow(false);
                          setImportCode('');
                          setImportUuid('');
                          query(1);
                        });
                      } else if (response.code == -1) {
                        setImportUuid(response.msg);
                        query(1);
                      } else {
                        message.error(response.msg);
                      }
                    }
                  }}
                >
                  <div>
                    <InboxOutlined style={{ fontSize: 40, color: '#3F969D' }} />
                  </div>
                  <p style={{ marginTop: 24 }} className="ant-upload-text">
                    点击这里批量导入样本信息
                  </p>
                </Dragger>
              </div>
              {importUuid ? (
                <div className="settle-error">
                  导入样本信息完成，存在未导入成功的样本
                  <Button type="link" onClick={downloadresult}>
                    下载详情
                  </Button>
                </div>
              ) : null}
            </>
          ) : (
            <>
              <div>目前仅支持已录入样本的批量信息更变。请选择要导入的产品线，一次只能导入相同模板的样本信息数据 </div>
              <div>
                <Button type="primary" onClick={() => setImportCode('NIPTJHBB')}>
                  NITP&NIPT-puls
                </Button>
              </div>
            </>
          )}
        </div>
      </Modal>
      <Modal
        title="样本结算"
        open={settleShow}
        onCancel={() => {
          setSettleShow(false);
          setSettleUuid('');
        }}
        maskClosable={false}
        footer={null}
      >
        <div className="report-pdf-list">
          <div>
            请导入要结算的样本编号EXCEL表格，请准保数据的准确性。如果您还没有模板，请<a href="/merchant/api/sample/downloadTemplate">点击下载</a>
          </div>
          <div style={{ width: '380px', height: '194px' }}>
            <Dragger
              name="file"
              showUploadList={false}
              action={`${CONSTANT.DOMAIN}/api/sample/upload-batch-settle`}
              onChange={info => {
                loading.show();
                const { response } = info.file;
                if (response) {
                  loading.destroy();
                  if (response.code == 0) {
                    message.success('结算成功！', 1.5, () => {
                      setSettleShow(false);
                      setSettleUuid('');
                      query(1);
                    });
                  } else if (response.code == -1000) {
                    setSettleUuid(response.msg);
                  } else {
                    message.error(response.msg);
                  }
                }
              }}
            >
              <div>
                <InboxOutlined style={{ fontSize: 40, color: '#3F969D' }} />
              </div>
              <p style={{ marginTop: 24 }} className="ant-upload-text">
                点击这里批量上传样本编号信息
              </p>
            </Dragger>
          </div>
          {settleUuid ? (
            <div className="settle-error">
              批量结算失败，存在不存在的编号或已结算的编号
              <Button type="link" onClick={downloadsettle}>
                下载详情
              </Button>
            </div>
          ) : null}
        </div>
      </Modal>
      <Modal
        title="支付状态"
        open={showPay}
        onCancel={() => {
          setShowPay(false);
          setPayDetail({});
        }}
        onOk={updatesample}
      >
        <div className="sample-pay-detail">
          <Row gutter={[0, 24]}>
            <Col span={24}>
              <span className="query-lable">支付状态：</span>
              <Select
                placeholder="请选择"
                allowClear
                value={payDetail.payStatus}
                onChange={v => {
                  if (v !== '已支付') {
                    setPayDetail({ ...payDetail, payStatus: v, payDate: null });
                  } else {
                    setPayDetail({ ...payDetail, payStatus: v });
                  }
                }}
              >
                <Option value="已支付">已支付</Option>
                <Option value="未支付">未支付</Option>
              </Select>
            </Col>
            <Col span={24}>
              <span className="query-lable">支付日期：</span>
              <DatePicker format="YYYY-MM-DD" allowClear value={payDetail.payDate ? moment(payDetail.payDate) : null} onChange={v => setPayDetail({ ...payDetail, payDate: v.format('YYYY-MM-DD') })} />
            </Col>
            <Col span={24}>
              <span className="query-lable">备注：</span>
              <TextArea
                placeholder="请输入"
                rows={5}
                value={payDetail.payRemark}
                onChange={e => {
                  setPayDetail({ ...payDetail, payRemark: e.target.value });
                }}
              />
            </Col>
          </Row>
        </div>
      </Modal>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
