import React, { Component, Fragment } from 'react';
import { connect } from 'dva';
import { Tabs, Table, Button, Modal, message, Radio, Input } from 'antd';
import * as Api from './api';

import styles from './index.less';

const { TextArea } = Input;

class Index extends Component {
  constructor(prop) {
    super(prop);
    console.log(prop);
    this.state = {
      tabs: [],
      tabsModal: [],
      activeTab: 0,
      fastList: [],
      modalType: '',
      periodId: prop.roleType === 'doc' ? '20000' : '10000',
      editPeriodId: '',
    };

    this.tableColumns = [
      {
        title: '回复内容',
        dataIndex: 'content',
        // width: '60%',
      },
      // {
      //   title: '阶段',
      //   dataIndex: 'periodName',
      // },
      // {
      //   title: '使用量',
      //   dataIndex: 'questionCount',
      // },
      {
        title: '操作',
        render: record => {
          return (
            <Fragment>
              <span
                className={styles.tableOperText}
                onClick={() => {
                  this.preEditTag(record.fastQueId, record.periodId);
                }}
              >
                编辑
              </span>
              <span className={styles.tableOperText} onClick={() => this.preDelete(record.fastQueId)}>
                删除
              </span>
            </Fragment>
          );
        },
      },
    ];
  }

  componentDidMount() {
    // this.getBusType();
    this.getFastList(); // 查询全部
  }

  getBusType = async () => {
    const { code, data = [] } = await Api.getBusType({
      type: this.props.roleType === 'doc' ? 1 : 0,
    });
    if (code == 0) {
      this.setState({ tabsModal: data });
      this.setState({ tabs: [{ dictKey: '', dictValue: '全部' }].concat(data) });
      this.getFastList(); // 查询全部
    }
  };

  getFastList = async e => {
    const { activeTab, tabs, periodId } = this.state;
    const tabIdx = e || activeTab;
    const idx = tabIdx * 1;
    const param = {
      fastType: 'answer',
      periodId,
      periodName: '全部',
      numPerPage: 1000,
      pageNum: 1,
    };
    const { code, data = {} } = await Api.getFastList(param);
    if (code == 0) {
      const { recordList = [] } = data;
      this.setState({ fastList: recordList });
    }
  };

  changeTab = e => {
    this.setState({ activeTab: e });
    this.getFastList(e);
  };

  handleSubmit = async () => {
    const { fastQueId, editPeriodId, content, modalType, periodId } = this.state;
    // if (!editPeriodId) {
    //   message.error('请选择阶段');
    //   return false;
    // }
    if (!content) {
      message.error('请输入快捷回复');
      return false;
    }
    if (modalType == 'add') {
      const param = {
        fastType: 'answer',
        periodId,
        content,
      };
      const { code, msg } = await Api.addFastQuestion(param);
      if (code == 0) {
        message.success('添加成功');
        this.setState({ modalType: '', editPeriodId: '', content: '' });
        this.getFastList(); // 查询全部
      } else {
        message.error(msg || '添加失败');
      }
    } else if (modalType == 'edit') {
      const param = {
        fastQueId,
        content,
        periodId,
      };
      const { code, msg } = await Api.updateFastQuestion(param);
      if (code == 0) {
        message.success('修改成功');
        this.setState({ modalType: '', editPeriodId: '', content: '' });
        this.getFastList(); // 查询全部
      } else {
        message.error(msg || '修改失败');
      }
    }
  };

  preDelete = id => {
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除该内容?',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.doDelete(id);
      },
    });
  };

  doDelete = async id => {
    const { code, msg } = await Api.deleteFastQuestion({ fastQueId: id });
    if (code == 0) {
      message.success('删除成功');
      this.getFastList();
    }
  };

  preEditTag = async (id, perId) => {
    const { code, data = {} } = await Api.queryFastInfo({ fastQueId: id }); // 查详情
    if (code == 0) {
      this.setState({ fastQueId: id, periodId: perId, content: data.content, modalType: 'edit' });
    }
  };

  render() {
    const { tabs = [], tabsModal = [], fastList = [], modalType, periodId, content } = this.state;
    return (
      <div className={styles.connect}>
        <div className={styles.flexLine}>
          <div className={styles.flexItem}>
            <Button type="primary" onClick={() => this.setState({ modalType: 'add' })}>
              添加快捷回复
            </Button>
          </div>
        </div>
        {/* {
          tabs.length > 0 ?
            <Tabs defaultActiveKey="0" onChange={this.changeTab} style={{ marginTop: 12 }}>
              {
                tabs.map((item, key) => {
                  return (
                    <Tabs.TabPane tab={item.dictValue} key={key} />
                  );
                })
              }
            </Tabs> : null
        } */}
        <Table
          style={{ marginTop: 16 }}
          dataSource={fastList}
          columns={this.tableColumns}
          rowKey="fastQueId"
          locale={{
            emptyText: '暂无数据',
          }}
          pagination={false}
        />
        <Modal
          title={modalType === 'add' ? '添加快捷回复' : '编辑快捷回复'}
          visible={modalType === 'add' || modalType === 'edit'}
          onOk={this.handleSubmit}
          onCancel={() => this.setState({ modalType: '', content: '' })}
          destroyOnClose
        >
          {/* <div className={styles.modalLine}>
            <div className={styles.modalLabel}>阶段：</div>
            <Radio.Group defaultValue={periodId.toString()} onChange={(e) =>{this.setState({editPeriodId: e.target.value});}} buttonStyle="solid">
              {
                 tabsModal.map((item, key) => {
                  return (
                    <Radio.Button
                      value={item.dictKey}
                      name={item.periodName}
                      key={key}
                      style={{ marginTop: 10 }}
                    >
                      {item.dictValue}
                    </Radio.Button>
                  );
                })
              }
            </Radio.Group>
          </div> */}
          <div className={styles.modalLine} key={modalType}>
            <div className={styles.modalLabel}>快捷回复：</div>
            <div key={modalType}>
              <TextArea
                placeholder="请输入智能回复的内容，限500字内"
                defaultValue={content}
                autosize={{ minRows: 2 }}
                maxLength={500}
                onBlur={e => {
                  this.setState({ content: e.target.value });
                }}
              />
            </div>
          </div>
        </Modal>
      </div>
    );
  }
}

export default connect()(Index);
