@charset "utf-8";

@import '../../resources/styles/mixins';

.hc-core {
  .hc-core-img {
    position: relative;
    max-width: 375px;
    max-height: 160px;
    min-width: 100px;
    min-height: 100px;
    overflow: hidden;
    display: inline-block;
    .hc-core-file {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      z-index: 1;
      opacity: 0;
      cursor: pointer;
    }

    .hc-core-view {
      max-width: 375px;
      max-height: 160px;
    }

    .hc-core-fire {
      display: none;
    }
  }

  .hc-core-mask {
    position: fixed;
    background: rgba(0, 0, 0, 0.4);
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1;
  }
  .hc-core-clip {
    border-radius: 4px;
    background: #fff;
    padding: 24px;
    min-width: 350px;
    .hc-core-clip-title {
      color: @primary-color;
      font-size: 18px;
      font-weight: bold;
    }

    .hc-core-clip-tip {
      margin-top: 16px;
    }
    .hc-core-clip-oper {
      margin-top: 16px;
      width: 750px;
      height: 320px;

      .hc-clip-cropper {
        width: 750px;
        height: 320px;
      }
    }
    .hc-core-clip-view {
      margin-top: 16px;
      display: flex;

      .core-clip-view-img {
        width: 375px;
        height: 160px;
        overflow: hidden;
        img {
          max-width: 375px;
          max-height: 160px;
        }
      }
      .core-clip-view-btn {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: flex-end;
      }
    }
  }
}
