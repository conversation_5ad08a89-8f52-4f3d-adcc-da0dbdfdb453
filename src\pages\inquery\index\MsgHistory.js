import React, { Component, Fragment } from 'react';
import { List, Modal, Input, Button, Empty, Select, message } from 'antd';
import MsgVideoWhite from '@/assets/message-video.png';
import * as Api from './api';
import styles from './charteditor.less';
import PubSub from 'pubsub-js';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      msgHistory: {},
      filter: '',
      queryFlg: 'xcx',
      deletePrompt: false,
      messageId: '',
    };
  }

  componentDidMount() {
    this.getChatMsgHistory();
  }

  componentWillReceiveProps(props) {
    if (props.show) {
      this.getChatMsgHistory();
    }
  }

  getChatMsgHistory = async (pageFlg = 0) => {
    const { chatInfo = {} } = this.props;
    const { msgHistory = {}, queryFlg } = this.state;
    const { currentPage = 1 } = msgHistory;

    const { code, data = {} } = await Api.queryChatHistoryRecord({
      groupId: chatInfo.id,
      numPerPage: 100,
      pageNum: currentPage + pageFlg,
      queryFlg,
    });
    if (code == 0) {
      this.setState({ msgHistory: data });
    }
  };

  deleteMessage = messageId => {
    this.setState({
      deletePrompt: true,
      messageId,
    });
  };

  deleteSubmit = async () => {
    const { messageId } = this.state;
    const { code, data = {}, msg = '' } = await Api.deleteChatMessage({
      messageId,
    });
    if (code == 0) {
      this.getChatMsgHistory();
      PubSub.publish('queryChatInfo'); //发布消息
      message.success('操作成功');
      this.setState({
        deletePrompt: false,
      });
      return;
    }
    message.error(msg);
  };

  onDeleteCancel = () => {
    this.setState({
      deletePrompt: false,
    });
  };

  searchContent = val => {
    // const val = e.currentTarget.value;
    this.setState({ filter: val });
  };

  renderFooter = () => {
    const { msgHistory = {} } = this.state;
    const { pageCount, currentPage } = msgHistory;
    return (
      <div className={styles.msgFooter}>
        <Button disabled={currentPage === 1}>上一页</Button>
        <Button disabled={currentPage >= pageCount}>下一页</Button>
      </div>
    );
  };

  previewImg = fileUrl => {
    if (fileUrl) {
      window.open(fileUrl);
    }
  };

  getImgMsg = item => {
    let leftIcon = null;

    if (item.type == 3) {
      leftIcon = (
        <>
          <img src={item.content} alt="" style={{ maxWidth: 200, maxHeight: 200, cursor: 'pointer' }} onClick={() => this.previewImg(item.content)} />
          <Button onClick={() => this.deleteMessage(item.messageId)} style={{ float: 'right' }} type="primary">
            删除
          </Button>
        </>
      );
    }
    if (item.type == 5) {
      leftIcon = (
        <>
          <img src={item.content} alt="" style={{ width: 32 }} />
          <Button onClick={() => this.deleteMessage(item.messageId)} style={{ float: 'right' }} type="primary">
            删除
          </Button>
        </>
      );
    }
    if (item.type == 6) {
      leftIcon = (
        <>
          <img src={MsgVideoWhite} alt="" style={{ width: 32 }} />
          <Button onClick={() => this.deleteMessage(item.messageId)} style={{ float: 'right' }} type="primary">
            删除
          </Button>
        </>
      );
    }
    return leftIcon;
  };

  changeTipType = val => {
    this.setState({ queryFlg: val }, this.getChatMsgHistory);
  };

  renderSraechType = () => {
    return (
      <Select defaultValue="xcx" style={{ width: 100 }} onChange={this.changeTipType}>
        <Select.Option value="xcx">小程序</Select.Option>
        <Select.Option value="old">企业微信</Select.Option>
      </Select>
    );
  };

  render() {
    // const { users = [] } = this.props;
    const { onCancel, show } = this.props;
    const { msgHistory = {}, filter = '', deletePrompt } = this.state;
    const { recordList = [] } = msgHistory;

    const showData = recordList.filter(item => {
      return (!filter && !filter.length) || (item.content || '').indexOf(filter) > -1 || (item.sendUserName || '').indexOf(filter) > -1;
    });

    return (
      <>
        <Modal
          title="消息记录"
          visible={show}
          onOk={this.handleSubmit}
          onCancel={() => {
            onCancel();
          }}
          footer={this.renderFooter()}
          destroyOnClose
        >
          <Input.Search placeholder="请输入关键字搜索" onSearch={this.searchContent} style={{ width: '100%' }} addonBefore={this.renderSraechType()} />
          <List className={styles.list}>
            {showData.map((item, i) => {
              let leftIcon = null;

              if (item.type == 3) {
                leftIcon = <img src={item.content} alt="" style={{ maxWidth: 200, maxHeight: 200, cursor: 'pointer' }} onClick={() => this.previewImg(item.content)} />;
              }
              if (item.type == 5) {
                leftIcon = <img src={item.content} alt="" style={{ width: 32 }} />;
              }
              if (item.type == 6) {
                leftIcon = <img src={MsgVideoWhite} alt="" style={{ width: 32 }} />;
              }

              let filterStr;
              if (!filter && !filter.length) {
                filterStr = item.sendUserName || '';
              } else {
                filterStr = `${item.sendUserName || ''}`.replace(new RegExp(filter, 'g'), `<span class="${styles.keyword}">${filter}</span>`);
              }
              filterStr = `${filterStr}\xa0\xa0${item.createTime}`;
              let filterContentStr = '';
              if (!filter && !filter.length) {
                // filterContentStr = item.content
                //   ? item.content +
                //     `<Button id='${item.messageId}' class='ant-btn ant-btn-primary' onClick='${this.deleteMessage}' style="float: right;margin:19px 0 0 0;"  type="primary">删除</Button>`
                //   : '';
                filterContentStr = item.content && (
                  <div>
                    {item.content}
                    <Button class="ant-btn ant-btn-primary" onClick={() => this.deleteMessage(item.messageId)} style={{ float: 'right', margin: '19px 0 0 0' }} type="primary">
                      删除
                    </Button>
                  </div>
                );
              } else {
                filterContentStr = item.content && (
                  <div>
                    {item.content}
                    <Button class="ant-btn ant-btn-primary" onClick={() => this.deleteMessage(item.messageId)} style={{ float: 'right', margin: '19px 0 0 0' }} type="primary">
                      删除
                    </Button>
                  </div>
                  // filterContentStr = `${item.content || ''}`.replace(
                  //   new RegExp(filter, 'g'),
                  //   `<span class="${styles.keyword}">${filter}</span>`
                );
              }

              return (
                <List.Item key={item.key || i}>
                  <List.Item.Meta
                    className={styles.meta}
                    title={
                      <div>
                        <div
                          className={styles.title}
                          dangerouslySetInnerHTML={{
                            __html: filterStr,
                          }}
                        >
                          {/* <div className={styles.extra}>{item.extra}</div> */}
                        </div>
                        <div style={{ marginTop: 6 }}>{this.getImgMsg(item)}</div>
                      </div>
                    }
                    description={
                      !leftIcon ? (
                        <div className={styles.descriptionModule}>
                          {/* <div
                          className={styles.description}
                          title={item.description}
                          dangerouslySetInnerHTML={{
                            __html: filterContentStr,
                          }}
                        /> */}
                          {filterContentStr}
                        </div>
                      ) : null
                    }
                  />
                </List.Item>
              );
            })}
            {!showData.length ? <Empty style={{ marginTop: 100 }} /> : null}
          </List>
        </Modal>
        <Modal title="删除提示" visible={deletePrompt} onOk={this.deleteSubmit} onCancel={this.onDeleteCancel}>
          <p>确定删除该条聊天记录？删除后病友端也会删除，且不可再恢复。</p>
        </Modal>
      </>
    );
  }
}

export default Index;
