import { message } from 'antd';
import moment from 'moment';
import * as api from './api';

export default {
  namespace: 'bill',
  state: {
    listData: [],
    dateData: [],
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
    clear() {
      return {};
    },
  },
  effects: {
    *queryByPage({ payload }, { call, put }) {
      const data = yield call(api.queryByPage, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { listData: data.data || [] },
        });
      }
    },
    *queryByDate({ payload }, { call, put }) {
      const data = yield call(api.queryByDate, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { dateData: data.data || [] },
        });
      }
    },
  },
};
