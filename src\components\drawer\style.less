@charset "utf-8";

@import '../../resources/styles/mixins';

.drawer-mask {
  position: fixed;
  z-index: 98;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #000;
  opacity: 0.2;
}

.drawer-body {
  position: fixed;
  z-index: 98;
  top: 0;
  right: 0;
  bottom: 0;
  width: 530px;
  background: #fff;
  overflow-y: scroll;
  box-shadow: -4px 0 9px 0 rgba(207, 207, 207, 0.5);
  -webkit-transition: -webkit-transform 0.5s ease-out;
  transition: -webkit-transform 0.5s ease-out;
  transition: transform 0.5s ease-out;
  transition: transform 0.5s ease-out, -webkit-transform 0.5s ease-out;
  will-change: transform;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
  background-color: #fff;
}

.drawer-body.drawer-appear {
  right: 0;
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  transform: translateX(0);
}
