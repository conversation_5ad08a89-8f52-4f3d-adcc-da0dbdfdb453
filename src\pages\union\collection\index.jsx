import React, { useEffect, useMemo, useState } from 'react';
import { useAntdTable } from 'ahooks';
import { merge } from 'lodash';
import moment from 'moment';
import { connect, history } from 'umi';
import { Form, Row, Col, Input, Select, Space, Button, Table, DatePicker } from 'antd';
import queryString from 'query-string';
import { filterObj, getDownload } from '@/utils/utils';
import { provinceData } from '../province';

import * as Api from './service';

const { RangePicker } = DatePicker;
const { Search } = Input;
const formItemLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 17 },
};
const provinceMap = provinceData.map(item => {
  return { label: item.label, value: item.label, children: item.children };
});

const Index = props => {
  const [form] = Form.useForm();
  const { permissionData = {}, menuType = '' } = props;
  const { btns = {} } = permissionData;
  const [cityList, setCityList] = useState([]);
  const [userList, setUserList] = useState([]);
  const [institutionList, setInstitutionList] = useState([]);
  const [provinceId, setProvinceId] = useState('');
  const [queryParam, setQueryParam] = useState({});

  useEffect(() => {
    getInstitution();
    getAllUser();
  }, []);

  useEffect(() => {
    if (provinceId) {
      const list = provinceMap.filter(item => item.value === provinceId);
      setCityList(
        list[0]?.children?.map(item => {
          return { label: item.label, value: item.label, children: item.children };
        }),
      );
    }
  }, [provinceId]);

  const onSearch = value => {
    setQueryParam({ ...queryParam, keyword: value });
    submit();
  };

  const getAllUser = async () => {
    const data = await Api.findAllUser({ menuType });
    if (data.code === 0) {
      setUserList(data.data || []);
    }
  };

  const getInstitution = async () => {
    const data = await Api.getInstitution({ menuType });
    if (data.code === 0) {
      setInstitutionList(data.data || []);
    }
  };

  const dataExport = async record => {
    const url = '/merchant/api/returnMoney/contractReturnMoneyDetailExport';
    const paramStr = queryString.stringify({
      contractId: record.contractId,
      ...filterObj(queryParam),
    });
    getDownload(`${url}?${paramStr}`);
  };

  const fetchList = async ({ current = 1, pageSize = 10 }) => {
    try {
      const { time = [], contactsId, institutionId, ...rest } = form.getFieldsValue();
      const params = {
        pageNum: current,
        numPerPage: pageSize,
        startDate: time[0] && moment(time[0]).format('YYYY-MM-DD'),
        endDate: time[0] && moment(time[1]).format('YYYY-MM-DD'),
        ...(rest || {}),
        contactsName: userList.filter(item => item.id === contactsId)[0]?.name,
        institutionName: institutionList.filter(item => item.id === institutionId)[0]?.institutionName,
        keyword: queryParam.keyword,
        menuType,
      };

      const data = await Api.fetchList({ ...params });

      return {
        total: Number(data.data?.totalCount) || 0,
        list: data?.data?.recordList || [],
      };
    } catch (error) {
      console.log(error);
    }
  };

  const { loading, tableProps, search } = useAntdTable(fetchList, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
  });

  const tableRealProps = useMemo(
    () =>
      merge(tableProps, {
        pagination: {
          // showQuickJumper: true,
          // showSizeChanger: true,
          showTotal: total => `共 ${total} 条`,
        },
      }),
    [tableProps],
  );

  const { submit, reset } = search;

  const columns = [
       {
      title: '合同编号',
      dataIndex: 'contractNumber',
      fixed: 'left',
    },
      {
      title: '客户编号',
      dataIndex: 'institutionCode',
      fixed: 'left',
    },
    {
      title: '客户单位',
      dataIndex: 'institutionName',
      fixed: 'left',
    },
    {
      title: '销售负责人',
      dataIndex: 'contactsName',
    },
 
    // {
    //   title: '发放采样包数',
    //   dataIndex: 'samplingPackageFf',
    // },
    // {
    //   title: '录入样本数',
    //   dataIndex: 'sampleCount',
    // },
    // {
    //   title: '作废数量',
    //   dataIndex: 'samplingPackageZf',
    // },
    // {
    //   title: '未收回采样包数',
    //   dataIndex: 'samplingPackageWsh',
    // },
    {
      title: '累计应结算金额',
      dataIndex: 'totalSettleMoney',
    },
    {
      title: '累计已结算金额',
      dataIndex: 'settledMoney',
    },
    {
      title: '累计回款金额',
      dataIndex: 'totalReturnMoney',
    },
    {
      title: '累计未回款金额',
      dataIndex: 'unSettleMoney',
    },
    {
      title: '客户省份',
      dataIndex: 'institutionProvince',
    },
    {
      title: '客户城市',
      dataIndex: 'institutionCity',
    },
  {
      title: '账期',
      dataIndex: 'zq',
    },
    {
      title: '操作',
      dataIndex: 'institutionId',
      fixed: 'right',
      render: (id, record) => (
        <div className="edit-btn">
          <span
            onClick={() =>
              history.push({pathname: `/union/collection/mdetail?institutionId=${id}&contractId=${record.contractId}&institutionName=${record.institutionName}&contractNumber=${record.contractNumber}&institutionCode=${record.institutionCode}&contactsName=${record.contactsName}&totalSettleMoney=${record.totalSettleMoney}&unSettleMoney=${record.unSettleMoney}&menuType=${menuType}`,state: { from: props.location } }
              )
            }
          >
            详情
          </span>
          {(!menuType || menuType === 'team') && (
            <span
              onClick={() => {
                dataExport(record);
              }}
            >
              下载报表
            </span>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="g-page p-product-list">
      <div className="g-query-box">
        <Form form={form} {...formItemLayout} labelWrap>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="time" label="查询日期">
                <RangePicker />
              </Form.Item>
            </Col>
            {/* <Col span={8}>
              <Form.Item name="keyword" label="客户/合同编号" initialValue="">
                <Input placeholder="请输入" />
              </Form.Item>
            </Col> */}
            <Col span={8}>
              <Form.Item name="institutionId" label="客户名称" initialValue="">
                <Select
                  fieldNames={{ label: 'institutionName', value: 'id' }}
                  options={[{ institutionName: '全部', id: '' }, ...institutionList]}
                  showSearch
                  filterOption={(input, option) => (option?.institutionName ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionProvince" label="客户省份" initialValue="">
                <Select
                  options={[{ label: '全部', value: '' }, ...provinceMap]}
                  onChange={v => {
                    setProvinceId(v);
                    form.setFieldsValue({ institutionCity: '' });
                  }}
                  showSearch
                  filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionCity" label="客户城市" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...cityList]} showSearch filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())} />
              </Form.Item>
            </Col>
            {menuType !== 'person' && (
              <Col span={8}>
                <Form.Item name="contactsId" label="销售人员" initialValue="">
                  <Select
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={[{ name: '全部', id: '' }, ...userList]}
                    showSearch
                    filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}
                  />
                </Form.Item>
              </Col>
            )}
            <Col span={menuType === 'person' ? 16 : 8} style={{  display: 'block' }}>
              <Space>
                <Button type="primary" onClick={submit} disabled={loading}>
                  查询
                </Button>
                <Button onClick={reset} disabled={loading}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="p-product-table">
        <Space direction="vertical" style={{ display: 'flex' }} size="middle">
          <Row>
            <Col span={8}>
              <Search
                placeholder="请输入客户/合同编号搜索"
                allowClear
                onSearch={onSearch}
                style={{
                  width: 260,
                }}
              />
            </Col>
            {(!menuType || menuType === 'team') && (
              <Col span={16} style={{ textAlign: 'right' }}>
                {btns['/union/collection/export'] && (
                  <Button type="primary" disabled={loading} onClick={dataExport}>
                    数据导出
                  </Button>
                )}
              </Col>
            )}
          </Row>
          <Table rowKey="id" {...tableRealProps} loading={loading} columns={columns} scroll={{ x: 'max-content' }} />
        </Space>
      </div>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Index);
