import * as utils from '../../utils/utils';

export function checkCT4(param) {
  return utils.request('/api/customize/checkCT4?_route=h242', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function checkphone(param) {
  return utils.request('/api/userinfo/check-phone', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function deleteAccount(param) {
  return utils.request('/api/userinfo/deleteuserinfo', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function accountList(param) {
  return utils.request('/api/userinfo/finduser', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function modifyPassword(param) {
  return utils.request('/api/userinfo/changepwd', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function resetPassword(param) {
  return utils.request('/api/userinfo/resetpwd', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function changeStatus(param) {
  return utils.request('/api/userinfo/changestatus', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function accountInfo(param) {
  return utils.request('/api/userinfo/finduserbyid', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function hisList(param) {
  return utils.request('/api/userinfo/gethislist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function permissionList(param) {
  return utils.request('/api/userinfo/userpermission', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function submitAccountInfo(param) {
  const { userId } = param;
  if (userId) {
    return utils.request('/api/userinfo/modifyuserinfo', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  } else {
    delete param.userId;
    return utils.request('/api/userinfo/addnewuser', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  }
}

export function isAccountAvailable(param) {
  return utils.request('/api/userinfo/isexist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function getRoles(param) {
  return utils.request('/api/identify/paging', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function modifyUserInfo(param) {
  return utils.request('/api/userinfo/modifyUserInfoByAccount', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function getAllianceinfo(param) {
  return utils.request('/api/institution/get-by-list', {
    method: 'POST',
    data: param,
  });
}

export function findAllUser(param) {
  return utils.request('/api/userinfo/findAllUser', {
    method: 'POST',
    data: param,
  });
}
