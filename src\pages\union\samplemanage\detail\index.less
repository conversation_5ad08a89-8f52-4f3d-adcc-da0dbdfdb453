.g-page.page-samplemanage-detail {
  .top-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0 0 24px;
    .ant-input-search-button {
      display: flex;
      align-items: center;
      justify-content: center;
      .anticon-search {
        width: 16px;
      }
    }
  }
  .container {
    display: flex;
    gap: 24px;
    margin: 16px 24px;
    padding: 0;
    background: transparent;
    > div {
      position: relative;
      flex: 1;
      &.left {
        background: linear-gradient(180deg, #e9f5f6 0%, #fff 100%);
        border-radius: 2px;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
      }
      &.right {
        min-width: 715px;
      }
    }
    .left {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      #imgPreview {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        overflow: hidden;
        background: #fff;
        .preview-mask {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          z-index: 10;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.45);
        }
        .preview-wrap {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          z-index: 10;
          .image-preview {
            height: 100%;
            text-align: center;
            pointer-events: none;
            .image-preview-body {
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              left: 0;
              overflow: hidden;
              .preview-img-wrapper {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
                > img {
                  max-width: 100%;
                  max-height: 100%;
                  vertical-align: middle;
                  transform: scale3d(1, 1, 1);
                  cursor: grab;
                  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
                  user-select: none;
                  pointer-events: auto;
                  &:active {
                    cursor: grabbing;
                  }
                }
              }
            }
          }
        }
        .preview-operations-wrapper {
          position: absolute;
          top: 0;
          right: 0;
          z-index: 10;
          width: 100%;
          .preview-operations {
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            color: rgba(255, 255, 255, 0.85);
            font-size: 14px;
            font-variant: tabular-nums;
            line-height: 1.5715;
            list-style: none;
            background: rgba(0, 0, 0, 0.1);
            pointer-events: auto;
            font-feature-settings: 'tnum', 'tnum';
            .preview-operations-progress {
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
            }
            .preview-operations-operation {
              margin-left: 12px;
              padding: 12px;
              cursor: pointer;
              transition: all 0.3s;
              &:hover {
                background: rgba(0, 0, 0, 0.2);
              }
              .anticon {
                font-size: 18px;
              }
            }
          }
          .preview-switch-left,
          .preview-switch-right {
            position: absolute;
            top: ~'calc((100vh - 54px - 48px - 60px) / 2)';
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            color: rgba(255, 255, 255, 0.85);
            background: rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            transition: all 0.3s;
            pointer-events: auto;
            &:hover {
              background: rgba(0, 0, 0, 0.2);
            }
            .anticon {
              font-size: 18px;
            }
          }
          .preview-switch-left {
            left: 8px;
          }
          .preview-switch-right {
            right: 8px;
          }
        }
        .ant-image {
          display: none;
        }
        .ant-image-preview-mask,
        .ant-image-preview-wrap,
        .ant-image-preview-operations-wrapper,
        .ant-image-preview-switch-left,
        .ant-image-preview-switch-right {
          position: absolute;
          z-index: 10;
        }
        .ant-image-preview-switch-left,
        .ant-image-preview-switch-right {
          top: ~'calc((100vh - 54px - 48px - 60px) / 2)';
        }
        .ant-image-preview-operations-wrapper {
          .anticon-close {
            transform: rotate(45deg);
          }
        }
      }
    }
    .right-box {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 4px;
      }
      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      }
      .ant-collapse {
        margin-bottom: 8px;
        border: none;
        .ant-collapse-item {
          border: none;
          .ant-collapse-header {
            padding: 16px 24px 16px 24px;
            font-weight: 500;
            background: #fff;
            .ant-collapse-arrow {
              right: 24px;
              color: #3f969d;
            }
          }
          .ant-collapse-content {
            border-color: #f0f0f0;
            .ant-collapse-content-box {
              padding: 24px 24px 0;
            }
          }
        }
      }
      .report-list {
        display: flex;
        // align-items: center;
        flex-direction: column;
        padding-bottom: 24px;
        .file-box {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          .anticon {
            margin-left: 8px;
            font-size: 20px;
            cursor: pointer;
          }
        }
        .ant-upload-picture-card-wrapper {
          width: auto;
          padding: 0;
          .ant-upload {
            margin: 0;
          }
        }
      }
    }
    .other-msg {
      .flex-box {
        display: flex;
        gap: 6px;
        align-items: center;
        .flex-box {
          flex: 1;
        }
        .flex-shrink {
          flex-shrink: 0;
        }
      }
      .ant-checkbox-group {
        display: block;
        .flex-box:not(:last-child) {
          margin-bottom: 8px;
        }
        .flex-shrink {
          width: 150px;
        }
      }
      .xzz-table {
        .ant-input {
          width: 110px;
        }
      }
      .ant-form-item {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }
  }
  .footer {
    padding: 16px 24px;
    text-align: center;
    background: #fff;
    // box-shadow: 0 -2px 8px rgb(0 0 0 / 8%);
    .ant-btn {
      margin-left: 8px;
    }
  }
  .flex-box {
    display: flex;
    gap: 6px;
    align-items: center;
    .flex-box {
      flex: 1;
    }
    .flex-shrink {
      flex-shrink: 0;
    }
  }
}
.samplemanage-detail-qrcode-box {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 700;
  font-size: 14px;
  text-align: center;
  img {
    display: inline-block;
    width: 180px;
    height: auto;
    margin-bottom: 24px;
    border: 4px solid #3f969d;
    border-radius: 8px;
    box-shadow: 0px 4px 16px 0px rgba(48, 161, 166, 0.1);
  }
}
