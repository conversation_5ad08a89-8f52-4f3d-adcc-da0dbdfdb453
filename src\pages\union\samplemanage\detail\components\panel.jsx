/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select, DatePicker } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { paneltemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, paneltemplate: { ...paneltemplate, ...payload } },
      },
    });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="申请单信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="【检测病种】疾病相关目标基因（panel）编码区外显子已知致病点突变">
                <Checkbox.Group value={paneltemplate.field1 ? paneltemplate.field1.split(',') : []} onChange={v => changeData({ field1: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={12}>
                      <Checkbox value="智力障碍相关基因（959 个）">智力障碍相关基因（959 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="耳聋相关基因（416 个）">耳聋相关基因（416 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="神经肌肉系统相关基因（1525 个）">神经肌肉系统相关基因（1525 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="遗传性骨病相关基因（439 个）">遗传性骨病相关基因（439 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="遗传性眼病相关基因（330 个）">遗传性眼病相关基因（330 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="皮肤系统遗传病相关基因（387 个）">皮肤系统遗传病相关基因（387 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="血液系统遗传病相关基因（288 个）">血液系统遗传病相关基因（288 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="代谢系统遗传病相关基因（239 个）">代谢系统遗传病相关基因（239 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="性发育异常相关基因（353 个）">性发育异常相关基因（353 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="遗传性心血管病相关基因（145 个）">遗传性心血管病相关基因（145 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="泌尿系统遗传病相关基因（506 个）">泌尿系统遗传病相关基因（506 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="凝血障碍相关基因（18 个）">凝血障碍相关基因（18 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="白化病相关基因（29 个）">白化病相关基因（29 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="囊肾相关基因（88 个）">囊肾相关基因（88 个）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="DMD 基因">DMD 基因</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
