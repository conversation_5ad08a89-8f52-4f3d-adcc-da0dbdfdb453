import React from 'react';
import { Input, Upload, Button, Select, message, DatePicker, Radio } from 'antd';
import { connect } from 'dva';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import TinymceEditor from '../../../../components/editor/TinymceEditor';

import * as CONSTANT from '../../../../config/constant/constant';
import '../style.less';

const FormItem = Form.Item;
const Option = Select.Option;
const { RangePicker } = DatePicker;

export default function IIHOCPanel({ type = 'detail', article = {}, typeList = [] }) {
  class ArticlePanel extends React.Component {
    state = {
      detail: { contentType: 1, ...article },
      secondTypeList: [],
    };

    articleDetail = hisId => {
      const { dispatch } = this.props;
      dispatch({
        type: 'microwebsite/articleDetail',
        payload: {
          hisId,
        },
      });
    };

    beforeUpload = file => {
      const isImage = file.type && file.type.indexOf('image') > -1;
      if (!isImage) {
        message.error('请选择图片进行上传!');
      }
      const size = file.size / 1024 / 1024 <= 2;
      if (!size) {
        message.error('图片大小不能超过2MB!');
      }
      return isImage && size;
    };

    uploadOnChange = e => {
      const { response } = e.file;
      if (response) {
        let picUrl = '';
        if (response.code != 0) {
          message.error('上传文件失败');
        } else {
          picUrl = response.data;
        }
        this.saveArticleDetail({
          picUrl,
        });
      }
    };

    validateHandler = (rule, value, callback) => {
      const { field } = rule;
      let content = '';
      switch (field) {
        case 'title':
          if (!value || value == '') {
            content = '请输入文章标题!';
          } else if (!/^[\S\s]{1,50}$/.test(value)) {
            content = '最多输入50个字符!';
          }
          break;
        case 'typeId':
          if (!value || value.length == 0) {
            content = '请选择文章类型!';
          }
          break;
        case 'position':
          if (!value || value.length == 0) {
            content = '请选择文章展示位置!';
          }
          break;
        case 'contentType':
          if (!value || value.length == 0) {
            content = '请选择文章内容形式!';
          }
          break;
        case 'picUrl':
          if (!value || value == '') {
            content = '请上传封面图片!';
          } else {
            const image = new Image(); // eslint-disable-line
            image.src = value;
            // if (
            //   !image.width ||
            //   image.width == 0 ||
            //   !image.height ||
            //   image.height == 0
            // ) {
            //   content = "图片资源无法加载或加载过慢!";
            // } else {
            // const ratio = image.width / image.height;
            // if (ratio > 2.2 || ratio < 1.8) {
            //   content = '图片尺寸与推荐尺寸350*174相差过大!';
            // }
            // }
          }
          break;
        // case "content":
        //   if (!value || value == "") {
        //     content = "请输入文章内容!";
        //   } else if (!/^[\s\S]{1,30000}$/.test(value)) {
        //     content = "最多输入30000个字符!";
        //   }
        //   break;
        default:
          break;
      }
      if (content && content != '') {
        callback(content);
      } else {
        callback();
      }
    };

    saveArticleDetail = param => {
      const $this = this;
      const { form } = $this.props;
      const { detail = {} } = $this.state;
      const { setFieldsValue } = form;
      setFieldsValue({
        ...param,
      });
      $this.setState({
        detail: {
          ...detail,
          ...param,
        },
      });
    };

    submitArticleDetail = status => {
      const $this = this;
      $this.state.detail.typeName = $this.state.detail.secondTypeName || $this.state.detail.typeName;
      $this.state.detail.typeId = $this.state.detail.secondTypeId || $this.state.detail.typeId;
      const {
        dispatch,
        form: { validateFieldsAndScroll },
      } = this.props;
      validateFieldsAndScroll(err => {
        if (!err) {
          dispatch({
            type: 'microwebsite/submitArticleDetail',
            payload: {
              ...$this.state.detail,
              position: ($this.state.detail.position || []).join(','),
              status,
            },
          });
        }
      });
    };

    render() {
      const { detail = {}, secondTypeList = [] } = this.state;
      const { form } = this.props;
      const { getFieldDecorator } = form;

      return (
        <div className="page-train-add">
          <div className="article-detail" style={{ margin: '24px 170px' }}>
            {/* <div style={{ padding: '20px 71.6px 0 0' }}>
              <Phone detail={detail} />
            </div> */}
            <div className="article-content">
              <Form style={{ marginTop: 12 }}>
                <h3>报名介绍</h3>
                <FormItem
                  label="标题"
                  labelCol={{ span: 6 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {getFieldDecorator('title', {
                    rules: [
                      {
                        required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.title,
                    onChange: e => {
                      this.saveArticleDetail({ title: e.target.value });
                    },
                  })(<Input style={{ width: 347 }} disabled={type == 'detail'} placeholder="请输入活动标题" />)}
                </FormItem>
                <FormItem
                  label="封面图片"
                  labelCol={{ span: 6 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {type == 'detail' ? (
                    <div style={{ display: 'flex' }}>
                      <img src={detail.picUrl} width="350px" height="174px" alt="" className="avatar" />
                    </div>
                  ) : detail.picUrl ? (
                    <div style={{ display: 'flex' }}>
                      <img src={detail.picUrl} width="350px" height="174px" alt="" className="avatar" />
                      <div style={{ padding: '146px 0 0 10px' }}>
                        <Upload name="idFile" action={`${CONSTANT.DOMAIN}/api/article/upfile`} beforeUpload={this.beforeUpload} showUploadList={false} onChange={this.uploadOnChange}>
                          <a>更换图片</a>
                        </Upload>
                      </div>
                    </div>
                  ) : (
                    <Upload
                      className="avatar-uploader"
                      name="idFile"
                      action={`${CONSTANT.DOMAIN}/api/article/upfile`}
                      beforeUpload={this.beforeUpload}
                      showUploadList={false}
                      onChange={this.uploadOnChange}
                    >
                      <div className="avatar-uploader-trigger">
                        <Icon
                          type="cloudupload"
                          style={{
                            fontSize: 32,
                            color: '#3F969D',
                            marginBottom: 14,
                          }}
                        />
                        <div
                          style={{
                            color: '#404040',
                            lineHeight: 1.5,
                            marginBottom: 7,
                          }}
                        >
                          点击此区域上传图片
                        </div>
                        <div style={{ color: '#919191', lineHeight: 1.5 }}>大小不超过2M，建议尺寸460*270</div>
                      </div>
                    </Upload>
                  )}
                  {getFieldDecorator('picUrl', {
                    rules: [
                      {
                        required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.picUrl,
                  })(<Input style={{ display: 'none' }} />)}
                </FormItem>
                <FormItem style={{ marginBottom: 0 }} label="正文" labelCol={{ span: 6 }}>
                  {type != 'detail' ? (
                    <TinymceEditor
                      id={`update-article-${detail.id}-editor`}
                      content={detail.content || ' '}
                      url={`${CONSTANT.DOMAIN}/api/article/upfile`}
                      onChange={content => {
                        this.saveArticleDetail({ content });
                      }}
                    />
                  ) : (
                    <div dangerouslySetInnerHTML={{ __html: detail.content }} />
                  )}
                  {getFieldDecorator('content', {
                    rules: [{ validator: this.validateHandler }],
                    initialValue: detail.content,
                  })(<Input style={{ display: 'none' }} />)}
                </FormItem>
                <h3 style={{ marginTop: '24px' }}>活动设置</h3>
                <FormItem
                  label="1.序号"
                  labelCol={{ span: 6 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {getFieldDecorator('title', {
                    rules: [
                      {
                        required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.title,
                    onChange: e => {
                      this.saveArticleDetail({ title: e.target.value });
                    },
                  })(<Input style={{ width: 347 }} disabled={type == 'detail'} placeholder="请输入活动标题" />)}
                </FormItem>

                <FormItem
                  label="2.活动上下架"
                  labelCol={{ span: 6 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {getFieldDecorator('title', {
                    rules: [
                      {
                        required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.title,
                    onChange: e => {
                      this.saveArticleDetail({ title: e.target.value });
                    },
                  })(
                    <Radio.Group>
                      <Radio value={1}>上架</Radio>
                      <Radio value={2}>下架</Radio>
                    </Radio.Group>,
                  )}
                </FormItem>

                <FormItem
                  label="3.是否需要线上报名"
                  labelCol={{ span: 6 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {getFieldDecorator('title', {
                    rules: [
                      {
                        required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.title,
                    onChange: e => {
                      this.saveArticleDetail({ title: e.target.value });
                    },
                  })(
                    <Radio.Group>
                      <Radio value={1}>是</Radio>
                      <Radio value={2}>否</Radio>
                    </Radio.Group>,
                  )}
                </FormItem>

                <FormItem
                  label="4.活动报名起止日期"
                  labelCol={{ span: 6 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {getFieldDecorator('title', {
                    rules: [
                      {
                        required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.title,
                    onChange: e => {
                      this.saveArticleDetail({ title: e.target.value });
                    },
                  })(<RangePicker />)}
                </FormItem>

                {type != 'detail' ? (
                  <div style={{ display: 'flex', justifyContent: 'center', marginTop: 24 }}>
                    <Button
                      type="primary"
                      size="default"
                      onClick={() => {
                        this.submitArticleDetail(1);
                      }}
                    >
                      确定
                    </Button>
                    <Button
                      size="default"
                      style={{ marginLeft: 16 }}
                      onClick={() => {
                        this.submitArticleDetail(0);
                      }}
                    >
                      取消
                    </Button>
                  </div>
                ) : null}
              </Form>
            </div>
          </div>
        </div>
      );
    }
  }

  return connect()(Form.create()(ArticlePanel));
}
