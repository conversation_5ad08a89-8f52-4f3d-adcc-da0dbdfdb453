.p-product-list {
  .edit-btn {
    display: flex;
    cursor: pointer;
    span {
      color: #3f969d;
      margin-right: 8px;
    }
  }

  .p-product-table {
    margin: 24px;
    padding: 24px;
    background-color: #fff;
  }
  .flex-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 16px;
  }
}
.remarks {
  display: inline-block;
  white-space: nowrap;
  width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.add-sampling-modal {
  .tip {
    position: absolute;
    top: 132px;
    left: 388px;
    span {
      color: red;
    }
  }
  .ant-picker {
    width: 100%;
  }
}
.overlay {
  color: red;
}
.page-train-add {
  margin: 24px 160px;
  padding: 24px;
  background-color: #fff;
  .avatar-uploader,
  .avatar-uploader-trigger,
  .avatar {
    width: 460px;
    height: 270px;
    text-align: center;
  }

  .avatar-uploader {
    display: block;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
  }

  .avatar-uploader-trigger {
    display: table-cell;
    vertical-align: middle;
    color: #999;
  }
  .ant-input-number {
    width: 200px;
  }
}
