/* eslint-disable no-plusplus */
import React, { Component } from 'react';
import { history as router } from 'umi';
import { Button, Input, message, Divider, Row, Col } from 'antd';
import DetailItem from '@/components/detailitem/index';
import PreviewImage from '@/components/PreviewImage';
import * as Api from './api';
import styles from './index.less';

const { TextArea } = Input;
class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      detail: {},
      photoIndex: '',
      idVisible: false,
      replyContent: '',
    };
  }

  componentDidMount() {
    this.getDetail();
  }

  getDetail = async () => {
    const {
      location: { query = {} },
    } = this.props;
    const { data, code } = await Api.getDetail({ id: query.id });
    if (code === 0) {
      this.setState({ detail: data });
    }
  };

  replyFeedBack = async () => {
    const {
      location: { query = {} },
    } = this.props;
    const { replyContent = '' } = this.state;

    if (!replyContent) {
      message.warning('请填写回复内容', 2);
      return;
    }
    const { code } = await Api.replyFeedBack({
      id: query.id,
      replyContent,
    });
    if (code === 0) {
      message.success('保存成功', 2, () => {
        this.setState({ idVisible: false });
        router.push({ pathname: '/patient/patientopinionbox' });
      });
    }
  };

  cancelReply = () => {
    router.push({ pathname: '/patient/patientopinionbox' });
  };

  changeUrl(item) {
    if (!item.url) {
      return false;
    }

    const { protocol, host, port } = window.location;
    if (protocol == 'https:') {
      return item.url;
    }

    if (protocol == 'http:') {
      try {
        const imgUrl = item.url;
        const imgUrlArr = imgUrl.split('//')[1].split('/');
        imgUrlArr[0] = host;
        const newUrl = `${window.location.protocol}//${imgUrlArr.join('/')}`;
        return newUrl;
      } catch (error) {
        console.log('域名解析有误');
        return item.url;
      }
    }
  }

  render() {
    const { detail = {}, photoIndex = '', idVisible = false } = this.state;
    const feedbackImg = detail.feedbackImg ? detail.feedbackImg.split(',') : [];
    const urls = feedbackImg.map(i => {
      return {
        // src: this.changeUrl({ url: i }),
        src: i,
      };
    });
    const basicList = [
      { label: '反馈类型', content: detail.feedbackType === '1' ? '投诉' : '建议' },
      { label: '反馈对象', content: detail.feedbackObject },
      { label: '病友家系号', content: detail.pid },
    ];
    return (
      <div className={styles.patientopinionDetail}>
        <div className={styles.detailInfo}>
          <DetailItem title="基本信息" detailList={basicList} />
          <div>
            <div className={styles.title}>反馈内容</div>
            <Row justify="start" type="flex">
              <Col>
                <div style={{ color: '#404040' }}>{detail.feedbackContent}</div>
                <div style={{ display: 'flex' }}>
                  <PreviewImage
                    images={urls}
                    index={photoIndex}
                    visible={idVisible}
                    onIndexChange={index => this.setState({ photoIndex: index })}
                    onClose={() => this.setState({ idVisible: false })}
                    styles={{
                      width: 96,
                      height: 96,
                      marginRight: 16,
                      borderRadius: 6,
                      cursor: 'pointer',
                      marginTop: 30,
                    }}
                  />
                </div>
              </Col>
            </Row>
          </div>
          <Divider dashed />
          {detail.status === '1' ? (
            <div style={{ marginBottom: 20 }}>
              <div className={styles.title}>已回复</div>
              <Row justify="start" type="flex">
                <Col>
                  <div style={{ color: '#404040' }}>{detail.replyContent}</div>
                </Col>
              </Row>
            </div>
          ) : (
            <div>
              <div className={styles.title}>回复处理</div>
              <Row justify="start" type="flex">
                <Col span={24}>
                  <TextArea rows={14} onChange={e => this.setState({ replyContent: e.target.value })} placeholder="请输入回复内容" />
                </Col>
              </Row>

              <div className={styles.btn}>
                <Button type="primary" style={{ marginLeft: '3%', cursor: 'pointer' }} onClick={() => this.replyFeedBack()}>
                  保存
                </Button>
                <Button
                  style={{ marginLeft: 30 }}
                  onClick={() => {
                    this.cancelReply();
                  }}
                >
                  取消
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
}

export default Index;
