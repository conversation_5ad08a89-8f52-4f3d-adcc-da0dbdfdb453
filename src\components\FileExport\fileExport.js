import React from 'react';
import PropTypes from 'prop-types';

/**
 * 文件导出组件，使用表单提交模式，后台返回文件流
 * @param {*} props
 */
function FileExport(props) {
  const { action, formData = [], children, uniqueId, style = {}, className = '' } = props;
  const onDownload = e => {
    // console.log(formData, 'formData');
    e.preventDefault();
    e.stopPropagation();
    document.getElementById(uniqueId || `download-${action}`).submit();
  };
  return (
    <span onClick={onDownload} style={{ display: 'inline-block', ...style }} className={className}>
      <form id={uniqueId || `download-${action}`} method="post" target="_blank" action={action}>
        {formData.map((item, index) => {
          return <input key={index} type="hidden" name={item.key} value={item.value} />;
        })}
      </form>
      {children || null}
    </span>
  );
}

FileExport.defaultProps = {
  action: '',
  formData: [],
  style: {},
};

FileExport.propTypes = {
  action: PropTypes.string,
  formData: PropTypes.array,
  style: PropTypes.object,
};

export default FileExport;
