import { buildConstMap } from '@/utils/utils';

export const SAMPLING_STATUS = [
  { value: 1, label: '在库' },
  { value: 2, label: '已分发' },
  { value: 3, label: '已回收' },
  { value: 0, label: '已作废' },
];

export const CO_MODE = [
  { value: 'CM1', label: '直营' },
  { value: 'CM2', label: '代理' },
];

export const DEPOSIT_STATUS = {
  1: '已支付',
  0: '未支付',
};

export const CANCEL_TYPE = [
  { value: 1, label: '负压不足' },
  { value: 2, label: '管子破裂' },
  { value: 3, label: '过期' },
  { value: 4, label: '其他' },
];

export const SAMPLING_STATUS_MAP = buildConstMap(SAMPLING_STATUS);
export const CO_MODE_MAP = buildConstMap(CO_MODE);
export const CANCEL_TYPE_MAP = buildConstMap(CANCEL_TYPE);
