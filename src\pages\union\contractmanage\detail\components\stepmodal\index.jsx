import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Modal, Form, Select, InputNumber, Input, Button, message, Divider } from 'antd';
import { MinusOutlined, CloseOutlined } from '@ant-design/icons';
import '../productmodal/index.less';

const Index = props => {
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  };
  const { dispatch, detail = {}, data = {}, onCancel = () => {} } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(true);
  const [itemData, setItemData] = useState(data);
  const { relProducts = [], step = [] } = detail;
  const { rules = [], products = [], editIndex } = itemData;

  const changeData = params => {
    setItemData(t => {
      return {
        ...t,
        ...params,
      };
    });
  };
  const addTiered = () => {
    changeData({
      rules: rules.concat({ price: products.map(v => ({ productId: v.sonProductId || v.productId, productName: `${v.productName}${v.sonProductName ? `——${v.sonProductName}` : ''}` })) }),
    });
  };
  const deleteTiered = index => {
    rules.splice(index, 1);
    changeData({ rules });
  };
  const changeTiered = (index, item) => {
    rules[index] = { ...(rules[index] || {}), ...item };
    changeData({ rules });
  };

  const getEffectiveProducts = () => {
    return relProducts.filter(v => {
      let existentProducts = [];
      step.forEach((stepItem, stepIndex) => {
        if (stepIndex !== editIndex) {
          existentProducts = existentProducts.concat(stepItem.products);
        }
      });
      if (v.sonProductId) {
        return (
          existentProducts.filter(existentProduct => existentProduct.sonProductId === v.sonProductId).length <= 0 ||
          products.filter(existentProduct => existentProduct.sonProductId === v.sonProductId).length > 0
        );
      }
      return (
        existentProducts.filter(existentProduct => existentProduct.productId === v.productId).length <= 0 || products.filter(existentProduct => existentProduct.productId === v.productId).length > 0
      );
    });
  };

  const submit = () => {
    form.validateFields().then(values => {
      let tiered = false;
      if (rules.length <= 0) {
        tiered = true;
      }
      rules.forEach(v => {
        const { start, end, price } = v;
        if ((!start && start !== 0) || (!end && end !== 0)) {
          tiered = true;
        }
        price.forEach(p => {
          if (!p.money && p.money !== 0) {
            tiered = true;
          }
        });
      });
      if (tiered) {
        message.warning('请将阶梯结算价补充完整');
        return false;
      }
      const step = JSON.parse(JSON.stringify(detail.step || []));
      const stepItem = { ...itemData, rules };
      if (editIndex || editIndex === 0) {
        step[editIndex] = stepItem;
      } else {
        step.push(stepItem);
      }
      dispatch({
        type: 'contract/save',
        payload: {
          subproducts: [],
          detail: { ...detail, step },
        },
      });
      setVisible(false);
      setTimeout(() => {
        onCancel();
      }, 500);
    });
  };

  return (
    <Modal
      className="add-qianyue-product"
      title="添加阶梯计价"
      visible={visible}
      destroyOnClose
      maskClosable={false}
      onCancel={() => {
        setVisible(false);
        setTimeout(() => {
          onCancel();
        }, 500);
      }}
      onOk={submit}
    >
      <Form form={form} {...formItemLayout}>
        <Form.Item name="productId" label="选择产品" rules={[{ required: true }]} initialValue={products.map(v => v.sonProductId || v.productId)}>
          <Select
            placeholder="请选择"
            mode="multiple"
            showSearch
            filterOption={(input, option) => {
              return option.productName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
            onChange={(v, o) => {
              changeData({
                products: o.map(p => {
                  const { item = {} } = p;
                  const product = { productId: item.productId, productName: item.productName };
                  if (item.sonProductId) {
                    product.sonProductId = item.sonProductId;
                    product.sonProductName = item.sonProductName;
                  }
                  return product;
                }),
                rules: rules.map(v => ({
                  ...v,
                  price: o.map(p => {
                    const { item = {} } = p;
                    return {
                      productId: item.sonProductId || item.productId,
                      productName: `${item.productName}${item.sonProductName ? `——${item.sonProductName}` : ''}`,
                      money: (v.price || []).filter(p => p.productId === item.sonProductId || p.productId === item.productId)[0]?.money || '',
                    };
                  }),
                })),
              });
            }}
          >
            {getEffectiveProducts().map((v, i) => (
              <Select.Option key={i} value={v.sonProductId || v.productId} item={v}>
                {v.productName}
                {v.sonProductName ? `——${v.sonProductName}` : ''}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item name="rules" label="阶梯结算价" required>
          <Input placeholder="请输入" type="hidden" />
          <Form.Item noStyle>
            <div className="jieti-box">
              {rules.map((item, index) => (
                <div key={index}>
                  {index > 0 ? <Divider /> : null}
                  <div className="jieti-item">
                    <InputNumber placeholder="样本数" min={0} precision={0} value={item.start} onChange={v => changeTiered(index, { start: v })} />
                    <MinusOutlined />
                    <InputNumber placeholder="样本数" min={0} precision={0} value={item.end} onChange={v => changeTiered(index, { end: v })} />
                    <CloseOutlined onClick={() => deleteTiered(index)} />
                  </div>
                  {(item.price || []).map((v, i) => (
                    <div className="jieti-item" key={i}>
                      <span className="jieti-text">{v.productName}结算价</span>
                      <InputNumber
                        placeholder="请输入"
                        min={0}
                        precision={2}
                        value={v.money}
                        onChange={v => {
                          item.price[i].money = v;
                          changeTiered(index, { price: item.price });
                        }}
                      />
                    </div>
                  ))}
                </div>
              ))}
              <Button type="primary" block ghost onClick={addTiered}>
                +添加阶梯
              </Button>
            </div>
          </Form.Item>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default connect(state => {
  return {
    ...state.contract,
  };
})(Index);
