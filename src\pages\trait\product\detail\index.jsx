/* eslint-disable react/jsx-no-duplicate-props */
import React, { useEffect, useMemo, useState } from 'react';
import { Table, message, Form, Row, Col, Input, Button } from 'antd';
import { useAntdTable } from 'ahooks';
import { merge } from 'lodash';
import { filterObj, getDownload } from '@/utils/utils';
import queryString from 'query-string';
import { connect } from 'umi';

import * as Api from '../service';
import { CUSTOMER_TYPE_MAP } from '../_data';
import './style.less';
const { Search } = Input;

const Index = props => {
  const [form] = Form.useForm();
  const [queryParam, setQueryParam] = useState({});
  const { id = '', menuType = '' } = queryString.parse(props.location.search);
  const { permissionData = {} } = props;
  const { btns = {} } = permissionData;

  const fetchList = async ({ current = 1, pageSize = 10 }) => {
    try {
      const { keyword } = form.getFieldsValue();
      const params = {
        pageNum: current,
        numPerPage: pageSize,
        productId: id,
        keyword,
        menuType,
      };

      setQueryParam(params);

      const { data } = await Api.getDetailList({ ...params });
      if (data.code !== 0) {
        message.error(data.msg);
        return;
      }
      return {
        total: Number(data.data.totalCount) || 0,
        list: data.data?.recordList || [],
      };
    } catch (error) {
      console.log(error);
    }
  };

  const { loading, tableProps, search } = useAntdTable(fetchList, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
  });

  const tableRealProps = useMemo(
    () =>
      merge(tableProps, {
        pagination: {
          // showQuickJumper: true,
          // showSizeChanger: true,
          showTotal: total => `共 ${total} 条`,
        },
      }),
    [tableProps],
  );

  const { submit, reset } = search;

  const dataExport = async () => {
    const url = '/merchant/api/product/export';
    const paramStr = queryString.stringify({
      ...filterObj(queryParam),
    });

    getDownload(`${url}?${paramStr}`);
  };

  const columns = [
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
    },
    {
      title: '客户名称',
      dataIndex: 'institutionName',
    },
    {
      title: '客户类型',
      dataIndex: 'institutionType',
      // render: v => CUSTOMER_TYPE_MAP[v]?.label
    },
    {
      title: '产品线',
      dataIndex: 'productName',
    },
    {
      title: '子产品',
      dataIndex: 'sonProductName',
    },
    {
      title: '结算价',
      dataIndex: 'fixedSettlementPrice',
    },
    {
      title: '签约日期',
      dataIndex: 'signingDate',
      render: v => v?.split(' ')[0],
    },
    {
      title: '到期日期',
      dataIndex: 'endDate',
      render: v => v?.split(' ')[0],
    },
  ];

  return (
    <div className="g-page p-product-list">
      <div className="g-query-box">
        <Form form={form}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="keyword" label="">
                <Search
                  placeholder="请输入客户名称或合同编号 搜索"
                  onSearch={submit}
                  allowClear
                  style={{
                    width: 320,
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={16} style={{ textAlign: 'right' }}>
              {btns['/trait/product/detail-export'] && (
                <Button type="primary" onClick={dataExport}>
                  数据导出
                </Button>
              )}
            </Col>
          </Row>
        </Form>
      </div>
      <div className="p-product-table">
        <Table rowKey="id" {...tableRealProps} loading={loading} columns={columns} />
      </div>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Index);
