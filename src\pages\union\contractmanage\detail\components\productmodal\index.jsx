import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Modal, Form, Select, InputNumber, Input, Button, message } from 'antd';
import { MinusOutlined, CloseOutlined } from '@ant-design/icons';
import './index.less';

const Index = props => {
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  };
  const { dispatch, products = [], subproducts = [], detail = {}, mastfilds = [], data = {}, onCancel = () => {}, menuType } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(true);
  const [itemData, setItemData] = useState(data);
  const { tieredSettlementPrice = [] } = itemData;

  const changeData = params => {
    setItemData(t => {
      return {
        ...t,
        ...params,
      };
    });
  };
  const addTiered = () => {
    changeData({ tieredSettlementPrice: tieredSettlementPrice.concat({}) });
  };
  const deleteTiered = index => {
    tieredSettlementPrice.splice(index, 1);
    changeData({ tieredSettlementPrice });
  };
  const changeTiered = (index, item) => {
    tieredSettlementPrice[index] = { ...(tieredSettlementPrice[index] || {}), ...item };
    changeData({ tieredSettlementPrice });
  };

  const getproducts = parentId => {
    dispatch({ type: 'contract/getproducts', payload: { parentId, menuType } });
  };
  const submit = () => {
    form.validateFields().then(values => {
      if (!itemData.editIndex && itemData.editIndex !== 0) {
        if (values.sonProductId) {
          if ((detail.relProducts || []).filter(item => item.sonProductId == values.sonProductId).length > 0) {
            message.warning('该合同已有当前产品，请重新选择！');
            return false;
          }
        } else {
          if ((detail.relProducts || []).filter(item => item.productId == values.productId).length > 0) {
            message.warning('该合同已有当前产品，请重新选择！');
            return false;
          }
        }
        if (values.cxgUnitPrice) {
          let cxgUnitPriceErr = false;
          (detail.relProducts || []).forEach(item => {
            if (item.cxgUnitPrice && item.productId == values.productId && item.cxgUnitPrice != values.cxgUnitPrice) {
              cxgUnitPriceErr = true;
            }
          });
          if (cxgUnitPriceErr) {
            message.warning('同一产品线下不同的子产品押金单价应一致，请重新输入');
            return false;
          }
        }
      }
      // if (mastfilds.includes('tieredSettlementPrice')) {
      //   let tiered = false;
      //   if (tieredSettlementPrice.length <= 0) {
      //     tiered = true;
      //   }
      //   tieredSettlementPrice.forEach(v => {
      //     const { start, end, price } = v;
      //     if ((!start && start !== 0) || (!end && end !== 0) || (!price && price !== 0)) {
      //       tiered = true;
      //     }
      //   });
      //   if (tiered) {
      //     // form.setFields([{
      //     //   name: 'tieredSettlementPriceCopy',
      //     //   errors: ['请将阶梯结算价补充完整'],
      //     // }]);
      //     message.warning('请将阶梯结算价补充完整');
      //     return false;
      //   }
      // }
      const relProducts = JSON.parse(JSON.stringify(detail.relProducts || []));
      const relProductsItem = { ...itemData, ...values, tieredSettlementPrice };
      if (itemData.editIndex || itemData.editIndex === 0) {
        relProducts[itemData.editIndex] = relProductsItem;
      } else {
        relProducts.push(relProductsItem);
      }
      dispatch({
        type: 'contract/save',
        payload: {
          subproducts: [],
          detail: { ...detail, relProducts },
        },
      });
      setVisible(false);
      setTimeout(() => {
        onCancel();
      }, 500);
    });
  };

  return (
    <Modal
      className="add-qianyue-product"
      title="添加签约产品"
      visible={visible}
      destroyOnClose
      maskClosable={false}
      onCancel={() => {
        setVisible(false);
        setTimeout(() => {
          dispatch({
            type: 'contract/save',
            payload: { subproducts: [] },
          });
          onCancel();
        }, 500);
      }}
      onOk={submit}
    >
      <Form form={form} {...formItemLayout} initialValues={itemData}>
        <Form.Item name="productId" label="选择产品线" rules={[{ required: true }]}>
          <Select
            placeholder="请选择"
            showSearch
            filterOption={(input, option) => {
              return option.productName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            }}
            options={products}
            fieldNames={{ label: 'productName', value: 'id' }}
            onChange={(v, o) => {
              getproducts(v);
              changeData({ productName: o.productName, sonProductName: '' });
              form.setFieldsValue({ sonProductId: null });
            }}
            disabled={itemData.editIndex || itemData.editIndex === 0}
          />
        </Form.Item>
        {subproducts.length > 0 ? (
          // <Form.Item name="sonProductId" label="选择子产品" rules={[{ required: true }]}>
          <Form.Item name="sonProductId" label="选择子产品">
            <Select
              placeholder="请选择"
              showSearch
              filterOption={(input, option) => {
                return option.productName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }}
              options={subproducts}
              fieldNames={{ label: 'productName', value: 'id' }}
              onChange={(v, o) => {
                changeData({ sonProductName: o.productName });
              }}
              disabled={itemData.editIndex || itemData.editIndex === 0}
            />
          </Form.Item>
        ) : (
          ''
        )}
        {mastfilds.includes('fixedSettlementPrice') ? (
          <Form.Item name="fixedSettlementPrice" label="固定结算单价" rules={[{ required: true }]}>
            <InputNumber placeholder="请输入" style={{ width: '100%' }} addonAfter="元" min={0} precision={2} />
          </Form.Item>
        ) : null}
        {mastfilds.includes('basePrice') ? (
          <Form.Item name="basePrice" label="底价" rules={[{ required: true }]}>
            <InputNumber placeholder="请输入" style={{ width: '100%' }} addonAfter="元" min={0} precision={2} />
          </Form.Item>
        ) : null}
        {mastfilds.includes('terminalPrice') ? (
          <Form.Item name="terminalPrice" label="终端价" rules={[{ required: true }]}>
            <InputNumber placeholder="请输入" style={{ width: '100%' }} addonAfter="元" min={0} precision={2} />
          </Form.Item>
        ) : null}
        {mastfilds.includes('settlementRatio') ? (
          <Form.Item name="settlementRatio" label="结算比例" rules={[{ required: true }]}>
            <InputNumber placeholder="请输入" style={{ width: '100%' }} addonAfter="%" min={0} precision={2} />
          </Form.Item>
        ) : null}
        {mastfilds.includes('promisedSales') ? (
          <Form.Item name="promisedSales" label="承诺销量">
            <InputNumber placeholder="请输入" style={{ width: '100%' }} min={0} precision={0} />
          </Form.Item>
        ) : null}
        {mastfilds.includes('cxgUnitPrice') ? (
          <Form.Item name="cxgUnitPrice" label="采样包押金单价" rules={[{ required: true }]}>
            <InputNumber placeholder="请输入" style={{ width: '100%' }} addonAfter="元" min={0} precision={2} />
          </Form.Item>
        ) : null}
        {/* {mastfilds.includes('tieredSettlementPrice') ? (
          <Form.Item name="tieredSettlementPriceCopy" label="阶梯结算价" required>
            <Input placeholder="请输入" type="hidden" />
            <Form.Item noStyle>
              <div className="jieti-box">
                {tieredSettlementPrice.map((item, index) => (
                  <div className="jieti-item" key={index}>
                    <InputNumber placeholder="样本数" min={0} precision={0} value={item.start} onChange={v => changeTiered(index, { start: v })} />
                    <MinusOutlined />
                    <InputNumber placeholder="样本数" min={0} precision={0} value={item.end} onChange={v => changeTiered(index, { end: v })} />
                    <span className="jieti-text">结算价</span>
                    <InputNumber placeholder="请输入" min={0} precision={2} value={item.price} onChange={v => changeTiered(index, { price: v })} />
                    <CloseOutlined onClick={() => deleteTiered(index)} />
                  </div>
                ))}
                <Button type="primary" block ghost onClick={addTiered}>
                  +添加阶梯
                </Button>
              </div>
            </Form.Item>
          </Form.Item>
        ) : null} */}
      </Form>
    </Modal>
  );
};
export default connect(state => {
  return {
    ...state.contract,
  };
})(Index);
