import React from 'react';
import { connect } from 'dva';
import { Table } from 'antd';

class AnswerUserList extends React.Component {
  closeDrawer() {
    this.props.dispatch({
      type: 'root/closeDrawer',
    });
  }
  render() {
    const { list, title } = this.props;
    const columns = [
      {
        title: '就诊人',
        dataIndex: 'userName',
        width: 95,
      },
      {
        title: '就诊卡',
        dataIndex: 'patCardNo',
      },
      {
        title: '手机号',
        dataIndex: 'userMobile',
      },
      {
        title: '答案',
        render: record => {
          return <span>{record.answerContent || record.optionName || record.questionName}</span>;
        },
        className: 'answer-content',
      },
      {
        title: '回答时间',
        dataIndex: 'updateTime',
      },
    ];

    return (
      <div className="answer-user-list">
        <p className="info-header">
          <span>问卷回答详情</span>
          <i className="merchanticon anticon-close close-icon" onClick={() => this.closeDrawer()} />
        </p>

        <div className="list-container">
          <Table columns={columns} dataSource={list} rowKey="updateTime" title={() => <p className="table-header-title">1、{title}</p>} />
        </div>
      </div>
    );
  }
}

export default connect(state => {
  return {
    drawerStatus: state.root.drawerStatus,
  };
})(AnswerUserList);
