import { post } from '@/utils/request';

export const fetchList = (param = {}) => post('/api/train/query-by-page', { data: param });

export const fetchDetailList = (param = {}) => post('/api/train-user/query-by-page', { data: param });

export const getInstitution = (param = {}) => post('/api/institution/get-by-list', { data: param });

export const add = (param = {}) => post('/api/train/add', { data: param });

export const update = (param = {}) => post('/api/train/update-by-id', { data: param });

export const deleteTrain = (param = {}) => post('/api/train/delete', { data: param });

export const getDetail = (param = {}) => post('/api/train/query-by-id', { data: param });

export const getApplyDetail = (param = {}) => post('/api/train-user/query-by-id', { data: param });

export const approve = (param = {}) => post('/api/train-user/access-by-id', { data: param });

export const reject = (param = {}) => post('/api/train-user/reject-by-id', { data: param });

export const getQRCode = (param = {}) => post('/api/train-user/train-qr-code', { data: param });

export const getorderdetail = (param = {}) => post('/api/order/getorderdetail', { data: param });
