@charset "utf-8";
.trait-modal-formitem {
  margin: 0 12px 24px;
}
.page-survey .question-list-container {
  margin: 24px;
  padding: 24px;
  background-color: #fff;
}
.page-survey .opt-area {
  color: #4C9CDF;
}
.survey-threeLevel-page {
  margin: 24px 170px;
}
/* 问卷详情 */
.survey-detail-container {
  position: relative;
}
.survey-detail-container .survey-detail-header,
.survey-detail-container .survey-detail-panle {
  padding: 16px 24px 24px;
  background-color: #fff;
}
.survey-detail-container .survey-detail-btn {
  background-color: #fff;
  margin-top: 10px;
  padding: 16px 24px 24px;
}
.survey-detail-container .survey-title {
  font-size: 18px;
  color: rgba(0, 0, 0, 0.75);
}
.survey-detail-container .ant-badge-status-success {
  background-color: #00a854 !important;
}
.survey-detail-container .ant-badge {
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-left: 20px;
  padding: 4px 20px;
}
.survey-detail-container .survey-item {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}
.survey-detail-container .survey-item .survey-item-label {
  display: inline-block;
  min-width: 85px;
}
.survey-detail-container .end-voting-btn {
  position: absolute;
  right: 24px;
  top: 16px;
}
.survey-detail-container .ticket-panle {
  float: right;
  background-color: #f9f9f9;
  overflow: hidden;
}
.survey-detail-container .ticket-panle .ticket-item-panle {
  float: left;
  padding: 20px 30px;
  text-align: center;
}
.survey-detail-container .ticket-panle .ticket-value {
  font-size: 42px;
  color: rgba(0, 0, 0, 0.75);
}
.survey-detail-container .survey-detail-panle {
  margin-top: 24px;
  padding: 0;
}
.survey-detail-container .survey-detail-panle .survey-detail-header {
  padding: 12px 30px;
  border-bottom: 1px #E9E9E9 solid;
  background-color: #f9f9f9;
}
.survey-detail-container .survey-detail-panle .survey-detail-item {
  padding: 12px 30px 12px 52px;
}
.survey-detail-container .survey-detail-panle .option-img {
  padding: 0 52px 10px;
  box-sizing: content-box;
}
.survey-detail-container .survey-detail-panle .questions-type {
  color: #51C488;
  padding: 2px 9px;
  border-radius: 4px;
  background-color: #ceefdf;
  margin-left: 8px;
}
.survey-detail-container .survey-detail-panle .questions-required {
  background-color: #FEE3CE;
  color: #F8811A;
  padding: 2px 9px;
  border-radius: 4px;
  margin-left: 8px;
}
.survey-detail-container .survey-detail-panle .questions-average {
  background-color: #108ee9;
  color: #ffffff;
  padding: 2px 9px;
  border-radius: 4px;
  margin-left: 8px;
}
/*答题 抽屉*/
.answer-user-list .info-header {
  padding: 0 25px;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  border-bottom: 1px #E9E9E9 solid;
}
.answer-user-list .close-icon {
  float: right;
}
.answer-user-list .list-container {
  margin: 0 24px;
}
.answer-user-list .table-header-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.75);
}
.answer-user-list .answer-content {
  width: 40%;
}
/* 手机预览 */
.question-phone .question-title {
  margin: 10px 0;
  text-align: center;
  font-size: 16px;
}
.question-phone .question-phone-list {
  margin: 10px 15px;
  color: rgba(0, 0, 0, 0.6);
}
.close-icon {
  cursor: pointer;
}
