import { Select, Modal } from 'antd';
import React from 'react';
import moment from 'moment';

const Option = Select.Option;

export function hisListItem(order) {
  /* 医院列表 */
  const hisList =
    order.hisList && order.hisList.data && order.hisList.data.length ? (
      order.hisList.data.map(item => (
        <Option key={item.hisId} value={`${item.hisId}`}>
          {item.hisName}
        </Option>
      ))
    ) : (
      <Option value="">暂无数据</Option>
    );
  return hisList;
}

export function busChannelLstItem(order) {
  /* 业务渠道列表 */
  let busChannelLst = null;
  if (order.busChannelLst && order.busChannelLst.data && order.busChannelLst.data.length) {
    busChannelLst = order.busChannelLst.data.map(item => (
      <Option key={item.dictKey} value={item.dictKey}>
        {item.dictValue}
      </Option>
    ));
    busChannelLst.unshift(
      <Option key="-1" value="">
        全部
      </Option>,
    );
  } else {
    busChannelLst = <Option value="">暂无数据</Option>;
  }
  return busChannelLst;
}

export function payWasItem(order) {
  /* 支付方法 */
  const payWaysOptions = [
    {
      label: '全部',
      value: '',
    },
  ];
  if (order.payChannelLst && order.payChannelLst.data && order.payChannelLst.data.length) {
    order.payChannelLst.data.forEach(item => {
      const tempArr = [];
      item.payWays.forEach(item1 => {
        tempArr.push({
          label: item1.payWay,
          value: item1.payWayCode,
        });
      });
      payWaysOptions.push({
        label: item.payChannel,
        value: item.payChannelCode,
        children: tempArr,
      });
    });
  }
  return payWaysOptions;
}

export function updateOrderStat(succallback, falcallback, title, canceltxt, oktxt) {
  /* 更新状态modal*/
  const confirm = Modal.confirm({
    title: title || '确定更新该订单为成功或失败状态？',
    content: <i onClick={() => confirm.destroy()} className="merchanticon anticon-close order-confirm-close" />,
    iconType: 'exclamationcircle',
    cancelText: canceltxt || '失败状态',
    okText: oktxt || '成功状态',
    maskClosable: false,
    onOk() {
      succallback();
    },
    onCancel() {
      falcallback();
    },
  });
  return confirm;
}

export function noDataRow() {
  return (
    <div className="ant-table-placeholder">
      <span>
        <i className="anticon anticon-frown-o" />
        暂无数据
      </span>
    </div>
  );
}

export function tableHeaderTime(time) {
  if (time instanceof Array) {
    return (
      <p>
        <span>{time[0] ? moment(time[0]).format('YYYY-MM-DD') : ''}</span>
        <span className="tsc-split">至</span>
        <span>{time[1] ? moment(time[1]).format('YYYY-MM-DD') : ''}</span>
      </p>
    );
  }
  return '';
}

export function getStatusName(busType, status) {
  let statusName = '';
  switch (busType) {
    case 'appointment_register':
      switch (status) {
        case 'L':
          statusName = '已锁号';
          break;
        case 'S':
          statusName = '预约成功';
          break;
        case 'F':
          statusName = '预约失败';
          break;
        case 'P':
          statusName = '付款中';
          break;
        case 'C':
          statusName = '已取消';
          break;
        case 'H':
          statusName = '预约异常';
          break;
        case 'Z':
          statusName = '预约异常';
          break;
        default:
          statusName = '';
          break;
      }
      break;
    case 'take_register':
      switch (status) {
        case 'L':
          statusName = '已锁号';
          break;
        case 'S':
          statusName = '取号成功';
          break;
        case 'F':
          statusName = '取号失败';
          break;
        case 'P':
          statusName = '付款中';
          break;
        case 'C':
          statusName = '已取消';
          break;
        case 'H':
          statusName = '取号异常';
          break;
        case 'Z':
          statusName = '取号异常';
          break;
        default:
          statusName = '';
          break;
      }
      break;
    case 'current_register':
      switch (status) {
        case 'L':
          statusName = '已锁号';
          break;
        case 'S':
          statusName = '挂号成功';
          break;
        case 'F':
          statusName = '挂号失败';
          break;
        case 'P':
          statusName = '付款中';
          break;
        case 'C':
          statusName = '已取消';
          break;
        case 'H':
          statusName = '挂号异常';
          break;
        case 'Z':
          statusName = '挂号异常';
          break;
        default:
          statusName = '';
          break;
      }
      break;
    case 'patcard_recharge':
      switch (status) {
        case 'S':
          statusName = '充值成功';
          break;
        case 'F':
          statusName = '充值失败';
          break;
        case 'P':
          statusName = '付款中';
          break;
        case 'C':
          statusName = '已取消';
          break;
        case 'H':
          statusName = '充值异常';
          break;
        case 'Z':
          statusName = '充值异常';
          break;
        default:
          statusName = '';
          break;
      }
      break;
    default:
      switch (status) {
        case 'S':
          statusName = '缴费成功';
          break;
        case 'F':
          statusName = '缴费失败';
          break;
        case 'P':
          statusName = '付款中';
          break;
        case 'C':
          statusName = '已取消';
          break;
        case 'H':
          statusName = '缴费异常';
          break;
        case 'Z':
          statusName = '缴费异常';
          break;
        default:
          statusName = '';
          break;
      }
      break;
  }
  return statusName;
}
