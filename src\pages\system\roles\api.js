import { post } from '@/utils/request';

export const getrolesbypage = (param = {}) => post('/api/identify/paging', { data: param });

export const deleteRole = (param = {}) => post('/api/identify/delete', { data: param }) || {};

export const getrolebyid = (param = {}) => post('/api/identify/getbyid', { data: param });

export const addrole = (param = {}) => post('/api/identify/add', { data: param });

export const updaterole = (param = {}) => post('/api/identify/update', { data: param });

export const getmenutree = (param = {}) => post('/api/identify/resource/tree', { data: param });
