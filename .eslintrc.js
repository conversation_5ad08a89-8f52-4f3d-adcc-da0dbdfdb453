module.exports = {
  extends: [require.resolve('@umijs/fabric/dist/eslint')],
  rules: {
    'generator-star-spacing': [0],
    'consistent-return': [0],
    'react/forbid-prop-types': [0],
    'react/jsx-filename-extension': [1, { extensions: ['.jsx'] }],
    'global-require': [1],
    'import/prefer-default-export': [0],
    'react/jsx-no-bind': [0],
    'react/prop-types': [0],
    'react/prefer-stateless-function': [0],
    'no-else-return': [0],
    'no-restricted-syntax': [0],
    'no-plusplus': [0],
    'import/no-extraneous-dependencies': [0],
    'no-use-before-define': [0],
    'jsx-a11y/no-static-element-interactions': [0],
    'no-nested-ternary': [0],
    'arrow-body-style': [0],
    'import/extensions': [0],
    'no-bitwise': [0],
    'no-cond-assign': [0],
    'import/no-unresolved': [0],
    'require-yield': [1],
    'max-len': ['error', 300, 2],
    'no-multiple-empty-lines': [1, { max: 2 }],
    'linebreak-style': [0],
    'lines-between-class-members': [0],
    'object-curly-spacing': [0],
    'no-param-reassign': [0],
    eqeqeq: [0],
    'no-console': [0],
    'class-methods-use-this': [0],
    'react/no-array-index-key': [0],
    'react/sort-comp': [0],
    'jsx-a11y/click-events-have-key-events': [0],
    'jsx-a11y/anchor-is-valid': [0],
    'react/jsx-closing-tag-location': [0],
    'jsx-a11y/no-noninteractive-element-interactions': [0],
    'jsx-a11y/label-has-for': [0],
    'object-curly-newline': [0],
    'import/no-named-as-default': [0],
  },
};
