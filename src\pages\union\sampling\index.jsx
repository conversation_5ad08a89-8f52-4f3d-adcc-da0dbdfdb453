/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-no-duplicate-props */
import React, { useEffect, useMemo, useState, useRef } from 'react';
import { history, connect } from 'umi';
import { Form, Row, Col, Input, Select, Space, Button, Table, Modal, InputNumber, message, Radio, Tooltip, DatePicker } from 'antd';
import { useAntdTable } from 'ahooks';
import { merge, values } from 'lodash';
import { filterObj, getDownload } from '@/utils/utils';
import queryString from 'query-string';
import moment from 'moment';
import * as Api from './service';
import { SAMPLING_STATUS, CO_MODE, SAMPLING_STATUS_MAP, DEPOSIT_STATUS, CANCEL_TYPE, CANCEL_TYPE_MAP } from './_data';
import { provinceData } from '../province';
import './style.less';

const provinceMap = provinceData.map(item => {
  return { label: item.label, value: item.label, children: item.children };
});

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const { TextArea, Search } = Input;
const { RangePicker } = DatePicker;
const MIN_TABLE_HEIGHT = 218;

const Index = props => {
  const { permissionData = {}, menuType = '' } = props;
  const { btns = {} } = permissionData;
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const [cancelForm] = Form.useForm();
  const samplingNumberStart = Form.useWatch('samplingNumberStart', addForm);
  const samplingNumberEnd = Form.useWatch('samplingNumberEnd', addForm);
  const institutionId = Form.useWatch('institutionId', addForm);

  const [userList, setUserList] = useState([]);
  const [cityList, setCityList] = useState([]);
  const [provinceId, setProvinceId] = useState('');
  const [queryParam, setQueryParam] = useState({});
  const [institutionList, setInstitutionList] = useState([]);
  const [productList, setProductList] = useState([]);
  const [contractList, setContractList] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isGrant, setIsGrant] = useState(false);
  const [isHighGrant, setIsHighGrant] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [packageNum, setPackageNum] = useState(0);
  const [packageId, setPackageId] = useState('');
  const [isEdit, setIsEdit] = useState(false);

  const [isFold, setIsFold] = useState(true);
  const [tableHeight, setTableHeight] = useState(MIN_TABLE_HEIGHT);
  const tableContent = useRef(null);

  useEffect(() => {
    if (tableContent.current) {
      setTableHeight(Math.max(tableContent.current.offsetHeight, MIN_TABLE_HEIGHT));
    }
  }, [tableContent.current, isFold]);

  useEffect(() => {
    getAllUser();
    getProduct();
    getInstitution();

    // 添加resize监听
    const handleResize = () => {
      if (tableContent.current) {
        setTableHeight(Math.max(tableContent.current.offsetHeight, MIN_TABLE_HEIGHT));
      }
    };

    window.addEventListener('resize', handleResize); // 添加事件监听器

    return () => {
      window.removeEventListener('resize', handleResize); // 移除事件监听器
    };
  }, []);

  useEffect(() => {
    if (provinceId) {
      const list = provinceMap.filter(item => item.value === provinceId);
      setCityList(
        list[0]?.children?.map(item => {
          return { label: item.label, value: item.label, children: item.children };
        }),
      );
    }
  }, [provinceId]);

  useEffect(() => {
    if (institutionId) {
      addForm.setFieldValue('contractId', null);
      setContractList([]);
      getContract();
    }
  }, [institutionId]);

  useEffect(() => {
    if (samplingNumberStart && samplingNumberEnd) {
      const number = Number(samplingNumberEnd?.match(/\d+(\d+)?/g)?.pop()) - Number(samplingNumberStart?.match(/\d+(\d+)?/g)?.pop()) + 1;
      if (number > 0) {
        setPackageNum(number);
      } else {
        setPackageNum(0);
      }
    }
  }, [samplingNumberStart, samplingNumberEnd]);

  useEffect(() => {
    if (!isModalOpen) {
      setIsGrant(false);
      setIsHighGrant(false);
      setPackageId('');
      setPackageNum(0);
      setContractList([]);
      addForm.resetFields();
    }
  }, [isModalOpen]);

  useEffect(() => {
    if (!isCancelModalOpen) {
      setIsEdit(false);
      setPackageId('');
      cancelForm.resetFields();
    }
  }, [isCancelModalOpen]);

  const getAllUser = async () => {
    const data = await Api.findAllUser({ menuType });
    if (data.code === 0) {
      setUserList(data.data || []);
    }
  };

  const getInstitution = async () => {
    const data = await Api.getInstitution({ menuType });
    if (data.code === 0) {
      setInstitutionList(data.data || []);
    }
  };

  const getContract = async () => {
    const data = await Api.getContract({ id: institutionId });
    if (data.code === 0) {
      setContractList(data.data || []);
    }
  };

  const getProduct = async () => {
    const data = await Api.getProduct({ parentId: '', menuType });
    if (data.code === 0) {
      setProductList(data.data || []);
    }
  };

  const fetchList = async ({ current = 1, pageSize = 10 }) => {
    try {
      const { createTime = [], grantDate = [], recycleDate = [], ...rest } = form.getFieldsValue();
      const params = {
        pageNum: current,
        numPerPage: pageSize,
        createBeginDate: createTime[0] && moment(createTime[0]).format('YYYY-MM-DD'),
        createEndDate: createTime[0] && moment(createTime[1]).format('YYYY-MM-DD'),
        grantBeginDate: grantDate[0] && moment(grantDate[0]).format('YYYY-MM-DD'),
        grantEndDate: grantDate[0] && moment(grantDate[1]).format('YYYY-MM-DD'),
        recycleBeginDate: recycleDate[0] && moment(createTime[0]).format('YYYY-MM-DD'),
        recycleEndDate: recycleDate[0] && moment(createTime[1]).format('YYYY-MM-DD'),
        inputData: queryParam.inputData,
        menuType,
        ...(rest || {}),
      };
      setQueryParam(params);

      const url = menuType === 'person' ? 'fetchListPerson' : menuType === 'team' ? 'fetchListTeam' : 'fetchList';

      const data = await Api[url]({ ...params });

      return {
        total: Number(data.data.totalCount) || 0,
        list: data?.data?.recordList || [],
      };
    } catch (error) {
      console.log(error);
    }
  };

  const { loading, tableProps, search } = useAntdTable(fetchList, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
  });

  const tableRealProps = useMemo(
    () =>
      merge(tableProps, {
        pagination: {
          // showQuickJumper: true,
          // showSizeChanger: true,
          showTotal: total => `共 ${total} 条`,
        },
      }),
    [tableProps],
  );

  const { submit, reset } = search;

  const dataExport = async () => {
    const url = '/merchant/api/sampling-package/export';
    const paramStr = queryString.stringify({
      ...filterObj(queryParam),
    });
    getDownload(`${url}?${paramStr}`);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const handleOk = () => {
    addForm.submit();
  };

  const warning = (title, values) => {
    Modal.confirm({
      title,
      content: '请您确认是否发放',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        grantPackage(values);
      },
    });
  };

  const grantPackage = async values => {
    const { institutionName, institutionProvince, institutionCity } = institutionList.filter(item => item.id === values.institutionId)[0];
    const res = await Api[isHighGrant ? 'forceGrantPackage' : 'grantPackage']({
      ...values,
      institutionName,
      institutionProvince,
      institutionCity,
      grantDate: moment(values.grantDate).format('YYYY-MM-DD'),
    });
    if (res.code === 0) {
      message.success('操作成功！');
      setIsModalOpen(false);
      submit();
    }
  };

  const onFinish = async values => {
    if (isGrant) {
      const data = await Api[isHighGrant ? 'forceGrantCheck' : 'grantCheck']({
        ...values,
        grantDate: moment(values.grantDate).format('YYYY-MM-DD'),
      });
      if (isHighGrant && data.code === 1) {
        // await message.warning(data.msg, 3);
        warning(data.msg, values);
        return;
      } else if (data.code !== 0) {
        message.error(data.msg);
        return;
      }
      grantPackage(values);
      return;
    }
    const data = await Api[packageId ? 'updatePackage' : 'addPackage']({
      ...values,
      productName: productList.filter(item => item.id == values.productId)[0].productName,
      badTime: values.badTime ? moment(values.badTime).format('YYYY-MM-DD') : '',
      arriveDate: values.arriveDate && moment(values.arriveDate).format('YYYY-MM-DD'),
      id: packageId,
    });
    if (data.code === 0) {
      message.success('操作成功！');
      setIsModalOpen(false);
      addForm.resetFields();
      submit();
    }
  };

  const handleEdit = async (id, status) => {
    const data = await Api.getPackageDetail({ id });
    if (data.code === 0) {
      setPackageId(id);
      setIsEdit(true);
      if (status === 0) {
        cancelForm.setFieldsValue({
          ...data.data,
          cancelDate: data.data.cancelDate ? moment(data.data.cancelDate) : null,
        });
        setIsCancelModalOpen(true);
      } else {
        addForm.setFieldsValue({
          ...data.data,
          badTime: data.data.badTime ? moment(data.data.badTime) : null,
          arriveDate: data.data.arriveDate ? moment(data.data.arriveDate) : null,
          cancelDate: data.data.cancelDate ? moment(data.data.cancelDate) : null,
          productId: data.data.productId + '',
        });
        setIsModalOpen(true);
      }
    }
  };

  const cancelFormFinish = async values => {
    const data = await Api[isEdit ? 'updatePackage' : 'cancelPackage']({
      ...values,
      // productName: productList.filter(item => item.id == values.productId)[0].productName,
      cancelDate: moment(values.cancelDate).format('YYYY-MM-DD'),
      id: packageId,
    });
    if (data.code === 0) {
      message.success('操作成功！');
      setIsCancelModalOpen(false);
      addForm.resetFields();
      submit();
    }
  };

  const onOk = () => {
    cancelForm.submit();
  };

  const onCancel = () => {
    setIsCancelModalOpen(false);
  };

  const handleCancelPackage = async id => {
    const data = await Api.getPackageDetail({ id });
    if (data.code === 0) {
      cancelForm.setFieldValue('samplingNumber', data.data.samplingNumber);
      setPackageId(id);
      setIsCancelModalOpen(true);
    }
  };

  const onSearch = value => {
    setQueryParam({ ...queryParam, inputData: value });
    submit();
  };

  const columns = [
    {
      title: '采样包编号',
      dataIndex: 'samplingNumber',
      fixed: 'left',
    },
    {
      title: '批次号',
      dataIndex: 'batchNumber',
    },
    {
      title: '产品线',
      dataIndex: 'productName',
    },
    {
      title: '状态',
      dataIndex: 'samplingStatus',
      render: v => SAMPLING_STATUS_MAP[v]?.label,
    },
    {
      title: '发放人',
      dataIndex: 'grantName',
    },
    {
      title: '发放日期',
      dataIndex: 'grantDate',
    },
    {
      title: '回收日期',
      dataIndex: 'recycleDate',
    },
    {
      title: '发放客户',
      dataIndex: 'institutionName',
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
    },
    {
      title: '客户省份',
      dataIndex: 'institutionProvince',
    },
    {
      title: '客户城市',
      dataIndex: 'institutionCity',
    },
    {
      title: '销售人员',
      dataIndex: 'contactsUserName',
    },
    {
      title: '押金状态',
      dataIndex: 'depositStatus',
      render: v => DEPOSIT_STATUS[v],
    },
    {
      title: '押金金额',
      dataIndex: 'depositAmount',
      render: (v, record) => (record.depositStatus === 0 ? '' : v),
    },
    {
      title: '押金余额',
      dataIndex: 'depositFree',
      render: (v, record) => (record.depositStatus === 0 ? '' : v),
    },
    {
      title: '到货日期',
      dataIndex: 'arriveDate',
    },
    {
      title: '保质期',
      dataIndex: 'badTime',
    },
    {
      title: '录入人员',
      dataIndex: 'inputPersonName',
    },
    {
      title: '录入日期',
      dataIndex: 'createTime',
    },
    {
      title: '作废类型',
      dataIndex: 'cancelType',
      render: v => CANCEL_TYPE_MAP[v]?.label,
    },
    {
      title: '作废日期',
      dataIndex: 'cancelDate',
    },
    {
      title: '备注',
      dataIndex: 'grantRemark',
      render: (v, record) => {
        return (
          <Tooltip
            title={
              <>
                <div>{v}</div>
                <div>{record.cancelRemark}</div>
              </>
            }
          >
            <span className="remarks">
              {v} {record.cancelRemark}
            </span>
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'id',
      fixed: 'right',
      render: (id, record) => (
        <div className="edit-btn">
          {btns['/union/sampling/edit'] && (record.samplingStatus === 0 || record.samplingStatus === 1) ? (
            <span
              onClick={() => {
                handleEdit(id, record.samplingStatus);
              }}
            >
              编辑
            </span>
          ) : (
            // <span style={{ width: '28px' }} />
            ''
          )}
          {btns['/union/sampling/cancel'] && (record.samplingStatus === 1 || record.samplingStatus === 2) ? (
            <span style={{ color: '#FF4D4F' }} onClick={() => handleCancelPackage(id)}>
              {' '}
              作废
            </span>
          ) : (
            ''
          )}
        </div>
      ),
    },
  ];
  if (menuType && !['team'].includes(menuType)) {
    // 删除操作这一项
    columns.pop();
  }

  return (
    <div className="g-page p-sampling-list">
      <div className="g-query-box">
        <Form form={form} {...formItemLayout} labelWrap>
          <Row gutter={16} className={isFold ? 'g-fold-box' : ''}>
            <Col span={8}>
              <Form.Item name="createDate" label="录入日期">
                <RangePicker />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="grantDate" label="发放日期">
                <RangePicker />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="recycleDate" label="回收日期">
                <RangePicker />
              </Form.Item>
            </Col>
            {/* <Col span={8}>
              <Form.Item name="inputData" label="采样包批次号/编号" initialValue="">
                <Input placeholder="请输入" />
              </Form.Item>
            </Col> */}
            <Col span={8}>
              <Form.Item name="samplingStatus" label="采样包状态" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...SAMPLING_STATUS]} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionId" label="发放客户" initialValue="">
                <Select
                  fieldNames={{ label: 'institutionName', value: 'id' }}
                  options={[{ institutionName: '全部', id: '' }, ...institutionList]}
                  showSearch
                  filterOption={(input, option) => (option?.institutionName ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionProvince" label="所在省份" initialValue="">
                <Select
                  options={[{ label: '全部', value: '' }, ...provinceMap]}
                  onChange={v => {
                    setProvinceId(v);
                    form.setFieldsValue({ institutionCity: '' });
                  }}
                  showSearch
                  filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionCity" label="所在城市" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...cityList]} showSearch filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())} />
              </Form.Item>
            </Col>
            {menuType !== 'person' && (
              <Col span={8}>
                <Form.Item name="inputPerson" label="录入人员" initialValue="">
                  <Select
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={[{ name: '全部', id: '' }, ...userList]}
                    showSearch
                    filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}
                  />
                </Form.Item>
              </Col>
            )}
            {menuType !== 'person' && (
              <Col span={8}>
                <Form.Item name="contactsUser" label="销售人员" initialValue="">
                  <Select
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={[{ name: '全部', id: '' }, ...userList]}
                    showSearch
                    filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}
                  />
                </Form.Item>
              </Col>
            )}
            <Col span={8}>
              <Form.Item name="cooperationMode" label="合作模式" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...CO_MODE]} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="productId" label="产品线" initialValue="">
                <Select
                  fieldNames={{ label: 'productName', value: 'id' }}
                  options={[{ productName: '全部', id: '' }, ...productList]}
                  showSearch
                  filterOption={(input, option) => (option?.productName ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="grantUserId" label="发放人" initialValue="">
                <Select
                  fieldNames={{ label: 'name', value: 'id' }}
                  options={[{ name: '全部', id: '' }, ...userList]}
                  showSearch
                  filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            <Col span={isFold ? 8 : menuType === 'person' ? 16 : 24} style={{ textAlign: 'right', display: 'block' }}>
              <Space>
                <Button type="primary" onClick={submit} disabled={loading}>
                  查询
                </Button>
                <Button onClick={reset} disabled={loading}>
                  重置
                </Button>
                <Button type="link" onClick={() => setIsFold(!isFold)}>
                  {isFold ? '展开' : '收起'}
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="container p-sampling-table">
        <Row>
          <Col span={8}>
            <Search
              placeholder="请输入采样包批次号/编号搜索"
              allowClear
              onSearch={onSearch}
              style={{
                width: 260,
                marginBottom: 16,
              }}
            />
          </Col>
          {(!menuType || menuType === 'team') && (
            <Col span={16} style={{ textAlign: 'right' }}>
              <Space>
                {btns['/union/sampling/add'] && (
                  <Button type="primary" disabled={loading} onClick={() => setIsModalOpen(true)}>
                    添加采样包
                  </Button>
                )}
                {btns['/union/sampling/grant'] && (
                  <Button
                    type="primary"
                    disabled={loading}
                    onClick={() => {
                      setIsGrant(true);
                      setIsModalOpen(true);
                    }}
                  >
                    批量发放
                  </Button>
                )}
                {btns['/union/sampling/force-grant'] && (
                  <Button
                    type="danger"
                    disabled={loading}
                    onClick={() => {
                      setIsGrant(true);
                      setIsHighGrant(true);
                      setIsModalOpen(true);
                    }}
                  >
                    高权限发放
                  </Button>
                )}
                {btns['/union/sampling/export'] && (
                  <Button type="primary" disabled={loading} onClick={dataExport}>
                    数据导出
                  </Button>
                )}
              </Space>
            </Col>
          )}
        </Row>
        <div className="table-box" style={{ minHeight: MIN_TABLE_HEIGHT }}>
          <div className="table-content" ref={tableContent}>
            <Table rowKey="id" {...tableRealProps} loading={loading} columns={columns} scroll={{ x: 'max-content', y: tableHeight - 55 - 48 }} />
          </div>
        </div>
      </div>
      <Modal
        title={isHighGrant ? <span style={{ color: '#3F969D' }}>高权限发放</span> : isGrant ? <span>发放采样包</span> : packageId ? '编辑采样包' : '添加采样包'}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        className="add-sampling-modal"
      >
        <Form form={addForm} onFinish={onFinish} {...{ labelCol: { span: 6 }, wrapperCol: { span: 12 } }}>
          {!packageId ? (
            <>
              <Form.Item name="samplingNumberStart" label="采样包编号起" rules={[{ required: true }]}>
                <Input placeholder="请输入起始编号" />
              </Form.Item>
              <Form.Item name="samplingNumberEnd" label="采样包编号止" rules={[{ required: true }]}>
                <Input placeholder="请输入截止编号" />
              </Form.Item>
            </>
          ) : (
            <Form.Item name="samplingNumber" label="采样包编号" rules={[{ required: true }]}>
              <Input placeholder="请输入" disabled />
            </Form.Item>
          )}

          {isGrant ? (
            <>
              <Form.Item name="institutionId" label="发放客户" rules={[{ required: true }]}>
                <Select
                  fieldNames={{ label: 'institutionName', value: 'id' }}
                  options={institutionList}
                  showSearch
                  filterOption={(input, option) => (option?.institutionName ?? '').toLowerCase().includes(input.toLowerCase())}
                  placeholder="请选择"
                />
              </Form.Item>
              {/* <Form.Item name="contractId" label="合同编号" rules={[{ required: true }]}>
                <Select
                  fieldNames={{ label: 'contractNumber', value: 'id' }}
                  options={contractList}
                  showSearch
                  filterOption={(input, option) => (option?.contractNumber ?? '').toLowerCase().includes(input.toLowerCase())}
                  placeholder="请选择"
                />
              </Form.Item> */}
              <Form.Item name="grantDate" label="发放日期" rules={[{ required: true }]}>
                <DatePicker />
              </Form.Item>
              <Form.Item name="grantRemark" label="备注">
                <TextArea />
              </Form.Item>
            </>
          ) : (
            <>
              <Form.Item name="productId" label="产品线" rules={[{ required: true }]}>
                <Select
                  fieldNames={{ label: 'productName', value: 'id' }}
                  options={productList}
                  placeholder="请选择"
                  showSearch
                  filterOption={(input, option) => (option?.productName ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
              <Form.Item name="batchNumber" label="批次号">
                <Input placeholder="请输入" />
              </Form.Item>
              <Form.Item name="badTime" label="保质期" rules={[{ required: false }]}>
                <DatePicker />
              </Form.Item>
              <Form.Item name="arriveDate" label="到货日期">
                <DatePicker />
              </Form.Item>
            </>
          )}
        </Form>
        {packageId ? (
          ''
        ) : (
          <div className="tip">
            共 <span>{packageNum}</span> 管
          </div>
        )}
      </Modal>
      <Modal title={isEdit ? '编辑采样包' : '作废采样包'} open={isCancelModalOpen} onOk={onOk} onCancel={onCancel} className="add-sampling-modal">
        <Form form={cancelForm} onFinish={cancelFormFinish} {...{ labelCol: { span: 6 }, wrapperCol: { span: 12 } }}>
          <Form.Item name="samplingNumber" label="采样包编号" rules={[{ required: true }]}>
            <Input placeholder="请输入" disabled />
          </Form.Item>
          <Form.Item name="cancelType" label="作废类型" rules={[{ required: true }]}>
            <Select options={CANCEL_TYPE} placeholder="请选择" />
          </Form.Item>
          <Form.Item name="cancelDate" label="作废日期" rules={[{ required: true }]}>
            <DatePicker />
          </Form.Item>
          <Form.Item name="cancelRemark" label="备注">
            <TextArea />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Index);
