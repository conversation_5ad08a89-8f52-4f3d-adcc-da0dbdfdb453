import { history } from 'umi';
import { message } from 'antd';

import * as CONSTANT from '../../config/constant/constant';
import * as service from './service';

export default {
  namespace: 'resetPwd',
  state: {
    captcha: `${CONSTANT.DOMAIN}/api/validate_code?${Date.now()}`,
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
  },
  effects: {
    *ressetpassword({ payload }, { call, put }) {
      const { oldPwd, newPwd, validateCode } = payload;
      const { data } = yield call(service.ressetpassword, { originPassword: oldPwd, newPassword: newPwd, validateCode: validateCode });
      message.destroy();
      if (data.code === 0) {
        message.success(data.data);
        history.push({
          pathname: '/login',
        });
      } else {
        message.error(data.msg);
        yield put({
          type: 'save',
          payload: {
            data,
            captcha: `${CONSTANT.DOMAIN}/api/validate_code?${Date.now()}`,
          },
        });
      }
    },
    *changeCaptcha({ payload }, { put }) {
      yield put({
        type: 'save',
        payload: {
          captcha: `${CONSTANT.DOMAIN}/api/validate_code?${Date.now()}`,
        },
      });
    },
  },
};
