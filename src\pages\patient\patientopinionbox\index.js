import React, { Component } from 'react';
import { connect } from 'dva';
import { Link } from 'react-router-dom';
import { Select, Row, Col, Button, Table, DatePicker, Input, Badge } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';
import FileExport from '@/components/FileExport/fileExport';

import * as Api from './api';
import styles from './index.less';

@connect(({ user }) => ({ currentUser: user.currentUser }))
@Form.create()
class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      tableData: {}, // 病友数据
      statusGroup: [
        { id: 0, name: '待回复' },
        { id: 1, name: '已回复' },
      ],
      typeGroup: [
        { id: 1, name: '投诉' },
        { id: 2, name: '建议' },
      ],
      pageSize: 10,
    };
    // 表格每页条数
    this.tableSize = ['10', '20', '30', '40', '50', '100'];
    // 表单样式
    this.formItemLayout = {
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 18,
      },
    };
    // 表格表头和每列数据
    this.tableColumns = [
      {
        title: '反馈时间',
        dataIndex: 'createTime',
        width: 200,
      },
      {
        title: '病友家系号',
        dataIndex: 'pid',
        width: 100,
      },
      {
        title: '反馈类型',
        dataIndex: 'feedbackType',
        width: 100,
        render: val => {
          switch (val) {
            case '1':
              return '投诉';
            case '2':
              return '建议';
          }
        },
      },
      {
        title: '反馈对象',
        dataIndex: 'feedbackObject',
        width: 200,
      },
      {
        title: '回复状态',
        dataIndex: 'status',
        width: 100,
        render: val => {
          switch (val) {
            case '0':
              return <Badge status="warning" text="待回复" />;
            case '1':
              return <Badge status="success" text="已回复" />;
          }
        },
      },

      {
        title: '回复时间',
        dataIndex: 'updateTime',
        width: 200,
        render: (val, col) => {
          switch (col.status) {
            case '0':
              return '';
            case '1':
              return val;
          }
        },
      },
      {
        title: '操作',
        width: 120,
        render: record => {
          return (
            <Link className={styles.tableOperText} to={`/patient/patientopinionbox/detail?id=${record.id}`}>
              详情
            </Link>
          );
        },
      },
    ];
  }

  componentDidMount() {
    this.getTableData();
  }

  // 获取投诉建议记录
  getTableData = async (pageNum = 1) => {
    const { pageSize } = this.state;
    const timeData = this.getQueryParam();
    const { status = '', feedbackType = '', fdStartDate = '', fdEndDate = '', pid = '' } = timeData;
    const param = {
      pid,
      status,
      feedbackType,
      fdStartDate,
      fdEndDate,
      pageNum,
      numPerPage: pageSize || 10,
    };
    const { code, data = {} } = await Api.getAllRecordList(param);
    if (code === 0) {
      this.setState({ tableData: data });
    }
  };

  // 获取查询参数
  getQueryParam = () => {
    const {
      form: { getFieldsValue },
    } = this.props;
    const value = getFieldsValue();
    const { status, feedbackType, feedbackTime = ['', ''], pid = '' } = value;
    const param = {
      status,
      pid,
      feedbackType,
    };
    // 处理反馈时间
    if (feedbackTime && feedbackTime[0] && feedbackTime[1]) {
      param.fdStartDate = feedbackTime[0] && moment(feedbackTime[0]).format('YYYY-MM-DD');
      param.fdEndDate = feedbackTime[1] && moment(feedbackTime[1]).format('YYYY-MM-DD');
    }
    return param;
  };

  // 根据条件渲染下拉选择框
  getSelect(label, name, options, optionConfig = {}, span) {
    const { key = 'dictKey', value = 'dictValue' } = optionConfig;
    const {
      form: { getFieldDecorator },
    } = this.props;
    return (
      <Col span={span}>
        <Form.Item {...this.formItemLayout} label={label}>
          {getFieldDecorator(name)(
            <Select placeholder="请选择">
              <Select.Option value="">全部</Select.Option>
              {options.map(opt => {
                return <Select.Option key={opt[key]}>{opt[value]}</Select.Option>;
              })}
            </Select>,
          )}
        </Form.Item>
      </Col>
    );
  }

  // 表格每页行数改变
  onShowSizeChange = (c, size) => {
    this.setState({ pageSize: size }, this.getTableData);
  };

  // 大于当前日期不能选
  disabledDate = time => {
    if (!time) {
      return false;
    }
    return time > moment();
  };

  render() {
    const { statusGroup, tableData = {}, pageSize, typeGroup } = this.state;

    const {
      form,
      form: { getFieldDecorator, getFieldsValue },
    } = this.props;
    const value = getFieldsValue();
    const { pid = '', status = '', feedbackType = '', feedbackTime = ['', ''] } = value;
    const param = {
      pid,
      status,
      feedbackType,
      fdStartDate: feedbackTime && feedbackTime[0] ? moment(feedbackTime[0]).format('YYYY-MM-DD') : '',
      fdEndDate: feedbackTime && feedbackTime[1] ? moment(feedbackTime[1]).format('YYYY-MM-DD') : '',
    };

    return (
      <div id="patientopinionbox">
        <Form>
          <Row justify={16} style={{ marginTop: 20, paddingBottom: 20 }}>
            <Col span={8}>
              <Form.Item {...this.formItemLayout} label="病友家系号">
                {getFieldDecorator('pid')(<Input style={{ width: '100%' }} placeholder="请输入病友家系号" />)}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item {...this.formItemLayout} label="反馈时间">
                {getFieldDecorator('feedbackTime')(<DatePicker.RangePicker allowClear disabledDate={this.disabledDate} format="YYYY-MM-DD" style={{ width: '100%' }} />)}
              </Form.Item>
            </Col>
            {this.getSelect('反馈类型', 'feedbackType', typeGroup, { key: 'id', value: 'name' }, 8)}
            {this.getSelect('回复状态', 'status', statusGroup, { key: 'id', value: 'name' }, 8)}
            <Col span={16} style={{ textAlign: 'right' }}>
              <Button type="primary" style={{ marginLeft: '8px', cursor: 'pointer' }} onClick={() => this.getTableData()}>
                查询
              </Button>
              <Button
                style={{ marginLeft: 30 }}
                onClick={() => {
                  form.resetFields();
                  this.getTableData();
                }}
              >
                重置
              </Button>

              <FileExport
                action={`${API_DOMAIN}/api/mch/feedback/exportAdviceList?hisId=242&status=${param.status}&feedbackType=${param.feedbackType}&fdStartDate=${param.fdStartDate}&fdEndDate=${param.fdEndDate}&pid=${param.pid}`}
              >
                <Button type="primary" style={{ marginLeft: '20px' }}>
                  导出表格
                </Button>
              </FileExport>
            </Col>
          </Row>
        </Form>
        <Table
          key={tableData.currentPage}
          style={{ marginTop: 16, background: '#fff', margin: '20px' }}
          dataSource={tableData.recordList || []}
          columns={this.tableColumns}
          rowKey={record => {
            return record.id;
          }}
          pagination={{
            total: tableData.totalCount || 0,
            showTotal: total => `共 ${total} 条`,
            onChange: pageNum => {
              this.getTableData(pageNum);
            },
            current: tableData.currentPage || 0,
            pageSize,
            showQuickJumper: true,
            showSizeChanger: true,
            onShowSizeChange: this.onShowSizeChange,
            pageSizeOptions: this.tableSize,
          }}
        />
      </div>
    );
  }
}

export default connect()(Index);
