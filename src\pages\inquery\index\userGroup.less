@import '~antd/lib/style/themes/default.less';

.leftOper {
  background-color: #134d64;
  padding-top: 32px;
  cursor: pointer;

  .oper {
    height: 48px;
    position: relative;
  }

  .unReadUsers {
    position: absolute;
    right: 2px;
    top: 2px;
    padding: 0 6px 2px;
    background: red;
    color: #fff;
    font-size: 10px;
    border-radius: 6px;
    line-height: 1;
  }

  .nofree {
    background: url(../../../assets/group-msg.png) no-repeat center center / 22px auto;

    &.on,
    &:hover {
      background: url(../../../assets/group-msg-on.png) no-repeat center center / 22px auto;
    }
  }

  .net {
    background: url(../../../assets/group-net.png) no-repeat center center / 19px auto;

    &.on,
    &:hover {
      background: url(../../../assets/group-net-on.png) no-repeat center center / 19px auto;
    }
  }

  .report {
    background: url(../../../assets/group-report.png) no-repeat center center / 16px auto;

    &.on,
    &:hover {
      background: url(../../../assets/group-report-on.png) no-repeat center center / 16px auto;
    }
  }

  .list {
    background: url(../../../assets/group-list.png) no-repeat center center / 18px auto;
  }

  .vip {
    background: url(../../../assets/group-msg.png) no-repeat center center / 22px auto;

    &.on,
    &:hover {
      background: url(../../../assets/group-msg-on.png) no-repeat center center / 22px auto;
    }
  }

  .end {
    background: url(../../../assets/group-end.png) no-repeat center center / 18px auto;

    &.on,
    &:hover {
      background: url(../../../assets/group-end-on.png) no-repeat center center / 22px auto;
    }
  }

  .unread {
    text-align: center;
    font-size: 12px;
    line-height: 14px;
    color: #7e9daa;
    padding-top: 10px;
    border-top: 1px solid #7e9daa;

    &.on,
    &:hover {
      color: #3eceb6;
    }
  }

  .move {
    background: url(../../../assets/group-move.png) no-repeat center center / 19px auto;

    &.on,
    &:hover {
      background: url(../../../assets/group-move-on.png) no-repeat center center / 19px auto;
    }
  }

  .remind {
    background: url(../../../assets/group-remind.png) no-repeat center center / 17px auto;

    &.on,
    &:hover {
      background: url(../../../assets/group-remind-on.png) no-repeat center center / 17px auto;
    }
  }

  .consultation {
    background: url(../../../assets/group-consultation.png) no-repeat center center / 17px auto;

    &.on,
    &:hover {
      background: url(../../../assets/group-consultation-on.png) no-repeat center center / 17px auto;
    }
  }

  .service {
    background: url(../../../assets/service.png) no-repeat center center / 17px auto;

    &.on,
    &:hover {
      background: url(../../../assets/service-on.png) no-repeat center center / 17px auto;
    }
  }

  .alluser {
    background: url(../../../assets/group-alluser.png) no-repeat center center / 20px auto;

    &.on,
    &:hover {
      background: url(../../../assets/group-alluser-on.png) no-repeat center center / 20px auto;
    }
  }

  .session {
    background: url(../../../assets/group-session.png) no-repeat center center / 18px auto;

    &.on,
    &:hover {
      background: url(../../../assets/group-session-on.png) no-repeat center center / 18px auto;
    }
  }
}

.leftContent {
  min-height: 100%;
  background: linear-gradient(180deg, rgba(62, 206, 182, 1) 0%, rgba(0, 193, 226, 1) 100%);

  :global {
    .ant-tabs {
      color: rgba(255, 255, 255, 0.9);
    }

    .ant-tabs-nav {
      margin: 0;
      padding: 0;
      border: 0;
      background: transparent;
      &::before {
        border: 0;
      }
    }

    .ant-tabs-nav .ant-tabs-tab {
      padding: 25px 12px 25px;
      font-size: 15px;
      margin: 0;
    }
    .ant-tabs-nav .ant-tabs-tab-active {
      .ant-tabs-tab-btn {
        color: #fff;
      }
    }

    .ant-tabs-tab-prev,
    .ant-tabs-tab-next {
      color: rgba(255, 255, 255, 0.45);
    }

    .ant-tabs-ink-bar {
      display: none !important;
    }
  }
}

.search {
  display: flex;
  margin: 16px 0 0 8px;
  padding-left: 29px;
  background: #fff url(../../../assets/group-search.png) no-repeat 9px center / 12px auto;
}

.ipt {
  border: none;
  flex: 1;
  height: 30px;
  line-height: 30px;
  font-size: 12px;
  background: none;
  outline: none;
  color: #000;

  &::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.32);
  }
}

.filter {
  width: 40px;
  background: @primary-color url(../../../assets/group-filter.png) no-repeat 12px center / 14px auto;
  cursor: pointer;
}

.dropdown {
  color: #1890ff;
  cursor: pointer;
  text-align: center;
  border-top: 1px solid #e9e9e9;
  padding: 10px 0;
}

.userListDate {
  padding: 10px 25px;
  color: rgba(255, 255, 255, 0.85);
  position: relative;
  cursor: pointer;

  &:after {
    content: '';
    position: absolute;
    right: 15px;
    top: 50%;
    width: 8px;
    height: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.85);
    border-right: 1px solid rgba(255, 255, 255, 0.85);
    transform: translateY(-50%) rotate(45deg);
  }

  &.open {
    &:after {
      transform: translateY(-50%) rotate(225deg);
    }
  }
}

.userListScroll {
  flex: 1;
  overflow-y: hidden;
  position: relative;
  border-top: 1px solid rgba(255, 255, 255, 0.5);
}

.userList {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow-y: auto;
  color: rgb(255, 255, 255);

  &::-webkit-scrollbar {
    display: none;
  }

  .userGroup {
    position: relative;

    &:after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      border-top: 1px solid rgba(255, 255, 255, 0.08);
      z-index: 1;
    }
  }

  .chatActive {
    position: relative;

    &:after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      margin-top: -6px;
      border-top: 6px solid transparent;
      border-right: 6px solid #fff;
      border-bottom: 6px solid transparent;
      border-left: 6px solid transparent;
    }
  }

  .userInfo {
    display: flex;
    align-items: center;
    padding: 22px 12px 22px 60px;
    cursor: pointer;
    position: relative;
    background: url(../../../assets/chat-group.png) no-repeat 18px center / 20px auto;
    font-weight: bold;

    &.active {
      color: #21a992;
      background: #fff url(../../../assets/chat-group-active.png) no-repeat 18px center / 20px auto;
    }

    .userBody {
      flex: 1;
      padding: 0 12px 0 0;

      .userName {
        font-size: 15px;

        &.outLine {
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }

    .userFoot {
      display: flex;

      .date {
        font-size: 15px;
        // color: #fff;
      }
    }
  }

  .fmList {
    background: #fff;
    position: relative;
    color: #667e7c;

    &:after {
      content: '';
      position: absolute;
      left: 30px;
      top: -14px;
      border-bottom: 7px solid #fff;
      border-top: 7px solid transparent;
      border-left: 7px solid transparent;
      border-right: 7px solid transparent;
      z-index: 1;
    }

    .userDetail {
      display: flex;
      align-items: center;
      padding: 10px 12px 10px 25px;
      font-size: 12px;
      cursor: pointer;

      &.active {
        color: #21a992;
        background: rgb(255, 255, 255);
      }

      .dBody {
        padding-left: 16px;
        flex: 1;

        &.outLine {
          color: rgba(0, 0, 0, 0.45);
        }
      }

      .dFoot {
        padding-left: 15px;
        color: #667e7c;

        &.newMsg {
          background: url(../../../assets/chat-msg.png) no-repeat left center / 10px auto;
          position: relative;

          &:before {
            content: attr(id);
            position: absolute;
            left: 0;
            color: red;
            top: 50%;
            font-size: 10px;
            transform: translateX(-100%) translateY(-50%);
          }
        }
      }

      .sexF {
        background: url(../../../assets/chat-woman.png) no-repeat 0 center / 10px auto;
      }

      .sexM {
        background: url(../../../assets/chat-man.png) no-repeat 0 center / 12px auto;
      }

      &.disable {
        color: #ccc;
        position: relative;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.5);
        }

        .sexF {
          background: url(../../../assets/chat-woman-outline.png) no-repeat 0 center / 12px auto;
        }

        .sexM {
          background: url(../../../assets/chat-man-outline.png) no-repeat 0 center / 12px auto;
        }
      }

      .feechatHead {
        background: url(../../../assets/group-header.png) no-repeat 0 center / 12px auto;
      }
    }

    &.netList {
      color: #667e7c;
      font-weight: bold;

      &:after {
        display: none;
      }

      .active {
        background: #fff;
      }
    }
  }

  .msgIcon {
    width: 12px;
    height: 22px;
    margin-right: 5px;
    display: none;
    // background: url(../../../assets/chat-msg-normal.png) no-repeat center center / 100% auto;

    &.hasNewMsg {
      display: block;
      background: url(../../../assets/chat-msg.png) no-repeat center center / 100% auto;
      position: relative;

      &:before {
        content: attr(id);
        position: absolute;
        left: -3px;
        color: red;
        top: 50%;
        font-size: 10px;
        transform: translateY(-50%) translateX(-100%);
      }
    }
  }

  .reportIcon {
    width: 10px;
    margin-right: 5px;
    display: none;

    // background: url(../../../assets/chat-report-normal.png) no-repeat center center / 100% auto;
    &.hasNewReport {
      display: block;
      background: url(../../../assets/chat-report.png) no-repeat center center / 100% auto;
    }
  }
}

.contextMenu {
  position: fixed;
  z-index: 1;
  cursor: pointer;
}

.allUserAddBtn {
  margin: 0 8px 10px;
  color: rgb(255, 255, 255);
  font-size: 12px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  vertical-align: middle;
  padding: 6px 0 8px;
  line-height: 1;
  text-align: center;
  cursor: pointer;
}

.userContent {
  display: flex;
  flex-direction: column;

  :global {
    .ant-tabs-tab-active {
      color: #fff;
      font-weight: bold;
      transform: scale(1.2);
    }
  }
}

.noChatList {
  padding: 12px 0;
  text-align: center;
  font-size: 10px;
  color: rgba(0, 0, 0, 0.65);
  background: #fff;
}

.filterDateBox {
  display: flex;
  padding-bottom: 10px;
  align-items: center;

  .filterLabel {
    padding: 0 10px 0 15px;
    color: #fff;
    font-size: 14px;
  }

  .filterDate {
    flex: auto;
  }
}

.fLabel {
  display: flex;
  padding: 0 0 10px 0;
  align-items: center;

  .labelItem {
    flex: auto;
    margin: 0 6px;
  }
}

.createConsultation {
  font-weight: bold;
  z-index: 99;
  position: fixed;
  left: 113px;
  bottom: 60px;
  width: 150px;
  height: 35px;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px -1px;
  border-radius: 5px;
}

.consultationRow {
  margin-bottom: 10;
  display: flex;
  align-items: center;
  padding: 5px 0;

  text {
    color: red;
  }
}
