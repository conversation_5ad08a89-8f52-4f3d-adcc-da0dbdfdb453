/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select } from 'antd';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { jsxjwsztemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, jsxjwsztemplate: { ...jsxjwsztemplate, ...payload } },
      },
    });
  };

  const changeTableData = (val, ind, key) => {
    const { field5 = [] } = jsxjwsztemplate;
    field5[ind][key] = val;
    changeData({ field5 });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="检测项目" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item>
                <Checkbox.Group value={jsxjwsztemplate.field1 ? jsxjwsztemplate.field1.split(',') : []} onChange={v => changeData({ field1: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="基础版">基础版</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="全面版">全面版</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="沉默型“2+0”验证">沉默型“2+0”验证</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="家系主编号">
                <Input placeholder="请输入" value={jsxjwsztemplate.field2} onChange={e => changeData({ field2: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="与受检者关系">
                <Input placeholder="请输入" value={jsxjwsztemplate.field3} onChange={e => changeData({ field3: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="亲属（包括本人）做过此检测">
                <div className="flex-box">
                  <Radio.Group value={jsxjwsztemplate.field4} onChange={e => changeData({ field4: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24} style={{ marginBottom: '24px' }}>
              <Table
                dataSource={jsxjwsztemplate.field5 || []}
                columns={[
                  {
                    title: '亲属检测编号',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field1} onChange={e => changeTableData(e.target.value, index, 'field1')} />;
                    },
                  },
                  {
                    title: '亲属姓名',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field2} onChange={e => changeTableData(e.target.value, index, 'field2')} />;
                    },
                  },
                  {
                    title: '亲属关系',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field3} onChange={e => changeTableData(e.target.value, index, 'field3')} />;
                    },
                  },
                  {
                    title: '亲属检测结果',
                    render: (cur, col, index) => {
                      return (
                        <Select placeholder="请选择" style={{ width: '100px' }} value={col.field4} onChange={v => changeTableData(v, index, 'field4')}>
                          <Option value="正常">正常</Option>
                          <Option value="携带者">携带者</Option>
                          <Option value="SMA患者">SMA患者</Option>
                        </Select>
                      );
                    },
                  },
                  {
                    title: '操作',
                    width: 60,
                    render: (cur, col, index) => (
                      <Button type="link" onClick={() => changeData({ field5: jsxjwsztemplate.field5.filter((item, ind) => index != ind) })}>
                        删除
                      </Button>
                    ),
                  },
                ]}
                pagination={false}
              />
              <Button type="dashed" block onClick={() => changeData({ field5: [...(jsxjwsztemplate.field5 || []), {}] })}>
                +添加亲属
              </Button>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="临床信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="临床症状">
                <Checkbox.Group value={jsxjwsztemplate.field6 ? jsxjwsztemplate.field6.split(',') : []} onChange={v => changeData({ field6: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={8}>
                      <Checkbox value="无">无</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="肌无力">肌无力</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="肌萎缩">肌萎缩</Checkbox>
                    </Col>
                    <Col span={24}>
                      <div className="flex-box">
                        <Checkbox value="其他">其他</Checkbox>
                        <div className="flex-box">
                          <Input placeholder="请输入" value={jsxjwsztemplate.field19} onChange={e => changeData({ field19: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="不良生育史">
                <div className="flex-box">
                  <Radio.Group value={jsxjwsztemplate.field7} onChange={e => changeData({ field7: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="曾育出生缺陷儿">曾育出生缺陷儿</Radio>
                  </Radio.Group>
                  {/* {jsxjwsztemplate.field7 == '曾育出生缺陷儿' ? ( */}
                  <div className="flex-box">
                    <Input placeholder="请输入" value={jsxjwsztemplate.field8} onChange={e => changeData({ field8: e.target.value })} />
                  </div>
                  {/* ) : null} */}
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="近亲婚配">
                <div className="flex-box">
                  <Radio.Group value={jsxjwsztemplate.field9} onChange={e => changeData({ field9: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">与配偶关系</div>
                    <Input placeholder="请输入" value={jsxjwsztemplate.field10} onChange={e => changeData({ field10: e.target.value })} />
                  </div>
                  <div className="flex-box">
                    <div className="flex-shrink">说明</div>
                    <Input placeholder="请输入" value={jsxjwsztemplate.field11} onChange={e => changeData({ field11: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="家族史">
                <div className="flex-box">
                  <Radio.Group value={jsxjwsztemplate.field12} onChange={e => changeData({ field12: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">与受检者关系</div>
                    <Input placeholder="请输入" value={jsxjwsztemplate.field13} onChange={e => changeData({ field13: e.target.value })} />
                  </div>
                  <div className="flex-box">
                    <div className="flex-shrink">说明</div>
                    <Input placeholder="请输入" value={jsxjwsztemplate.field14} onChange={e => changeData({ field14: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="其他遗传病史">
                <div className="flex-box">
                  <Radio.Group value={jsxjwsztemplate.field15} onChange={e => changeData({ field15: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <Input placeholder="请输入" value={jsxjwsztemplate.field16} onChange={e => changeData({ field16: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="近期是否进行过骨髓移植或接受输血">
                <div className="flex-box">
                  <Radio.Group value={jsxjwsztemplate.field17} onChange={e => changeData({ field17: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <Input placeholder="请输入" value={jsxjwsztemplate.field18} onChange={e => changeData({ field18: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
