import React from 'react';
import { Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';

import './style.less';

import userHead from '../../../../resources/images/common/userHeader.png';

export default class DoctorEdit extends React.Component {
  state = {};

  closeDrawer = () => {};

  render() {
    const { detail: doctor } = this.state;
    return (
      <div className="doctor-drawer">
        <div className="doctor-detail-head">
          <div style={{ textAlign: 'right' }}>
            <Icon
              onClick={() => {
                this.closeDrawer();
              }}
              type="close"
              style={{ color: '#fff', fontSize: 16, cursor: 'pointer' }}
            />
          </div>
          <div
            style={{
              position: 'absolute',
              bottom: 10,
              paddingLeft: 173,
              color: '#fff',
              fontSize: 18,
            }}
          >
            {doctor.name}
          </div>
          <img className="doctor-detail-avatar" src={doctor.img || userHead} alt="" />
        </div>
        <div className="doctor-detail-body">
          <div className="horizontal-line">
            <div className="horizontal-label">医生性别:</div>
            <div className="horizontal-text">{doctor.sexDesc}</div>
          </div>
          <div className="horizontal-line">
            <div className="horizontal-label">医生编号:</div>
            <div className="horizontal-text">{doctor.no}</div>
          </div>
          <div className="horizontal-line">
            <div className="horizontal-label">所属科室:</div>
            <div className="horizontal-text">{doctor.deptName}</div>
          </div>
          <div className="horizontal-line">
            <div className="horizontal-label">医生职称:</div>
            <div className="horizontal-text">
              {doctor.title}
              {doctor.levelDesc ? `丨${doctor.levelDesc}` : ''}
            </div>
          </div>
          <div className="horizontal-line" style={{ marginTop: 28 }}>
            <div className="horizontal-title">医生擅长</div>
          </div>
          <div className="horizontal-line">
            <div className="horizontal-text">{doctor.skill}</div>
          </div>
          <div className="horizontal-line" style={{ marginTop: 21 }}>
            <div className="horizontal-title">医生介绍</div>
          </div>
          <div className="horizontal-line">
            <div className="horizontal-text" dangerouslySetInnerHTML={{ __html: doctor.summary }} />
          </div>
        </div>
      </div>
    );
  }
}
