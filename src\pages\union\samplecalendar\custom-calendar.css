/* 日历整体样式 */
.ant-picker-calendar {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  height: calc(100vh - 240px) !important;
  min-height: 400px; /* 设置最小高度 */
}

/* 表格单元格高度设置 */
.ant-picker-calendar .ant-picker-content > tbody > tr > td {
  height: calc((100vh - 300px) / 6) !important;
  min-height: 60px !important; /* 设置单元格最小高度 */
  padding: 0 !important;
  border: 1px solid #f0f0f0 !important;
  position: relative;
}

/* 单元格内部容器 */
.ant-picker-calendar .ant-picker-cell-inner {
  height: 100% !important;
  min-height: 100% !important;
  position: relative;
  padding: 4px;
  display: flex;
  flex-direction: column;
}

/* 日期数字样式 - 只在右上角显示 */
.ant-picker-calendar-date-value {
  position: absolute;
  top: 4px;
  right: 8px;
  font-size: 12px;
  color: #666;
  z-index: 1;
}

/* 内容区域 - 修改为占满整个单元格 */
.ant-picker-calendar-date-content {
  height: 100% !important;
  width: 100% !important;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 !important; /* 移除之前的margin-top */
  padding: 4px;
}

/* 周末日期特殊样式 */
.ant-picker-cell.ant-picker-cell-weekend .ant-picker-calendar-date-value {
  color: #1890ff;
}

/* 选中状态 */
.ant-picker-cell-selected {
  background-color: #f5f5f5 !important;
}