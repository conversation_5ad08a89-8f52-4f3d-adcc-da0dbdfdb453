import React from 'react';
import { Tooltip } from 'antd';

import './style.less';

export default class Cell extends React.Component {
  render() {
    const { title = '', text = '', editClick, deleteClick, isDragging, connectDragSource, connectDropTarget } = this.props;
    const opacity = isDragging ? 0 : 1;

    return connectDragSource(
      connectDropTarget(
        <div className="sort-article-type-card" style={{ opacity }}>
          <div className="card-content">
            <div className="card-title">{title}</div>
            {text.length > 23 ? (
              <Tooltip placement="bottom" overlayStyle={{ width: 400 }} title={text}>
                <div className="card-desc">说明：{text}</div>
              </Tooltip>
            ) : (
              <div className="card-desc">说明：{text}</div>
            )}
          </div>
          <div className="card-bottom">
            <a onClick={editClick}>编辑</a>
            <span style={{ color: '#4C9CDF' }}>丨</span>
            <a onClick={deleteClick}>删除</a>
          </div>
        </div>,
      ),
    );
  }
}
