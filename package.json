{"private": true, "scripts": {"start": "cross-env umi dev", "build": "cross-env umi build", "build:uat": "cross-env umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/compatible": "^1.1.0", "@ant-design/pro-layout": "^5.0.12", "@haici/gmsm4": "^1.0.1", "@umijs/preset-react": "1.x", "@umijs/test": "^3.2.27", "ahooks": "^2.9.1", "antd": "4.24.13", "braft-editor": "^2.3.7", "decimal.js": "^10.0.1", "framer-motion": "^6.5.1", "image-compressor.js": "^1.1.4", "immutability-helper": "^3.1.1", "lint-staged": "^10.0.7", "lodash": "^4.17.20", "md5": "^2.3.0", "prettier": "^1.19.1", "prop-types": "^15.8.1", "pubsub-js": "^1.9.2", "query-string": "^5.0.1", "rc-notification": "^2.0.2", "react": "^16.12.0", "react-cropper": "^1.0.1", "react-dnd": "^7.7.0", "react-dnd-html5-backend": "^7.7.0", "react-dom": "^16.12.0", "react-photo-view": "0.5.7", "react-router-breadcrumbs-hoc": "^3.2.10", "tinymce": "^4.6.4", "umi": "^3.2.27", "umi-request": "^1.0.5", "vod-js-sdk-v6": "^1.4.3", "whatwg-fetch": "^3.5.0", "yorkie": "^2.0.0"}, "devDependencies": {"@types/whatwg-fetch": "^0.0.33", "@umijs/fabric": "^2.5.7", "cross-env": "^7.0.2"}}