.page-samplemanage {
  .g-query-box {
    .col-item {
      .ant-form-item {
        flex: auto;
        margin-bottom: 0;
        .ant-picker {
          width: 100%;
        }
        .text-right {
          text-align: right;
          .ant-btn:not(:last-child) {
            margin-right: 8px;
          }
        }
      }
    }
    .ant-btn-link {
      color: #3f969d;
    }
    .g-fold-box {
      .ant-col:nth-child(n + 3):not(:last-child) {
        display: none;
      }
    }
  }
  .samplemanage-table {
    .ant-input-search-button {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .anticon-search {
      width: 16px;
    }
  }

  .container {
    display: flex;
    flex-direction: column;
    .table-box {
      flex: 1;
      position: relative;
      .table-content {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        th,
        td {
          min-width: 88px;
          // &:first-child {
          //   min-width: 60px;
          // }
          &:nth-child(6),
          &:nth-child(17),
          &:nth-last-child(2),
          &:nth-last-child(3),
          &:nth-last-child(7),
          &:nth-last-child(9),
          &:nth-last-child(12) {
            min-width: 116px;
          }
          &:nth-child(2),
          &:nth-last-child(5) {
            min-width: 102px;
          }
        }
      }
    }
    .ellipsis {
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.report-pdf-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  > .report-pdf-item {
    color: #3f969d;
    text-decoration: underline;
    cursor: pointer;
  }
  .settle-error {
    color: #ff4d4f;
  }
}
.sample-pay-detail {
  .ant-col-24 {
    display: flex;
    align-items: flex-start;
    .query-lable {
      flex-shrink: 0;
      min-width: 80px;
      line-height: 32px;
      color: rgba(0, 0, 0, 0.85);
    }
    .ant-input,
    .ant-select,
    .ant-picker {
      flex: auto;
    }
  }
}
