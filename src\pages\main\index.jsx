import React, { useEffect } from 'react';
import { connect, history } from 'umi';

const Index = ({ menuTree = [], ...props }) => {
  useEffect(() => {
    if (menuTree[0]) {
      if (menuTree[0].children && menuTree[0].children.length > 0) {
        const { url } = menuTree[0].children[0];
        history.replace({ pathname: url });
      } else {
        const { url } = menuTree[0];
        history.replace({ pathname: url });
      }
    }
  }, [menuTree]);

  return <div></div>;
};

export default connect(state => {
  return {
    menuTree: state.root.menuTree,
  };
})(Index);
