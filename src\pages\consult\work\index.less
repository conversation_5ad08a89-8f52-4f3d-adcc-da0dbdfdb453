@import '~antd/lib/style/themes/default.less';

.connect {
  background: #fff;
  box-sizing: border-box;
  flex: 1;
}

.flexLine {
  display: flex;
  align-items: center;
  .flexItem {
    flex: 1;
  }
}
.maxWidth600 {
  max-width: 600px;
  overflow: hidden;
}
.nowrap {
  white-space: nowrap;
}
.tableOperText {
  color: @primary-color;
  margin-left: 15px;
  cursor: pointer;

  &:first-child {
    margin-left: 0;
  }
}

.tableOperTextDisable {
  color: #888;
  cursor: normal;
}

.status0 {
  color: #888;
}
.status1 {
  color: @primary-color;
}
.status2 {
  color: orange;
}
.tableReceiveNum {
  width: 80px;
  text-align: center;
  display: inline-block;
}
.tabColumReceiveNum {
  width: 240px;
  max-width: 240px;
  overflow: hidden;
}
.tabColumMax {
  max-width: 300px;
  word-break: break-all;
}

.sortItem {
  cursor: pointer;
}
