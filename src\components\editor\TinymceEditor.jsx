import React, { Component } from 'react';
import tinymce from 'tinymce';
import 'tinymce/themes/modern';
import 'tinymce/plugins/image';
import 'tinymce/plugins/media';

const PATH = 'assets/';

class TinymceEditor extends Component {
  state = {
    editorId: new Date().getTime(),
  };
  componentDidMount() {
    // console.log('TinymceEditor-componentDidMount');
    this.remount = true;
    this.initEditor();
  }

  componentWillReceiveProps(nextProps) {
    // 判断是否需要重载编辑器内容
    if (this.props.content) {
      this.remount = false;
    }
    if (nextProps.id != this.props.id) {
      this.remount = true;
    }
    this.remountContent(nextProps.content);
  }

  componentWillUnmount() {
    this.removeEditor();
  }

  initEditor = () => {
    const $this = this;
    const {
      name = 'idFile',
      // id,
      domain = '',
      height = 386,
      url = '/api/his/upfile',
    } = $this.props;
    tinymce.init({
      // selector: '#tinymce_editor',
      selector: `#${this.state.editorId}`,
      height,
      skin_url: `${PATH}tinymce/skins/merchant`,
      language_url: `${PATH}tinymce/langs/zh_CN.GB2312.js`,
      plugins: ['image media'],
      toolbar: ['undo redo | insert | styleselect | bold italic ' + '| alignleft aligncenter alignright alignjustify ' + '| outdent indent | link image media'],
      forced_root_block: 'div',
      style_formats: [
        {
          title: '首行缩进',
          block: 'div',
          styles: { 'text-indent': '2em' },
        },
        {
          title: '行高',
          items: [
            { title: '1', styles: { 'line-height': '1' }, inline: 'span' },
            { title: '1.5', styles: { 'line-height': '1.5' }, inline: 'span' },
            { title: '2', styles: { 'line-height': '2' }, inline: 'span' },
            { title: '2.5', styles: { 'line-height': '2.5' }, inline: 'span' },
            { title: '3', styles: { 'line-height': '3' }, inline: 'span' },
          ],
        },
        {
          title: '段落间距',
          block: 'div',
          styles: { 'margin-top': '10px' },
        },
      ],
      convert_urls: false,
      automatic_uploads: true,
      images_upload_url: `${domain}${url}`,
      file_picker_types: 'image',
      file_picker_callback: cb => {
        const input = document.createElement('input'); // eslint-disable-line
        input.setAttribute('type', 'file');
        input.setAttribute('accept', 'image/gif,image/jpeg,image/jpg,image/png');
        input.onchange = () => {
          const file = input.files[0];
          const reader = new FileReader(); // eslint-disable-line
          reader.readAsDataURL(file);
          reader.onload = () => {
            const blobid = `blobid${new Date().getTime()}`;
            const blobCache = tinymce.activeEditor.editorUpload.blobCache;
            const base64 = reader.result.split(',')[1];
            const blobInfo = blobCache.create(blobid, file, base64);
            blobCache.add(blobInfo);
            cb(blobInfo.blobUri(), { title: file.name });
          };
        };
        input.click();
      },
      images_upload_handler: async (blobInfo, success, failure, progress) => {
        let xhr;
        if (window.XMLHttpRequest) {
          // eslint-disable-line
          xhr = new XMLHttpRequest(); // eslint-disable-line
        } else {
          xhr = new ActiveXObject('Microsoft.XMLHTTP'); // eslint-disable-line
        }
        xhr.open('POST', `${domain}${url}`);
        xhr.withCredentials = true;
        xhr.upload.onprogress = e => {
          progress((e.loaded / e.total) * 100);
        };
        xhr.onerror = () => {
          failure(xhr.status);
        };
        xhr.onload = () => {
          if (xhr.status < 200 || xhr.status >= 300) {
            failure(xhr.status);
            return;
          }
          const json = JSON.parse(xhr.responseText);
          if (!json || typeof json.data != 'string') {
            failure(xhr.responseText);
            return;
          }
          success(json.data);
        };
        const formData = new FormData(); // eslint-disable-line
        formData.append(name, blobInfo.blob(), blobInfo.filename());
        xhr.send(formData);
      },
      setup: editor => {
        $this.editor = editor;
        editor.on('keyup change', () => {
          const content = editor.getContent();
          $this.props.onChange(content);
        });
      },
    });
  };

  removeEditor = () => {
    if (this.editor) {
      try {
        tinymce.EditorManager.execCommand('mceRemoveEditor', true, `${this.state.editorId}`);
        // tinymce.EditorManager.execCommand('mceRemoveEditor', true, 'tinymce_editor');
        // tinymce.remove(this.editor);
      } catch (e) {
        console.log('编辑器未装载或已卸载，无需手动卸载'); // eslint-disable-line
      }
    }
  };

  remountContent = (content = '') => {
    if (this.remount && this.editor) {
      try {
        this.editor.setContent(content);
      } catch (e) {
        console.log('正在装载编辑器时，无需手动重载内容'); // eslint-disable-line
      }
    }
  };

  render() {
    const { content } = this.props;
    console.log(content);

    // return <textarea id="tinymce_editor" value={content} readOnly />;
    return <textarea id={`${this.state.editorId}`} value={content} readOnly />;
  }
}

export default TinymceEditor;
