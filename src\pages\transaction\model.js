import { message } from 'antd';
import * as utils from '../../utils/utils';
import * as Api from './service';

export default {
  namespace: 'order',
  state: {
    hisName: '', // 打印 时 title为医院的名字
    hisList: [],
    busChannelLst: [],
    payChannelLst: [],
    busType: [],
    order: {
      orderLst: [],
      orderInfo: {},
      errorOrderLst: [],
      orderStatus: [],
    },
    quick: {
      quickLst: [],
    },
    process: {
      recordLst: [],
      orderInfo: {},
    },
    exportOrder: {
      hisList: [],
      loadStatus: 'unload',
      loadStatusDesc: '生成账单',
      reportId: false,
      channelCheckAll: false,
      channelIndeterminate: false,
      channelTypeGroup: [],
      businessCheckAll: false,
      businessIndeterminate: false,
      businessTypeGroup: [],
    },
    financial: {
      hisList: [],
      detail: {},
      option: false,
      summary: {},
      different: {},
    },
    financialLog: {
      merchantVal: [],
      logLst: [],
      logInfo: [],
    },
  },
  reducers: {
    updateHisLst(state, { payload: resData }) {
      return {
        ...state,
        hisList: resData.hisListData,
        hisName: resData.loginUserInfo ? resData.loginUserInfo.hisName : '',
      };
    },
    updateBusChannelLst(state, { payload: resData }) {
      return { ...state, busChannelLst: resData };
    },
    updatePayChannelLst(state, { payload: resData }) {
      return { ...state, payChannelLst: resData };
    },
    updateOrderStatus(state, { payload: resData }) {
      return {
        ...state,
        order: {
          ...state.order,
          orderStatus: resData,
        },
      };
    },
    updateBusType(state, { payload: resData }) {
      return { ...state, busType: resData };
    },
    updateOrderLst(state, { payload: resData }) {
      return {
        ...state,
        order: {
          ...state.order,
          orderLst: resData,
        },
      };
    },
    updateOrderInfo(state, { payload: resData }) {
      return {
        ...state,
        order: {
          ...state.order,
          orderInfo: resData,
        },
      };
    },
    updateErrorOrderLst(state, { payload: resData }) {
      return {
        ...state,
        order: {
          ...state.order,
          errorOrderLst: resData,
        },
      };
    },
    updateQuickOrderLst(state, { payload: resData }) {
      return {
        ...state,
        quick: {
          ...state.quick,
          quickLst: resData,
        },
      };
    },
    updateInputsOrderLst(state, { payload: resData }) {
      return {
        ...state,
        process: {
          ...state.process,
          recordLst: resData,
        },
      };
    },
    updateOrderbyorderId(state, { payload: resData }) {
      return {
        ...state,
        process: {
          ...state.process,
          orderInfo: resData,
        },
      };
    },
    saveExportOrder(state, { payload }) {
      return {
        ...state,
        exportOrder: {
          ...state.exportOrder,
          ...payload,
        },
      };
    },
    saveFinancial(state, { payload }) {
      return {
        ...state,
        financial: {
          ...state.financial,
          ...payload,
          option: {
            ...state.financial.option,
            ...payload.option,
          },
        },
      };
    },
    saveMerchant(state, { payload: resData }) {
      return {
        ...state,
        financialLog: {
          ...state.financialLog,
          merchantVal: resData,
        },
      };
    },
    saveLogLst(state, { payload: resData }) {
      return {
        ...state,
        financialLog: {
          ...state.financialLog,
          logLst: resData,
        },
      };
    },
    saveLogInfo(state, { payload: resData }) {
      return {
        ...state,
        financialLog: {
          ...state.financialLog,
          logInfo: resData,
        },
      };
    },
    clearExportOrder(state) {
      return { ...state, exportOrder: {} };
    },
    clearFinancial(state) {
      return { ...state, financial: {} };
    },
    clear(state) {
      return { ...state, busChannelLst: [], busType: [], quick: {} };
    },
    clearBusType(state) {
      return { ...state, busType: [] };
    },
  },
  effects: {
    /* 获取医院列表 */
    *fetchHisLst({ payload: reqData }, { call, put, select }) {
      const { data } = yield call(Api.getHisList, reqData);
      const loginUserInfo = yield select(state => state.root);
      yield put({
        type: 'updateHisLst',
        payload: {
          hisListData: data,
          ...loginUserInfo,
        },
      });
    },
    /* 查询业务办理通道 */
    *fetchBusChannelLst({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getBusChannel);
      if (data && data.data && data.data.length) {
        const resData = yield call(Api.loadBusinessTypeGroup, {}); // , { type: data.data[0].dictKey}
        yield put({ type: 'updateBusType', payload: resData.data });
        const resData1 = yield call(Api.getPayChannelWay, { busChannel: data.data[0].dictKey });
        yield put({ type: 'updatePayChannelLst', payload: resData1.data });
      }
      yield put({ type: 'updateBusChannelLst', payload: data });
    },
    /* 查询支付渠道及方法 */
    *fetchPayChannelLst({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getPayChannelWay, reqData);
      yield put({ type: 'updatePayChannelLst', payload: data });
    },
    /* 查询订单状态 */
    *fetchOrderStatus({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getBusStatus, reqData);
      yield put({ type: 'updateOrderStatus', payload: data });
    },
    /* 查询医院开通的业务类型 */
    *fetchBusType({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getBusType);
      yield put({ type: 'updateBusType', payload: data });
    },
    /* 查询订单列表 */
    *fetchOrderLst({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getOrderLst, reqData);
      if (data.code == 0) {
        yield put({ type: 'updateOrderLst', payload: data });
      } else {
        message.error(data.msg || '查询失败');
      }
    },
    /* 查询订单详情 */
    *fetchOrderInfo({ payload: reqData }, { call, put }) {
      const resData = yield call(Api.getOrderInfo, reqData);
      yield put({ type: 'updateOrderInfo', payload: resData });
    },
    /* 更新订单状态 */
    *refreshOrderInfo({ payload: reqData }, { call }) {
      const { data } = yield call(Api.updateOrderInfo, reqData);
      if (data && data.code == 0) {
        message.success('更新成功');
      } else {
        message.error(data.msg || '更新失败');
      }
      return data;
    },
    /* 退款 */
    *reBackOrder({ payload: reqData, next }, { call, put }) {
      const resData = yield call(Api.refundOrder, reqData.rebackData);
      if (resData.data && resData.data.code == 0) {
        message.success('操作成功');
        /* yield put({
          type: 'fetchOrderInfo',
          payload: reqData.rebackData,
        });
        yield put({
          type: 'fetchOrderbyorderId',
          payload: reqData.rebackData.id,
        }); */
        yield put({
          type: 'root/closeDrawer',
        });
        if (reqData.queryType == 'order') {
          const { data } = yield call(Api.getOrderLst, reqData.queryData);
          yield put({ type: 'updateOrderLst', payload: data });
          if (typeof next === 'function') {
            // 退款后停留在查看页面
            const { recordList = [] } = (data || {}).data || {};
            const { index } = reqData.rebackData || {};
            next(recordList[index]);
          }
        } else if (reqData.queryType == 'abnormal') {
          const { data } = yield call(Api.getErrorOrdersByPage, reqData.queryData);
          yield put({ type: 'updateErrorOrderLst', payload: data });
        } else {
          const { data } = yield call(Api.queryListSoon, reqData.queryData);
          if (data.code == 0) {
            yield put({ type: 'updateQuickOrderLst', payload: data });
          } else {
            message.error(data.msg || '查询失败');
          }
        }
      } else {
        message.error(resData.data.msg || '退款失败');
      }
    },
    /* 异常订单列表查询 */
    *fetchErrOrderLst({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getErrorOrdersByPage, reqData);
      yield put({ type: 'updateErrorOrderLst', payload: data });
    },
    /* 快速查询订单列表 */
    *fetchQuerySoonLst({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.queryListSoon, reqData);
      if (data.code == 0) {
        yield put({ type: 'updateQuickOrderLst', payload: data });
      } else {
        message.error(data.msg || '查询失败');
      }
    },
    /* 通过渠道查询业务类型 */
    *fetchBusTypeGroup({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.loadBusinessTypeGroup, reqData);
      yield put({ type: 'updateBusType', payload: data });
      if (data && data.data && data.data.length) {
        // 有预约挂号就默认选中预约挂号
        const resData = yield call(Api.getBusStatus, { busType: data.data[0].dictKey });
        yield put({ type: 'updateOrderStatus', payload: resData.data });
      }
    },
    /* 处理记录订单列表 */
    *fetchOrderLstByInputs({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.findOrderByInputs, reqData);
      yield put({ type: 'updateInputsOrderLst', payload: data });
    },
    /* 处理记录列表详情 */
    *fetchOrderbyorderId({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.findorderbyorderId, reqData);
      yield put({ type: 'updateOrderbyorderId', payload: data });
    },
    /* 处理记录 下载报表 */
    *downloadProcessRecord({ payload: reqData }) {
      const data = utils.jsonToQueryString({ ...reqData });
      utils.download(`/api/ordermanager/exportorderbyinputs?${data}`, reqData);
    },
    /* 异常订单下载 */
    *fetchDownloadErrorOrders({ payload: reqData }) {
      const data = utils.jsonToQueryString({ ...reqData });
      utils.download(`/api/order/downloaderrororders?${data}`, reqData);
    },
    *initExportOptions({ payload }, { call, put, select }) {
      const loginUserInfo = yield select(state => {
        return state.root.loginUserInfo;
      });
      let responseData;
      let hisList;
      switch (loginUserInfo.userType) {
        case 'platform': // 平台用户
          responseData = yield call(Api.getHisList);
          if (responseData && responseData.data && responseData.data.code == 0) {
            hisList = responseData.data.data;
            yield put({
              type: 'saveExportOrder',
              payload: {
                hisList,
              },
            });
            if (hisList && hisList.length > 0) {
              yield put({
                type: 'loadChannelTypeGroup',
                payload: {
                  hisId: hisList[0].hisId,
                },
              });
            }
          }
          break;
        case 'normal': // 普通用户
          yield put({
            type: 'loadChannelTypeGroup',
          });
          break;
        default:
          break;
      }
    },
    *loadChannelTypeGroup({ payload = {} }, { call, put }) {
      const { hisId = false } = payload;
      const { data } = yield call(Api.loadChannelTypeGroup, hisId ? { type: 'biz_channel', hisId } : { type: 'biz_channel' });
      if (data.code == 0) {
        const channelTypeGroup = utils.generateLabelValueList({
          list: data.data,
          labelIndex: 'dictValue',
          valueIndex: 'dictKey',
        });
        yield put({
          type: 'saveExportOrder',
          payload: {
            channelTypeGroup,
            channelCheckAll: channelTypeGroup.length == 1,
            channelIndeterminate: channelTypeGroup.length > 1,
          },
        });
        yield put({
          type: 'loadBusinessTypeGroup',
          payload: hisId ? { type: channelTypeGroup[0].value, hisId } : { type: channelTypeGroup[0].value },
        });
      } else {
        message.error(data.msg);
      }
    },
    *loadBusinessTypeGroup({ payload }, { call, put }) {
      const { data } = yield call(Api.loadBusinessTypeGroup, payload);
      if (data.code == 0) {
        const businessTypeGroup = utils.generateLabelValueList({
          list: data.data,
          labelIndex: 'dictValue',
          valueIndex: 'dictKey',
        });
        yield put({
          type: 'saveExportOrder',
          payload: {
            businessTypeGroup,
            businessCheckAll: businessTypeGroup.length == 1,
            businessIndeterminate: businessTypeGroup.length > 1,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *genarateReport({ payload }, { call, put }) {
      const { data } = yield call(Api.genarateReport, payload);
      if (data.code == 0) {
        yield put({
          type: 'saveExportOrder',
          payload: {
            reportId: data.data.reportId,
            loadStatus: 'loading',
          },
        });
      } else {
        yield put({
          type: 'saveExportOrder',
          payload: {
            loadStatus: 'exception',
            loadStatusDesc: data.msg,
          },
        });
      }
    },
    *getReportUrl({ payload }, { call, put }) {
      const { reportId } = payload;
      if (reportId) {
        const { data } = yield call(Api.getReportUrl, payload);
        const ret = data.data;
        if (data.code == 0 && ret) {
          if (ret.reportStatus != 0) {
            yield put({
              type: 'saveExportOrder',
              payload: {
                loadStatus: data.data.reportStatus == 1 ? 'success' : 'exception',
                reportUrl: data.data.reportUrl,
              },
            });
          }
        }
      }
    },
    loadReport({ payload }) {
      const today = new Date();
      const a = document.createElement('a'); // eslint-disable-line
      a.href = payload;
      a.download = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}.xls`;
      a.click();
      message.destroy();
      message.success('下载成功');
    },
    *initFinancialOptions({ payload }, { call, put, select }) {
      const { type } = payload;
      const loginUserInfo = yield select(state => {
        return state.root.loginUserInfo;
      });
      let responseData;
      let hisList;
      switch (loginUserInfo.userType) {
        case 'platform': // 平台用户
          responseData = yield call(Api.getHisList);
          if (responseData && responseData.data && responseData.data.code == 0) {
            hisList = responseData.data.data;
            yield put({
              type: 'saveFinancial',
              payload: {
                hisList,
              },
            });
            if (hisList && hisList.length > 0) {
              if (type == 'auto') {
                yield put({
                  type: 'getAutomaticOptionData',
                  payload: {
                    hisId: hisList[0].hisId,
                  },
                });
              } else if (type == 'last') {
                yield put({
                  type: 'getLastOptionData',
                  payload: {
                    hisId: hisList[0].hisId,
                  },
                });
              }
            }
          }
          break;
        case 'normal': // 普通用户
          if (type == 'auto') {
            yield put({
              type: 'getAutomaticOptionData',
            });
          } else if (type == 'last') {
            yield put({
              type: 'getLastOptionData',
            });
          }
          break;
        default:
          break;
      }
    },
    *getLastOptionData({ payload = {} }, { call, put }) {
      const { hisId = false } = payload;
      const { data } = yield call(Api.getLastOptionData, hisId ? { hisId } : {});
      if (data.code === 0) {
        yield put({
          type: 'saveFinancial',
          payload: {
            option: data.data,
          },
        });
        const ret = data.data;
        if (ret) {
          const param = {
            beginDate: ret.billDate || '',
            insideMerchantId: (ret.channelList && ret.channelList.length > 0 && ret.channelList[0].code) || '',
          };
          yield put({
            type: 'getFinancialProcess',
            payload: param,
          });
          yield put({
            type: 'getFinancialSummary',
            payload: param,
          });
        }
      } else {
        message.error(data.msg);
      }
    },
    *getAutomaticOptionData({ payload = {} }, { call, put }) {
      const { hisId = false } = payload;
      const { data } = yield call(Api.getAutomaticOptionData, hisId ? { hisId } : {});
      if (data.code === 0) {
        yield put({
          type: 'saveFinancial',
          payload: {
            option: data.data,
          },
        });
        const ret = data.data;
        if (ret) {
          yield put({
            type: 'getFinancialSummary',
            payload: {
              beginDate: ret.beginDate || '',
              endDate: ret.endDate || '',
              insideMerchantId: (ret.channelList && ret.channelList.length > 0 && ret.channelList[0].code) || '',
            },
          });
        }
      } else {
        message.error(data.msg);
      }
    },
    *generateFinancial({ payload }, { call }) {
      const { data } = yield call(Api.generateFinancial, payload);
      if (data.code != 0) {
        message.error(data.msg);
        // notification.open({
        //   icon: <Icon type="info-circle" style={{ color: '#f57f17' }} />, // eslint-disable-line
        //   message: '温馨提示',
        //   description: data.msg,
        //   duration: 6,
        // });
      }
    },
    *getFinancialDetail({ payload }, { call, put }) {
      yield put({
        type: 'saveFinancial',
        payload: {
          detail: {},
        },
      });
      const { data } = yield call(Api.getFinancialDetail, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveFinancial',
          payload: {
            detail: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *getFinancialSummary({ payload }, { call, put }) {
      yield put({
        type: 'saveFinancial',
        payload: {
          summary: {},
          different: {},
          process: {},
        },
      });
      const { data } = yield call(Api.getFinancialSummary, payload);
      if (data.code === 0) {
        if (data.data && data.data.items && data.data.items.length > 0) {
          data.data.items.map((item, idx) => {
            item.key = idx;
            return item;
          });
        }
        yield put({
          type: 'saveFinancial',
          payload: {
            summary: data.data,
          },
        });
        const isFlat = data.data.isFlat;
        if (typeof isFlat !== 'undefined' && !isFlat) {
          yield put({
            type: 'getFinancialDiff',
            payload: {
              ...payload,
              pageNum: 1,
            },
          });
        }
      } else {
        message.error(data.msg);
      }
    },
    *getFinancialDiff({ payload }, { call, put }) {
      yield put({
        type: 'saveFinancial',
        payload: {
          different: {},
        },
      });
      const { data } = yield call(Api.getFinancialDiff, payload);
      if (data.data && data.data.recordList && data.data.recordList.length > 0) {
        data.data.recordList.map((item, idx) => {
          item.key = idx + 1;
          return item;
        });
      }
      if (data.code === 0) {
        yield put({
          type: 'saveFinancial',
          payload: {
            different: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *getFinancialProcess({ payload }, { call, put }) {
      const { data } = yield call(Api.getFinancialProcess, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveFinancial',
          payload: {
            process: data.data,
          },
        });
      }
    },
    *downloadFinancial({ payload }, { call }) {
      const { data } = yield call(Api.downloadFinancial, payload);
      if (data.code === 0) {
        const a = document.createElement('a'); // eslint-disable-line
        const url = data.data.downloadUrl;
        const filename = `${new Date().getTime()}.csv`;
        a.href = url;
        a.download = filename;
        a.click();
      } else {
        message.error(data.msg);
      }
    },
    /* 手动刷新订单 */
    *getorderRefresh({ payload }, { call, put }) {
      const { data } = yield call(Api.getorderRefresh, payload.reqData);
      if (data.code == 0) {
        yield put({
          type: 'fetchOrderInfo',
          payload: payload.queryData,
        });
        const bizOrderId = payload.queryData.id;
        yield put({
          type: 'fetchOrderbyorderId',
          payload: bizOrderId,
        });
      } else {
        message.error('刷新失败');
      }
    },
    /* 对账日志获取商户号 */
    *getFinaLogMerchant({ payload }, { call, put }) {
      const { data } = yield call(Api.getLogMerchant, payload);
      yield put({ type: 'saveMerchant', payload: data });
    },
    /* 对账日志列表 */
    *getFinaLogLst({ payload }, { call, put }) {
      const { data } = yield call(Api.getLogLst, payload);
      yield put({ type: 'saveLogLst', payload: data });
    },
    /* 对账日志详情 */
    *getLogProcess({ payload }, { call, put }) {
      const { data } = yield call(Api.getLogProcess, payload);
      yield put({ type: 'saveLogInfo', payload: data });
    },
    /* 重新对账 */
    *getRepeatbillRes({ payload }, { call, put }) {
      const { data } = yield call(Api.getRepeatbill, payload);
      if (data.code == 0) {
        message.success('操作成功');
        yield put({
          type: 'root/closeDrawer',
        });
      } else {
        message.error(data.msg || '操作失败');
      }
    },
  },
};
