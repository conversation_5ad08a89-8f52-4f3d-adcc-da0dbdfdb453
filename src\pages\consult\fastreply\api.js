import { post } from '@/utils/request';

export function getBusType(param) {
  return post('/api/receive/getBusType', { data: param }) || {};
}

export function getFastList(param) {
  return post('/api/receive/queryFastList', { data: param }) || {};
}

export function queryFastInfo(param) {
  return post('/api/receive/queryFastInfo', { data: param }) || {};
}

export function addFastQuestion(param) {
  return post('/api/receive/addFastQuestion', { data: param }) || {};
}

export function updateFastQuestion(param) {
  return post('/api/receive/updateFastQuestion', { data: param }) || {};
}

export function deleteFastQuestion(param) {
  return post('/api/receive/deleteFastQuestion', { data: param }) || {};
}
