@charset "utf-8";
@import '../../resources/styles/mixins';

.page-hospital,
.page-department,
.page-coordinate,
.page-doctor,
.doctor-body,
.page-article,
.page-article-type,
.page-article-list,
.article-type-body,
.article-list-body {
  flex: auto;
  display: flex;
  flex-direction: column;

  .doctor-option,
  .article-type-option,
  .article-list-option {
    padding: 20px 24px 10px;
    background: #fff;
    font-size: 12px;
    display: flex;

    .tab-label {
      padding: 2px 8px 2px 0;
    }

    .tab-btns {
      position: relative;
      flex: 1;
      line-height: 22px;

      .tab-btn {
        // display: inline-block;
        margin-right: 16px;
        padding: 2px 12px;
        cursor: pointer;

        &.active {
          background: @primary-color;
          border-radius: 4px;
          color: #fff;
        }
      }
    }
  }

  .doctor-list,
  .article-list,
  .article-type-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin: 24px;
    padding: 16px 24px 24px;
    background: #fff;
    border-radius: 4px;
  }

  .hospital-detail,
  .department-detail,
  .coordinate-body,
  .article-detail {
    flex: auto;
    margin: 24px;
    padding: 16px 24px 24px;
    background: #fff;
    border-radius: 4px;
    display: flex;
    min-height: 600px;

    .hospital-content,
    .article-content {
      width: 1px;
      position: relative;
      flex: auto;
      padding-top: 8px;

      .hospital-title {
        display: flex;
        font-size: 28px;
        height: 28px;
        line-height: 28px;
        color: @title-color;

        .hospital-title-text {
          flex: auto;
          overflow: hidden;
        }

        .hospital-title-option {
          font-size: 14px;
          min-width: 37px;
        }
      }

      .hospital-brief {
        font-size: 14px;
        margin-top: 22px;
        padding: 7px 10px 13px;
        background: #f9f9f9;
        border-left: 2px solid #d9d9d9;
      }

      .hospital-banner {
        max-width: 680px;
        margin-top: 15px;
        text-align: center;
      }

      .hospital-desc {
        margin-top: 20px;
        font-size: 14px;
        overflow: hidden;
      }
    }

    .avatar-uploader,
    .avatar-uploader-trigger,
    .avatar {
      width: 350px;
      height: 174px;
      text-align: center;
    }

    .avatar-uploader {
      display: block;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
    }

    .avatar-uploader-trigger {
      display: table-cell;
      vertical-align: middle;
      color: #999;
    }
  }
}

.coordinate-body {
  .list-edifice {
    position: relative;
    width: 367px;
    font-size: 14px;

    .list-title {
      font-weight: 500;
      color: @title-color;
      padding-left: 9px;
    }

    .list-body {
      position: absolute;
      top: 28px;
      right: 0;
      bottom: 0;
      left: 0;
      border: 1px solid @border-color;
      border-radius: 4px;

      .list-items {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 62px;
        left: 0;
        overflow-x: hidden;
        overflow-y: scroll;

        .list-item {
          height: 52px;
          padding-left: 9px;

          a {
            font-size: 12px;
            margin: 2px 4px;
            visibility: hidden;
          }

          &:hover {
            color: @primary-5;

            a {
              visibility: visible;
            }
          }

          &.disabled {
            color: @disable-color;
          }

          &.active {
            background: @primary-color;
            color: #fff;

            a {
              color: #fff;

              &:hover {
                color: @link-color;
              }
            }
          }

          &:first-child {
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
          }
        }
      }

      .list-button {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        padding: 17px 0;
        text-align: center;
      }
    }
  }

  .list-floor {
    flex: 1;
    margin: 28px 0 0 26px;
    box-shadow: 0 0 11px 0 rgba(168, 168, 168, 0.21);
    border-radius: 4px;
    position: relative;

    .list-title {
      font-weight: 500;
      color: @title-color;
      padding: 20px 20px 14px 26px;
    }

    .list-body {
      position: absolute;
      top: 52px;
      right: 0;
      bottom: 0;
      left: 0;

      .list-table {
        position: absolute;
        top: 0;
        right: 20px;
        bottom: 62px;
        left: 26px;
        font-size: 12px;
        overflow-x: hidden;
        overflow-y: scroll;
      }
    }

    .list-button {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      padding: 17px 0;
      text-align: center;
    }
  }

  .edifice-coordinate {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;

    .image-box {
      position: relative;
      width: 380px;
      height: 194px;
      margin-top: 180px;

      .option-box {
        position: absolute;
        z-index: 2;
        background-color: rgba(55, 55, 55, 0.6);
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        display: none;
      }

      &:hover {
        .option-box {
          display: inherit;
        }
      }
    }
  }
}

.dept-panel {
  display: flex;
  flex: auto;
  flex-direction: column;

  .dept-panel-title {
    font-size: 14px;
    color: @title-color;
    display: flex;
    line-height: 28px;
    font-weight: 700;
  }

  .dept-panel-body {
    display: flex;
    flex: auto;
    margin-top: 4px;

    .dept-panel-list {
      display: flex;
      flex: 2;
    }

    .dept-detail,
    .dept-edit {
      display: flex;
      flex: 3;

      .no-dept {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .no-dept-img {
          width: 184px;
          height: 184px;
          background-image: url(../../resources/images/common/pet.png);
          background-size: 100%;
        }

        .no-dept-text {
          margin-top: 40px;
          font-size: 14px;
        }
      }
    }

    .dept-detail,
    .dept-edit {
      margin-left: 24px;
    }

    .dept-panel-list {
      cursor: default;
      border: 1px solid @border-color;
      border-radius: 4px;

      .panel-list-left {
        border-right: 1px solid @border-color;

        .list-items {
          .list-item:first-child {
            > .item-name {
              border-top-left-radius: 4px;
            }
          }

          .list-item {
            &.indeterminate {
              > .item-name {
                background: #f7f7f7;
              }
            }
          }
        }
      }

      .panel-list-right {
        .list-items {
          > .list-item:first-child {
            > .item-name {
              border-top-right-radius: 4px;
            }
          }

          .list-item {
            &.indeterminate {
              > .item-name {
                background: #f7f7f7;
              }
            }
          }
        }
      }

      .panel-list-left,
      .panel-list-right {
        position: relative;
        flex: 1;

        .list-items {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 62px;
          left: 0;
          overflow-x: hidden;
          overflow-y: scroll;

          .list-item {
            > .list-sub-items {
              display: none;
            }

            .item-name {
              display: flex;
              align-items: center;
              font-size: 14px;
              padding: 8px 0 8px 24px;

              > span {
                flex: auto;
              }

              > a {
                font-size: 12px;
                margin: 2px 4px;
                visibility: hidden;
                min-width: 24px;
              }

              &:hover {
                color: @primary-5;

                > a {
                  visibility: visible;
                }
              }
            }

            &.active {
              > .item-name {
                background: @primary-color;
                color: #fff;

                > a {
                  color: #fff;

                  &:hover {
                    color: @link-color;
                  }
                }
              }
            }

            &.indeterminate {
              > .list-sub-items {
                display: inherit;
              }
            }
          }

          .list-item.list-sub-items {
            .item-name,
            .item-option {
              font-size: 12px;
              padding-left: 48px;
            }
          }
        }

        .list-button {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          padding: 17px 0;
          text-align: center;
        }
      }
    }

    .dept-detail-panel {
      display: flex;
      flex: 1;
      flex-direction: column;
      box-shadow: 0 0 11px 0 rgba(168, 168, 168, 0.21);
      border-radius: 4px;

      .detail-panel-title {
        display: flex;
        color: #fff;
        background: @primary-color;
        padding: 16px 37px 16px 70px;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;

        > span {
          font-size: 18px;
          flex: 1;
        }
      }

      .detail-panel-body {
        flex: 1;
        padding: 6px 57px 0 70px;

        .detail-list {
          display: flex;
          padding: 6px 0;

          .detail-label {
            padding-right: 8px;
          }

          .detail-intro {
            flex: 1;
          }
        }
      }
    }
  }
}

.page-account-list {
  display: flex;
  flex-direction: column;
  flex: auto;

  .option-panel {
    padding: 12px 24px;
    background: #fff;
  }

  .account-list {
    flex: 1;
    margin: 24px;
    padding: 16px 21px;
    border-radius: 4px;
    background: #fff;
  }
}

.page-account-edit {
  display: flex;
  padding: 0 0 126px 0;
  flex: auto;
  color: @title-color;
  font-size: 12px;
  border-radius: 4px;
  margin: 24px;
  background: #fff;
  justify-content: center;

  .edit-panel {
    width: 943px;

    .edit-board {
      margin-top: 30px;

      .edit-tuber {
        padding: 36px 0 10px 0;
        border-radius: 4px;
        background: #f9f9f9;
      }

      .edit-title {
        font-weight: 500;
        margin-bottom: 5px;
        font-size: 14px;
      }

      .edit-permission-table {
        border: 1px solid @border-color;
        border-radius: 4px;

        > .permission-table-node:first-child {
          border-right: 0;
        }

        .permission-table-node {
          display: flex;
          align-items: center;
          padding: 18.5px 20px;
          min-width: 156px;
          border-right: 1px solid @border-color;
          border-bottom: 1px solid @border-color;
        }

        .permission-table-cell {
          display: flex;
          flex: auto;
          border-bottom: 1px solid @border-color;

          &:last-child {
            border-bottom: 0;
          }

          .permission-table-board {
            display: flex;
            flex: auto;
            flex-wrap: wrap;

            > .permission-table-cell {
              width: 100%;
              border-right: 0;

              &:last-child {
                border: 0;
              }
            }

            > .permission-table-node {
              border: 0;
            }
          }

          > .permission-table-node {
            border-bottom: 0;
          }
        }
      }
    }
  }
}

.page-modify-password {
  flex: auto;
  margin: 24px;
  border-radius: 4px;
  background: #fff;
}

.editable-row-text {
  padding: 5px;
}

.editable-row-operations a {
  margin-right: 8px;
}

.hospital-info-edit {
  .ant-form-item-label {
    max-width: 75px;
  }
}
