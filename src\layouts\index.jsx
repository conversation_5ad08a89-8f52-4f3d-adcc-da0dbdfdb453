import React from 'react';
import { withRouter, connect } from 'umi';
import { Layout } from 'antd';

import SiderMenu from './components/SiderMenu';
import TopMenu from './components/TopMenu';
import logo from '@/resources/images/common/logo.png';
import styles from './style.less';

const { Header, Sider, Content } = Layout;
// 路由白名单，配置在里面时，将不再渲染菜单
const ROUTE_WHITE_LIST = ['/', '/login', '/resetPwd', '/main', '/inquery/index', '/download'];

class LayoutFullWrapper extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      menuFold: true,
    };
  }

  componentDidMount() {
    this.initApp();
  }
  initApp = () => {
    const { dispatch, location } = this.props;
    dispatch({
      type: 'root/initApp',
      payload: location.pathname,
    });
  };

  render() {
    const { rootData, location, children, dispatch } = this.props;
    const { menuTree = [], loginUserInfo = {}, drawerStatus } = rootData;
    const { pathname } = location;
    // const match = pathname.match(/\/(\w*)\/(\w*)\/(\w*)/);
    const match = pathname.match(/\/([\w-]*)\/([\w-]*)\/([\w-]*)/);
    const siderDisplay = match && match[3] ? 'none' : '';

    if (ROUTE_WHITE_LIST.includes(location.pathname)) {
      return <React.Fragment>{children}</React.Fragment>;
    }

    return (
      <Layout
        style={{
          minWidth: 1340,
          minHeight: '100vh',
          overflow: `${drawerStatus ? 'hidden' : ''}`,
        }}
      >
        <Sider style={{ display: siderDisplay }} width={208} className={styles['g-sider']}>
          <div style={{ width: 208, padding: '40px 0', textAlign: 'center' }}>
            <img src={logo} style={{ width: 68.7, borderRadius: '50%' }} alt="" />
            <div style={{ marginTop: 16, color: '#fff', fontSize: 16, fontWeight: 700 }}>湖南家辉遗传专科医院</div>
          </div>
          <SiderMenu data={menuTree} location={location} dispatch={dispatch} />
        </Sider>
        <Layout style={{ paddingLeft: siderDisplay ? '' : 208 }}>
          <Header style={{ padding: 0, height: '54px', lineHeight: '54px' }}>
            <TopMenu loginUserInfo={loginUserInfo} location={location} />
          </Header>
          <Layout className={styles.container}>
            <Content className={styles.content}>{children}</Content>
          </Layout>
        </Layout>
      </Layout>
    );
  }
}

export default withRouter(
  connect(state => {
    return {
      rootData: state.root,
    };
  })(LayoutFullWrapper),
);
