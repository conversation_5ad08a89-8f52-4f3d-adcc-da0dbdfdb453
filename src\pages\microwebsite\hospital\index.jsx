import React from 'react';
import { Link } from 'react-router-dom';
import { Tabs } from 'antd';
import { connect } from 'dva';

import Phone from './components/Phone';

import '../style.less';

const TabPane = Tabs.TabPane;

class HospitalDetail extends React.Component {
  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'microwebsite/hospitalList',
      next: 'hospitalDetail',
    });
  }

  hospitalDetail = hisId => {
    const { dispatch } = this.props;
    dispatch({
      type: 'microwebsite/hospitalDetail',
      payload: {
        hisId,
      },
    });
  };

  render() {
    const { detail = {}, list = [], permissionData = {} } = this.props;
    const { btns = {} } = permissionData;
    if (!list || list.length == 0) return null;
    return (
      <div className="page-hospital">
        <Tabs
          animated={false}
          defaultActiveKey={`${list[0].hisId}`}
          style={{ display: 'flex', flex: 'auto', flexDirection: 'column' }}
          onChange={hisId => {
            this.hospitalDetail(hisId);
          }}
        >
          {list.map(item => {
            return (
              <TabPane tab={item.hisName} key={`${item.hisId}`}>
                <div className="hospital-detail">
                  <div style={{ padding: '24px 71.6px 0 8px' }}>
                    <Phone detail={detail} />
                  </div>
                  <div className="hospital-content">
                    <div className="hospital-title">
                      <div className="hospital-title-text">{detail.name}</div>
                      {btns['/microwebsite/hospital/edit'] ? (
                        <Link className="hospital-title-option" to={`/microwebsite/hospital/edit?hisId=${item.hisId}`}>
                          编辑
                        </Link>
                      ) : null}
                    </div>
                    <div className="hospital-brief">
                      <div>等级：{detail.levelName}</div>
                      <div style={{ marginTop: 10 }}>电话：{detail.telNo}</div>
                      <div style={{ marginTop: 10 }}>地址：{detail.address}</div>
                    </div>
                    <img className="hospital-banner" src={detail.imgPath} alt="" />
                    <div className="hospital-desc" dangerouslySetInnerHTML={{ __html: detail.introduction }} />
                  </div>
                </div>
              </TabPane>
            );
          })}
        </Tabs>
      </div>
    );
  }
}

export default connect(state => {
  return {
    ...state.microwebsite.hospital,
    permissionData: state.root.permissionData,
  };
})(HospitalDetail);
