import React from 'react';
import { PhotoProvider, PhotoConsumer } from 'react-photo-view';
import 'react-photo-view/dist/index.css';
import { Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';

const PreviewImage = props => {
  const { images = [], styles = {}, isDelete } = props;
  const toolbarRender = (val = {}) => {
    return (
      <div onClick={() => val.onRotate(val.rotate + 90)}>
        <Icon type="reload" style={{ color: 'white', fontSize: 18 }} />
      </div>
    );
  };

  return (
    <PhotoProvider toolbarRender={toolbarRender}>
      {images.map((item, index) => (
        <div key={index}>
          <PhotoConsumer key={item.src} src={item.src} intro={item.src}>
            <img src={item.src} alt="" style={styles} />
          </PhotoConsumer>
          {/* {
            isDelete ? (<div style={{ color: 'red', textAlign: 'center', cursor: 'pointer' }}>删除</div>) : (null)
          } */}
        </div>
      ))}
    </PhotoProvider>
  );
};

export default PreviewImage;
