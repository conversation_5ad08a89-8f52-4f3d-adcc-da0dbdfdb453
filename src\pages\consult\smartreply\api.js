import { post } from '@/utils/request';

export function getBusType(param) {
  return post('/api/consult/area/group/list', { data: param }) || {};
}

export function getRobotList(param) {
  return post('/api/receive/queryChatRobotList', { data: param }) || {};
}

export function queryChatRobotInfo(param) {
  return post('/api/receive/queryChatRobotInfo', { data: param }) || {};
}

export function addChatRobot(param) {
  return post('/api/receive/addChatRobot', { data: param }) || {};
}

export function updateChatRobot(param) {
  return post('/api/receive/updateChatRobot', { data: param }) || {};
}

export function deleteChatRobot(param) {
  return post('/api/receive/deleteChatRobot', { data: param }) || {};
}
