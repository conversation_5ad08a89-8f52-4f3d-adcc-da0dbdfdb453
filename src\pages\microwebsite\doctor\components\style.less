@charset "utf-8";
@import '../../../../resources/styles/mixins';

.doctor-drawer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow-x: hidden;
  overflow-y: scroll;

  .horizontal-line {
    display: flex;
    margin-bottom: 11px;

    .horizontal-title {
      flex: auto;
      font-size: 14px;
      font-weight: 500;
      color: @title-color;
    }

    .horizontal-label {
      color: @text-color;
      width: 72px;
    }

    .horizontal-text {
      flex: auto;
      color: @text-color;
    }
  }

  .doctor-edit-head {
    display: flex;
    font-size: 16px;
    padding: 19px 22px 24px 15px;

    .edit-title {
      flex: 1;
      font-weight: 500;
      color: @title-color;
    }
  }

  .doctor-edit-body {
    margin: 26px 49px 55px 18px;

    .avatar-uploader,
    .avatar-uploader-trigger,
    .avatar {
      width: 290px !important;
      height: 114px;
    }
    .avatar-uploader {
      display: block;
      background: #f9f9f9;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
    }
    .avatar-uploader-trigger {
      padding-top: 26px;
      font-size: 28px;
      color: #999;
      text-align: center;
    }
  }

  .doctor-detail-head {
    position: relative;
    background: @primary-color;
    padding: 19px 22px 10px 0;
    height: 165px;

    .doctor-detail-avatar {
      max-width: 100px;
      border-radius: 4px;
      margin: 56px 0 0 56px;
    }
  }

  .doctor-detail-body {
    position: relative;
    font-size: 14px;
    padding: 10px 60px 0 173px;
  }
}
