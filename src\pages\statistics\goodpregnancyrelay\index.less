@import '~antd/lib/style/themes/default.less';

:global #goodpregnancyrelay {
  .ant-row::before,
  .ant-row::after {
    display: initial;
  }
  .ant-col-8 {
    width: 25%;
    padding: 10px 0;
  }
  .ant-form,
  .ant-legacy-form {
    line-height: 3;
    background-color: #fff;
    padding: 15px 15px 0 15px;
  }
  .ant-legacy-form-item {
    margin-bottom: 0;
  }
  .ant-col {
    padding: 3px 0;
  }
  .antd-pro-pages-statistics-goodpregnancyrelay-index-operArea {
    margin-top: 0 !important;
    background-color: #fff;
    padding: 20px 0 20px 35px;
  }
  .ant-col-14 {
    width: 68%;
  }
}

:global {
  .antd-pro-layouts-about-layout-frame {
    margin: 0 !important;
  }
}

.searchModule {
  display: flex;
  flex-direction: column;
}

.minWidth120 {
  min-width: 120px;
}
.minWidth150 {
  min-width: 150px;
}
.maxWidth300 {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
}
.tableOperText {
  color: @primary-color;
  margin-left: 15px;
  cursor: pointer;

  &:first-child {
    margin-left: 0;
  }
}

.tableOperTextDisable {
  color: #888;
  cursor: normal;
}

.operArea {
  display: flex;
  align-items: flex-end;
  margin-top: 30px;

  .left {
    flex: 1;
  }
}

.right {
  display: flex;
  justify-content: flex-end;
  padding: 16px 23px 0px 0;
  margin-bottom: -15px;
}

.tableCol {
  padding-top: 10px;

  &:first-child {
    padding-top: 0;
  }
}

.selTip {
  font-size: 12px;
  color: red;
}

.noticeList {
  max-height: 750px;
  overflow-y: auto;
  width: 900px;
}

:global {
  .ant-table-pagination.ant-pagination {
    margin: 16px 30px;
  }
  .ant-modal-body {
    padding: 15px 25px;
  }
  .ant-upload-picture-card-wrapper {
    padding: 17px;
  }
}

.patientInfo {
  display: flex;
  justify-content: space-between;
  padding-top: 5px;
  div {
    flex: 1;
  }
  div:nth-child(2) {
    text-align: center;
  }
  div:nth-child(3) {
    text-align: right;
  }
}

.title {
  font-weight: bold;
  margin-top: 10px;
  padding: 10px 0;
}

.flexLine {
  display: flex;
  align-items: flex-start;
  margin-top: 15px;
  .flexLabel {
    width: 75px;
    text-align: right;
    padding-right: 10px;
    span {
      color: red;
      padding-right: 2px;
    }
  }
  .flexItem {
    flex: 1;
    .countNum {
      margin-top: -24px;
      position: absolute;
      right: 35px;
    }
  }
  .flexInput {
    input {
      width: 180px;
    }
    margin-right: 10px;
  }
}

.hidden {
  display: none;
}

.operArea {
  display: flex;
  align-items: flex-end;
  margin-top: 30px;

  .left {
    flex: 1;
  }
}

.right {
  display: flex;
  justify-content: flex-end;
  padding: 16px 23px 0px 0;
  margin-bottom: -15px;
  align-items: center;
}
