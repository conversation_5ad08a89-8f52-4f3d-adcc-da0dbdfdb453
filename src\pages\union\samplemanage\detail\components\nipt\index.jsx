/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, DatePicker, Select } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { niptjhbbTemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, niptjhbbTemplate: { ...niptjhbbTemplate, ...payload } },
      },
    });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="孕妇基本信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item label="孕妇年龄（周岁）">
                <Input placeholder="请输入" value={niptjhbbTemplate.field1} onChange={e => changeData({ field1: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="胎儿父亲年龄（周岁）">
                <Input placeholder="请输入" value={niptjhbbTemplate.field2} onChange={e => changeData({ field2: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="孕周（周、天）" required>
                <div className="flex-box">
                  <Form.Item
                    name="aaa"
                    initialValue={niptjhbbTemplate.field3}
                    rules={[{ required: true, message: '请输入孕周(周)' }]}
                    // validateStatus={!niptjhbbTemplate.field3 ? 'error' : 'success'}
                    // help={!niptjhbbTemplate.field3 ? '请输入孕周(周)' : ''}
                  >
                    <Input placeholder="请输入周" value={niptjhbbTemplate.field3} onChange={e => changeData({ field3: e.target.value })} />
                  </Form.Item>
                  <Form.Item
                    name="bbb"
                    initialValue={niptjhbbTemplate.field4}
                    rules={[{ required: true, message: '请输入孕周(天)' }]}
                    // validateStatus={!niptjhbbTemplate.field4 ? 'error' : 'success'}
                    // help={!niptjhbbTemplate.field4 ? '请输入孕周(周)' : ''}
                  >
                    <Input placeholder="请输入天" value={niptjhbbTemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
                  </Form.Item>
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="末次月经">
                {/* <Input placeholder="请输入" value={niptjhbbTemplate.field5} onChange={e => changeData({ field5: e.target.value })} /> */}
                <DatePicker format="YYYY-MM-DD" value={niptjhbbTemplate.field5 ? moment(niptjhbbTemplate.field5) : null} onChange={(date, dateString) => changeData({ field5: dateString })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="月经周期（天）">
                <Input placeholder="请输入" value={niptjhbbTemplate.field6} onChange={e => changeData({ field6: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="孕产史" key="1">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item label="孕次">
                <Input placeholder="请输入" value={niptjhbbTemplate.field7} onChange={e => changeData({ field7: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="产次">
                <Input placeholder="请输入" value={niptjhbbTemplate.field8} onChange={e => changeData({ field8: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="不良孕产史">
                <div className="flex-box">
                  <Radio.Group value={niptjhbbTemplate.field9} onChange={e => changeData({ field9: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  {niptjhbbTemplate.field9 == '有' ? (
                    <div className="flex-box">
                      <div className="flex-shrink">描述</div>
                      <Input placeholder="请输入" value={niptjhbbTemplate.field30} onChange={e => changeData({ field30: e.target.value })} />
                    </div>
                  ) : null}
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="家族遗传病">
                <div className="flex-box">
                  <Radio.Group value={niptjhbbTemplate.field10} onChange={e => changeData({ field10: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  {niptjhbbTemplate.field10 == '有' ? (
                    <div className="flex-box">
                      <div className="flex-shrink">描述</div>
                      <Input placeholder="请输入" value={niptjhbbTemplate.field31} onChange={e => changeData({ field31: e.target.value })} />
                    </div>
                  ) : null}
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="夫妻双方染色体核型/基因/细胞学检查">
                <div className="flex-box">
                  <Radio.Group value={niptjhbbTemplate.field11} onChange={e => changeData({ field11: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  {niptjhbbTemplate.field11 == '有' ? (
                    <div className="flex-box">
                      <div className="flex-shrink">描述</div>
                      <Input placeholder="请输入" value={niptjhbbTemplate.field12} onChange={e => changeData({ field12: e.target.value })} />
                    </div>
                  ) : null}
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="产前检查" key="1">
          <Row gutter={[16, 0]}>
            <Col span={12}>
              <Form.Item label="妊娠情况">
                <div className="flex-box">
                  <Radio.Group value={niptjhbbTemplate.field13} onChange={e => changeData({ field13: e.target.value })}>
                    <Radio value="单胎">单胎</Radio>
                    <Radio value="双胎">双胎</Radio>
                    <Radio value="双胎减胎">双胎减胎</Radio>
                    <Radio value="其他">其他</Radio>
                  </Radio.Group>
                  {niptjhbbTemplate.field13 == '其他' ? (
                    <div className="flex-box">
                      <Input placeholder="请输入" value={niptjhbbTemplate.field14} onChange={e => changeData({ field14: e.target.value })} />
                    </div>
                  ) : null}
                </div>
              </Form.Item>
            </Col>
            {/* <Col span={12}>
              <Form.Item label="试管婴儿">
                <Radio.Group value={niptjhbbTemplate.field15} onChange={e => changeData({ field15: e.target.value })}>
                  <Radio value="否">否</Radio>
                  <Radio value="是">是</Radio>
                </Radio.Group>
              </Form.Item>
            </Col> */}
            <Col span={12}>
              <Form.Item label="是否IVF">
                <Select placeholder="请选择" allowClear value={niptjhbbTemplate.field32} onChange={v => changeData({ field32: v })}>
                  <Option value="是">是</Option>
                  <Option value="否">否</Option>
                </Select>
                {/* <Radio.Group value={niptjhbbTemplate.field32} onChange={e => changeData({ field32: e.target.value })}>
                  <Radio value="是">是</Radio>
                  <Radio value="否">否</Radio>
                </Radio.Group> */}
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="超声检查">
                <Checkbox.Group value={niptjhbbTemplate.field16 ? niptjhbbTemplate.field16.split(',') : []} onChange={v => changeData({ field16: v.join(',') })}>
                  <Checkbox value="未见异常">未见异常</Checkbox>
                  <div className="flex-box">
                    <Checkbox value="胎儿结构异常" className="flex-shrink">
                      胎儿结构异常
                    </Checkbox>
                    <div className="flex-box">
                      <Input placeholder="请输入" value={niptjhbbTemplate.field17} onChange={e => changeData({ field17: e.target.value })} />
                    </div>
                  </div>
                  <div className="flex-box">
                    <Checkbox value="软指标高风险" className="flex-shrink">
                      软指标高风险
                    </Checkbox>
                    <div className="flex-box">
                      <Input placeholder="请输入" value={niptjhbbTemplate.field18} onChange={e => changeData({ field18: e.target.value })} />
                    </div>
                  </div>
                  <div className="flex-box">
                    <Checkbox value="介入性手术治疗" className="flex-shrink">
                      介入性手术治疗
                    </Checkbox>
                    <div className="flex-box">
                      <Input placeholder="请输入" value={niptjhbbTemplate.field19} onChange={e => changeData({ field19: e.target.value })} />
                    </div>
                  </div>
                  <div className="flex-box">
                    <Checkbox value="其他" className="flex-shrink">
                      其他
                    </Checkbox>
                    <div className="flex-box">
                      <Input placeholder="请输入" value={niptjhbbTemplate.field20} onChange={e => changeData({ field20: e.target.value })} />
                    </div>
                  </div>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="血清筛查">
                <Radio.Group value={niptjhbbTemplate.field21} onChange={e => changeData({ field21: e.target.value })}>
                  <Radio value="未做">未做</Radio>
                  <Radio value="已做">已做</Radio>
                </Radio.Group>
                风险值：21-三体：1/
                <Input style={{ width: '77px', margin: '0 16px 0 8px' }} placeholder="请输入" value={niptjhbbTemplate.field22} onChange={e => changeData({ field22: e.target.value })} />
                18-三体：1/
                <Input style={{ width: '77px', margin: '0 16px 0 8px' }} placeholder="请输入" value={niptjhbbTemplate.field23} onChange={e => changeData({ field23: e.target.value })} />
                NTD
                <Input style={{ width: '77px', margin: '0 0 0 8px' }} placeholder="请输入" value={niptjhbbTemplate.field24} onChange={e => changeData({ field24: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="预约介入性穿刺手术">
                <div className="flex-box">
                  <Radio.Group value={niptjhbbTemplate.field25} onChange={e => changeData({ field25: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="已预约">已预约</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <DatePicker format="YYYY-MM-DD" value={niptjhbbTemplate.field26 ? moment(niptjhbbTemplate.field26) : null} onChange={(date, dateString) => changeData({ field26: dateString })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="临床诊断">
                <TextArea placeholder="请输入" defaultValue={niptjhbbTemplate.field27} onChange={e => changeData({ field27: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="其他信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item>
                <Checkbox.Group value={niptjhbbTemplate.field28 ? niptjhbbTemplate.field28.split(',') : []} onChange={v => changeData({ field28: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="细胞治疗">细胞治疗</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="肿瘤患者">肿瘤患者</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="一年内异体输血">一年内异体输血</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="其他">
                <TextArea placeholder="请输入" defaultValue={niptjhbbTemplate.field29} onChange={e => changeData({ field29: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
