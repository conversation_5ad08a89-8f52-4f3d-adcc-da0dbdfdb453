import { Component } from 'react';
import { connect } from 'dva';
import { Select, Row, Col, Button, Table, DatePicker, Input, message, Modal } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';
import * as Api from './api';
import styles from './index.less';
import DetailModal from './detail/index';
const { Search } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;

// DetailModal的connect
@connect(({ reportmail }) => ({
  visible: reportmail.detailVisible,
  pagination: reportmail.pagination,
  currentDetail: null,
  listData: [],
}))
@Form.create()
class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      tableData: {}, // 数据
      pageSize: 10,
      currentPage: 1, // 当前页码
      currentDate: moment(), // 当前日期
    };
    // 表格表头和每列数据
    this.tableColumns = [
      {
        title: '客户名称',
        dataIndex: 'institutionName',
      },
      {
        title: '报告接收邮箱',
        dataIndex: 'mailAddress',
      },

      {
        title: '产品名称',
        dataIndex: 'groupedProductName',
      },
      {
        title: '报告总数',
        dataIndex: 'reportSum',
      },
      {
        title: '发送状态',
        dataIndex: 'pushStatus',
        render: text => {
          const expressMap = {
            0: '未发送',
            1: '已发送',
            2: '发送失败',
          };
          return expressMap[text] || text; // 显示映射值或原值
        },
      },
      {
        title: '发送时间',
        dataIndex: 'pushDate',
        render: text => (text ? moment(text).format('YYYY-MM-DD HH:mm') : '-'),
      },
      {
        title: '下载状态',
        dataIndex: 'downloadStatus',
        render: text => {
          const expressMap = {
            0: '未下载',
            1: '已下载',
          };
          return expressMap[text] || text; // 显示映射值或原值
        },
      },
      {
        title: '下载时间',
        dataIndex: 'downloadDate',
        render: text => (text ? moment(text).format('YYYY-MM-DD HH:mm') : '-'),
      },
      {
        title: '操作',
        fixed: 'right',
        render: record => {
          return (
            <div className={styles['edit-btn']}>
              <span
                onClick={() => {
                  this.getDetail(record);
                }}
              >
                详情
              </span>
              {record.reportSum > 0 && record.pushStatus === '0' ? (
                <span
                  onClick={() => {
                    this.warning('发送报告', [record]);
                  }}
                >
                  发送报告
                </span>
              ) : null}
            </div>
          );
        },
      },
    ];
  }

  // 组件挂载时获取数据
  componentDidMount() {
    this.getTableData();
  }

  // 搜索功能
  onSearch = value => {
    this.setState({ searchKeyword: value }, this.handleSearch);
  };

  // 获取查询参数
  getQueryParam = (pageNum = 1) => {
    const { form } = this.props;
    const values = form.getFieldsValue();
    const { searchKeyword } = this.state;

    const params = {
      ...values,
      pageNum,
      numPerPage: this.state.pageSize,
      search: searchKeyword,
    };

    // 处理发送时间范围参数
    if (values.createTime && values.createTime.length === 2) {
      params.pushStartDate = values.createTime[0]?.format('YYYY-MM-DD');
      params.pushEndDate = values.createTime[1]?.format('YYYY-MM-DD');
    }
    delete params.createTime; // 删除createTime字段，避免传递不必要的参数

    // 处理交接时间范围参数
    if (values.reportTime && values.reportTime.length === 2) {
      params.reportStartDate = values.reportTime[0]?.format('YYYY-MM-DD');
      params.reportEndDate = values.reportTime[1]?.format('YYYY-MM-DD');
    }
    delete params.reportTime; // 删除reportTime字段，避免传递不必要的参数

    return params;
  };

  // 查询按钮
  handleSearch = () => {
    this.getTableData();
  };

  // 重置表单
  handleReset = () => {
    this.props.form.resetFields();
    this.setState(
      {
        searchKeyword: '',
      },
      this.handleSearch,
    );
  };

  // 获取表格数据
  getTableData = async (pageNum = 1) => {
    // const { pageSize } = this.state;
    this.setState({
      currentPage: pageNum, // 更新当前页码
    });
    const params = {
      ...this.getQueryParam(pageNum),
      // numPerPage: pageSize, // 确保传递当前pageSize
      // pageNum
    };
    try {
      const { code, data } = await Api.queryReportMailList(params);
      if (code === 0) {
        this.setState({
          tableData: data,
          // pagination: {
          //   current: data.currentPage,
          //   pageSize: data.numPerPage,
          //   total: data.totalCount
          // }
        });
      }
    } catch (error) {
      message.error('数据获取失败');
    }
  };

  // 表格每页行数改变
  onShowSizeChange = (c, size) => {
    this.setState(
      {
        pageSize: size,
      },
      () => {
        this.getTableData(); // 重置到第一页
      },
    );
  };

  // 获取详情
  // 注意：这里的record是从表格行中传递过来的，包含了所有必要的信息
  getDetail = record => {
    var queryParams = {};
    const { form } = this.props;
    const values = form.getFieldsValue();
    // 处理交接时间范围参数
    if (values.reportTime && values.reportTime.length === 2) {
      queryParams.reportStartDate = values.reportTime[0]?.format('YYYY-MM-DD');
      queryParams.reportEndDate = values.reportTime[1]?.format('YYYY-MM-DD');
    }
    this.props.dispatch({
      type: 'reportmail/openDetail',
      payload: {
        record, // 传递当前行的记录
        queryParams, // 传递查询参数
      },
    });
    var payload = {
      pageNum: 1, // 默认第一页
      numPerPage: this.props.pagination.pageSize,
      id: record.id, // 列表ID
      institutionId: record.institutionId, // 客户ID
      groupedProductId: record.groupedProductId, // 产品ID
    };
    if (values.reportTime && values.reportTime.length === 2) {
      payload.reportStartDate = values.reportTime[0]?.format('YYYY-MM-DD');
      payload.reportEndDate = values.reportTime[1]?.format('YYYY-MM-DD');
    }

    this.props.dispatch({
      type: 'reportmail/fetchDetail',
      payload: payload,
    });
  };

  // 确认发送报告
  warning = (title, values) => {
    Modal.confirm({
      title,
      content: '请您确认是否发送报告？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.mailReport(values);
      },
    });
  };

  // 获取邮件报告参数
  getMailReportParams = () => {
    const { form } = this.props;
    const values = form.getFieldsValue();
    const params = {};
    // 处理交接时间范围参数
    if (values.reportTime && values.reportTime.length === 2) {
      params.reportStartDate = values.reportTime[0]?.format('YYYY-MM-DD');
      params.reportEndDate = values.reportTime[1]?.format('YYYY-MM-DD');
    }
    return params;
  };

  // 发送报告
  mailReport = async records => {
    const { currentPage } = this.state;
    const reqlist = [];
    // 遍历records数组，构建入参数组对象
    records.forEach(record => {
      //构建一个入参数组对象
      const value = {
        id: record.id, // 列表ID
        institutionId: record.institutionId, // 客户ID
        groupedProductId: record.groupedProductId, // 产品ID
      };
      reqlist.push(value);
    });
    // 构建请求参数
    const params = {
      ...this.getMailReportParams(), // 获取邮件报告参数
      reqlist, // 入参数组
    };
    try {
      const { code, msg } = await Api.mailreport(params);
      if (code === 0) {
        message.success('发送成功', 1, () => {
          this.getTableData(currentPage);
        });
      } else {
        message.error(msg || '发送失败');
      }
    } catch (error) {
      message.error('发送失败，请稍后重试');
    }
  };

  // 批量发送报告
  batchMailReport = () => {
    const { selectedRows } = this.state;
    if (!selectedRows || selectedRows.length === 0) {
      message.warning('请先选择要发送的报告');
      return;
    }
    this.warning('批量发送报告', selectedRows);
  };

  render() {
    const { tableData = {}, pageSize, currentDate } = this.state;
    const {
      form,
      form: { getFieldDecorator },
    } = this.props;
    return (
      <div className="g-page page-manage-report-mail">
        <Form className="g-query-box">
          <Row gutter={[16, 24]}>
            {/* 第一行 */}
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="发送时间" name="createTime">
                {getFieldDecorator('createTime')(<RangePicker style={{ width: '100%' }} format="YYYY-MM-DD" placeholder={['开始日期', '结束日期']} />)}
              </Form.Item>
            </Col>
            <Col span={6} className={styles['col-item']}>
              <Form.Item label="发送状态">
                {getFieldDecorator('pushStatus', {
                  initialValue: '', // 新增默认值设置
                })(
                  <Select placeholder="请选择">
                    <Option value="">全部</Option>
                    <Option value="0">未发送</Option>
                    <Option value="1">已发送</Option>
                    <Option value="2">发送失败</Option>
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="下载状态">
                {getFieldDecorator('downloadStatus', {
                  initialValue: '', // 新增默认值设置
                })(
                  <Select placeholder="请选择">
                    <Option value="">全部</Option>
                    <Option value="0">未下载</Option>
                    <Option value="1">已下载</Option>
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={8} className={styles['col-item']}>
              <Form.Item label="交接时间" name="reportTime">
                {getFieldDecorator('reportTime', { initialValue: [moment(currentDate, 'YYYY-MM-DD'), moment(currentDate, 'YYYY-MM-DD')] })(
                  <RangePicker style={{ width: '100%' }} format="YYYY-MM-DD" placeholder={['开始日期', '结束日期']} />,
                )}
              </Form.Item>
            </Col>
            {/* 操作按钮 */}
            <Col span={8}>
              <Button type="primary" onClick={this.handleSearch}>
                查询
              </Button>
              <Button
                onClick={() => {
                  form.resetFields();
                  this.handleReset();
                }}
                style={{ marginLeft: 8 }}
              >
                重置
              </Button>
            </Col>
          </Row>
        </Form>
        <div className="container">
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Search
              placeholder="请输入客户名称、产品名称"
              allowClear
              onSearch={this.onSearch}
              enterButton
              value={this.state.searchKeyword}
              onChange={e => this.setState({ searchKeyword: e.target.value })}
              style={{ width: 380, marginBottom: 16 }}
            />
            <div className="g-table-opt-cell">
              {
                <Button type="primary" onClick={this.batchMailReport}>
                  批量发送
                </Button>
              }
            </div>
          </div>

          <div className="table-box">
            <div className="table-content">
              <Table
                key={tableData.currentPage}
                rowKey={(record, index) => index} // 使用索引作为唯一键
                dataSource={tableData.recordList || []}
                columns={this.tableColumns}
                scroll={{ x: 'max-content' }} // 新增的滚动配置
                pagination={{
                  total: tableData.totalCount || 0,
                  showTotal: total => `共 ${total} 条`,
                  onChange: pageNum => {
                    this.getTableData(pageNum);
                  },
                  current: tableData.currentPage || 0,
                  pageSize,
                  showQuickJumper: true,
                  showSizeChanger: true,
                  onShowSizeChange: this.onShowSizeChange,
                }}
                rowSelection={{
                  onChange: (selectedRowKeys, selectedRows) => {
                    this.setState({ selectedRows });
                  },
                }}
              />
            </div>
          </div>
        </div>
        <DetailModal />
      </div>
    );
  }
}

export default Index;
