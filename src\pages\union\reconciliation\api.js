import { post, download } from '@/utils/request';

// 查询对账数据列表
export const getBillByPage = (param = {}) => post('/api/sample/bill/getBillByPage', { data: param });

// 手工执行对账
export const execBill = (id) => post('/api/sample/bill/execBill', { data: { id } });

// 查询对账样本明细
export const getBillDetail = (billNo) => post('/api/sample/bill/getBillDetail', { data: { billNo } });

// 导出样本明细
export const exportBillDetail = (billNo) => download('/api/sample/bill/exportBillDetail', { data: { billNo } });

// 对账状态变更（核对状态，发票状态）
export const updateBillStatus = (param = {}) => post('/api/sample/bill/update', { data: param });

export const getAllUser = (param = {}) => post('/api/userinfo/findAllUser', { data: param });