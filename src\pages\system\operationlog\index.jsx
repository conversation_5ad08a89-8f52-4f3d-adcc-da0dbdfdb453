import React from 'react';
import { Form } from 'antd';
import { useAntdTable } from 'ahooks';
import dayjs from 'dayjs';
import Search from './components/search';
import Table from './components/table';
import * as Api from './service';
import './style.less';

const Index = () => {
  const [form] = Form.useForm();

  const fetchList = async ({ current, pageSize, sorter = {} }, formData) => {
    try {
      const { data: res } = await Api.fetchList({
        pageNum: current,
        numPerPage: pageSize,
        sort: sorter.order === 'ascend' ? '' : 'desc',
        orderBy: 'create_time',
        startTime: formData.time ? dayjs(formData.time[0]).format('YYYY-MM-DD') : '',
        endTime: formData.time ? dayjs(formData.time[1]).format('YYYY-MM-DD') : '',
        ...formData,
      });
      const { data } = res;
      return {
        total: data.totalCount || 0,
        list: data.recordList || [],
      };
    } catch (err) {
      return {
        total: 0,
        list: [],
      };
    }
  };

  const { tableProps, search, loading } = useAntdTable(fetchList, {
    defaultPageSize: 20,
    form,
  });
  const { submit, reset } = search;
  return (
    <div className="operation-log-page">
      <Search form={form} submit={submit} reset={reset} loading={loading} />
      <Table refresh={submit} tableProps={tableProps} />
    </div>
  );
};

export default Index;
