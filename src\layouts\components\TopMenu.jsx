import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Breadcrumb, Modal } from 'antd';
import { Icon } from '@ant-design/compatible';
import breadcrumbNameMap from './Breadcrumb';
import '../style.less';

const confirm = Modal.confirm;

function MerchantBreadcrumb({ location }) {
  const pathSnippets = location.pathname.split('/').filter(i => i);
  const length = pathSnippets.length;

  if (!length) return null;

  // 三级编辑页面，通过面包屑返回时需要做弹窗确认
  const editPageMap = {
    '/microwebsite/hospital/edit': true,
    '/microwebsite/article/edit': true,
    '/microwebsite/article/new': true,
    '/trait/survey/create': true,
    '/trait/course/release': true,
    '/trait/course/modify': true,
    '/system/account/new': true,
    '/system/account/modify': true,
    '/transaction/query/info': true,
  };

  if (!!!breadcrumbNameMap[location.pathname]) return null; // eslint-disable-line

  if (length < 3) {
    return (
      <Breadcrumb style={{ lineHeight: '54px' }}>
        <Breadcrumb.Item>{breadcrumbNameMap[location.pathname]}</Breadcrumb.Item>
      </Breadcrumb>
    );
  }

  const breadcrumbItems = pathSnippets.map((_, index) => {
    if (index == 0) return null;
    const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;
    return (
      <Breadcrumb.Item key={url}>
        {index == length - 1 ? (
          breadcrumbNameMap[url]
        ) : (
          <a
            onClick={() => {
              if (editPageMap[location.pathname]) {
                confirm({
                  title: <div>确定离开当前页面吗？</div>,
                  content: '离开后将丢失当前页面的数据，请确认是否离开当前页面。',
                  onOk() {
                    history.push(url);
                  },
                });
              } else {
                if (window.history.length > 1) {
                  history.goBack();
                } else {
                  history.push(url);
                }
              }
            }}
          >
            {breadcrumbNameMap[url]}
          </a>
        )}
      </Breadcrumb.Item>
    );
  });

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Icon type="caret-left" style={{ fontSize: 12 }} />
      <Breadcrumb>{breadcrumbItems}</Breadcrumb>
    </div>
  );
}

function MerchantHeader({ dispatch, loginUserInfo, location }) {
  return (
    <div className="merchant-header">
      <MerchantBreadcrumb location={location} />
      <div className="opt-cell">
        <div className="opt-cell-user">
          <span className="user-info">{loginUserInfo.userName}</span>
        </div>
        <div className="separate-line">丨</div>
        <div
          className="opt-cell-off"
          onClick={() => {
            dispatch({
              type: 'root/logout',
            });
          }}
        >
          <span className="btn-off">退出</span>
        </div>
      </div>
    </div>
  );
}

export default connect()(MerchantHeader);
