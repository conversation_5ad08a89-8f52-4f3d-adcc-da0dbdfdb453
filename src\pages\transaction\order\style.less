@charset "utf-8";
@import '../../../resources/styles/mixins';

.page-transaction {
  .tsc-top-panle {
    .col-item {
      padding: 0 21px 0 24px;
    }

    .ant-calendar-picker {
      display: block;
    }

    .btn-bar {
      text-align: right;
      .col-item();
    }
  }
}
.order-all {
  .ant-pagination-jump-prev {
    display: none;
  }
  .ant-pagination-item {
    // display: none;
    &.ant-pagination-item-active {
      display: inline-block;
    }
  }
  .ant-pagination-next,
  .ant-pagination-prev {
    border: none;
    a:after {
      display: none;
    }
  }
}
.foot-btn {
  span {
    display: inline-block;
    margin-right: 16px;
    background-color: #ffffff;
    border: 1px solid #d9d9d9;
    color: #000;
    padding: 0 12px;
    border-radius: 4px;
    height: 24px;
    line-height: 24px;
    cursor: pointer;
    &.active {
      background-color: #f0fbf9;
      border: 1px solid #3f969d;
      color: #3f969d;
    }
  }
}
