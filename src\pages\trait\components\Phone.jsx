/* eslint-disable react/no-array-index-key */
import React from 'react';
import { Radio, Input, Checkbox, Icon, Row, Col, Select, Cascader, DatePicker, message, Button, Upload } from 'antd';
import { provinceData, QUESTIONS_TYPE, QUESTIONS_TYPE_ARR } from '../survey/province';
import moment from 'moment';
import './phone.less';

const RadioGroup = Radio.Group;
const { Option } = Select;

export default class Phone extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedOptions: {}, // 存储选中的选项 {titleId: {optionId: true}}
      inputValues: {}, // 存储文本输入框的值 {titleId: {optionId: value}}
      secondLevelChecked: {}, // 存储二级复选框的选中状态 {titleId: {optionId: {secondOptionIdx: true}}}
      hasAttachment: {}, // 存储是否上传附件 {titleId: true}
      fileList: {}, // 记录上传的文件 {titleId: [file1, file2]}
      fillBlankValues: {}, // 存储填空题（多空填值）的输入 {titleId: {index: value}}
    };
  }
  
  // 处理单选题选择
  handleRadioChange = (titleId, optionId) => {
    const { selectedOptions } = this.state;
    const { phoneInfo } = this.props;
    
    // 获取选中题目
    const question = phoneInfo.titleList.find(q => q.titleId === titleId);
    if (!question) return;
    
    // 获取选中选项
    const selectedOption = question?.optionList[optionId];
    console.log('选中单选选项:', selectedOption);
    
    // 如果选项存在并启用了二级选项，自动赋予默认值
    if (selectedOption && this.isSecondLevelEnabled(selectedOption)) {
      
      // 初始化二级选项属性 - 只使用驼峰命名 - 确保是字符串类型
      const existingOptionType = this.getOptionType(selectedOption);
      selectedOption.optionType = existingOptionType ? existingOptionType.toString() : '1';
      
      if (!this.getSecondOptionContent(selectedOption)) {
        if (selectedOption.optionType === '1') {
          // 如果是文本输入，设置空内容
          selectedOption.secondOptionContent = '';
        } else if (selectedOption.optionType === '2') {
          // 如果是多选，设置默认选项
          selectedOption.secondOptionContent = '选项1,选项2,选项3';
        }
      }
      
      // 打印二级多值填空信息
      if (selectedOption.optionType === '3') {
        console.log('选中二级多值填空选项:', selectedOption);
        console.log('二级多值填空内容:', this.getSecondOptionContent(selectedOption));
      }
      
      console.log('初始化后的选项:', selectedOption);
    }
    
    // 清除该题目之前的选择
    const newSelectedOptions = {
      ...selectedOptions,
      [titleId]: {
        [optionId]: true
      }
    };
    
    this.setState({ selectedOptions: newSelectedOptions });
  }
  
  // 处理多选题选择
  handleCheckboxChange = (titleId, optionId, checked) => {
    const { selectedOptions } = this.state;
    const { phoneInfo } = this.props;
    
    // 获取选中题目
    const question = phoneInfo.titleList.find(q => q.titleId === titleId);
    if (!question) return;
    
    // 获取选中选项
    const selectedOption = question?.optionList[optionId];
    console.log('选中多选选项:', selectedOption, '状态:', checked);
    
    // 如果选项被选中且存在并启用了二级选项，自动赋予默认值
    if (checked && selectedOption && this.isSecondLevelEnabled(selectedOption)) {
      
      // 初始化二级选项属性 - 只使用驼峰命名 - 确保是字符串类型
      const existingOptionType = this.getOptionType(selectedOption);
      selectedOption.optionType = existingOptionType ? existingOptionType.toString() : '1';
      
      if (!this.getSecondOptionContent(selectedOption)) {
        if (selectedOption.optionType === '1') {
          // 如果是文本输入，设置空内容
          selectedOption.secondOptionContent = '';
        } else if (selectedOption.optionType === '2') {
          // 如果是多选，设置默认选项
          selectedOption.secondOptionContent = '选项1,选项2,选项3';
        }
      }
      
      // 打印二级多值填空信息
      if (selectedOption.optionType === '3') {
        console.log('选中二级多值填空选项:', selectedOption);
        console.log('二级多值填空内容:', this.getSecondOptionContent(selectedOption));
      }
      
      console.log('初始化后的选项:', selectedOption);
    }
    
    // 获取当前题目的选择状态
    const currentTitleSelections = selectedOptions[titleId] || {};
    
    // 更新选择状态
    const newSelectedOptions = {
      ...selectedOptions,
      [titleId]: {
        ...currentTitleSelections,
        [optionId]: checked
      }
    };
    
    this.setState({ selectedOptions: newSelectedOptions });
  }
  
  // 处理二级选项中的文本输入
  handleSecondLevelInputChange = (titleId, optionId, value) => {
    const { inputValues } = this.state;
    
    // 更新输入值
    const newInputValues = {
      ...inputValues,
      [titleId]: {
        ...(inputValues[titleId] || {}),
        [optionId]: value
      }
    };
    
    this.setState({ inputValues: newInputValues });
  }
  
  // 处理二级选项中的复选框选择
  handleSecondLevelCheckboxChange = (titleId, optionId, secondOptionIdx, checked) => {
    const { secondLevelChecked } = this.state;
    
    // 更新选中状态
    const newSecondLevelChecked = {
      ...secondLevelChecked,
      [titleId]: {
        ...(secondLevelChecked[titleId] || {}),
        [optionId]: {
          ...((secondLevelChecked[titleId] || {})[optionId] || {}),
          [secondOptionIdx]: checked
        }
      }
    };
    
    this.setState({ secondLevelChecked: newSecondLevelChecked });
  }

  // 处理填空题（多空填值）中的输入
  handleFillBlankChange = (titleId, index, value) => {
    const { fillBlankValues } = this.state;
    
    // 更新输入值
    const newFillBlankValues = {
      ...fillBlankValues,
      [titleId]: {
        ...(fillBlankValues[titleId] || {}),
        [index]: value
      }
    };
    
    this.setState({ fillBlankValues: newFillBlankValues });
  }
  
  // 获取填空题中某个空的值
  getFillBlankValue = (titleId, index) => {
    const { fillBlankValues } = this.state;
    return fillBlankValues[titleId] && fillBlankValues[titleId][index] || '';
  }
  
  // 处理附件上传
  handleFileUpload = (titleId, info) => {
    const { fileList } = this.state;
    
    if (info.file.status === 'done') {
      message.success(`${info.file.name} 上传成功`);
      
      // 更新文件列表
      if (!fileList[titleId]) {
        fileList[titleId] = [];
      }
      fileList[titleId].push(info.file);
    
      this.setState({ fileList });
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
    }
  }
  
  // 移除上传的文件
  handleFileRemove = (titleId, file) => {
    const { fileList } = this.state;
    
    if (fileList[titleId]) {
      const index = fileList[titleId].indexOf(file);
      if (index !== -1) {
        fileList[titleId].splice(index, 1);
        this.setState({ fileList });
      }
    }
    
    return true;
  }
  
  // 检查选项是否被选中
  isOptionSelected = (titleId, optionId) => {
    const { selectedOptions } = this.state;
    return selectedOptions[titleId] && selectedOptions[titleId][optionId];
  }
  
  // 获取二级选项的输入值
  getSecondLevelInputValue = (titleId, optionId) => {
    const { inputValues } = this.state;
    return inputValues[titleId] && inputValues[titleId][optionId] || '';
  }
  
  // 检查二级选项的复选框是否被选中
  isSecondLevelOptionChecked = (titleId, optionId, secondOptionIdx) => {
    const { secondLevelChecked } = this.state;
    return secondLevelChecked[titleId] && 
           secondLevelChecked[titleId][optionId] && 
           secondLevelChecked[titleId][optionId][secondOptionIdx];
  }
  
  // 检查附件是否被选中
  isAttachmentChecked = (titleId) => {
    const { hasAttachment } = this.state;
    return hasAttachment[titleId];
  }
  
  // 保存问卷
  handleSave = () => {
    const { selectedOptions, inputValues, secondLevelChecked, hasAttachment, fillBlankValues } = this.state;
    const { phoneInfo } = this.props;
    
    // 检查必填题是否已填写
    let allRequiredFilled = true;
    let firstUnfilledQuestion = null;
    
    if (phoneInfo.titleList) {
      for (let i = 0; i < phoneInfo.titleList.length; i++) {
        const item = phoneInfo.titleList[i];
        
        // 跳过非必填题和说明性题目
        if (item.required !== '1' || 
            [QUESTIONS_TYPE.DUANLUO_SHUOMING.value, QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(item.questionsType)) {
          continue;
        }
        
        // 检查是否已选择
        const hasSelected = selectedOptions[item.titleId] && 
                           Object.values(selectedOptions[item.titleId]).some(val => val);
        
        if (!hasSelected) {
          allRequiredFilled = false;
          firstUnfilledQuestion = item;
          break;
        }
      }
    }
    
    if (!allRequiredFilled) {
      message.error(`请完成必填题: ${firstUnfilledQuestion.questionsTitle}`);
      return;
    }
    
    // 收集数据
    const formData = {
      selectedOptions,
      inputValues,
      secondLevelChecked,
      hasAttachment,
      fillBlankValues
    };
    
    console.log('提交的表单数据:', formData);
    message.success('问卷提交成功');
  }
  
  // 获取选项的类型
  getOptionType = (option) => {
    // 先尝试使用驼峰命名的字段
    if (option.optionType !== undefined) {
      return option.optionType;
    }
    
    // 再尝试使用下划线命名的字段
    if (option.option_type !== undefined) {
      return option.option_type;
    }
    
    return null;
  }
  
  // 判断选项是否启用了二级选项
  isSecondLevelEnabled = (option) => {
    // 如果选项类型为"0"，则表示没有启用二级选项
    const optionType = option.optionType || option.option_type;
    if (optionType === '0') {
      return false;
    }
    
    // 如果有类型或内容，且类型不为0，就认为启用了二级选项
    return !!(optionType || option.secondOptionContent || option.second_option_content);
  }
  
  // 获取二级选项的内容
  getSecondOptionContent = (option) => {
    return option.secondOptionContent || option.second_option_content || '';
  }
  
  // 渲染二级选项中的输入框
  renderSecondLevelInput = (titleId, idx, item1) => {
    return (
      <div style={{ marginTop: 5 }}>
        <Input 
          style={{ width: 200 }} 
          placeholder="请输入" 
          value={this.getSecondLevelInputValue(titleId, idx)}
          onChange={(e) => this.handleSecondLevelInputChange(titleId, idx, e.target.value)}
        />
      </div>
    );
  }
  
  // 渲染二级选项中的复选框组
  renderSecondLevelCheckboxes = (titleId, idx, item1) => {
    const content = this.getSecondOptionContent(item1);
    if (!content) return null;
    
    const options = content.split(',').filter(opt => opt.trim() !== '');
    
    return (
      <div style={{ marginTop: 5 }}>
        {options.map((option, secondOptionIdx) => (
          <Checkbox 
            key={secondOptionIdx}
            style={{ display: 'block', marginLeft: 0, marginBottom: 5 }}
            checked={this.isSecondLevelOptionChecked(titleId, idx, secondOptionIdx)}
            onChange={(e) => this.handleSecondLevelCheckboxChange(titleId, idx, secondOptionIdx, e.target.checked)}
          >
            {option}
          </Checkbox>
        ))}
      </div>
    );
  }
  
  // 渲染上传附件区域
  renderAttachmentUpload = (item) => {
    return (
      <div className="attachment-area" style={{ marginTop: 10, borderTop: '1px dashed #eee', paddingTop: 10 }}>
        <p style={{ marginBottom: 5 }}>
          {item.fileUploadDescribe || '请上传附件'}
        </p>
        <Upload
          name="file"
          action="/api/file/upload"
          onChange={(info) => this.handleFileUpload(item.titleId, info)}
          onRemove={(file) => this.handleFileRemove(item.titleId, file)}
        >
          <Button>
            <Icon type="upload" /> 上传附件
          </Button>
        </Upload>
      </div>
    );
  }

  // 渲染填空题（多空填值）的标题
  renderMultiFillBlankTitle = (item) => {
    if (!item.optionList || !item.optionList.length || !item.optionList[0].optionContent) {
      return <span className="title">{item.questionsTitle}</span>;
    }

    const content = item.optionList[0].optionContent;
    console.log('渲染多值填空题标题，内容:', content);
    
    // 简单地将{val}替换为___字符串
    const displayContent = content.replace(/\{val\}/g, "___");
    
         return <span className="title" style={{ color: '#333', fontWeight: 'normal' }}>{displayContent}</span>;
  }
  
  // 渲染二级选项中的填空题（多值填空）
  renderSecondLevelMultiFillBlank = (titleId, idx, item1) => {
    const content = this.getSecondOptionContent(item1);
    if (!content) return null;

    console.log('渲染二级多值填空，内容:', content);
    
    // 简单地将{val}替换为___字符串
    const displayContent = content.replace(/\{val\}/g, "___");
    
    // 添加调试信息
    console.log('处理后的显示内容:', displayContent);
    
    // 尝试一种完全不同的方式显示内容，避免可能的CSS冲突
    return (
      <div 
        style={{ 
          marginTop: 5, 
          maxWidth: 300,
          padding: '5px',
          border: '1px solid #f0f0f0',
          backgroundColor: '#fafafa',
          color: '#333',
          fontSize: '14px',
          fontWeight: 'normal',
          display: 'block',
          position: 'relative',
          zIndex: 100
        }}
        className="second-level-multifill"
      >
        {/* <div style={{ color: 'red', marginBottom: '5px' }}>多值填空内容:</div> */}
        <div style={{ color: 'black' }}>{displayContent}</div>
      </div>
    );
  }
  
  componentDidMount() {
    const { phoneInfo } = this.props;
    
    let hasSecondLevelOptions = false;
    
    // 检查所有题目，看是否存在启用二级选项的选项
    if (phoneInfo && phoneInfo.titleList) {
      phoneInfo.titleList.forEach(item => {
        if (item.optionList && item.optionList.length) {
          if ([QUESTIONS_TYPE.DAN_XUAN.value, QUESTIONS_TYPE.DUO_XUAN.value].includes(item.questionsType)) {
            item.optionList.forEach((option, idx) => {
              if (this.isSecondLevelEnabled(option)) {
                hasSecondLevelOptions = true;
                console.log(`发现启用二级选项的选项 - 题目ID: ${item.titleId}, 题目类型: ${item.questionsType}, 选项索引: ${idx}`);
                console.log(`选项详情:`, option);
                console.log(`选项类型:`, this.getOptionType(option));
                console.log(`二级选项内容:`, this.getSecondOptionContent(option));
              }
            });
          }
        }
      });
      
      if (hasSecondLevelOptions) {
        console.log('存在启用二级选项的选项');
      }
    }
  }

  render() {
    const { phoneInfo } = this.props;
    const beginTime = moment(moment().add(-1, 'M')).format('YYYY-MM-DD');
    const endTime = moment().format('YYYY-MM-DD');
      
    return (
      <div className="phone question-phone">
        <div className="phone-head">问卷调查</div>
        <div className="phone-body">
          <h3 className="f-toe question-title">{phoneInfo.examTitle || ''}</h3>
          <Row className="question-phone-list">
            <Col span={6}>调查范围：</Col>
            <Col span={18}>{phoneInfo.scopeInvestigation == 0 || !phoneInfo.scopeInvestigation ? '全部用户' : '符合参与条件的用户'}</Col>
          </Row>
          <Row className="question-phone-list">
            <Col span={6}>有效时间：</Col>
            <Col span={18}>{`${phoneInfo.beginTime || beginTime}至${phoneInfo.endTime || endTime}`}</Col>
          </Row>
          <Row className="question-phone-list">
            <Col span={6}>问卷描述：</Col>
            <Col span={18} className="mydescript">
              {/* <pre>{phoneInfo.examDesc || ''}</pre> */}
              {(phoneInfo.examDesc || '').split('\n').map((v, i) => (
                <div key={i} style={{ textIndent: '4ch' }}>
                  {v}
                </div>
              ))}
            </Col>
          </Row>
          <Row className="question-phone-list">
            <Col span={6}>发布单位：</Col>
            <Col span={18}>{phoneInfo.releaseCompany || ''}</Col>
          </Row>

          {phoneInfo.titleList
            ? phoneInfo.titleList.map((item, index) => {
                return (
                  <div key={item.titleId} className="phone-question-list">
                    <p>
                      {![QUESTIONS_TYPE.DUANLUO_SHUOMING.value, QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(item.questionsType) ? (
                        <span>{index + 1}、</span>
                      ) : null}

                      {item.questionsType === QUESTIONS_TYPE.MULTI_FILL_BLANK.value ? (
                        this.renderMultiFillBlankTitle(item)
                      ) : (
                        <span className={item.questionsType === QUESTIONS_TYPE.DUANLUO_SHUOMING.value ? 'bold title' : 'title'}>
                          {item.questionsTitle}
                        </span>
                      )}

                      {![QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(item.questionsType) ? (
                        <span className="questions-type">{QUESTIONS_TYPE_ARR[item.questionsType] || ''}</span>
                      ) : null}

                      {![QUESTIONS_TYPE.DUANLUO_SHUOMING.value, QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(item.questionsType) && (
                        <span className="questions-required">{item.required == '1' ? '必填' : '非必填'}</span>
                      )}
                    </p>

                    {'578'.indexOf(item.questionsType) === -1 && item.questionsType !== QUESTIONS_TYPE.MULTI_FILL_BLANK.value && (
                        <RadioGroup name={`question_${item.titleId}`}>
                        {item.optionList && item.optionList.length
                          ? item.optionList.map((item1, idx) => {
                            const type = item.questionsType;
                            const extInputList = item1.frameDefaultText ? item1.frameDefaultText.split(';') : [];
                              
                              // 检查是否启用了二级选项
                              if (this.isSecondLevelEnabled(item1)) {
                                console.log(`选项${idx + 1}:`, item1, '选项类型:', this.getOptionType(item1));
                              }
                              
                            return type === QUESTIONS_TYPE.DAN_XUAN.value ? (
                              <div key={idx} className="option-list-item list-item-flex">
                                  <Radio 
                                    value={idx}
                                    onChange={() => this.handleRadioChange(item.titleId, idx)}
                                    checked={this.isOptionSelected(item.titleId, idx)}
                                    style={{ display: 'flex', alignItems: 'flex-start' }}
                                  >
                                    {item1.optionContent}
                                  </Radio>
                                  
                                  {/* 二级选项支持 - 只有当选项被选中且optionType不为"0"时才显示 */}
                                  {this.isOptionSelected(item.titleId, idx) && 
                                   this.isSecondLevelEnabled(item1) && 
                                   this.getOptionType(item1) !== '0' && (
                                    <div className="second-level-options">
                                      {this.getOptionType(item1) === '1' && this.renderSecondLevelInput(item.titleId, idx, item1)}
                                      {this.getOptionType(item1) === '2' && this.renderSecondLevelCheckboxes(item.titleId, idx, item1)}
                                      {this.getOptionType(item1) === '3' && this.renderSecondLevelMultiFillBlank(item.titleId, idx, item1)}
                                    </div>
                                  )}
                                {item1.haveRemarkFrame == 1 && extInputList && extInputList.length
                                  ? extInputList.map((ext, extIndex) => {
                                      return <Input key={extIndex} style={{ width: 140 }} value={ext} disabled />;
                                    })
                                  : null}
                                {item1.imageUrl ? (
                                  <div>
                                    <img className="preview-img" width={60} height={60} src={item1.imageUrl} alt="" />
                                  </div>
                                ) : null}
                              </div>
                            ) : type == QUESTIONS_TYPE.DUO_XUAN.value ? (
                                <div className="option-list-item list-item-flex" key={idx}>
                                  <Checkbox 
                                    onChange={(e) => this.handleCheckboxChange(item.titleId, idx, e.target.checked)}
                                    checked={this.isOptionSelected(item.titleId, idx)}
                                    style={{ display: 'flex', alignItems: 'flex-start' }}
                                  >
                                    {item1.optionContent}
                                  </Checkbox>
                                  {/* 二级选项支持 - 只有当选项被选中且optionType不为"0"时才显示 */}
                                  {this.isOptionSelected(item.titleId, idx) && 
                                   this.isSecondLevelEnabled(item1) && 
                                   this.getOptionType(item1) !== '0' && (
                                    <div className="second-level-options">
                                      {this.getOptionType(item1) === '1' && this.renderSecondLevelInput(item.titleId, idx, item1)}
                                      {this.getOptionType(item1) === '2' && this.renderSecondLevelCheckboxes(item.titleId, idx, item1)}
                                      {this.getOptionType(item1) === '3' && this.renderSecondLevelMultiFillBlank(item.titleId, idx, item1)}
                                    </div>
                                  )}
                                {item1.haveRemarkFrame == 1 ? <Input style={{ width: 200 }} value={item1.frameDefaultText} disabled /> : null}
                                {item1.imageUrl ? (
                                  <div>
                                    <img className="preview-img" width={60} height={60} src={item1.imageUrl} alt="" />
                                  </div>
                                ) : null}
                              </div>
                            ) : [QUESTIONS_TYPE.DUOHANG_TIANKONG.value, QUESTIONS_TYPE.QIAN_MING.value].includes(type) ? (
                              <Input.TextArea key={idx} maxLength={200} className="option-list-item" style={{ width: '330px' }} rows={6} placeholder="请输入问题答案（最多200字）" />
                            ) : type === QUESTIONS_TYPE.SHOUJI_HAOMA.value ? (
                              <Input key={idx} maxLength={11} className="option-list-item" style={{ width: '100%' }} rows={6} placeholder="请输入手机号码" />
                            ) : type === QUESTIONS_TYPE.SHENFEN_ZHENG.value ? (
                              <Input key={idx} maxLength={11} className="option-list-item" style={{ width: '100%' }} rows={6} placeholder="请输入身份证号" />
                            ) : type === QUESTIONS_TYPE.DANHANG_TIANKONG.value ? (
                              <Input key={idx} maxLength={10} className="option-list-item" style={{ width: '100%' }} rows={6} placeholder="请输入" />
                            ) : type === QUESTIONS_TYPE.XING_MING.value ? (
                              <Input key={idx} maxLength={10} className="option-list-item" style={{ width: '100%' }} rows={6} placeholder="请输入姓名" />
                            ) : type === QUESTIONS_TYPE.DI_ZHI.value ? (
                              <Cascader key={idx} style={{ marginTop: '15px', width: '115%' }} options={provinceData} placeholder="请选择地址" />
                            ) : type === QUESTIONS_TYPE.XIA_QU.value ? (
                              <Cascader key={idx} style={{ marginTop: '15px', width: '115%' }} options={provinceData} placeholder="请选择辖区" />
                            ) : type == QUESTIONS_TYPE.RIQI_XUANZE.value ? (
                              <DatePicker
                                key={idx}
                                style={{ marginTop: '15px', width: '100%' }}
                                onChange={() => {
                                  this.provinceChange();
                                }}
                                placeholder="请选择日期"
                              />
                            ) : [QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(type) ? null : (
                              <Icon
                                key={idx}
                                className="option-list-item"
                                style={{
                                  fontSize: 24,
                                  marginRight: 10,
                                  color: '#FFC028',
                                }}
                                type="star"
                              />
                            );
                          })
                        : null}
                    </RadioGroup>
                  )}
                  {'78'.indexOf(item.questionsType) > -1 ? (
                    <Select mode={item.questionsType == 8 ? 'multiple' : ''} style={{ width: 200, marginTop: 10 }} placeholder="请选择">
                      {(item.optionList || []).map(opt => {
                        return <Option key={opt.optionContent}>{opt.optionContent}</Option>;
                      })}
                    </Select>
                  ) : null}
                    
                    {/* 附件上传区域 - 单独显示在题目下方 */}
                    {(item.fileFlg === '1' || item.fileFlg === 1 || item.has_attachment === '1' || item.has_attachment === 1) && this.renderAttachmentUpload(item)}
                </div>
              );
            })
          : null}
              
            <div className="submit-area">
              <Button type="primary" onClick={this.handleSave}>提交问卷</Button>
            </div>
        </div>
    </div>
  );
  }
}
