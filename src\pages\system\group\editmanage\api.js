import * as request from '@/utils/request';

export function getUserList(param) {
  return request.post('/api/team/getUsersGroupByIdeId', { data: param }) || {};
}

export function getManageList(param) {
  return request.post('/api/team/paging', { data: param }) || {};
}

export function addTeam(param) {
  return request.post('/api/team/add', { data: param }) || {};
}

export function updateTeam(param) {
  return request.post('/api/team/update', { data: param }) || {};
}

export function groupInfo(param) {
  return request.post('/api/team/getbyid', { data: param }) || {};
}
