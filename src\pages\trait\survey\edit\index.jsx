/* eslint-disable react/no-deprecated */
import React from 'react';
import { connect } from 'dva';
import { DatePicker, Radio, Input, Row, Col, Button, message } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';

import ConditionModal from '../../components/ConditionModal';
import Phone from '../../components/Phone';
import * as utils from '../../../../utils/utils';

import './style.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const RadioGroup = Radio.Group;
let initLoad = true;

class CreateSurvey extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      conditionModal: false,
      conditionLabel: false,
      busTypeArr: [],
      pushText: {},
      phoneInfo: {},
    };
    this.busTypeChange = e => {
      const { checked, value } = e.target;
      const { pushText } = this.state;
      if (!checked) {
        // 如果没有checked 就删除
        delete pushText[value];
      } else {
        pushText[value] = {};
        pushText[value].pushType = '0';
      }
      this.setState({
        pushText,
      });
    };

    this.timePeriodChange = (type, e) => {
      const { pushText } = this.state;
      if (!pushText[type]) {
        pushText[type] = {};
      }
      pushText[type].pushType = e.target.value;
      this.setState({
        pushText,
      });
    };

    this.timeChange = (type, value, pushtype) => {
      const { pushText } = this.state;
      if (!pushText[type]) {
        pushText[type] = {};
      }
      pushText[type].pushParam = value;
      pushText[type].pushType = pushtype;
      this.setState({
        pushText,
      });
    };

    this.limitFlagChange = (type, e) => {
      const { pushText } = this.state;
      if (!pushText[type]) {
        pushText[type] = {};
      }
      if (pushText[type]?.limitFlag) {
        pushText[type].limitFlag = null;
      } else {
        pushText[type].limitFlag = 1;
      }
      this.setState({
        pushText,
      });
    };

    this.pushTimelimitChange = (type, value, pushtype) => {
      console.log(type, value, pushtype);
      // const { pushText } = this.state;
      // if (!pushText[type]) {
      //   pushText[type] = {};
      // }
      // pushText[type].pushParam = value;
      // pushText[type].pushType = pushtype;
      // this.setState({
      //   pushText,
      // });
    };

    this.keyToValue = index => {
      const { data } = this.props.trait.question.conditionLst;
      let value = '';
      if (data) {
        data.forEach(item => {
          if (item.dictKey == index) {
            value = item.dictValue;
          }
        });
      }
      return value;
    };

    this.saveQuestionDetail = (name, e) => {
      const { phoneInfo } = this.state;
      if (name == 'time') {
        phoneInfo.beginTime = moment(e[0]).format('YYYY-MM-DD');
        phoneInfo.endTime = moment(e[1]).format('YYYY-MM-DD');
      } else {
        const { value } = e.target;
        phoneInfo[name] = value;

        if (name == 'scopeInvestigation') {
          const style = value != 0;
          this.setState({
            conditionLabel: style,
          });
        }
      }
      this.setState({
        phoneInfo,
      });
    };
  }
  componentDidMount() {
    const { dispatch, location } = this.props;
    this.id = utils.queryStringToJson(location.search).id;
    if (this.id) {
      dispatch({
        type: 'trait/fetchQuestionInfo',
        payload: { examId: this.id },
      });
    }
    dispatch({
      type: 'trait/fetchQuestionCondition',
      payload: { type: 'question_type' },
    });
  }
  componentWillReceiveProps(nextProps) {
    const { trait } = nextProps;
    if (initLoad && trait.question.questionInfo.data) {
      const data = trait.question.questionInfo.data;
      const tempData = data.pushText ? JSON.parse(data.pushText) : {};
      const tempPushText = {};
      if (tempData.length) {
        // 开始数据结构没有设计好。。。。
        tempData.forEach(item => {
          tempPushText[item.bizType] = {};
          tempPushText[item.bizType].bizType = item.bizType;
          tempPushText[item.bizType].bizValue = item.bizValue;
          tempPushText[item.bizType].pushType = item.pushType;
          tempPushText[item.bizType].pushParam = item.pushParam;
        });
      }
      this.setState({
        phoneInfo: data,
        pushText: tempPushText,
        conditionLabel: data.pushText,
      });
      initLoad = false;
    }
  }
  componentWillUnmount() {
    this.props.dispatch({
      type: 'trait/clear',
    });
    initLoad = true;
  }
  addConditionModal() {
    this.setState({
      conditionModal: true,
    });
  }
  conditionChange(e) {
    const { value } = e.target;
    const style = value != 0;
    this.setState({
      conditionLabel: style,
    });
  }

  saveQuestion(type) {
    // type值不为空就是保存至草稿箱 然后跳到列表，反之先保存到草稿箱，然后新建题目
    const { pushText } = this.state;
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const reqData = values;
        reqData.beginTime = moment(values.time[0]).format('YYYY-MM-DD');
        reqData.endTime = moment(values.time[1]).format('YYYY-MM-DD');
        reqData.status = '0';
        if (type) {
          // 保存到草稿箱然后跳到列表页
          reqData.jumpLst = true;
          reqData.jumpTabs = '0';
        }
        if (reqData.scopeInvestigation == 1 && !Object.keys(pushText).length) {
          message.info('请选择参与条件');
          return;
        }
        if (reqData.scopeInvestigation == 1) {
          // 按照条件选择后拼接数据
          const temp = [];
          reqData.pushText = [];
          Object.keys(pushText).forEach(item => {
            const data = pushText[item];
            let itParam;
            // 防止不选下拉框
            if (data.pushParam) {
              itParam = data.pushParam;
            } else if (data.pushType == 2) {
              itParam = '10:00';
            } else if (data.pushType == 1) {
              itParam = '10';
            } else if (data.pushType == 0) {
              itParam = '';
            }
            reqData.pushText.push({
              bizType: item,
              bizValue: this.keyToValue(item),
              pushType: data.pushType,
              pushParam: itParam, // 防止不选下拉框
            });
            temp.push(this.keyToValue(item));
          });
          reqData.scopeInvestigation = temp.join(',');
          reqData.pushText = JSON.stringify(reqData.pushText);
        } else {
          reqData.pushText = [];
        }
        if (this.id) {
          reqData.examId = this.id;
        }
        this.props.dispatch({
          type: 'trait/saveQuestion',
          payload: reqData,
        });
        this.props.form.resetFields();
      }
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const { trait } = this.props;
    const { conditionModal, conditionLabel, pushText, phoneInfo } = this.state;
    const formItemLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };
    const questionInfo = trait.question.questionInfo.data ? trait.question.questionInfo.data : {};

    return (
      <div className="create-survey-page">
        <div className="preview-window">
          <Phone phoneInfo={phoneInfo} />
        </div>

        <div className="create-survey-info">
          <Form hideRequiredMark>
            <FormItem label="问卷标题" {...formItemLayout}>
              {getFieldDecorator('examTitle', {
                initialValue: questionInfo.examTitle || '',
                onChange: e => {
                  this.saveQuestionDetail('examTitle', e);
                },
                rules: [
                  {
                    required: 'Y',
                    message: '请输入问卷标题',
                  },
                ],
              })(<Input style={{ maxWidth: '388px' }} maxLength="20" placeholder="请输入问卷标题" />)}
            </FormItem>

            <FormItem style={{ marginBottom: conditionLabel ? 0 : 24 }} label="调查范围" {...formItemLayout}>
              {getFieldDecorator('scopeInvestigation', {
                initialValue: questionInfo.scopeInvestigation && questionInfo.scopeInvestigation != 0 ? '1' : '0',
                onChange: e => {
                  this.saveQuestionDetail('scopeInvestigation', e);
                },
                rules: [
                  {
                    required: 'Y',
                    message: '请输入调查范围',
                  },
                ],
              })(
                <RadioGroup onChange={e => this.conditionChange(e)}>
                  <Radio value={'0'}>所有用户</Radio>
                  <Radio value={'1'}>符合参与条件的用户</Radio>
                </RadioGroup>,
              )}
            </FormItem>
            <div className={conditionLabel ? '' : 'f-none'}>
              <Row>
                <Col className="condition-label" offset={3} style={{ width: '388px' }}>
                  {Object.keys(pushText).map(item => {
                    return (
                      <span className="label-item" key={item}>
                        {this.keyToValue(item)}
                      </span>
                    );
                  })}
                  <span className="label-item" onClick={() => this.addConditionModal()}>
                    +添加条件
                  </span>
                </Col>
              </Row>
            </div>
            <FormItem label="有效期" {...formItemLayout}>
              {getFieldDecorator('time', {
                initialValue: questionInfo.beginTime ? [moment(questionInfo.beginTime, 'YYYY-MM-DD'), moment(questionInfo.endTime, 'YYYY-MM-DD')] : [moment(), moment().add(1, 'M')],
                onChange: e => {
                  this.saveQuestionDetail('time', e);
                },
                rules: [
                  {
                    required: 'Y',
                    message: '请选择日期范围',
                  },
                ],
              })(
                <RangePicker
                  style={{ maxWidth: '388px' }}
                  // className="ant-col-24"
                  format="YYYY-MM-DD"
                  allowClear={false}
                  disabledDate={date => {
                    return (
                      date.valueOf() <
                      moment()
                        .add(-1, 'd')
                        .valueOf()
                    );
                  }}
                />,
              )}
            </FormItem>

            <FormItem label="发布单位" {...formItemLayout}>
              {getFieldDecorator('releaseCompany', {
                initialValue: questionInfo.releaseCompany || '',
                onChange: e => {
                  this.saveQuestionDetail('releaseCompany', e);
                },
                rules: [
                  {
                    required: 'Y',
                    message: '请输入发布单位',
                  },
                ],
              })(<Input style={{ maxWidth: '388px' }} maxLength="20" placeholder="请输入发布单位" />)}
            </FormItem>

            <FormItem label="问卷描述" {...formItemLayout}>
              {getFieldDecorator('examDesc', {
                initialValue: questionInfo.examDesc || '',
                onChange: e => {
                  this.saveQuestionDetail('examDesc', e);
                },
                rules: [
                  {
                    required: 'Y',
                    message: '问卷描述',
                    // max: 150,
                  },
                ],
              })(<Input.TextArea style={{ maxWidth: '388px' }} rows={6} placeholder="请输入问卷描述" />)}
            </FormItem>
            <Row type="flex" justify="start">
              <Col offset={4} span={20} className="btn-bar">
                <Button onClick={() => this.saveQuestion('1')}>保存至草稿箱</Button>
                <Button className="ant-col-offset-1" type="primary" onClick={() => this.saveQuestion()}>
                  下一步
                </Button>
              </Col>
            </Row>
          </Form>
        </div>

        <ConditionModal
          visible={conditionModal}
          labelData={trait.question.conditionLst.data ? trait.question.conditionLst.data : []}
          cancleFunc={() =>
            this.setState({
              conditionModal: false,
              pushText: this.id ? pushText : {},
            })
          }
          okFunc={() => this.setState({ conditionModal: false })}
          busTypeChange={e => this.busTypeChange(e)}
          timePeriodChange={(type, e) => this.timePeriodChange(type, e)}
          timeChange={(type, value, pushtype) => this.timeChange(type, value, pushtype)}
          defaultData={pushText}
          limitFlagChange={(type, e) => this.limitFlagChange(type, e)}
          pushTimelimitChange={(type, value, pushtype) => this.pushTimelimitChange(type, value, pushtype)}
        />
      </div>
    );
  }
}

export default connect(state => {
  return {
    trait: state.trait,
  };
})(Form.create()(CreateSurvey));
