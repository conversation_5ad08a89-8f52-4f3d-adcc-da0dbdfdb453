@import '~antd/lib/style/themes/default.less';

.chartHradOper {
  padding-left: 20px;
  line-height: 1;
}

// .chatButton {
//   cursor: pointer;
// }
// .chatButton:before {
//   content: attr('已开启');
//   position: absolute;
//   z-index: 10;
//   color: pink;
//   mask: linear-gradient(to left, red, transparent);
// }

// span{
//   position:relative;
//   display:inline-block;
// }
.chatButton:hover {
  cursor: pointer;
}
.chatButton:hover:before {
  content: attr(data-tooltip);
  background: #d9444a;
  color: #fff;
  padding: 0.8em 1em;
  position: absolute;
  left: 100%;
  top: -70%;
  margin-left: 14px;
  // white-spack:pre;
}
.chatButton:hover:after {
  content: ' ';
  position: absolute;
  left: 80%;
  width: 0;
  height: 0;
  border-right: 8px solid #d9444a;
  border-top: 8px solid transpatrnt;
  border-bottom: 8px solid transparent;
}

.chartHradBtn {
  display: inline-block;
  width: 30px;
  height: 30px;
  margin-left: 8px;
  background-position: 0 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  cursor: pointer;
}
.chbtn0 {
  background-image: url(../../../assets/icon-readed.png);
}
.chbtn1 {
  background-image: url(../../../assets/icon-move.png);
}
.chbtn2 {
  background-image: url(../../../assets/icon-record.png);
}
.chbtn3 {
  background-image: url(../../../assets/icon-reg.png);
}
.chbtn4 {
  background-image: url(../../../assets/icon-users.png);
}
.chbtn5 {
  background-image: url(../../../assets/icon-refresh.png);
}
.chbtn6 {
  background-image: url(../../../assets/icon-addcontent.png);
}
.chbtn7 {
  background-image: url(../../../assets/icon-cancel.png);
}
.chbtn8 {
  background-image: url(../../../assets/icon-end.png);
}
.tagCloseBox {
  position: absolute;
  width: 20px;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  opacity: 0;
  cursor: pointer;
}

.list {
  height: 400px;
  overflow: auto;
  background: #fff;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  width: 300px;

  &::-webkit-scrollbar {
    display: none;
  }
  .item {
    padding-right: 24px;
    padding-left: 24px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;

    .meta {
      width: 100%;
    }

    .avatar {
      margin-top: 4px;
      background: #fff;
    }
    .iconElement {
      font-size: 32px;
    }

    &.read {
      opacity: 0.4;
    }
    &:last-child {
      border-bottom: 0;
    }
    &:hover {
      background: @primary-1;
    }
    .title {
      margin-bottom: 8px;
      font-weight: normal;
    }
    .description {
      font-size: 12px;
      line-height: @line-height-base;
    }
    .datetime {
      margin-top: 4px;
      font-size: 12px;
      line-height: @line-height-base;
    }
    .extra {
      float: right;
      margin-top: -1.5px;
      margin-right: 0;
      color: @text-color-secondary;
      font-weight: normal;
    }
  }
  .loadMore {
    padding: 8px 0;
    color: @primary-6;
    text-align: center;
    cursor: pointer;
    &.loadedAll {
      color: rgba(0, 0, 0, 0.25);
      cursor: unset;
    }
  }
}

.infoRow {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #d9d9d9;
  span {
    color: black;
    padding: 0 10px 0 0;
  }
  div {
    color: rgb(172, 172, 172);
    flex: 1;
  }
}

:global {
  .ant-collapse-header {
    background-color: white;
    font-weight: 500;
    // border-bottom: 1px solid #d9d9d9 !important;
  }
  .ant-collapse-icon-position-right > .ant-collapse-item > .ant-collapse-header {
    padding: 12px 0px;
  }
  .ant-collapse-content > .ant-collapse-content-box {
    padding: 10px 0px;
  }
  .ant-collapse-borderless > .ant-collapse-item > .ant-collapse-content {
    background-color: white;
  }
  .ant-collapse-borderless > .ant-collapse-item {
    border-bottom: 0;
  }
  .ant-upload-picture-card-wrapper {
    width: 88%;
  }
}

.collapse {
  div {
    border: 0;
  }
}

.baseInfo {
  display: flex;
  flex-wrap: wrap;
  border-top: 1px solid #000;
  border-bottom: 1px solid #000;
  margin: 17px 0;
  padding: 10px 0;
  .baseItem {
    display: flex;
    align-items: center;
    flex: 1;
    margin: 10px 0;
    span {
      white-space: nowrap;
    }
  }
  .content {
    width: 120px;
    border-bottom: 1px solid #000;
    text-align: center;
  }
}

.clinicInfo {
  // background-color: rgb(242, 242, 242);
  .clickItem {
    padding: 6px 15px;
    span {
      font-weight: bold;
    }
  }
}
