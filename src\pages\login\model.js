import React from 'react';
import * as CONSTANT from '../../config/constant/constant';
import * as service from './service';
import { sm4Encrypt } from '@/utils/utils';

export default {
  namespace: 'login',
  state: {
    captcha: `${CONSTANT.DOMAIN}/api/validate_code?${Date.now()}`,
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
  },
  effects: {
    *doLogin({ payload }, { call, put }) {
      yield put({
        // 清除权限树
        type: 'root/save',
        payload: { permissionData: {} },
      });

      const { username, password, validateCode } = payload;
      const { data } = yield call(service.doLogin, {
        // mark: 1,
        // username: sm4Encrypt(username),
        // password: sm4Encrypt(password),
        username,
        password,
        validate_code: validateCode,
      });
      if (data.code === 0) {
        service.operationLog();
      }
      return data;
    },
    *changeCaptcha({ payload }, { put }) {
      yield put({
        type: 'save',
        payload: {
          captcha: `${CONSTANT.DOMAIN}/api/validate_code?${Date.now()}`,
        },
      });
    },
  },
};
