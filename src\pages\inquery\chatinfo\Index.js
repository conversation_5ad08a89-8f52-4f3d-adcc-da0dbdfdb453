/* eslint-disable no-plusplus */
import React, { Component } from 'react';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Icon, Button } from 'antd';
import * as Api from './api';
import styles from './index.less';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      msgHistory: {},
      previewImgUrl: '',
    };
  }

  componentDidMount() {
    this.getChatMsgHistory();
  }

  async getChatMsgHistory(pageFlg = 0) {
    const {
      location: { query = {} },
    } = this.props;
    const { msgHistory = {} } = this.state;
    const { currentPage = 1 } = msgHistory;

    const { code, data = {} } = await Api.queryChatHistoryRecord({
      groupId: query.id,
      numPerPage: 200,
      pageNum: currentPage + pageFlg,
      queryFlg: 'xcx',
    });
    if (code === 0) {
      this.setState({ msgHistory: data });
    }
  }

  render() {
    const { msgHistory = {}, previewImgUrl } = this.state;
    const { recordList = [], currentPage = 1, pageCount = 1 } = msgHistory;

    return (
      <div className={styles['page-record-container']}>
        <div className={styles['page-content']}>
          {!recordList || recordList.length === 0 ? (
            <div className={styles['page-right']} />
          ) : (
            <div className={styles['page-right']}>
              <div
                className={styles['chat-content']}
                ref={node => {
                  this.chatContentNode = node;
                }}
              >
                {recordList.map((item, index) => {
                  return (
                    <div key={index}>
                      <div
                        className={styles['chat-content-notice']}
                        dangerouslySetInnerHTML={{
                          __html: item.createTime,
                        }}
                      />
                      <div className={`${styles['chat-content-item']} ${styles.left}`}>
                        <div className={styles['msg-container']}>
                          {item.content.indexOf('http') === 0 ? (
                            <div className={styles['msg-image']} onClick={() => this.setState({ previewImgUrl: item.content })}>
                              <img src={item.content} />
                            </div>
                          ) : item.content ? (
                            <div
                              className={styles['msg-text']}
                              dangerouslySetInnerHTML={{
                                __html: item.content,
                              }}
                            />
                          ) : null}
                        </div>
                        <Avatar icon={<UserOutlined />} style={{ margin: '0 30px' }} />
                        <div>{item.sendUserName}</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {previewImgUrl ? (
          <div className={styles['workstation-preview-box']}>
            <Icon type="close" className="btn-close" onClick={() => this.setState({ previewImgUrl: '' })} />
            <div className={styles['preview-img']}>
              <img src={previewImgUrl} alt="" />
            </div>
          </div>
        ) : null}

        {/* <div className={styles.msgFooter}>
          <Button disabled={currentPage === 1} onClick={() => this.getChatMsgHistory(-1)}>
            上一页
          </Button>
          <Button
            disabled={currentPage >= pageCount}
            style={{ marginLeft: 30 }}
            onClick={() => this.getChatMsgHistory(1)}
          >
            下一页
          </Button>
        </div> */}
      </div>
    );
  }
}

export default Index;
