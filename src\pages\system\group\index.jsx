import React, { Component } from 'react';
import { connect } from 'dva';
import { Radio } from 'antd';
import { history as router } from 'umi';
import BaseTeam from './BaseTeam';
import ManageTeam from './ManageTeam';

import styles from './index.less';

class Index extends Component {
  constructor(prop) {
    super(prop);

    const {
      location: { query = {} },
    } = this.props;
    this.query = query;

    this.state = {
      activeTab: this.query.activeTab || '1',
    };
  }

  componentDidMount() {}

  changeTab = e => {
    this.setState({ activeTab: e.target.value });
    router.replace({
      pathname: '/system/group',
      query: { activeTab: e.target.value },
    });
  };

  render() {
    const { activeTab } = this.state;
    return (
      <div className="g-page">
        <div className={`container ${styles.page}`}>
          <Radio.Group defaultValue={activeTab} size="large" onChange={this.changeTab}>
            <Radio.Button value="1">基础团队</Radio.Button>
            <Radio.Button value="2">管理团队</Radio.Button>
          </Radio.Group>
          {activeTab == 1 && <BaseTeam />}
          {activeTab == 2 && <ManageTeam />}
        </div>
      </div>
    );
  }
}

export default connect()(Index);
