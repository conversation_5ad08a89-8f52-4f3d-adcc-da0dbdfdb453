/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, DatePicker, Table, Button } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { jhcahTemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, jhcahTemplate: { ...jhcahTemplate, ...payload } },
      },
    });
  };

  const changeTableData = (val, ind, key) => {
    const { field6 = [] } = jhcahTemplate;
    field6[ind][key] = val;
    changeData({ field6 });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="其他信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={16}>
              <Form.Item label="生化检测：17-OHP其他生化检测结果">
                <Input placeholder="请输入" value={jhcahTemplate.field1} onChange={e => changeData({ field1: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="近期是否进行过骨髓移植、或细胞治疗、或接受输血">
                <div className="flex-box">
                  <Radio.Group value={jhcahTemplate.field2} onChange={e => changeData({ field2: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">最近一次日期</div>
                    <DatePicker format="YYYY-MM-DD" value={jhcahTemplate.field3 ? moment(jhcahTemplate.field3) : null} onChange={(date, dateString) => changeData({ field3: dateString })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="其他遗传病史">
                <div className="flex-box">
                  <Radio.Group value={jhcahTemplate.field4} onChange={e => changeData({ field4: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="亲属（包括本人）做过此检测">
                <div className="flex-box">
                  <Radio.Group value={jhcahTemplate.field5} onChange={e => changeData({ field5: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24} style={{ marginBottom: '24px' }}>
              <Table
                dataSource={jhcahTemplate.field6 || []}
                columns={[
                  {
                    title: '亲属检测编号',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field1} onChange={e => changeTableData(e.target.value, index, 'field1')} />;
                    },
                  },
                  {
                    title: '亲属姓名',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field2} onChange={e => changeTableData(e.target.value, index, 'field2')} />;
                    },
                  },
                  {
                    title: '亲属关系',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field3} onChange={e => changeTableData(e.target.value, index, 'field3')} />;
                    },
                  },
                  {
                    title: '亲属检测结果',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field4} onChange={e => changeTableData(e.target.value, index, 'field4')} />;
                    },
                  },
                  {
                    title: '操作',
                    width: 60,
                    render: (cur, col, index) => (
                      <Button type="link" onClick={() => changeData({ field6: jhcahTemplate.field6.filter((item, ind) => index != ind) })}>
                        删除
                      </Button>
                    ),
                  },
                ]}
                pagination={false}
              />
              <Button type="dashed" block onClick={() => changeData({ field6: [...(jhcahTemplate.field6 || []), {}] })}>
                +添加亲属
              </Button>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
