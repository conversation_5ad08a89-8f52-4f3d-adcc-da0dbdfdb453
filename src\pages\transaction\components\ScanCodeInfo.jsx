/* eslint-disable react/no-string-refs */
import React from 'react';
import { connect } from 'dva';
import { Modal, Button, Input, Table, Image } from 'antd';
import { Decimal } from 'decimal.js';
import * as utils from '../../../utils/utils';
import * as SearchItem from '../components/SearchItem';
import '../styles.less';

class ScancodeInfo extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      backReason: '',
      rundPwd: '',
    };
  }

  closeDrawer() {
    this.props.dispatch({
      type: 'root/closeDrawer',
    });
    this.setState({
      backReason: '',
      rundPwd: '',
    });
  }

  handleRefund() {
    this.setState({ isModalOpen: true });
  }
  handleOk() {
    const { scanOrder = {} } = this.props;
    const { orderInfo = {} } = scanOrder;
    const refundFee = this.refs?.userrefuntfeeinput?.input?.value;
    this.props.funcReBack(this.state.backReason, refundFee, orderInfo.id, orderInfo.busType, this.state.rundPwd);
  }
  handleCancel() {
    this.setState({ isModalOpen: false });
  }

  render() {
    const { scanOrder = {}, showModal, btns = {}, userType } = this.props;
    const { isModalOpen, backReason, rundPwd } = this.state;
    const { orderInfo = {} } = scanOrder;
    let { extFieldsViews = '' } = orderInfo;

    let maxRefundFee = orderInfo.totalRealFee || 0;
    if (orderInfo.totalRefundFee > 0) {
      maxRefundFee = new Decimal(maxRefundFee).sub(new Decimal(orderInfo.totalRefundFee)).toNumber();
    }
    if (orderInfo.extFieldsViews) {
      let str = extFieldsViews.replace("''", '');
      extFieldsViews = JSON.parse(str);
      try {
        extFieldsViews.products = JSON.parse(extFieldsViews.products);
      } catch (error) {}
      if (extFieldsViews.reportMailingType == 2) {
        extFieldsViews.products.push({
          productName: '快递费',
          productprice: extFieldsViews.reportMailingFees,
        });
      }
      const total = extFieldsViews.products.reduce((prev, cur) => prev + Number(cur.productprice), 0);
      extFieldsViews.products.push({
        productName: '合计',
        productId: -1,
        productprice: total,
      });
    }
    if (orderInfo.payCertificate) {
      try {
        orderInfo.payCertificate = JSON.parse(orderInfo.payCertificate);
      } catch (error) {}
    }
    const tableData = [orderInfo];
    const orderTableData = [{ ...orderInfo, ...extFieldsViews }];
    const columnsPatient = [
      {
        title: '姓名',
        dataIndex: 'patientName',
        width: '25%',
      },
      {
        title: '手机号',
        dataIndex: 'patientMobile',
        width: '25%',
      },
      {
        title: '身份证号',
        dataIndex: 'patientIdNo',
        width: '25%',
      },
      {
        title: '就诊卡号',
        dataIndex: 'patCardNo',
        width: '25%',
      },
    ];
    const columnsProduct = [
      {
        title: '项目',
        // dataIndex: 'productName',
        width: '50%',
        render: v => <span>{!v.productId ? `(${v.productName})${v.remark || ''}` : v.productName}</span>,
      },
      {
        title: '金额（元）',
        width: '50%',
        render: record => {
          return <p>{utils.comma(record.productprice || 0)}</p>;
        },
      },
    ];
    const columnsOrder = [
      {
        title: '订单状态',
        dataIndex: 'statusDesc',
        width: 110,
      },
      {
        title: '支付方式',
        dataIndex: 'payMethod',
        width: 110,
      },
      {
        title: '是否加急',
        width: 110,
        render: record => {
          return <p>{record.isMerge == 1 ? '是' : '否'}</p>;
        },
      },
      {
        title: '合作客户',
        dataIndex: 'deptName',
        width: 110,
      },
      {
        title: '支付时间',
        dataIndex: 'paydTime',
        width: 110,
      },
      {
        title: '付款流水号',
        dataIndex: 'agtOrdNum',
        width: 110,
      },
      {
        title: '开单方式',
        render: record => {
          return <span>{record.subSource == 1 ? '患者开单' : '客户开单'}</span>;
        },
        width: 110,
      },
      {
        title: '来源医生',
        dataIndex: 'hisSerialNo',
        width: 110,
      },
      {
        title: '订单号',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '缴费金额',
        width: 110,
        render: record => {
          return <p>{utils.comma(record.totalRealFee || 0)}</p>;
        },
      },
    ];
    const columnsRefund = [
      {
        title: '退款时间',
        dataIndex: 'createTime',
      },
      {
        title: '退款状态',
        render: record => {
          return <span>{record.status === '1' ? '退款受理' : record.status === '2' ? '退款成功' : record.status === '3' ? '退款失败' : '发起退款'}</span>;
        },
      },
      {
        title: '退款金额',
        render: record => {
          return <p>{utils.comma(record.refundFee || 0)}</p>;
        },
      },
      {
        title: '退款流水号',
        dataIndex: 'refundSerialNo',
      },
      {
        title: '退款原因',
        dataIndex: 'refundDesc',
      },
      {
        title: '退款操作用户',
        dataIndex: 'userName',
      },
    ];
    return (
      <div className="order-info-panle" id="orderInfoPnale">
        <p className="info-header btn-header">
          <span>订单详情</span>
        </p>
        <div className="order-main scancode-box-main">
          <div className="section no-border">
            <p className="info-item-header">患者信息</p>
            <Table columns={columnsPatient} dataSource={tableData} rowKey="bizOrderId" pagination={false} />
          </div>
          <div className="section no-border">
            <p className="info-item-header">开单信息</p>
            <Table columns={columnsProduct} dataSource={extFieldsViews.products} rowKey="bizOrderId" pagination={false} />
          </div>
          <div className="section no-border">
            <p className="info-item-header">订单信息</p>
            <Table columns={columnsOrder} dataSource={orderTableData} rowKey="bizOrderId" pagination={false} scroll={{ x: 1500 }} />
          </div>
          {orderInfo.payCertificate?.length > 0 ? (
            <div className="section no-border">
              <p className="info-item-header">门诊缴费凭证</p>
              <Image.PreviewGroup>
                {orderInfo.payCertificate.map((v, i) => (
                  <Image width={200} key={i} src={v} />
                ))}
              </Image.PreviewGroup>
            </div>
          ) : null}
          {orderInfo && orderInfo.refundList && orderInfo.refundList.length ? (
            <div className="section no-border">
              <p className="info-item-header">退款信息</p>
              <Table columns={columnsRefund} dataSource={orderInfo.refundList} rowKey="bizOrderId" pagination={false} />
            </div>
          ) : null}
          {((orderInfo.totalRefundFee && orderInfo.totalRealFee > orderInfo.totalRefundFee) || !orderInfo.totalRefundFee) &&
          orderInfo.status === 'S' &&
          btns['/transaction/scancode/refund'] &&
          orderInfo.payMethod == '微信支付' &&
          !userType ? (
            <div style={{ textAlign: 'right', paddingBottom: '16px' }}>
              <Button type="primary" onClick={() => this.handleRefund()}>
                退款
              </Button>
            </div>
          ) : null}
        </div>
        <Modal className="refund-box" title="退款" open={isModalOpen && showModal} onOk={() => this.handleOk()} onCancel={() => this.handleCancel()}>
          <div className="m-refund align-center">
            <div className="refund-label require">退款金额：</div>
            <div className="refund-input">
              <Input placeholder="请输入最多两位小数退款金额" defaultValue={new Decimal(maxRefundFee).div(new Decimal(100).toNumber())} ref="userrefuntfeeinput" maxLength="8" />元
            </div>
          </div>
          <div className="m-refund">
            <p className="refund-label require">退款原因：</p>
            <div className="refund-input">
              <Input.TextArea placeholder="请输入退款原因（最多40字）" value={backReason} maxLength={40} rows={4} onChange={e => this.setState({ backReason: e.target.value })} />
            </div>
          </div>
          <div className="m-refund">
            <div className="refund-label require">退款密码：</div>
            <div className="refund-input">
              <Input placeholder="请输入退款密码" type="password" value={rundPwd} onChange={e => this.setState({ rundPwd: e.target.value })} />
            </div>
          </div>
        </Modal>
      </div>
    );
  }
}
export default connect(state => {
  return {
    scanOrder: state.scanOrder,
    drawerStatus: state.root.drawerStatus,
    btns: state.root.permissionData.btns,
  };
})(ScancodeInfo);
