import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Tabs, Upload, Button, message } from 'antd';
import { Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import '../style.less';

import loading from '../../../components/loading/Loading';
import * as utils from '../../../utils/utils';
import * as CONSTANT from '../../../config/constant/constant';

const TabPane = Tabs.TabPane;
const Dragger = Upload.Dragger;

export default connect(state => {
  return {
    hisList: state.microwebsite.hospital.list,
    detail: state.microwebsite.hospital.detail,
  };
})(
  class EdificeCoordinate extends React.Component {
    componentDidMount() {
      const { dispatch } = this.props;
      dispatch({
        type: 'microwebsite/hospitalList',
        next: 'hospitalDetail',
      });
    }

    hospitalDetail = hisId => {
      const { dispatch } = this.props;
      this.activeHisId = hisId;
      dispatch({
        type: 'microwebsite/hospitalDetail',
        payload: {
          hisId,
        },
      });
    };

    beforeUpload = file => {
      const isImage = file.type && file.type.indexOf('image') > -1;
      if (!isImage) {
        message.error('请选择图片进行上传!');
      }
      const size = file.size / 1024 / 1024 < 1;
      if (!size) {
        message.error('图片大小不能超过1MB!');
      }
      return isImage && size;
    };

    uploadOnChange = e => {
      const { response } = e.file;
      if (response) {
        if (response.code != 0) {
          message.error('上传文件失败');
        } else {
          message.success('上传文件成功');
          const img = (response.data && response.data.url) || '';
          const { dispatch } = this.props;

          const {
            hisList = [],
            location: { search = {} },
          } = this.props;
          const hisId = utils.queryStringToJson(search).hisId || this.activeHisId || (hisList[0] && hisList[0].hisId);
          dispatch({
            type: 'microwebsite/upFloorImg',
            payload: {
              floorImgUrl: img,
              hisId,
            },
          });
        }
      }
    };

    render() {
      const {
        hisList = [],
        detail = {},
        location: { search = {} },
      } = this.props;
      const hisId = utils.queryStringToJson(search).hisId || (hisList[0] && hisList[0].hisId);
      return (
        <div className="page-coordinate">
          <Tabs
            animated={false}
            defaultActiveKey={`${hisId}`}
            style={{ display: 'flex', flex: 'auto', flexDirection: 'column' }}
            onChange={id => {
              this.hospitalDetail(id);
            }}
          >
            {hisList.map(item => {
              return (
                <TabPane tab={item.hisName} key={`${item.hisId}`}>
                  <div className="coordinate-body">
                    <div className="edifice-coordinate">
                      <div className="option-box">
                        {detail.floorImgPath ? (
                          <div className="image-box">
                            <img src={detail.floorImgPath} alt="" style={{ width: 380, height: 194 }} />
                            <div className="option-box">
                              <Dragger
                                data={{
                                  partnerId: 'merchant',
                                  serviceType: 'test',
                                }}
                                name="upfile"
                                action={`${CONSTANT.DOMAIN}/api/files/uploadpic`}
                                multiple={false}
                                showUploadList={false}
                                beforeUpload={this.beforeUpload}
                                onChange={this.uploadOnChange}
                              >
                                <div>
                                  <Icon type="inbox" style={{ fontSize: 40, color: '#3F969D' }} />
                                </div>
                                <p style={{ marginTop: 24, color: '#FFF' }} className="ant-upload-text">
                                  点击这里重新上传
                                </p>
                                <p style={{ color: '#fff', marginTop: 24, fontSize: '14px', padding: '0 10px' }}>大小不超过1M，建议尺寸750*1200，以能清楚辨识楼栋为标准</p>
                              </Dragger>
                            </div>
                          </div>
                        ) : (
                          <div style={{ marginTop: 180, width: 380, height: 194 }}>
                            <Dragger
                              data={{
                                partnerId: 'merchant',
                                serviceType: 'test',
                              }}
                              name="upfile"
                              action={`${CONSTANT.DOMAIN}/api/files/uploadpic`}
                              multiple={false}
                              showUploadList={false}
                              beforeUpload={this.beforeUpload}
                              onChange={this.uploadOnChange}
                            >
                              <div>
                                <Icon type="inbox" style={{ fontSize: 40, color: '#3F969D' }} />
                              </div>
                              <p style={{ marginTop: 24 }} className="ant-upload-text">
                                点击这里上传
                              </p>
                              <p style={{ color: '#919191', marginTop: 24, fontSize: '14px', padding: '0 10px' }}>大小不超过1M，建议尺寸750*1200，以能清楚辨识楼栋为标准</p>
                            </Dragger>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </TabPane>
              );
            })}
          </Tabs>
        </div>
      );
    }
  },
);
