import { message } from 'antd';
import moment from 'moment';
import * as api from './api';

export default {
  namespace: 'contract',
  state: {
    listData: {},
    institutionList: [], // 客户列表
    allPersonnel: [], // 账号列表
    allGroup: [], // 团队列表
    products: [], // 产品线
    subproducts: [], // 子产品
    institutionContract: [], // 客户有效合同
    detail: {
      xcxSamplePrefectFlag: '0',
      xcxOutpatientPaymentFlag: '0',
      useMailFlag: '1',
    },
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
    clear() {
      return {};
    },
  },
  effects: {
    *getinstitutionbylist({ payload }, { call, put }) {
      const data = yield call(api.getinstitutionbylist, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { institutionList: data.data || [] },
        });
      }
    },
    *getAllUser({ payload }, { call, put }) {
      const data = yield call(api.getAllUser, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { allPersonnel: data.data || [] },
        });
      }
    },
    *getAllGroup({ payload }, { call, put }) {
      const data = yield call(api.getAllGroup, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { allGroup: data.data?.recordList || [] },
        });
      }
    },
    *getproducts({ payload = {} }, { call, put }) {
      const data = yield call(api.getproducts, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: payload.parentId ? { subproducts: data.data || [] } : { products: data.data || [] },
        });
      }
    },
    *getcontractbyinstitution({ payload }, { call, put }) {
      const data = yield call(api.getcontractbyinstitution, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { institutionContract: data.data || [] },
        });
      }
    },

    *getcontractbypage({ payload }, { call, put }) {
      const data = yield call(api.getcontractbypage, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { listData: data.data || {} },
        });
      }
    },
    *getDetail({ payload }, { call, put }) {
      const data = yield call(api.getDetail, payload);
      if (data.code === 0 && data.data) {
        const { data: detail } = data;
        if (detail.assignedUnit) detail.assignedUnit = Number(detail.assignedUnit);
        if (!detail.xcxSamplePrefectFlag) detail.xcxSamplePrefectFlag = '0';
        if (!detail.xcxOutpatientPaymentFlag) detail.xcxOutpatientPaymentFlag = '0';
        if (detail.signingUnit) detail.signingUnit = Number(detail.signingUnit);
        if (detail.salesPersonnel) detail.salesPersonnel = Number(detail.salesPersonnel);
        if (detail.signingDate) detail.signingDate = moment(detail.signingDate);
        if (detail.startDate) detail.startDate = moment(detail.startDate);
        if (detail.endDate) detail.endDate = moment(detail.endDate);
        if (detail.files) {
          try {
            detail.files = JSON.parse(detail.files);
          } catch (error) {
            detail.files = detail.files.split(';').map(v => ({ name: v, url: v }));
          }
        }
        if (detail.step) detail.step = JSON.parse(detail.step);
        (detail.relProducts || []).forEach(v => {
          if (v.tieredSettlementPrice) {
            try {
              v.tieredSettlementPrice = JSON.parse(v.tieredSettlementPrice);
            } catch (error) {}
          }
        });
        (detail.institutions || []).forEach(v => {
          if (v.institutionId) {
            v.institutionId = Number(v.institutionId);
          }
          if (v.relContractId) {
            v.relContractId = v.relContractId.split(';');
          }
        });
        yield put({
          type: 'save',
          payload: { detail },
        });
        return detail;
      }
      return false;
    },
    *deletecontract({ payload }, { call }) {
      const data = yield call(api.deletecontract, payload);
      return data.code === 0;
    },
    *addcontract({ payload }, { call }) {
      const data = yield call(api.addcontract, payload);
      return data.code === 0;
    },
    *updatecontract({ payload }, { call }) {
      const data = yield call(api.updatecontract, payload);
      return data.code === 0;
    },
    *checkdelete({ payload }, { call }) {
      const data = yield call(api.checkdelete, payload);
      return data.code === 0;
    },
    *exportcontract({ payload }, { call }) {
      yield call(api.exportcontract, payload);
    },
    *exportGrant({ payload }, { call }) {
      yield call(api.exportGrant, payload);
    },
  },
};
