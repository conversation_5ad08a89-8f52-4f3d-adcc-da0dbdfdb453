import React, { Component, Fragment } from 'react';
import { List, Modal, Input, Button, Empty, message } from 'antd';
import GroupHandMove from './GroupHandMove';
import * as Api from './api';
import styles from './allgroupuseradd.less';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      list: [],
      filter: '',
      moveInfo: {},
    };
  }

  componentDidMount() {
    // this.getData();
  }

  componentWillReceiveProps(props) {
    // if (props.show) {
    //   this.getData();
    // }
  }

  getData = async () => {
    const { filter = '' } = this.state;
    const { code, data = [] } = await Api.searchPatientGroupAllUser({ content: filter });
    if (code == 0) {
      this.setState({ list: data });
    }
  };

  searchContent = val => {
    // const val = e.currentTarget.value;
    const { filter = '' } = this.state;
    if (filter !== val) {
      this.setState({ filter: val }, this.getData);
    }
  };

  addUser = async item => {
    const { groupId } = item;
    const { code } = await Api.addAllUserToPatientGroup({ groupId });
    if (code == 0) {
      const { onSuccess } = this.props;
      message.success('添加成功');
      this.getData();
      onSuccess();
    }
  };

  setMoveInfo = item => {
    const { onCancel } = this.props;
    this.setState({ moveInfo: { ...item, id: item.groupId } });
    onCancel();
    this.setState({ list: [], filter: '' });
  };

  render() {
    const { onCancel, show, onSuccess } = this.props;
    const { list = [], filter = '', moveInfo } = this.state;
    return (
      <Fragment>
        <GroupHandMove chatInfo={moveInfo} getChatList={onSuccess} type="justModal" />
        <Modal
          title="添加PID"
          visible={show}
          onOk={this.handleSubmit}
          onCancel={() => {
            onCancel();
            this.setState({ list: [], filter: '' });
          }}
          footer={null}
          destroyOnClose
        >
          <Input.Search placeholder="请输入要PID或病友姓名，按Enter进行搜索" onSearch={this.searchContent} style={{ width: '100%' }} />
          <List className={styles.list} split={false}>
            {list.map((item, i) => {
              let filterStr;
              if (!filter && !filter.length) {
                filterStr = `${item.pid}(${item.name})`;
              } else {
                filterStr = `${item.pid}(${item.name})`.replace(new RegExp(filter, 'g'), `<span class="${styles.keyword}">${filter}</span>`);
              }
              return (
                <List.Item key={item.key || i} className={styles.item}>
                  <List.Item.Meta
                    className={styles.meta}
                    title={
                      <div className={styles.title}>
                        <span
                          dangerouslySetInnerHTML={{
                            __html: filterStr,
                          }}
                        />
                        <div className={styles.extra}>
                          <Button type="primary" size="small" style={{ marginRight: 10 }} ghost onClick={() => this.setMoveInfo(item)}>
                            转移
                          </Button>
                          {item.isAddType == 1 ? (
                            <Button disabled type="primary" size="small" ghost>
                              已添加
                            </Button>
                          ) : (
                            <Button type="primary" size="small" ghost onClick={() => this.addUser(item)}>
                              <Icon type="plus" />
                              添加
                            </Button>
                          )}
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              );
            })}
            {!list.length ? <Empty style={{ marginTop: 100 }} /> : null}
          </List>
        </Modal>
      </Fragment>
    );
  }
}

export default Index;
