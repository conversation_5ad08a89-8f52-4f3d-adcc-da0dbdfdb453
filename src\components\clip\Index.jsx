import React, { Component } from 'react';
import { Button, message } from 'antd';
import { Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import Cropper from 'react-cropper';
import 'cropperjs/dist/cropper.css';

import { upload } from '../../utils/utils';

import './style.less';

export default class CoreClip extends Component {
  state = {
    clipFullSrc: null,
    clipSrc: null,
  };

  readImg() {
    const fileReader = new FileReader();
    fileReader.onload = e => {
      const dataURL = e.target.result;
      this.setState({ clipFullSrc: dataURL });
    };
    fileReader.readAsDataURL(this.imgSrc);
  }

  fileSelect(e) {
    const file = e.target.files[0];
    if (!/\.(gif)|(GIF)|(png)|(PNG)|(jpg)|(JPG)|(jpeg)|(JPEG)|(jpe)|(JPE)$/.test(file.name)) {
      message.error('请选择图片类型文件');
      return false;
    }
    this.imgSrc = file;
    this.readImg();
  }

  uploadClipImg() {
    const fileType = (this.imgSrc.name || 'tx.jpg').match(/\.([a-zA-Z]+$)/)[1];
    this.cropper.getCroppedCanvas().toBlob(
      async blob => {
        // 创造提交表单数据对象
        const formData = new FormData();
        // 添加要上传的文件
        formData.append('upfile', blob, `${Date.now()}.${fileType}`);
        formData.append('partnerId', 'merchant');
        formData.append('serviceType', 'test');
        // 上传图片
        const { data = {} } = await upload('/api/files/uploadpic', {
          method: 'POST',
          body: formData,
        });
        if (data.code == 0) {
          const { url } = data.data || {};
          // 提示上传完毕
          this.setState({ clipSrc: url });
          this.resetClip();
          const { afterUpload } = this.props;
          if (typeof afterUpload === 'function') {
            afterUpload(url);
          }
        } else {
          message.error(data.msg);
        }
      },
      `image/${fileType}`,
      0.7,
    );
  }

  resetClip() {
    this.fileFire.value = '';
    this.setState({ clipFullSrc: null });
  }

  render() {
    const { defaultUrl, aspectRatio, operSize = {} } = this.props;
    let { clipFullSrc, clipSrc } = this.state;
    if (!clipSrc) {
      clipSrc = defaultUrl;
    }
    return (
      <div className="hc-core">
        <div className="hc-core-img">
          {clipSrc ? (
            <img className="hc-core-view" src={clipSrc} alt="" />
          ) : (
            <Button size="large">
              <Icon type="upload" />
              选择图片
            </Button>
          )}
          <input type="file" accept="image/*" className="hc-core-file" ref={fileFire => (this.fileFire = fileFire)} onChange={this.fileSelect.bind(this)} />
        </div>
        {clipFullSrc ? (
          <div className="hc-core-mask">
            <div className="hc-core-clip">
              <div className="hc-core-clip-title">图片裁剪</div>
              <div className="hc-core-clip-tip">支持图片格式：所有图片类型</div>
              <div className="hc-core-clip-oper" style={operSize}>
                <Cropper
                  src={this.state.clipFullSrc}
                  className="hc-clip-cropper"
                  ref={cropper => (this.cropper = cropper)}
                  zoomable={false}
                  aspectRatio={aspectRatio}
                  guides={true}
                  preview=".core-clip-view-img"
                  dragMode="crop"
                  style={operSize}
                />
              </div>
              <div className="hc-core-clip-view">
                <div className="core-clip-view-img" />
                <div className="core-clip-view-btn">
                  <Button size="large" onClick={this.resetClip.bind(this)}>
                    取消
                  </Button>
                  <Button size="large" className="ant-col-offset-1" type="primary" onClick={this.uploadClipImg.bind(this)}>
                    确定
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : null}
      </div>
    );
  }
}
