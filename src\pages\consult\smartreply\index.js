import React, { Component, Fragment } from 'react';
import { connect } from 'dva';
import { Tabs, Table, Button, Modal, message, Radio, Input } from 'antd';
import * as Api from './api';

import styles from './index.less';

const { TextArea } = Input;

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      tabs: [],
      tabsModal: [],
      activeTab: 0,
      list: {},
      fastList: [],
      modalType: '',
      periodId: prop.roleType === 'doc' ? 'doctor' : 'all',
      editPeriodId: '',
    };

    this.tableColumns = [
      {
        title: '问题',
        dataIndex: 'questionContent',
        // width: 200,
      },
      {
        title: '回复内容',
        dataIndex: 'answerContent',
      },
      // {
      //   title: '阶段',
      //   dataIndex: 'periodName',
      //   width: 100,
      // },
      // {
      //   title: '使用量',
      //   dataIndex: 'robotCount',
      //   width: 80,
      // },
      {
        title: '操作',
        width: 120,
        render: record => {
          return (
            <Fragment>
              <span
                className={styles.tableOperText}
                onClick={() => {
                  this.preEditTag(record.chatRobotId, record.periodId);
                }}
              >
                编辑
              </span>
              <span className={styles.tableOperText} onClick={() => this.preDelete(record.chatRobotId)}>
                删除
              </span>
            </Fragment>
          );
        },
      },
    ];
  }

  componentDidMount() {
    this.getBusType();
  }

  getBusType = async () => {
    const { code, data = [] } = await Api.getBusType({
      type: this.props.roleType === 'doc' ? 1 : 0,
    });
    console.log(data);
    if (code == 0) {
      this.setState({ tabsModal: data });
      this.setState({ tabs: [{ no: '', name: '全部' }].concat(data) });
      this.getRobotList(); // 查询全部
    }
  };

  getRobotList = async (e, pageNum = 1) => {
    const { activeTab, tabs, periodId } = this.state;
    const tabIdx = e || activeTab;
    const idx = tabIdx * 1;
    const param = {
      periodId,
      periodName: (tabs[idx] || {}).name,
      numPerPage: 10,
      pageNum,
    };
    const { code, data = {} } = await Api.getRobotList(param);
    if (code == 0) {
      const { recordList = [] } = data;
      this.setState({ fastList: recordList, list: data });
    }
  };

  changeTab = e => {
    this.setState({ activeTab: e });
    this.getRobotList(e);
  };

  handleSubmit = async () => {
    const { chatRobotId, editPeriodId, questionContent, answerContent, modalType, periodId } = this.state;
    // if (!editPeriodId) {
    //   message.error('请选择阶段');
    //   return false;
    // }
    if (!questionContent) {
      message.error('请输入问题');
      return false;
    }
    if (!answerContent) {
      message.error('请输入智能回复');
      return false;
    }
    if (modalType == 'add') {
      const param = {
        questionContent,
        answerContent,
        periodId,
      };
      const { code, msg } = await Api.addChatRobot(param);
      if (code == 0) {
        message.success('添加成功');
        this.setState({ modalType: '', editPeriodId: '', questionContent: '', answerContent: '' });
        this.getRobotList(); // 查询全部
      } else {
        message.error(msg || '添加失败');
      }
    } else if (modalType == 'edit') {
      const param = {
        chatRobotId,
        questionContent,
        answerContent,
        periodId,
      };
      const { code, msg } = await Api.updateChatRobot(param);
      if (code == 0) {
        message.success('修改成功');
        this.setState({ modalType: '', editPeriodId: '', questionContent: '', answerContent: '' });
        this.getRobotList(); // 查询全部
      } else {
        message.error(msg || '修改失败');
      }
    }
  };

  preDelete = id => {
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除该内容?',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.doDelete(id);
      },
    });
  };

  doDelete = async id => {
    const { code, msg } = await Api.deleteChatRobot({ chatRobotId: id });
    if (code == 0) {
      message.success('删除成功');
      this.getRobotList();
    }
  };

  preEditTag = async (id, perId) => {
    const { code, data = {} } = await Api.queryChatRobotInfo({ chatRobotId: id }); // 查详情
    if (code == 0) {
      this.setState({ chatRobotId: id, periodId: perId, questionContent: data.questionContent, answerContent: data.answerContent, modalType: 'edit' });
    }
  };

  render() {
    const { tabs = [], tabsModal = [], fastList = [], list, modalType, periodId, questionContent, answerContent } = this.state;
    console.log(tabsModal);
    return (
      <div className={styles.connect}>
        <div className={styles.flexLine}>
          <div className={styles.flexItem}>
            <Button type="primary" onClick={() => this.setState({ modalType: 'add' })}>
              添加智能回复
            </Button>
          </div>
        </div>
        {/* {
          tabs.length > 0 ?
            <Tabs defaultActiveKey="0" onChange={this.changeTab} style={{ marginTop: 12 }}>
              {
                tabs.map((item, key) => {
                  return (
                    <Tabs.TabPane tab={item.name} key={key} />
                  );
                })
              }
            </Tabs> : null
        } */}
        <Table
          // style={{ marginTop: -14 }}
          dataSource={fastList}
          columns={this.tableColumns}
          rowKey="chatRobotId"
          locale={{
            emptyText: '暂无数据',
          }}
          pagination={{
            showQuickJumper: true,
            // showSizeChanger: true,
            defaultCurrent: list.currentPage || 1,
            current: list.currentPage || 1,
            total: list.totalCount || 0,
            showTotal: () => {
              return `共${list.totalCount || 0}条`;
            },
            onChange: pageNum => {
              this.getRobotList('', pageNum);
            },
          }}
        />
        <Modal
          title={modalType === 'add' ? '添加智能回复' : '编辑智能回复'}
          visible={modalType === 'add' || modalType === 'edit'}
          onOk={this.handleSubmit}
          onCancel={() => this.setState({ modalType: '', questionContent: '', answerContent: '' })}
          destroyOnClose
        >
          {/* <div className={styles.modalLine}>
            <div className={styles.modalLabel}>阶段：</div>
            <Radio.Group value={periodId.toString()} onChange={(e) =>{this.setState({editPeriodId: e.target.value});}} buttonStyle="solid">
              <Radio.Button value="all" name="全部" key="all">全部</Radio.Button>
              {
                tabsModal.map((item, key) => {
                  return (
                    <Radio.Button
                      value={item.no}
                      name={item.name}
                      key={key}
                      style={{ marginTop: 10 }}
                    >
                      {item.name}
                    </Radio.Button>
                  );
                })
              }
            </Radio.Group>
          </div> */}
          <div className={styles.modalLine}>
            <div className={styles.modalLabel}>问题：</div>
            <div>
              <TextArea
                placeholder="请输入问题内容，限50字内"
                defaultValue={questionContent}
                autosize={{ minRows: 2, maxRows: 2 }}
                maxLength={50}
                onBlur={e => {
                  this.setState({ questionContent: e.target.value });
                }}
              />
            </div>
          </div>
          <div className={styles.modalLine}>
            <div className={styles.modalLabel}>智能回复：</div>
            <div key={modalType}>
              <TextArea
                placeholder="请输入智能回复的内容，限500字内"
                defaultValue={answerContent}
                autosize={{ minRows: 5 }}
                onBlur={e => {
                  this.setState({ answerContent: e.target.value });
                }}
              />
            </div>
          </div>
        </Modal>
      </div>
    );
  }
}

export default connect()(Index);
