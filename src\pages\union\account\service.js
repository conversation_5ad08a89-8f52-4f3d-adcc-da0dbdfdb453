import { post } from '@/utils/request';

export const fetchList = (param = {}) => post('/api/doctor/register/query-by-page', { data: param });

export const fetchTotalList = (param = {}) => post('/api/sampleStat/productAllCount', { data: param });

export const findAllUser = (param = {}) => post('/api/userinfo/findAllUser', { data: param });

export const getInstitution = (param = {}) => post('/api/institution/get-by-list', { data: param });

export const getProduct = (param = {}) => post('/api/product/get-products', { data: param });

export const selectListByParam = (param = {}) => post('/api/returnMoney/selectListByParam', { data: param });

export const audit = (param = {}) => post('/api/doctor/register/audit', { data: param });

export const reject = (param = {}) => post('/api/doctor/register/reject', { data: param });

export const getrolesbypage = (param = {}) => post('/api/identify/paging', { data: param });
