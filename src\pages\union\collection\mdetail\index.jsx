import React, { useEffect, useState } from 'react';
import { Form, Input, InputNumber, Button, Table, DatePicker, Modal, message, Popconfirm, Descriptions } from 'antd';
import queryString from 'query-string';
import moment from 'moment';
import { connect } from 'umi';
import * as Api from '../service';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const Index = (props) => {
  const [addForm] = Form.useForm();
  const queryParams = queryString.parse(props.location.search);
  
  const { 
    institutionId, 
    contractId, 
    contractNumber, 
    institutionName,
    institutionCode,
    contactsName,
    totalSettleMoney, 
    unSettleMoney,
    menuType = '' 
  } = queryParams;

  const { permissionData = {} } = props;
  const { btns = {} } = permissionData;

  const [id, setId] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [detailList, setDetailList] = useState([]);
  const [currentRecord, setCurrentRecord] = useState(null);

  const [list, setList] = useState([
  {
    id: 1,
    month: '2023-01-01',
    shouldSettleAmount: 50000,
    settledAmount: 45000,
    shouldReturnAmount: 50000,
    returnedAmount: 48000,
    accountPeriod: 30,
    reconciliationStatus: 'completed',
    operationLog: '张三 2023-01-05 更新',
    remark: '首月回款'
  },
  {
    id: 2,
    month: '2023-02-01',
    shouldSettleAmount: 55000,
    settledAmount: 50000,
    shouldReturnAmount: 55000,
    returnedAmount: 52000,
    accountPeriod: 30,
    reconciliationStatus: 'completed',
    operationLog: '李四 2023-02-10 更新',
    remark: '正常回款'
  },
  {
    id: 3,
    month: '2023-03-01',
    shouldSettleAmount: 60000,
    settledAmount: 55000,
    shouldReturnAmount: 60000,
    returnedAmount: 58000,
    accountPeriod: 45,
    reconciliationStatus: 'pending',
    operationLog: '王五 2023-03-15 更新',
    remark: '账期延长'
  },
  {
    id: 4,
    month: '2023-04-01',
    shouldSettleAmount: 65000,
    settledAmount: 60000,
    shouldReturnAmount: 65000,
    returnedAmount: 62000,
    accountPeriod: 30,
    reconciliationStatus: 'discrepancy',
    operationLog: '赵六 2023-04-20 更新',
    remark: '金额有差异'
  },
  {
    id: 5,
    month: '2023-05-01',
    shouldSettleAmount: 70000,
    settledAmount: 65000,
    shouldReturnAmount: 70000,
    returnedAmount: 0,
    accountPeriod: 60,
    reconciliationStatus: 'pending',
    operationLog: '张三 2023-05-25 更新',
    remark: '待回款'
  }
]);
  useEffect(() => {
    console.log('组件挂载，开始获取列表数据');
    fetchList();
  }, []);

  const handleCancel = () => {
    setIsModalOpen(false);
    setId('');
    addForm.resetFields();
  };

  const handleDetailCancel = () => {
    setIsDetailModalOpen(false);
    setCurrentRecord(null);
  };

  const handleEdit = async (record) => {
    addForm.setFieldsValue({
      ...record,
      returnDate: moment(record.returnDate),
    });
    setId(record.id);
    setIsModalOpen(true);
  };

  const onAddFinish = async (values) => {
    const url = id ? 'updateMoney' : 'addMoney';
    try {
      const data = await Api[url]({
        ...values,
        returnDate: moment(values.returnDate).format('YYYY-MM-DD'),
        institutionId,
        contractId,
        contractNumber,
        institutionName,
        id,
      });
      if (data.code === 0) {
        message.success('操作成功');
        addForm.resetFields();
        fetchList();
      }
    } catch (error) {
      console.error('提交出错:', error);
    }
    setIsModalOpen(false);
    setId('');
  };

  const handleDelete = async (id) => {
    const data = await Api.deleteMoney({ id });
    if (data.code === 0) {
      message.success(data.msg || '删除成功', 1, () => {
        fetchList();
      });
    }
  };

 // 3. 修改fetchList方法，保留假数据
const fetchList = async () => {
  try {
    console.log('使用模拟数据，跳过API调用');
    // 直接使用假数据，不调用API
    // setList(data.data || []);
  } catch (error) {
    console.error('获取列表出错:', error);
  }
};
  // 1. 移除不存在的API调用，改用假数据
const fetchDetailList = async (record) => {
  try {
    // 使用假数据替代API调用
    const mockDetailData = [
      {
        id: 1,
        returnDate: '2023-01-10',
        returnMoney: 15000,
        notes: '部分回款',
        updateUserName: '张三',
        updateUserAccount: 'zhangsan',
        updateTime: '2023-01-10 10:30:00'
      },
      {
        id: 2,
        returnDate: '2023-01-20',
        returnMoney: 18000,
        notes: '第二次回款',
        updateUserName: '李四',
        updateUserAccount: 'lisi',
        updateTime: '2023-01-20 14:15:00'
      },
      {
        id: 3,
        returnDate: '2023-01-25',
        returnMoney: 15000,
        notes: '尾款',
        updateUserName: '王五',
        updateUserAccount: 'wangwu',
        updateTime: '2023-01-25 09:45:00'
      }
    ];
    setDetailList(mockDetailData);
  } catch (error) {
    console.error('获取回款明细失败:', error);
    // 即使出错也设置默认数据，确保UI正常显示
    setDetailList([]);
  }
};


  const handleViewDetail = (record) => {
    setCurrentRecord(record);
    fetchDetailList(record);
    setIsDetailModalOpen(true);
  };

  const detailColumns = [
    {
      title: '回款日期',
      dataIndex: 'returnDate',
      render: text => moment(text).format('YYYY-MM-DD')
    },
    {
      title: '回款金额（元）',
      dataIndex: 'returnMoney',
      render: text => text?.toFixed(2) || '0.00'
    },
    {
      title: '回款备注',
      dataIndex: 'notes',
      render: text => text || '-'
    },
    {
      title: '更新人',
      dataIndex: 'updateUserName',
      render: text => text || '-'
    },
    {
      title: '账号',
      dataIndex: 'updateUserAccount',
      render: text => text || '-'
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      render: text => text ? moment(text).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '操作',
      dataIndex: 'id',
      fixed: 'right',
      render: (id, record) => (
        <div className="edit-btn">
          {btns['/union/collection/edit'] && (
            <Button type="link" size="small" onClick={() => handleEdit(record)}>
              编辑
            </Button>
          )}
          {btns['/union/collection/delete'] && (
            <Popconfirm 
              title="确定要删除当前记录吗？" 
              onConfirm={() => handleDelete(id)} 
              okText="确认" 
              cancelText="取消"
            >
              <Button type="link" size="small" style={{ color: '#FF4D4F' }}>删除</Button>
            </Popconfirm>
          )}
        </div>
      ),
    }
  ];

  const mainColumns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      fixed: 'left'
    },
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
      width: 100,
      render: text => moment(text).format('YYYY-MM')
    },
    {
      title: '应结算金额(元)',
      dataIndex: 'shouldSettleAmount',
      key: 'shouldSettleAmount',
      width: 120,
      render: text => text?.toFixed(2) || '0.00'
    },
    {
      title: '已结算金额(元)',
      dataIndex: 'settledAmount',
      key: 'settledAmount',
      width: 120,
      render: text => text?.toFixed(2) || '0.00'
    },
    {
      title: '应回款金额(元)',
      dataIndex: 'shouldReturnAmount',
      key: 'shouldReturnAmount',
      width: 120,
      render: text => text?.toFixed(2) || '0.00'
    },
    {
      title: '已回款金额(元)',
      dataIndex: 'returnedAmount',
      key: 'returnedAmount',
      width: 120,
      render: text => text?.toFixed(2) || '0.00'
    },
    {
      title: '账期(天)',
      dataIndex: 'accountPeriod',
      key: 'accountPeriod',
      width: 100
    },
    {
      title: '对账情况',
      dataIndex: 'reconciliationStatus',
      key: 'reconciliationStatus',
      width: 100,
      render: status => {
        const statusMap = {
          'pending': '未对账',
          'completed': '已对账',
          'discrepancy': '有差异'
        };
        return statusMap[status] || status;
      }
    },
    {
      title: '操作日志',
      dataIndex: 'operationLog',
      key: 'operationLog',
      width: 100,
      render: text => text || '-'
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 150,
      render: text => text || '-'
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 150,
      render: (_, record) => (
        <div className="edit-btn">
          {btns['/union/collection/edit'] && (
            <Button 
              type="link" 
              size="small"
              onClick={() => {
                setId(record.id);
                setIsModalOpen(true);
              }}
            >
              增加回款
            </Button>
          )}
          {btns['/union/collection/delete'] && (
            <Button 
              type="link" 
              size="small"
              onClick={() => handleViewDetail(record)}
            >
              回款明细
            </Button>
          )}
        </div>
      )
    }
  ];

  if (menuType && !['team'].includes(menuType)) {
    mainColumns.pop();
  }

  return (
    <div className="g-page p-product-list">
      <div className="p-product-table">
        <div className="flex-box" style={{ justifyContent: 'space-between' }}>
          <div style={{ display: 'flex' }}>
            <h3 style={{ marginRight: '40px' }}>合同编号: {contractNumber}</h3>
            <h3 style={{ marginRight: '40px' }}>客户编号: {institutionCode}</h3>
            <h3 style={{ marginRight: '40px' }}>客户单位: {institutionName}</h3>
            <h3 style={{ marginRight: '40px' }}>销售负责人: {contactsName}</h3>
            <h3 style={{ marginRight: '40px', color: 'red' }}>累计待结算金额: ￥{totalSettleMoney}元</h3>
            <h3 style={{ marginRight: '40px', color: 'red' }}>累计待汇款金额: ￥{unSettleMoney}元</h3>
          </div>
          <div>
            {(!menuType || menuType === 'team') && btns['/union/collection/add'] && (
              <Button
                type="primary"
                onClick={() => setIsModalOpen(true)}
                style={{ marginRight: 10 }}
              >
                增加回款
              </Button>
            )}
            {(!menuType || menuType === 'team') && btns['/union/collection/export'] && (
              <Button type="primary" onClick={() => setIsModalOpen(true)}>
                下载报表
              </Button>
            )}
          </div>
        </div>
        
        <Table 
          rowKey="id" 
          dataSource={list} 
          columns={mainColumns} 
          pagination={false} 
          scroll={{ x: 'max-content' }} 
        />
      </div>

      {/* 添加/编辑回款模态框 */}
      <Modal
        title={`${id ? '编辑' : '添加'}回款`}
        open={isModalOpen}
        onOk={() => addForm.submit()}
        onCancel={handleCancel}
        className="add-sampling-modal"
        width={800}
         zIndex={2000}
      >
        <Form form={addForm} onFinish={onAddFinish} {...formItemLayout}>
          <Form.Item name="returnDate" label="回款日期" rules={[{ required: true }]}>
            <DatePicker allowClear={false} style={{ width: '100%' }} popupStyle={{ zIndex: 3000 }} />
          </Form.Item>
          <Form.Item name="returnMoney" label="回款金额" rules={[{ required: true }]}>
            <InputNumber min={0} placeholder="请输入" addonAfter="¥" precision={2} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="notes" label="备注">
            <TextArea placeholder="请输入" rows={3} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 回款明细模态框 */}
      <Modal
        title={`回款明细 - ${currentRecord?.month ? moment(currentRecord.month).format('YYYY-MM') : ''}`}
        open={isDetailModalOpen}
        onCancel={handleDetailCancel}
        footer={null}
        width={1200}
      >
        {currentRecord && (
         <Descriptions bordered column={2} style={{ marginBottom: 16 }}>
  <Descriptions.Item 
    label={<span style={{ fontWeight: 'bold' }}>客户单位</span>}
    style={{ fontWeight: 'bold' }}
  >
    {institutionName}
  </Descriptions.Item>
  <Descriptions.Item 
    label={<span style={{ fontWeight: 'bold' }}>合同编号</span>}
    style={{ fontWeight: 'bold' }}
  >
    {contractNumber}
  </Descriptions.Item>
  {/* <Descriptions.Item label="月份">
    {currentRecord?.month ? moment(currentRecord.month).format('YYYY-MM') : '-'}
  </Descriptions.Item>
  <Descriptions.Item label="应回款金额">
    {currentRecord?.shouldReturnAmount?.toFixed(2) || '0.00'}元
  </Descriptions.Item>
  <Descriptions.Item label="已回款金额">
    {currentRecord?.returnedAmount?.toFixed(2) || '0.00'}元
  </Descriptions.Item>
  <Descriptions.Item label="待回款金额">
    {(currentRecord?.shouldReturnAmount - currentRecord?.returnedAmount)?.toFixed(2) || '0.00'}元
  </Descriptions.Item> */}
</Descriptions>
        )}
        
        <Table
          rowKey="id"
          columns={detailColumns}
          dataSource={detailList}
          pagination={false}
          scroll={{ x: 'max-content' }}
        />
      </Modal>
    </div>
  );
};

export default connect(state => ({
  permissionData: state.root.permissionData,
}))(Index);