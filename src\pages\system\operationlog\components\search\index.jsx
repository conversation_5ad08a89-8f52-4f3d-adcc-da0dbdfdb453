import React from 'react';
import { Button, Col, Form, Input, Row, Select, DatePicker } from 'antd';
import { OPERATION_STATUS, OPERATION_TYPE } from '../../_data';

const { RangePicker } = DatePicker;

const Widget = ({ submit, reset, loading, form }) => {
  return (
    <div className="log-search-card">
      {/* <h4>
        最大储存条数为10000条，最大储存天数为90天，达到最大储存条数或天数时，自动清理最早的操作日志。
      </h4>
      <h4 style={{ marginBottom: '24px' }}>当前已经储存4389条，最早的操作日志为65天前。</h4> */}
      <Form form={form} labelAlign="left">
        <Row gutter={24}>
          <Col span={6}>
            <Form.Item label="操作时间" name="time">
              <RangePicker />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作人" name="operator">
              <Input placeholder="请输入" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={6}>
            <Form.Item label="操作模块" name="module">
              <Input placeholder="请输入" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作状态" name="state">
              <Select placeholder="请选择">
                {OPERATION_STATUS.map(item => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="操作类型" name="operationType">
              <Select placeholder="请选择">
                {OPERATION_TYPE.map(item => (
                  <Select.Option key={item} value={item}>
                    {item}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item>
              <Button type="primary" onClick={submit} loading={loading} style={{ marginRight: '12px' }}>
                查询
              </Button>
              <Button onClick={reset} loading={loading}>
                重置
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default Widget;
