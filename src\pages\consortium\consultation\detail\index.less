@import '~antd/lib/style/themes/default.less';

.uPage {
  height: 100%;
  background-color: #fff;
  // margin-top: 53px;
  padding: 24px;
  margin: 24px;
  :global {
    .ant-steps-label-vertical .ant-steps-item-content {
      width: 130px;
    }
    .ant-steps-label-vertical .ant-steps-item-icon {
      margin-left: 49px;
    }
    .antd-pro-layouts-about-layout-frame {
      margin: 0;
    }
    .antd-pro-pages-patients-consultationdetails-index-timeLine {
      padding: 20px;
    }
    .ant-form-item {
      margin-bottom: 0;
    }
    .ant-upload-picture-card-wrapper {
      padding: 10px;
    }
    .ant-steps-item-subtitle {
      white-space: nowrap;
    }
    .ant-steps-item-tail {
      width: 180px;
    }
    .ant-collapse {
      flex: 1;
      border: 0;
    }
    .ant-collapse-content {
      background-color: #f7f7f7;
      border-bottom: 1px solid #d9d9d9 !important;
    }
    .ant-collapse-header {
      padding-left: 0px !important;
      font-weight: 500;
      background-color: #f7f7f7;
      border-bottom: 1px solid #d9d9d9 !important;
    }
    .ant-upload-picture-card-wrapper {
      background-color: #f7f7f7;
    }
  }

  .timeStep {
    display: block;
    white-space: nowrap;
    background-color: #f7f7f7;
    padding: 10px;
    overflow-x: auto;
    :global {
      .ant-steps-item-container {
        width: 175px;
      }
      .ant-steps-item-title {
        font-size: 15px;
      }
    }
  }

  .mBack {
    background-color: #fff;
    position: absolute;
    z-index: 3;
    top: 2px;
    left: 256px;
    display: flex;
    height: 53px;
    align-items: center;
    padding-left: 25px;
    width: 83%;
    .backText {
      color: rgb(155, 158, 155);
      font-weight: bold;
      border-right: 1px solid #cbcbd0;
      padding-right: 20px;
      margin-right: 20px;
      padding-left: 5px;
      cursor: pointer;
    }

    .consultationTitle {
      font-weight: bold;
    }
  }
}

.mTitle {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.detialsModule {
  display: flex;
  .borderView {
    height: 18px;
    width: 4px;
    background-color: #3f969d;
    margin-left: -1px;
  }
  .title {
    background-color: #fff;
    padding: 5px 0;
    margin: 0 10px;
    position: relative;
    font-weight: bold;
    color: #000;
    font-size: 15px;
  }
  .infoModule {
    padding-left: 25px;
    padding-bottom: 10px;
    background-color: rgb(247, 247, 247);
  }
  .leftModule {
    flex: 1;
    margin-right: 20px;
  }
  .rightModule {
    flex: 1;
    width: 50%;
  }
  .contentModule {
    border: 1px solid #e4e4e4;
    border-radius: 4px;
    margin: 25px 0;
  }
  .moduleItemTitle {
    font-size: 17px;
    font-weight: bold;
    color: #000;
    padding-top: 25px;
  }
}

.detailsTitle {
  font-weight: bold;
  padding-top: 10px;
  color: black;
  background-color: #f7f7f7;
  span {
    color: rgb(102, 102, 102);
    font-weight: normal;
  }
}

.applyInfo {
  padding-top: 10px;
  background-color: #f7f7f7;
  display: flex;
  span {
    color: rgb(102, 102, 102);
    font-weight: normal;
    padding-right: 6px;
    word-break: break-word;
  }
  p {
    color: black;
    font-weight: bold;
    white-space: nowrap;
    width: 70px;
    min-width: 70px;
  }
}

.timeLine {
  padding: 15px 0 15px 40px;
  .statusStage {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
}

.consultationRow {
  margin-bottom: 10;
  display: flex;
  align-items: center;
  padding: 5px 0;
  text {
    color: red;
  }
}

.contentLayout {
  display: flex;
  padding-top: 5px;
  padding-left: 25px;
  p {
    color: rgb(102, 102, 102);
    font-weight: normal;
  }
  .consultationReport {
    white-space: nowrap;
  }
  .reportContent {
    padding: 15px 5px;
    word-break: break-all;
  }
}

.textNum {
  margin-top: -23px;
  position: absolute;
  right: 34px;
}

.statusIcon1 {
  margin: 0 6px;
  width: 20px;
  height: 20px;
  background: url(../../../../assets/stepStatus1.png) no-repeat center center / 12px auto;
}

.iconLeft {
  width: 10px;
  height: 10px;
  border-top: 1.5px solid #cbcbd0;
  border-left: 1.5px solid #cbcbd0;
  transform: rotate(315deg);
}

.collapseTitle {
  display: flex;
  justify-content: space-between;
  padding-right: 10px;
  .collapseTime {
    color: #666666;
  }
}

.hoverModule {
  display: flex;
  align-items: center;
}

.hoverModule:hover {
  .iconLeft {
    width: 10px;
    height: 10px;
    border-top: 1.5px solid #3f969d;
    border-left: 1.5px solid #3f969d;
    transform: rotate(315deg);
  }
  .backText {
    color: #3f969d;
    font-weight: bold;
    border-right: 1px solid #cbcbd0;
    padding-right: 20px;
    margin-right: 20px;
    padding-left: 5px;
    cursor: pointer;
  }
}
.downloadPic {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 15px;
}

.downloadAll {
  color: rgb(64, 207, 183);
  font-size: 13px;
  cursor: pointer;
}

.mNull {
  padding: 12px;
  background-color: #f7f7f7;
}

.uButton {
  display: flex;
  flex-direction: column;
  height: 55px;
  justify-content: space-between;
}

.search-box {
  :global {
    .ant-input-search-button {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .anticon-search {
      width: 16px;
    }
  }
}
