import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { Modal, Form, Select, message } from 'antd';

const Index = props => {
  const formItemLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };
  const { dispatch, institutionList = [], institutionContract = [], detail = {}, contractTypeValue, data = {}, onCancel = () => {} } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(true);
  const [itemData, setItemData] = useState(data);
  const [addInstitutions, setAddInstitutions] = useState(detail.institutions || []);

  const changeData = params => {
    setItemData(t => {
      return {
        ...t,
        ...params,
      };
    });
  };
  const getcontractbyinstitution = id => {
    if (contractTypeValue == 'CT7') {
      dispatch({ type: 'contract/getcontractbyinstitution', payload: { id } });
    }
  };
  const submit = () => {
    form.validateFields().then(values => {
      if (contractTypeValue == 'CT7') {
        if (!itemData.editIndex && itemData.editIndex !== 0) {
          if ((detail.institutions || []).filter(item => item.institutionId == values.institutionId).length > 0) {
            message.warning('该合同已有当前授权医院，请重新选择！');
            return false;
          }
        }
        const institutions = JSON.parse(JSON.stringify(detail.institutions || []));
        const institutionsItem = { ...itemData, ...values };
        if (itemData.editIndex || itemData.editIndex === 0) {
          institutions[itemData.editIndex] = institutionsItem;
        } else {
          institutions.push(institutionsItem);
        }
        dispatch({
          type: 'contract/save',
          payload: {
            institutionContract: [],
            detail: { ...detail, institutions },
          },
        });
      } else {
        dispatch({
          type: 'contract/save',
          payload: {
            institutionContract: [],
            detail: { ...detail, institutions: addInstitutions },
          },
        });
      }
      setVisible(false);
      setTimeout(() => {
        onCancel();
      }, 500);
    });
  };

  return (
    <Modal
      className="add-yiyuan-product"
      title="添加授权医院"
      visible={visible}
      destroyOnClose
      maskClosable={false}
      onCancel={() => {
        setVisible(false);
        setTimeout(() => {
          dispatch({
            type: 'contract/save',
            payload: { institutionContract: [] },
          });
          onCancel();
        }, 500);
      }}
      onOk={submit}
    >
      <Form form={form} {...formItemLayout} initialValues={{ ...itemData, institutionsId: (addInstitutions || []).map(v => v.institutionId) }}>
        {contractTypeValue == 'CT7' ? (
          <>
            <Form.Item name="institutionId" label="选择医院" rules={[{ required: true }]}>
              <Select
                placeholder="请选择"
                showSearch
                filterOption={(input, option) => {
                  return option.institutionName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                }}
                options={institutionList}
                fieldNames={{ label: 'institutionName', value: 'id' }}
                onChange={(v, o) => {
                  getcontractbyinstitution(v);
                  changeData({
                    institutionName: o.institutionName,
                    institutionProvinceName: o.institutionProvince,
                    institutionCityName: o.institutionCity,
                    relContractName: '',
                  });
                  form.setFieldsValue({ relContractId: [] });
                }}
              />
            </Form.Item>
            <Form.Item name="relContractId" label="关联合同" rules={[{ required: institutionContract?.length > 0 }]}>
              <Select
                placeholder="请选择"
                showSearch
                filterOption={(input, option) => {
                  return option.contractNumber.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                }}
                mode="multiple"
                options={institutionContract}
                fieldNames={{ label: 'contractNumber', value: 'id' }}
                notFoundContent="无可关联合同，请维护"
                onChange={(v, o) => {
                  console.log(v);
                  changeData({ relContractName: (o || []).map(item => item.contractNumber).join(';') });
                }}
              />
            </Form.Item>
          </>
        ) : (
          <Form.Item name="institutionsId" label="选择医院" rules={[{ required: true }]}>
            <Select
              placeholder="请选择"
              mode="multiple"
              showSearch
              filterOption={(input, option) => {
                return option.institutionName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }}
              options={institutionList}
              fieldNames={{ label: 'institutionName', value: 'id' }}
              onChange={(v, o) => {
                setAddInstitutions(
                  (o || []).map(item => {
                    const existList = (detail.institutions || []).filter(v => v.institutionId == item.id);
                    if (existList.length > 0) {
                      return existList[0];
                    } else {
                      return {
                        institutionId: item.id,
                        institutionName: item.institutionName,
                        institutionProvinceName: item.institutionProvince,
                        institutionCityName: item.institutionCity,
                        relContractName: '',
                      };
                    }
                  }),
                );
              }}
            />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};
export default connect(state => {
  return {
    ...state.contract,
  };
})(Index);
