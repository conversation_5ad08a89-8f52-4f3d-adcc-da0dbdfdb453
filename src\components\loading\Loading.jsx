import React from 'react';
import Notification from 'rc-notification';
import { Spin } from 'antd';

let loadingInstance;
let delayTimeout;

function getLoadingInstance() {
  loadingInstance = loadingInstance || Notification.newInstance({});
  return loadingInstance;
}

function destroy() {
  if (delayTimeout) {
    clearTimeout(delayTimeout);
  }
  document.getElementById('root').style.overflow = ''; // eslint-disable-line
  if (loadingInstance != undefined) {
    loadingInstance.destroy();
    loadingInstance = null;
  }
}

function show() {
  document.getElementById('root').style.overflow = 'hidden'; // eslint-disable-line
  destroy();
  delayTimeout = setTimeout(() => {
    const instance = getLoadingInstance();
    instance.notice({
      duration: 0,
      key: 'merchant-loading',
      content: React.createElement(
        'div',
        {
          style: {
            position: 'fixed',
            zIndex: 999,
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0)',
          },
        },
        React.createElement(Spin, {
          tip: 'Loading',
        }),
      ),
    });
    if (delayTimeout) {
      clearTimeout(delayTimeout);
    }
  }, 300);
}

export default {
  show,
  destroy,
};
