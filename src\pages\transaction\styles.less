@charset "utf-8";
@import '../../resources/styles/mixins';
.transaction-drawer {
  min-width: 900px;
}
.page-transaction {
  display: flex;
  flex: auto;
  flex-direction: column;
  .export-table-link {
    position: absolute;
    top: 24px;
    right: 24px;
    font-size: 14px;
    cursor: pointer;
  }
  .tsc-top-panle {
    padding: 24px 0 32px 0;
    background-color: #fff;
  }
  .tsc-main-panle {
    position: relative;
    display: flex;
    flex: auto;
    flex-direction: column;
    margin: 24px;
    padding: 24px;
    background-color: #fff;
  }
  .space {
    padding: 12px 24px;
  }
  .tsc-split {
    margin: 0 4px;
  }
  .pager-box {
    margin-top: 18px;
  }
  .tsc-main-header {
    margin-bottom: 10px;
    color: @title-color;
    font-size: 14px;
  }
  .tsc-table-item {
    padding: 12px 24px;
    color: @text-color;
    box-shadow: inset 0 -1px 0 0 @border-color;
    .opt-cloum {
      color: @link-color;
    }
    .ant-col-2,
    .ant-col-3,
    .ant-col-4 {
      p,
      span {
        overflow: hidden;
        white-space: normal;
        text-overflow: ellipsis;
      }
    }
  }
  .tsc-table-header {
    padding: 12px 24px;
    background-color: @body-color;
  }
  /* 订单详情 抽屉 */
  .order-info-panle {
    .info-header {
      height: 54px;
      padding: 0 25px;
      font-size: 16px;
      line-height: 54px;
      border-bottom: 1px @border-color solid;
      .close-icon {
        float: right;
        cursor: pointer;
      }
    }
    .btn-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .order-main {
      padding: 0 25px 0;
      .section {
        position: relative;
        padding-bottom: 15px;
        border-bottom: 1px dashed @border-color;
        &.no-border {
          border: none;
        }
        &:after {
          position: absolute;
          bottom: -1px;
          border-top: 1px @border-color dash;
          content: '';
        }
        .info-item-header {
          padding: 21px 0 15px;
          color: @text-color;
          font-size: 14px;
        }
        .row-label {
          display: inline-block;
          min-width: 65px;
          margin: 0 15px 15px 0;
          color: @desc-color;
          font-size: 12px;
        }
        .row-value-label {
          display: inline-block;
          max-width: 120px;
          word-break: break-all;
        }
        .reback-steps {
          margin: 40px 0;
          .anticon-check {
            font-size: 12px;
          }
          .ant-steps-title {
            max-width: 66px;
            color: @desc-color;
            font-size: 14px;
          }
        }
        .order-info-btn {
          margin: 30px 0;
        }
      }
      .last-section {
        border-bottom: none;
      }
    }
    .scancode-box-main {
      .ant-table-thead > tr > th {
        font-weight: normal;
      }
    }
    .m-refund {
      display: flex;
      align-items: center;
      padding: 10px 0 0;
      .refund-label {
        min-width: 6em;
      }
      .refund-input {
        width: 200px;
        margin-right: 10px;
      }
      .refund-tip {
        margin-left: 20px;
        color: @desc-color;
        font-size: 12px;
      }
    }
  }
}
.page-financial {
  display: flex;
  flex: auto;
  flex-direction: column;
  .financial-option {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    .option-panel {
      width: 900px;
    }
  }
  .financial-notice {
    display: flex;
    margin-top: 24px;
    .financial-notice-text {
      flex: 1;
      padding-left: 9px;
    }
  }
  .financial-form {
    margin-top: 48px;
    text-align: center;
  }
  .financial-step {
    margin: 46px 0;
  }
  .financial-list {
    flex: 1;
    margin: 24px;
    padding: 16px 21px;
    background: #fff;
    border-radius: 4px;
  }
}
.order-confirm-close {
  position: absolute;
  top: 5px;
  right: 15px;
  font-size: 12px;
  cursor: pointer;
}

.print-oper-person {
  display: none;
  padding-top: 30px;
}

/* 打印 */
@media print {
  .ant-layout-sider {
    display: none;
  }
  .drawer-body {
    width: 102%;
    min-width: 100%;
  }
  .transaction-drawer {
    .order-info-btn {
      display: none;
    }
  }
  .print-oper-person {
    display: block;
  }
}
/* 交易管理公共头部样式 */
.page-transaction {
  .transaction-form-header {
    padding: 24px 0 32px 0;
    background-color: #fff;
    .col-item {
      padding: 0 21px 0 24px;
    }
    .btn-bar {
      padding: 0 21px 0 24px;
      text-align: right;
    }
  }
  .page-transaction-table {
    position: relative;
    display: flex;
    flex: auto;
    flex-direction: column;
    margin: 24px;
    padding: 0 24px;
    background-color: #fff;
  }
}
/* 对账日志 */
.financialog-info-drawer {
  min-width: 900px;
  .main {
    margin: 0 24px;
    .info {
      border-bottom: 1px dashed @border-color;
    }
    .title {
      margin: 21px 0;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
    }
    .row-label {
      display: inline-block;
      min-width: 65px;
      margin: 0 15px 15px 0;
      color: #919191;
      font-size: 12px;
    }
    .row-value {
      color: @title-color;
    }
    .table-item {
      color: rgba(0, 0, 0, 0.75);
    }
    .ant-timeline-item-content {
      padding-bottom: 12px;
    }
    .tsc-table-header {
      padding: 16px 49px;
    }
    .log {
      .tsc-table-item {
        padding: 0 36px;
        box-shadow: none;
      }
      .log-timeline {
        padding: 12px 25px 18px;
      }
      .ant-timeline-item-last {
        .ant-timeline-item-content {
          min-height: auto !important;
        }
      }
      .ant-timeline-item-content {
        box-shadow: inset 0 -1px 0 0 @border-color;
      }
    }
    .bottom-oper {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}

.refund-box {
  .m-refund {
    display: flex;
    margin-bottom: 20px;
    &:last-child {
      margin: 0;
    }
    &.align-center {
      align-items: center;
    }
  }
  .refund-input {
    display: flex;
    flex: 1;
    align-items: center;
  }
  .require::before {
    color: red;
    content: '*';
  }
}
