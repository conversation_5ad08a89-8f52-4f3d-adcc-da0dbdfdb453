import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  But<PERSON>, 
  Card, 
  Row, 
  Col,
  Calendar,
  Dropdown,
  Space,
  Typography,
  Divider,
  Tabs,
  Radio,
  Form,
  DatePicker,
  Spin,
  message
} from 'antd';
import { SearchOutlined, DownOutlined } from '@ant-design/icons';
import moment from 'moment';
import '@ant-design/compatible/assets/index.css';
import { getDataSummary, getinstitutions, getinstitutionbylist } from './api';
import './custom-calendar.css';

const { Option } = Select;
const { TabPane } = Tabs;
const { Text } = Typography;
const { RangePicker } = DatePicker;

const CustomCalendar = () => {
  const [viewMode, setViewMode] = useState('year');
  const [selectedYear, setSelectedYear] = useState(moment().year());
  const [selectedMonth, setSelectedMonth] = useState(moment().month() + 1);
  const [selectedDate, setSelectedDate] = useState(moment().date());
  const [activeTab, setActiveTab] = useState('all');
  const [selectedProducts, setSelectedProducts] = useState({
    main: [],
    side: []
  });
  const [loading, setLoading] = useState(false);
  const [apiData, setApiData] = useState([]);
  const [branchList] = useState([
    { id: 'all', name: '全部' },
    { id: 'headquarter', name: '总部' },
    { id: 'branch1', name: '新疆' }
  ]);
  const [selectedBranch, setSelectedBranch] = useState('全部');
  const [institutionList, setInstitutionList] = useState([]);
  const [hospitalList, setHospitalList] = useState([]);
  const [selectedInstitution, setSelectedInstitution] = useState('全部');
  const [selectedHospital, setSelectedHospital] = useState('全部');


const calculateTotal = (data) => {
  if (!data) return 0;
  
  try {
  if (activeTab === 'all') {
    return (data.mainProduct || 0) + (data.secondProduct || 0);
  } else if (activeTab === 'main') {
    if (selectedProducts.main.length > 0) {
      return data[productFieldMap[selectedProducts.main[0]]] || 0;
    }
    return data.mainProduct || 0;
  } else if (activeTab === 'side') {
    if (selectedProducts.side.length > 0) {
      return data[productFieldMap[selectedProducts.side[0]]] || 0;
    }
    return data.secondProduct || 0;
    }
  } catch (error) {
    console.error('Error calculating total:', error);
    return 0;
  }
  
  return 0;
};
  // 产品映射
  const productFieldMap = {
    WES: 'wes',
    NIPT: 'nipt',
    NIPT_PLUS: 'niptplus',
    CNV_SEQ: 'cnv',
    CARRIER: 'xdzsc',
    FOLATE: 'ys',
    SMA: 'sma',
    PE: 'zx',
    HEARING: 'el',
    FRAX: 'cxx',
    FRAX_PRENATAL: 'cxxCQ',
    OTHER: 'other'
  };

  const productNameMap = {
    WES: '全外显子组基因检测(WES)',
    NIPT: 'NIPT',
    NIPT_PLUS: 'NIPT-plus',
    CNV_SEQ: 'CNV-seq',
    CARRIER: '单基因遗传病携带者筛查',
    FOLATE: '叶酸代谢能力基因检测',
    SMA: '脊髓性肌肉萎缩症SMA基因检测',
    PE: '子痫前期风险筛查',
    HEARING: '耳聋基因检测',
    FRAX: '脆性X综合征检测',
    FRAX_PRENATAL: '脆性X综合征检测(产前)',
    OTHER: '其他产品(160余种)'
  };

  // 获取有数据的年份列表
const getAvailableYears = () => {
  if (!apiData || apiData.length === 0) return [];
  
  try {
  const years = new Set();
  apiData.forEach(item => {
    // 直接使用date字段，不做格式化处理
      if (item && item.date && typeof item.date === 'string') {
        if (item.date.length === 4) {
          const year = parseInt(item.date);
          if (!isNaN(year)) {
            years.add(year);
          }
        }
    }
  });
  
  return Array.from(years).sort((a, b) => a - b);
  } catch (error) {
    console.error('Error getting available years:', error);
    return [];
  }
};

  // 获取数据
const fetchData = async () => {
  try {
    setLoading(true);
    const params = {
      dateType: viewMode === 'year' ? 'year' : 
                viewMode === 'month' ? 'year' : 'month',
      institutionId: selectedInstitution === '全部' ? undefined : selectedInstitution,
      submitHospitalId: selectedHospital === '全部' ? undefined : selectedHospital,
      dataOwner: selectedBranch === 'ALL' ? undefined : selectedBranch
    };
    
    if (viewMode === 'month') {
      params.date = selectedYear.toString();
    } else if (viewMode === 'day') {
      params.date = `${selectedYear}${selectedMonth.toString().padStart(2, '0')}`;
    }
    
    const response = await getDataSummary(params);
    if (response && response.code === 0) {
      // 保持原始数据格式，不做任何修改
      setApiData(response.data || []);
    } else {
      message.error(response?.msg || '获取数据失败');
      setApiData([]); // 确保在错误情况下清空数据
    }
  } catch (error) {
    message.error('获取数据时出错');
    console.error('Error fetching data:', error);
    setApiData([]); // 确保在异常情况下清空数据
  } finally {
    setLoading(false);
  }
};

  // 获取合作客户列表
  const fetchInstitutions = async () => {
    try {
      const response = await getinstitutions();
      if (response && response.code === 0) {
        setInstitutionList(response.data || []);
      }
    } catch (error) {
      console.error('Error fetching institutions:', error);
    }
  };

  // 获取送检医院列表
  const fetchHospitals = async () => {
    try {
      const response = await getinstitutionbylist();
      if (response && response.code === 0) {
        setHospitalList(response.data || []);
      }
    } catch (error) {
      console.error('Error fetching hospitals:', error);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    fetchData();
    fetchInstitutions();
    fetchHospitals();
  }, [viewMode, selectedYear, selectedMonth]);

  // 处理产品选择变化
  const handleProductChange = (type, checkedValues) => {
    setSelectedProducts({
      main: type === 'main' ? checkedValues : [],
      side: type === 'side' ? checkedValues : []
    });
  };

  // 处理Tab切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    setSelectedProducts({
      main: [],
      side: []
    });
  };

  // 检查数据是否存在
  const hasData = (date) => {
  return apiData.some(d => {
    // 处理年视图数据
    if (d.date.length === 4) {
      return d.date === date.toString();
    }
    // 处理月视图数据
    if (d.date.includes('-') && d.date.length === 7) {
      return d.date === date;
    }
    // 处理日视图数据
    const apiDate = moment(d.date).format('YYYY-MM-DD');
    return apiDate === date;
  });
};

  // YearCard组件
const YearCard = ({ year }) => {
  try {
  // 直接匹配年份字符串
    const item = apiData.find(d => d && d.date === year.toString());
  
  const calculateAllTotal = () => {
    if (!item) return null;
      try {
    return (item.mainProduct || 0) + (item.secondProduct || 0);
      } catch (error) {
        console.error('Error calculating all total:', error);
        return 0;
      }
  };

  const calculateMainTotal = () => {
    if (!item) return null;
      try {
    return item.mainProduct || 0;
      } catch (error) {
        console.error('Error calculating main total:', error);
        return 0;
      }
  };

  const calculateSideTotal = () => {
    if (!item) return null;
      try {
    return item.secondProduct || 0;
      } catch (error) {
        console.error('Error calculating side total:', error);
        return 0;
      }
  };

  const calculateSingleProduct = (product) => {
    if (!item) return null;
      try {
    return item[productFieldMap[product]] || 0;
      } catch (error) {
        console.error('Error calculating single product:', error);
        return 0;
      }
  };

  const allTotal = item ? calculateAllTotal() : null;
  const mainTotal = item ? calculateMainTotal() : null;
  const sideTotal = item ? calculateSideTotal() : null;
  const singleProductTotal = item ? (selectedProducts.main.length > 0 ? 
    calculateSingleProduct(selectedProducts.main[0]) : 
    selectedProducts.side.length > 0 ? 
    calculateSingleProduct(selectedProducts.side[0]) : 
    null) : null;
  
  return (
    <div style={{ 
      position: 'relative',
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      borderRadius: 8
    }}>
      {/* 年份背景 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '8em',
        fontWeight: 'bold',
        color: 'rgba(19, 194, 194, 0.1)',
        zIndex: 5,
        userSelect: 'none',
        pointerEvents: 'none'
      }}>
        {year}
      </div>
      
      {/* 卡片内容 */}
      <div style={{
        position: 'relative',
        zIndex: 2,
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
        background: '#fff'
      }}>
        {item ? (
          <div style={{ 
            fontSize: '3em',
            color: '#2d666f',
            fontWeight: 'bold',
            textAlign: 'center'
          }}>
            {activeTab === 'all' && allTotal}
            {activeTab === 'main' && (
              selectedProducts.main.length > 0 ? singleProductTotal : mainTotal
            )}
            {activeTab === 'side' && (
              selectedProducts.side.length > 0 ? singleProductTotal : sideTotal
            )}
          </div>
        ) : (
          <div style={{ 
            fontSize: '1.5em',
            color: '#999',
            textAlign: 'center'
          }}>
            暂无数据
          </div>
        )}
      </div>
    </div>
  );
  } catch (error) {
    console.error('Error rendering year card:', error);
    return (
      <div style={{ 
        position: 'relative',
        height: '100%',
        width: '100%',
        overflow: 'hidden',
        borderRadius: 8,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff'
      }}>
        <div style={{ 
          fontSize: '1.5em',
          color: '#999',
          textAlign: 'center'
        }}>
          数据错误
        </div>
      </div>
    );
  }
};
  // MonthCard组件
const MonthCard = ({ month }) => {
  // 将月份格式化为"YYYY-MM"格式，与API返回数据一致
  const monthStr = `${selectedYear}-${month.toString().padStart(2, '0')}`;
  const item = apiData.find(d => d && d.date === monthStr);
  
  const calculateTotal = () => {
    if (!item) return null;
    
    try {
    if (activeTab === 'all') {
      return (item.mainProduct || 0) + (item.secondProduct || 0);
    } else if (activeTab === 'main') {
      if (selectedProducts.main.length > 0) {
        return item[productFieldMap[selectedProducts.main[0]]] || 0;
      }
      return item.mainProduct || 0;
    } else if (activeTab === 'side') {
      if (selectedProducts.side.length > 0) {
        return item[productFieldMap[selectedProducts.side[0]]] || 0;
      }
      return item.secondProduct || 0;
      }
    } catch (error) {
      console.error('Error calculating month total:', error);
      return 0;
    }
    
    return 0;
  };

  const total = calculateTotal();

  return (
    <div style={{ 
      position: 'relative',
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      borderRadius: 8
    }}>
      {/* 卡片内容 */}
      <div style={{
        position: 'relative',
        zIndex: 2,
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
        background: '#fff'
      }}>
        {/* 月份标签 - 显示在右上角 */}
        <div style={{
          position: 'absolute',
          top: 8,
          right: 8,
          padding: '4px 8px',
          background: '#f0f0f0',
          borderRadius: 4,
          fontSize: '1em',
          color: '#666'
        }}>
          {month}月
        </div>
        
        {item ? (
          <div style={{ 
            fontSize: '2.5em',
            color: '#2d666f',
            fontWeight: 'bold',
            textAlign: 'center'
          }}>
            {total}
          </div>
        ) : (
          <div style={{ 
            fontSize: '1.5em',
            color: '#999',
            textAlign: 'center'
          }}>
            暂无数据
          </div>
        )}
      </div>
    </div>
  );
};
  // 自定义日期单元格渲染
const dateCellRender = (value) => {
  if (!value || !moment.isMoment(value)) return null;

  try {
  const date = value.date();
  const month = value.month() + 1;
  const year = value.year();
  const dateStr = `${year}-${month.toString().padStart(2, '0')}-${date.toString().padStart(2, '0')}`;
  
  const item = apiData.find(d => {
      if (!d || !d.date) return false;
      try {
    const apiDate = moment(d.date).format('YYYY-MM-DD');
    return apiDate === dateStr;
      } catch (e) {
        return false;
      }
  });

  const calculateTotal = () => {
    if (!item) return null;
    
      try {
    if (activeTab === 'all') {
      return (item.mainProduct || 0) + (item.secondProduct || 0);
    } else if (activeTab === 'main') {
      if (selectedProducts.main.length > 0) {
        return item[productFieldMap[selectedProducts.main[0]]] || 0;
      }
      return item.mainProduct || 0;
    } else if (activeTab === 'side') {
      if (selectedProducts.side.length > 0) {
        return item[productFieldMap[selectedProducts.side[0]]] || 0;
      }
      return item.secondProduct || 0;
        }
      } catch (error) {
        console.error('Error calculating date cell total:', error);
        return 0;
    }
    
    return 0;
  };

  const total = calculateTotal();

  return (
    <div style={{ 
      position: 'relative',
      height: '100%',
      width: '100%',
      padding: '4px'
    }}>
      {item && (
        <div style={{
          position: 'relative',
          zIndex: 5,
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          overflow: 'hidden',
        }}>
          <div style={{ 
            fontSize: '1.6em', // 调整为相对单位
            color: item ? '#2d666f' : '#999',
            fontWeight: 'bold',
            textAlign: 'center',
            wordBreak: 'break-word', // 允许换行
            maxWidth: '100%', // 限制最大宽度
            padding: '2px' // 添加内边距
          }}>
            {total !== null ? total : '无数据'}
          </div>
        </div>
      )}
    </div>
  );
  } catch (error) {
    console.error('Error rendering date cell:', error);
    return null;
  }
};

  // 渲染产品选择下拉框
  const renderProductSelect = () => {
    let displayText = '全部';
    if (activeTab === 'main') {
      displayText = selectedProducts.main.length > 0 ? 
        `主翼产品-${productNameMap[selectedProducts.main[0]]}` : 
        '主翼产品';
    } else if (activeTab === 'side') {
      displayText = selectedProducts.side.length > 0 ? 
        `侧翼产品-${productNameMap[selectedProducts.side[0]]}` : 
        '侧翼产品';
    }

    return (
      <Dropdown
        overlay={
          <Card 
            style={{ 
              width: 300,
              borderRadius: 8,
              boxShadow: '0 3px 6px rgba(0,0,0,0.1)'
            }}
            bodyStyle={{ padding: 0 }}
          >
            <Tabs 
              activeKey={activeTab} 
              onChange={handleTabChange}
              tabBarStyle={{ margin: 0 }}
              tabBarGutter={16}
            >
              <TabPane 
                tab={<span style={{ color: activeTab === 'all' ? '#1890ff' : undefined }}>全部</span>} 
                key="all"
              >
                <div style={{ maxHeight: 300, overflowY: 'auto', padding: '16px' }}>
                  <Radio.Group
                    value="all"
                    style={{ width: '100%' }}
                  >
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Radio value="all" checked >全部</Radio>
                    </Space>
                  </Radio.Group>
                </div>
              </TabPane>
              <TabPane 
                tab={<span style={{ color: activeTab === 'main' ? '#1890ff' : undefined }}>主翼产品</span>} 
                key="main"
              >
                <div style={{ maxHeight: 300, overflowY: 'auto', padding: '16px' }}>
                  <Radio.Group
                    value={selectedProducts.main.length > 0 ? selectedProducts.main[0] : null}
                    onChange={(e) => {
                      setSelectedProducts({
                        main: [e.target.value],
                        side: []
                      });
                    }}
                    style={{ width: '100%' }}
                  >
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Radio value="NIPT" >{productNameMap.NIPT}</Radio>
                      <Radio value="NIPT_PLUS" >{productNameMap.NIPT_PLUS}</Radio>
                      <Radio value="CNV_SEQ" >{productNameMap.CNV_SEQ}</Radio>
                    </Space>
                  </Radio.Group>
                </div>
              </TabPane>
              <TabPane 
                tab={<span style={{ color: activeTab === 'side' ? '#1890ff' : undefined }}>侧翼产品</span>} 
                key="side"
              >
                <div style={{ maxHeight: 300, overflowY: 'auto', padding: '16px' }}>
                  <Radio.Group
                    value={selectedProducts.side.length > 0 ? selectedProducts.side[0] : null}
                    onChange={(e) => {
                      setSelectedProducts({
                        main: [],
                        side: [e.target.value]
                      });
                    }}
                    style={{ width: '100%' }}
                  >
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Radio value="WES" >{productNameMap.WES}</Radio>
                      <Radio value="CARRIER" >{productNameMap.CARRIER}</Radio>
                      <Radio value="FOLATE" >{productNameMap.FOLATE}</Radio>
                      <Radio value="SMA" >{productNameMap.SMA}</Radio>
                      <Radio value="PE" >{productNameMap.PE}</Radio>
                      <Radio value="HEARING">{productNameMap.HEARING}</Radio>
                      <Radio value="FRAX" >{productNameMap.FRAX}</Radio>
                      <Radio value="FRAX_PRENATAL" >{productNameMap.FRAX_PRENATAL}</Radio>
                      <Radio value="OTHER" >{productNameMap.OTHER}</Radio>
                    </Space>
                  </Radio.Group>
                </div>
              </TabPane>
            </Tabs>
          </Card>
        }
        trigger={['click']}
      >
        <Button 
          style={{ 
            width: 296,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}
        >
          <span>{displayText}</span>
          <DownOutlined style={{ fontSize: 12 }} />
        </Button>
      </Dropdown>
    );
  };

  // 处理年月选择
  const handleDateChange = (date, dateString) => {
    if (date) {
      setSelectedYear(date.year());
      setSelectedMonth(date.month() + 1);
    }
  };

  // 处理查询按钮点击
  const handleSearch = () => {
    fetchData();
  };

  return (
    <div style={{ padding: 0, background: '#f5f5f5', minHeight: '80vh' }}>
      {/* 操作栏 */}
      <div style={{ 
        background: '#fff', 
        padding: 20, 
        borderRadius: 0,
        boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
      }}>
        <Form layout="inline">
          <Row gutter={4} align="middle" style={{ width: '100%' }}>
            {/* 时间范围选择 */}
            <Col>
              <Form.Item label="时间范围">
                <Space>
                  <Button 
                    type={viewMode === 'year' ? 'primary' : 'default'}
                    onClick={() => setViewMode('year')}
                    style={{ 
                      background: viewMode === 'year' ? '#3f969d' : undefined,
                      borderColor: viewMode === 'year' ? '#3f969d' : undefined
                    }}
                  >
                    年
                  </Button>
                  <Button 
                    type={viewMode === 'month' ? 'primary' : 'default'}
                    onClick={() => setViewMode('month')}
                    style={{ 
                      background: viewMode === 'month' ? '#3f969d' : undefined,
                      borderColor: viewMode === 'month' ? '#3f969d' : undefined
                    }}
                  >
                    月
                  </Button>
                  <Button 
                    type={viewMode === 'day' ? 'primary' : 'default'}
                    onClick={() => setViewMode('day')}
                    style={{ 
                      background: viewMode === 'day' ? '#3f969d' : undefined,
                      borderColor: viewMode === 'day' ? '#3f969d' : undefined
                    }}
                  >
                    日
                  </Button>
                </Space>
              </Form.Item>
            </Col>

            {/* 采样日期选择 */}
            {viewMode === 'month' && (
              <Col>
                <Form.Item label="采样年份">
                  <DatePicker
                    picker="year"
                    value={moment(`${selectedYear}`, 'YYYY')}
                    onChange={(date) => {
                      if (date) {
                        setSelectedYear(date.year());
                      }
                    }}
                    style={{ width: 180 }}
                    allowClear={false}
                  />
                </Form.Item>
              </Col>
            )}

            {viewMode === 'day' && (
              <Col>
                <Form.Item label="采样日期">
                  <DatePicker
                    picker="month"
                    value={moment(`${selectedYear}-${selectedMonth}`, 'YYYY-MM')}
                    onChange={handleDateChange}
                    style={{ width: 180 }}
                    allowClear={false}
                  />
                </Form.Item>
              </Col>
            )}

            {/* 合作客户选择 */}
            <Col>
              <Form.Item label="合作客户">
                <Select 
                  value={selectedInstitution}
                  onChange={value => setSelectedInstitution(value)}
                  style={{ width: 180 }}
                  showSearch
                  filterOption={(input, option) => {
                    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  <Option value="全部">全部</Option>
                  {institutionList.map(v => (
                    <Option value={v.id} key={v.id}>
                      {v.institutionName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* 送检医院选择 */}
            <Col>
              <Form.Item label="送检医院">
                <Select 
                  value={selectedHospital}
                  onChange={value => setSelectedHospital(value)}
                  style={{ width: 180 }}
                  showSearch
                  filterOption={(input, option) => {
                    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  <Option value="全部">全部</Option>
                  {hospitalList.map(v => (
                    <Option value={v.id} key={v.id}>
                      {v.institutionName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* 主/侧翼产品选择 */}
            <Col>
              <Form.Item label="主/侧翼产品">
                {renderProductSelect()}
              </Form.Item>
            </Col>

            <Col>
              <Form.Item label="所属分中心">
                <Select 
                  value={selectedBranch}
                  onChange={value => setSelectedBranch(value)}
                  style={{ width: 120 }}
                >
                  <Option value="ALL">全部</Option>
                  <Option value="XJ">新疆分中心</Option>
                  <Option value="PARENT">总部</Option>
                </Select>
              </Form.Item>
            </Col>

            {/* 查询按钮 */}
            <Col>
              <Form.Item>
                <Button 
                  type="primary" 
                  icon={<SearchOutlined />}
                  onClick={handleSearch}
                >
                  查询
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      
      {/* 内容展示区 */}
      <div style={{ 
        background: '#fff', 
        padding: 20, 
        marginTop: 10,
        borderRadius: 8,
        boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
        height: 'calc(100vh - 180px)'
      }}>
        {loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
            <Spin size="large" />
          </div>
        ) : viewMode === 'year' ? (
          <Row gutter={16}>
            {getAvailableYears().map(year => (
              <Col key={year} span={6}>
                <Card 
                  style={{ 
                    height: 300,
                    padding: 0,
                    border: '1px solid #d9d9d9'
                  }}
                  bodyStyle={{ padding: 0, height: '100%' }}
                >
                  <YearCard year={year} />
                </Card>
              </Col>
            ))}
          </Row>
        ) : viewMode === 'month' ? (
        <div>
    <div style={{ marginBottom: 16, textAlign: 'center', fontSize: '1.2em' }}>
      {selectedYear}年 月度数据
    </div>
      <Row gutter={16}>
      {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(month => {
        const monthStr = `${selectedYear}-${month.toString().padStart(2, '0')}`;
        const monthData = apiData.find(d => d.date === monthStr);
        const hasData = !!monthData;
        
        return (
          <Col key={month} span={6} style={{ marginBottom: 16 }}>
            <Card 
              style={{ 
                height: 200,
                padding: 0,
                border: '1px solid #d9d9d9',
                opacity: hasData ? 1 : 0.6
              }}
              bodyStyle={{ padding: 0, height: '100%' }}
            >
              <MonthCard month={month} />
            </Card>
          </Col>
        );
      })}
    </Row>
  </div>
        ) : (
          <div style={{ marginTop: 20 }}>
            <Calendar
              value={moment(`${selectedYear}-${selectedMonth}-${selectedDate}`, 'YYYY-MM-DD')}
              onPanelChange={(value) => {
                setSelectedYear(value.year());
                setSelectedMonth(value.month() + 1);
              }}
              onSelect={(value) => setSelectedDate(value.date())}
              headerRender={({ value, onChange }) => (
                <div style={{ padding: 10, textAlign: 'center' }}>
                  <Button type="text" onClick={() => onChange(value.clone().subtract(1, 'month'))}>
                    {'<'}
                  </Button>
                  <span style={{ margin: '0 20px', fontSize: 18 }}>
                    {value.format('YYYY年MM月')}
                  </span>
                  <Button type="text" onClick={() => onChange(value.clone().add(1, 'month'))}>
                    {'>'}
                  </Button>
                </div>
              )}
              dateCellRender={dateCellRender}
              style={{
                border: '1px solid #d9d9d9',
                borderRadius: '8px',
                height: '100%'
              }}
              fullscreen={false}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomCalendar;