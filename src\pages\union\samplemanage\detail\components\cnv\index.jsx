/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Divider, InputNumber } from 'antd';
import '../../index.less';

const { TextArea } = Input;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { cnvTemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, cnvTemplate: { ...cnvTemplate, ...payload } },
      },
    });
  };

  const changeTableData = (val, ind, key) => {
    const { field37 = [] } = cnvTemplate;
    field37[ind][key] = val;
    changeData({ field37 });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="产前/流产物检测" key="1">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item label="孕次">
                <Input placeholder="请输入" value={cnvTemplate.field1} onChange={e => changeData({ field1: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="产次">
                <Input placeholder="请输入" value={cnvTemplate.field2} onChange={e => changeData({ field2: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="试管婴儿">
                <Radio.Group value={cnvTemplate.field11} onChange={e => changeData({ field11: e.target.value })}>
                  <Radio value="否">否</Radio>
                  <Radio value="是">是</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="孕周（周、天）">
                <div className="flex-box">
                  <Input placeholder="请输入周" value={cnvTemplate.field5} onChange={e => changeData({ field5: e.target.value })} />
                  <Input placeholder="请输入天" value={cnvTemplate.field6} onChange={e => changeData({ field6: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="末次月经">
                <Input placeholder="请输入" value={cnvTemplate.field7} onChange={e => changeData({ field7: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="月经周期（天）">
                <Input placeholder="请输入" value={cnvTemplate.field8} onChange={e => changeData({ field8: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="不良孕史">
                <div className="flex-box">
                  <Radio.Group value={cnvTemplate.field3} onChange={e => changeData({ field3: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  {cnvTemplate.field3 == '有' ? (
                    <>
                      <div className="flex-box">
                        <div className="flex-shrink">描述</div>
                        <Input placeholder="请输入" value={cnvTemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
                      </div>
                      <div className="flex-box">
                        <div className="flex-shrink">不良孕史次数</div>
                        <InputNumber placeholder="请输入" style={{ width: '100%' }} min={1} precision={0} value={cnvTemplate.field59} onChange={value => changeData({ field59: value })} />
                      </div>
                    </>
                  ) : null}
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="妊娠情况">
                <div className="flex-box">
                  <Radio.Group value={cnvTemplate.field9} onChange={e => changeData({ field9: e.target.value })}>
                    <Radio value="单胎">单胎</Radio>
                    <Radio value="双胎">双胎</Radio>
                    <Radio value="其他">其他</Radio>
                  </Radio.Group>
                  {cnvTemplate.field9 == '其他' ? (
                    <div className="flex-box">
                      <Input placeholder="请输入" value={cnvTemplate.field10} onChange={e => changeData({ field10: e.target.value })} />
                    </div>
                  ) : null}
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="超声检查">
                <Checkbox.Group value={cnvTemplate.field12 ? cnvTemplate.field12.split(',') : []} onChange={v => changeData({ field12: v.join(',') })}>
                  <Checkbox value="未见异常">未见异常</Checkbox>
                  <div className="flex-box">
                    <Checkbox value="胎儿结构异常" className="flex-shrink">
                      胎儿结构异常
                    </Checkbox>
                    <div className="flex-box">
                      <Input placeholder="请输入" value={cnvTemplate.field13} onChange={e => changeData({ field13: e.target.value })} />
                    </div>
                  </div>
                  <div className="flex-box">
                    <Checkbox value="软指标高风险" className="flex-shrink">
                      软指标高风险
                    </Checkbox>
                    <div className="flex-box">
                      <Input placeholder="请输入" value={cnvTemplate.field14} onChange={e => changeData({ field14: e.target.value })} />
                    </div>
                  </div>
                  <div className="flex-box">
                    <Checkbox value="介入性手术治疗" className="flex-shrink">
                      介入性手术治疗
                    </Checkbox>
                    <div className="flex-box">
                      <Input placeholder="请输入" value={cnvTemplate.field15} onChange={e => changeData({ field15: e.target.value })} />
                    </div>
                  </div>
                  <div className="flex-box">
                    <Checkbox value="其他" className="flex-shrink">
                      其他
                    </Checkbox>
                    <div className="flex-box">
                      <Input placeholder="请输入" value={cnvTemplate.field16} onChange={e => changeData({ field16: e.target.value })} />
                    </div>
                  </div>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="血清筛查">
                <Radio.Group value={cnvTemplate.field17} onChange={e => changeData({ field17: e.target.value })}>
                  <Radio value="未做">未做</Radio>
                  <Radio value="已做">已做</Radio>
                </Radio.Group>
                风险值：21-三体：1/
                <Input style={{ width: '77px', margin: '0 16px 0 8px' }} placeholder="请输入" value={cnvTemplate.field18} onChange={e => changeData({ field18: e.target.value })} />
                18-三体：1/
                <Input style={{ width: '77px', margin: '0 16px 0 8px' }} placeholder="请输入" value={cnvTemplate.field19} onChange={e => changeData({ field19: e.target.value })} />
                NTD
                <Input style={{ width: '77px', margin: '0 0 0 8px' }} placeholder="请输入" value={cnvTemplate.field20} onChange={e => changeData({ field20: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="产前诊断">
                <div className="flex-box">
                  <Radio.Group value={cnvTemplate.field21} onChange={e => changeData({ field21: e.target.value })}>
                    <Radio value="未做">未做</Radio>
                    <Radio value="已做">已做</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">结果</div>
                    <Input placeholder="请输入" value={cnvTemplate.field22} onChange={e => changeData({ field22: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="无创DNA产前检测">
                <div className="flex-box">
                  <Radio.Group value={cnvTemplate.field23} onChange={e => changeData({ field23: e.target.value })}>
                    <Radio value="未做">未做</Radio>
                    <Radio value="已做">已做</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">结果</div>
                    <Input placeholder="请输入" value={cnvTemplate.field24} onChange={e => changeData({ field24: e.target.value })} />
                  </div>
                  <div className="flex-box">
                    <div className="flex-shrink">检测编号</div>
                    <Input placeholder="请输入" value={cnvTemplate.field25} onChange={e => changeData({ field25: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="夫妻双方染色体核型/基因/细胞学检查">
                <div className="flex-box">
                  <Radio.Group value={cnvTemplate.field26} onChange={e => changeData({ field26: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">描述</div>
                    <Input placeholder="请输入" value={cnvTemplate.field27} onChange={e => changeData({ field27: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="家族遗传病">
                <Radio.Group value={cnvTemplate.field28} onChange={e => changeData({ field28: e.target.value })}>
                  <Radio value="无">无</Radio>
                  <Radio value="有">有</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="患者检测" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item>
                <Checkbox.Group value={cnvTemplate.field29 ? cnvTemplate.field29.split(',') : []} onChange={v => changeData({ field29: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="智力障碍">智力障碍</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="特殊面容">特殊面容</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="先天性心脏病">先天性心脏病</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="发育迟缓">发育迟缓</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="肌无力">肌无力</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="自闭症">自闭症</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="肢体畸形">肢体畸形</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="脏器畸形">脏器畸形</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="生殖系统畸形">生殖系统畸形</Checkbox>
                    </Col>
                    <Col span={18}>
                      <div className="flex-box">
                        <Checkbox value="其他">其他</Checkbox>
                        <div className="flex-box">
                          <Input placeholder="请输入" value={cnvTemplate.field30} onChange={e => changeData({ field30: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="其他诊断">
                <div className="flex-box">
                  <Radio.Group value={cnvTemplate.field31} onChange={e => changeData({ field31: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">诊断项目</div>
                    <Input placeholder="请输入" value={cnvTemplate.field32} onChange={e => changeData({ field32: e.target.value })} />
                  </div>
                  <div className="flex-box">
                    <div className="flex-shrink">诊断结果</div>
                    <Input placeholder="请输入" value={cnvTemplate.field33} onChange={e => changeData({ field33: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="家族遗传病">
                <div className="flex-box">
                  <Radio.Group value={cnvTemplate.field34} onChange={e => changeData({ field34: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">描述</div>
                    <Input placeholder="请输入" value={cnvTemplate.field35} onChange={e => changeData({ field35: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="亲属（包括本人）做过此检测">
                <div className="flex-box">
                  <Radio.Group value={cnvTemplate.field36} onChange={e => changeData({ field36: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24} style={{ marginBottom: '24px' }}>
              <Table
                dataSource={cnvTemplate.field37 || []}
                columns={[
                  {
                    title: '亲属检测编号',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field1} onChange={e => changeTableData(e.target.value, index, 'field1')} />;
                    },
                  },
                  {
                    title: '亲属姓名',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field2} onChange={e => changeTableData(e.target.value, index, 'field2')} />;
                    },
                  },
                  {
                    title: '亲属关系',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field3} onChange={e => changeTableData(e.target.value, index, 'field3')} />;
                    },
                  },
                  {
                    title: '亲属检测结果',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field4} onChange={e => changeTableData(e.target.value, index, 'field4')} />;
                    },
                  },
                  {
                    title: '操作',
                    width: 60,
                    render: (cur, col, index) => (
                      <Button type="link" onClick={() => changeData({ field37: cnvTemplate.field37.filter((item, ind) => index != ind) })}>
                        删除
                      </Button>
                    ),
                  },
                ]}
                pagination={false}
              />
              <Button type="dashed" block onClick={() => changeData({ field37: [...(cnvTemplate.field37 || []), {}] })}>
                +添加亲属
              </Button>
            </Col>
            <Col span={24}>
              <Form.Item label="主诉">
                <TextArea placeholder="请输入" key={detail.id} defaultValue={cnvTemplate.field38} onChange={e => changeData({ field38: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="其他" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="检测类型一">
                <Checkbox.Group value={cnvTemplate.field39 ? cnvTemplate.field39.split(',') : []} onChange={v => changeData({ field39: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="产前">产前</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="流产物">流产物</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="送检材料">
                <Checkbox.Group value={cnvTemplate.field40 ? cnvTemplate.field40.split(',') : []} onChange={v => changeData({ field40: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={8}>
                      <Checkbox value="羊水">羊水</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="脐带血">脐带血</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="绒毛">绒毛</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="皮肤">皮肤</Checkbox>
                    </Col>
                    <Col span={16}>
                      <div className="flex-box">
                        <Checkbox value="其他">其他</Checkbox>
                        <div className="flex-box">
                          <Input placeholder="请输入" value={cnvTemplate.field41} onChange={e => changeData({ field41: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="样品总量">
                <Input placeholder="请输入" value={cnvTemplate.field42} onChange={e => changeData({ field42: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="样品管数">
                <Input placeholder="请输入" value={cnvTemplate.field43} onChange={e => changeData({ field43: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="医院编号">
                <Input placeholder="请输入" value={cnvTemplate.field44} onChange={e => changeData({ field44: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="特殊情况备注">
                <Input placeholder="请输入" value={cnvTemplate.field45} onChange={e => changeData({ field45: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="检测辅助样品">
                <Checkbox.Group value={cnvTemplate.field46 ? cnvTemplate.field46.split(',') : []} onChange={v => changeData({ field46: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={8}>
                      <Checkbox value="孕妇外周血">孕妇外周血</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="胎儿父亲外周血">胎儿父亲外周血</Checkbox>
                    </Col>
                    <Col span={8}>
                      <div className="flex-box">
                        <Checkbox value="其他">其他</Checkbox>
                        <div className="flex-box">
                          <Input placeholder="请输入" value={cnvTemplate.field47} onChange={e => changeData({ field47: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="样品总量">
                <Input placeholder="请输入" value={cnvTemplate.field48} onChange={e => changeData({ field48: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="样品管数">
                <Input placeholder="请输入" value={cnvTemplate.field49} onChange={e => changeData({ field49: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="医院编号">
                <Input placeholder="请输入" value={cnvTemplate.field50} onChange={e => changeData({ field50: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="特殊情况备注">
                <Input placeholder="请输入" value={cnvTemplate.field51} onChange={e => changeData({ field51: e.target.value })} />
              </Form.Item>
            </Col>
            <Divider />
            <Col span={24}>
              <Form.Item label="检测类型二">
                <Checkbox.Group value={cnvTemplate.field52 ? cnvTemplate.field52.split(',') : []} onChange={v => changeData({ field52: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="患者">患者</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="送检材料">
                <Checkbox.Group value={cnvTemplate.field53 ? cnvTemplate.field53.split(',') : []} onChange={v => changeData({ field53: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={8}>
                      <Checkbox value="外周血">外周血</Checkbox>
                    </Col>
                    <Col span={16}>
                      <div className="flex-box">
                        <Checkbox value="其他">其他</Checkbox>
                        <div className="flex-box">
                          <Input placeholder="请输入" value={cnvTemplate.field54} onChange={e => changeData({ field54: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="样品总量">
                <Input placeholder="请输入" value={cnvTemplate.field55} onChange={e => changeData({ field55: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="样品管数">
                <Input placeholder="请输入" value={cnvTemplate.field56} onChange={e => changeData({ field56: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="医院编号">
                <Input placeholder="请输入" value={cnvTemplate.field57} onChange={e => changeData({ field57: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="特殊情况备注">
                <Input placeholder="请输入" value={cnvTemplate.field58} onChange={e => changeData({ field58: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
