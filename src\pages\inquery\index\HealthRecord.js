import React, { Component, Fragment } from 'react';
import { Card, Select, Row, Col, Tabs, Steps, Input } from 'antd';

import styles from './right.less';

const { TabPane } = Tabs;
const { Step } = Steps;
const { TextArea } = Input;

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {};
  }

  componentDidMount() {}

  handleEditorChange = item => {
    const { cb } = this.props;
    cb(item);
  };

  render() {
    const { manInfo = {}, femaleInfo = {} } = this.props;
    const { marriageRecord: manMarriageRecord, basicInfo: manBasicInfo } = manInfo || {};
    const { marriageRecord: femaleMarriageRecord, basicInfo: femaleBasicInfo } = femaleInfo || {};

    return (
      <div className={styles.healthRecord}>
        <div>
          <div className={styles.title}>病情描述</div>
          <div className={styles.info}>
            {/* <div className={styles.infoTitle}>名称：</div> */}
            <div>{manInfo.description || '未填写'}</div>
          </div>
          <div className={styles.detail}>
            <div className={styles.title}>女方健康信息</div>
            <div className={styles.basic}>
              <div className={styles.basicTitle}>基本信息</div>
              <Row>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>身高：</div>
                  <div>{femaleBasicInfo && femaleBasicInfo.height}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>体重：</div>
                  <div>{femaleBasicInfo && femaleBasicInfo.weight}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>职业：</div>
                  <div>{femaleBasicInfo && femaleBasicInfo.profession}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>学历：</div>
                  <div>{femaleBasicInfo && femaleBasicInfo.education}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>民族：</div>
                  <div>{femaleBasicInfo && femaleBasicInfo.nation}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>居住地址：</div>
                  <div>{femaleBasicInfo && femaleBasicInfo.address}</div>
                </Col>
              </Row>
            </div>
            <div className={styles.basic}>
              <div className={styles.basicTitle}>婚育史</div>
              <Row>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>婚姻情况：</div>
                  <div>{femaleMarriageRecord && femaleMarriageRecord.hyzk}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>婚龄：</div>
                  <div>{femaleMarriageRecord && femaleMarriageRecord.hl}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>性生活：</div>
                  <div>{femaleMarriageRecord && femaleMarriageRecord.xsh}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>怀孕次数：</div>
                  <div>{femaleMarriageRecord && femaleMarriageRecord.hyjc}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>生育次数：</div>
                  <div>{femaleMarriageRecord && femaleMarriageRecord.syjc}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>早产次数：</div>
                  <div>{femaleMarriageRecord && femaleMarriageRecord.zcjc}</div>
                </Col>
              </Row>
            </div>
            <div className={styles.basic}>
              <div className={styles.basicTitle}>妇科问题</div>
              <Row>
                <Col span={24} className={styles.colInfo}>
                  <div className={styles.colLeft}>问题：</div>
                  <div>{femaleInfo.questionStatus == '0' ? '无' : femaleInfo.questionStatus == '2' ? '不清楚' : femaleInfo.questionStatus == '1' ? femaleInfo.F_disease_type : '未填写'}</div>
                </Col>
              </Row>
            </div>
            <div className={styles.basic}>
              <div className={styles.basicTitle}>既往病史</div>
              <Row>
                <Col span={24} className={styles.colInfo}>
                  <div className={styles.colLeft}>名称：</div>
                  <div>
                    {femaleInfo.anamnesis_record}&nbsp;&nbsp;&nbsp;&nbsp;
                    {femaleInfo.anamnesisRecord}
                  </div>
                </Col>
              </Row>
            </div>
          </div>
          <div className={styles.detail}>
            <div className={styles.title}>男方健康信息</div>
            <div className={styles.basic}>
              <div className={styles.basicTitle}>基本信息</div>
              <Row>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>身高：</div>
                  <div>{manBasicInfo && manBasicInfo.height}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>体重：</div>
                  <div>{manBasicInfo && manBasicInfo.weight}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>职业：</div>
                  <div>{manBasicInfo && manBasicInfo.profession}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>学历：</div>
                  <div>{manBasicInfo && manBasicInfo.education}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>民族：</div>
                  <div>{manBasicInfo && manBasicInfo.nation}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>居住地址：</div>
                  <div>{manBasicInfo && manBasicInfo.address}</div>
                </Col>
              </Row>
            </div>
            <div className={styles.basic}>
              <div className={styles.basicTitle}>婚育史</div>
              <Row>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>婚姻情况：</div>
                  <div>{manMarriageRecord && manMarriageRecord.hyzk}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>婚龄：</div>
                  <div>{manMarriageRecord && manMarriageRecord.hl}</div>
                </Col>
                <Col span={8} className={styles.colInfo}>
                  <div className={styles.colLeft}>生育史：</div>
                  <div>{manMarriageRecord && manMarriageRecord.sys}</div>
                </Col>
              </Row>
            </div>
            <div className={styles.basic}>
              <div className={styles.basicTitle}>小蝌蚪问题</div>
              <Row>
                <Col span={24} className={styles.colInfo}>
                  <div className={styles.colLeft}>问题：</div>
                  <div>{manInfo.questionStatus == '0' ? '无' : manInfo.questionStatus == '2' ? '不清楚' : manInfo.questionStatus == '1' ? manInfo.M_disease_type : '未填写'}</div>
                </Col>
              </Row>
            </div>
            <div className={styles.basic}>
              <div className={styles.basicTitle}>既往病史</div>
              <Row>
                <Col span={24} className={styles.colInfo}>
                  <div className={styles.colLeft}>名称：</div>
                  <div>
                    {manInfo.anamnesis_record}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    {manInfo.anamnesisRecord}
                  </div>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default Index;
