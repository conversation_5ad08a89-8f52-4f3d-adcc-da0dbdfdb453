/* eslint-disable react/no-deprecated */
/* eslint-disable max-len */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
import React from 'react';
import { connect } from 'dva';
import { history as hashHistory } from 'umi';
import { Button, Radio, Input, Row, Col, Checkbox, Select, Upload, message, Modal, Cascader, DatePicker } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';
import Phone from '../../components/Phone';
import * as utils from '../../../../utils/utils';
import * as CONSTANT from '../../../../config/constant/constant';
import { provinceData, QUESTIONS_TYPE, QUESTIONS_TYPE_ARR } from '../province';
import '../create/style.less';

const RadioGroup = Radio.Group;
const { Option } = Select;
class CreateQuestion extends React.Component {
  constructor(props) {
    super(props);
    this.textAreaRef = React.createRef();
    this.state = {
      phoneInfo: {},
      questionType: '9',
      addQuestionPanle: false,
      selectedOptions: {}, // 存储选中的选项 {titleId: {optionId: true}}
      multiFillContent: '', // 为多空填值添加状态
      questionList: [
        {
          questionsTitle: '',
          required: '1',
          sorted: '',
          // questionsType: '9',
          questionsType: QUESTIONS_TYPE.SHOUJI_HAOMA.value,
          jsonToOption: [
            {
              optionContent: '',
              imageUrl: '',
            },
          ],
        },
      ],
      multiFillContent: '',
    };
    this.textAreaRef = React.createRef();
    this.questionOptionChange = (index, e) => {
      const { value } = e.target;
      const { questionList } = this.state;
      questionList[index].questionsType = value;
      if (
        [
          QUESTIONS_TYPE.DUOHANG_TIANKONG.value,
          QUESTIONS_TYPE.DAFEN_TI.value,
          QUESTIONS_TYPE.QIAN_MING.value,
          QUESTIONS_TYPE.SHOUJI_HAOMA.value,
          QUESTIONS_TYPE.SHENFEN_ZHENG.value,
          QUESTIONS_TYPE.DI_ZHI.value,
          QUESTIONS_TYPE.DANHANG_TIANKONG.value,
          QUESTIONS_TYPE.RIQI_XUANZE.value,
          QUESTIONS_TYPE.XIA_QU.value,
          QUESTIONS_TYPE.MULTI_FILL_BLANK.value, // 使用QUESTIONS_TYPE.MULTI_FILL_BLANK.value
        ].includes(value)
      ) {
        // 填空或者打分 没有questionOptionChange
        questionList[index].jsonToOption = [{}];
      }

      // 选择新冠要自动补全标题
      if (value === QUESTIONS_TYPE.XIN_GUAN.value) {
        questionList[index].questionsTitle = '点击查询新冠疫情风险地区名单';
      }

      // 选择处理意见要自动补全标题
      if (value === QUESTIONS_TYPE.CHULI_YIJIAN.value) {
        questionList[index].questionsTitle = '处理意见(添加后患者不可见，用于医院对问卷详情的备注)';
      }

      // 如果没有选择新冠或者处理意见questionsTitle则为空
      if (![QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(value)) {
        questionList[index].questionsTitle = '';
      }

      // 选择单选题
      if (value === QUESTIONS_TYPE.DAN_XUAN.value) {
        questionList[index].jsonToOption = [
          {
            extInputList: [],
            optionContent: '',
            imageUrl: '',
          },
        ];
      }
      
      // 选择多选题
      if (value === QUESTIONS_TYPE.DUO_XUAN.value) {
        questionList[index].jsonToOption = [
          {
            extInputList: [],
            optionContent: '',
            imageUrl: '',
          },
        ];
      }

      // 1. state中增加multiFillContent和textAreaRef
      if (value === QUESTIONS_TYPE.MULTI_FILL_BLANK.value) {
        questionList[index].questionsTitle = '请填写下列框中需要回答的填空题，需要回答的内容请插入占位符';
        this.setState({ multiFillContent: '' });
        questionList[index].jsonToOption = [
          { optionContent: '' },
        ];
      }

      this.setState({
        questionType: value,
        questionList,
      });
    };
    this.addQuestion = () => {
      const { trait = {} } = this.props;
      const questionInfo = trait.question.questionInfo.data ? trait.question.questionInfo.data : {};

      console.log('%c 🍵 questionInfo.titleList: ', 'font-size:20px;background-color: #6EC1C2;color:#fff;', questionInfo.titleList);

      const questionList = [
        {
          questionsTitle: '',
          required: '1',
          sorted: (questionInfo.titleList || []).length + 1,
          questionsType: QUESTIONS_TYPE.SHOUJI_HAOMA.value,
          jsonToOption: [
            {
              optionContent: '',
              imageUrl: '',
            },
          ],
        },
      ];
      this.setState({
        addQuestionPanle: true,
        questionList,
        selectedOptions: {}, // 清除选中状态
      });
    };

    this.addOptionToQuestion = (index, questionType) => {
      const { questionList } = this.state;
      if (questionType === QUESTIONS_TYPE.DUO_XUAN.value) {
        questionList[index].jsonToOption.push({
          optionContent: '',
          imageUrl: '',
          questionType: 0,
          // 不再添加enableSecondLevel参数
        });
      } else if (questionType === QUESTIONS_TYPE.DAN_XUAN.value) {
        questionList[index].jsonToOption.push({
          extInputList: [],
          optionContent: '',
          imageUrl: '',
          // 不再添加enableSecondLevel参数
        });
      } else {
        questionList[index].jsonToOption.push({
          optionContent: '',
          imageUrl: '',
        });
      }
      this.setState({
        questionList,
      });
    };
    this.isRequire = (index, e) => {
      const { checked } = e.target;
      const { questionList } = this.state;
      questionList[index].required = checked ? '1' : '0';
      this.setState({
        questionList,
      });
    };
    this.deleteOption = (key, key1) => {
      const { questionList } = this.state;
      questionList[key].jsonToOption.splice(key1, 1);
      this.setState({
        questionList,
      });
    };
    this.questionTile = (index, e) => {
      const { questionList } = this.state;
      const { value } = e.target;
      questionList[index].questionsTitle = value;
      this.setState({
        questionList,
      });
    };
    this.editQuestionItem = (index, prop, e) => {
      const { questionList } = this.state;
      const { value } = e.target;
      questionList[index][prop] = value;
      this.setState({
        questionList,
      });
    };
    this.deleteQuestion = titleId => {
      const { examId } = this;
      const { dispatch } = this.props;
      Modal.confirm({
        title: '确定删除该问卷题目吗?',
        onOk() {
          dispatch({
            type: 'trait/deleteTitleForid',
            payload: {
              examId,
              titleId,
            },
          });
        },
      });
    };
    this.optionValue = (key, key1, e) => {
      const { value } = e.target;
      const { questionList } = this.state;
      questionList[key].jsonToOption[key1].optionContent = value;
      this.setState({
        questionList,
      });
    };
    
    this.optionContentChange = (key, key1, e) => {
      const { value } = e.target;
      const { questionList } = this.state;
      questionList[key].jsonToOption[key1].optionContent = value;
      
      // 确保二级选项字段存在
      if (!questionList[key].jsonToOption[key1].optionType) {
        questionList[key].jsonToOption[key1].optionType = '0'; // 默认为不启用二级选项
      }
      if (!questionList[key].jsonToOption[key1].secondOptionContent && questionList[key].jsonToOption[key1].optionType !== '0') {
        questionList[key].jsonToOption[key1].secondOptionContent = '';
      }
      
      this.setState({
        questionList,
      });
    };
    //  是否互斥选项
    this.isMutex = (key, key1, e) => {
      const { questionList } = this.state;
      questionList[key].jsonToOption[key1].optionGroup = e == true ? 1 : 0;
      this.setState({
        questionList,
      });
    };
    this.saveQuestion = () => {
      const { examId, state } = this;
      
      // 添加调试日志
      console.log('准备保存的问题数据:', JSON.parse(JSON.stringify(state.questionList[0])));
      console.log('问题类型:', state.questionList[0].questionsType);
      console.log('问题选项:', JSON.parse(JSON.stringify(state.questionList[0].jsonToOption)));
      
      if (!state.questionList[0].questionsTitle) {
        message.info('请填写题目名称');
        return;
      }
      const { sorted } = state.questionList[0];
      if (!/^\d*$/.test(sorted)) {
        message.info('请输入整数序号');
        return false;
      }

      let jsonToOption = '';
      if (state.questionList[0].jsonToOption) {
        // 新增：填空题（多空填值）特殊处理
        if (state.questionList[0].questionsType === QUESTIONS_TYPE.MULTI_FILL_BLANK.value) {
          state.questionList[0].jsonToOption[0].optionContent = this.state.multiFillContent;
        }
        jsonToOption = JSON.parse(JSON.stringify(state.questionList[0].jsonToOption));
        jsonToOption.map(item => {
          delete item.extInputList;
          
          // 处理启用二级选项的选项
          if (item.enableSecondLevel || item.enable_second_level || item.optionType || item.secondOptionContent) {
            // 保留旧的属性，用于兼容原有代码
            const optionType = item.optionType ? item.optionType.toString() : (item.option_type ? item.option_type.toString() : '1');
            const secondOptionContent = item.secondOptionContent || item.second_option_content || '';
            
            // 删除所有字段，只保留需要的字段
            delete item.option_type;
            delete item.second_option_content;
            delete item.enable_second_level;
            delete item.enableSecondLevel; // 删除enableSecondLevel字段，不传递给API
            
            // 添加符合API要求的字段（驼峰形式）- 确保字段类型正确
            item.optionType = optionType;
            item.secondOptionContent = secondOptionContent;
            
            // 打印转换后的数据
            console.log('处理后的二级选项数据:', item);
          }
        });
        jsonToOption = JSON.stringify(jsonToOption);
      }
      console.log('保存选项JSON:', jsonToOption);
      
      let optionContentOk = true; // 检测选项是否有内容
      if (['0', '1'].includes(state.questionList[0].questionsType) && jsonToOption) {
        state.questionList[0].jsonToOption.forEach(item => {
          // 检查选项内容是否为空
          if (!item.optionContent) {
            optionContentOk = false;
          }
          
          // 检查二级选项：只有当optionType不为"0"且存在时才检查
          if (item.optionType && item.optionType !== '0') {
            // 如果启用了二级选项但二级内容为空，则验证失败
            if (!item.secondOptionContent) {
              optionContentOk = false;
            }
            
            // 对于二级多值填空选项，检查是否包含至少一个{val}占位符
            if (item.optionType === '3' && !item.secondOptionContent.includes('{val}')) {
              message.info('二级多值填空选项必须包含至少一个{val}占位符');
              optionContentOk = false;
            }
          }
        });
      }
      if (!optionContentOk) {
        message.info('题目选项未填写完整！');
        return;
      }
      console.log('%c 🍆 state.questionList[0]: ', 'font-size:20px;background-color: #7F2B82;color:#fff;', state.questionList[0]);

      // 处理附件相关字段
      let extraParams = {};
      if (state.questionList[0].has_attachment) {
        extraParams = {
          fileFlg: state.questionList[0].has_attachment ? '1' : '0',
          fileUploadDescribe: state.questionList[0].has_attachment ? (state.questionList[0].attachment_desc || '') : '',
        };
      }

      new Promise((resolve, reject) => {
        this.props.dispatch({
          type: 'trait/saveTitleAndOption',
          payload: {
            examId,
            jsonToOption,
            questionsTitle: state.questionList[0].questionsTitle,
            questionsType: state.questionList[0].questionsType,
            required: [QUESTIONS_TYPE.DUANLUO_SHUOMING.value, QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(state.questionList[0].questionsType)
              ? '0'
              : state.questionList[0].required,
            titleId: state.questionList[0].titleId,
            maxAnswer: state.questionList[0].questionsType == 1 ? 
              state.questionList[0].maxAnswer || state.questionList[0].jsonToOption.length : '',
            sorted: state.questionList[0].questionsType === QUESTIONS_TYPE.CHULI_YIJIAN.value ? '999' : sorted,
            ...extraParams,
            resolve,
            reject,
          },
        });
      }).then(data => {
        // 成功后清空填写的内容 并隐藏添加问题的面板
        if (data.data.code == 0) {
          this.setState({
            questionList: [
              {
                questionsTitle: '',
                required: '1',
                sorted: '',
                questionsType: QUESTIONS_TYPE.SHOUJI_HAOMA.value,
                jsonToOption: [
                  {
                    optionContent: '',
                    imageUrl: '',
                  },
                ],
              },
            ],
            addQuestionPanle: false,
            questionType: QUESTIONS_TYPE.SHOUJI_HAOMA.value,
          });
        }
      });
    };
    this.uploadOnChange = (index, e) => {
      const { response } = e.file;
      const { questionList } = this.state;
      if (response) {
        if (response.code != 0) {
          message.error('上传文件失败');
        } else {
          questionList[0].jsonToOption[index].imageUrl = response.data.url;
          this.setState({
            questionList,
          });
        }
      }
    };
    this.delImg = index => {
      const { questionList } = this.state;
      questionList[0].jsonToOption[index].imageUrl = '';
      this.setState({
        questionList,
      });
    };
    this.maxOption = value => {
      const { questionList } = this.state;
      // 如果输入为空，则设置为 undefined，这样在保存时会使用默认值（选项总数）
      questionList[0].maxAnswer = value === '' ? undefined : value;
      this.setState({
        questionList,
      });
    };
    this.releaseQuestion = () => {
      const info = this.props.trait.question.questionInfo.data;
      const { beginTime, endTime, scopeInvestigation, pushText = [] } = info;
      this.props.dispatch({
        type: 'trait/releaseQuestion',
        payload: {
          data: {
            examId: this.examId,
            beginTime,
            endTime,
            scopeInvestigation,
            pushText,
          },
          status: moment().isBefore(beginTime) ? '2' : '1',
        },
      });
    };
    this.saveQuestionToDrafts = () => {
      this.props.dispatch({
        type: 'trait/saveQuestionToDrafts',
        payload: {
          examId: this.examId,
          status: '0',
          jumpTabs: '0',
        },
      });
    };
    this.editQuestion = index => {
      const info = JSON.parse(JSON.stringify(this.props.trait.question.questionInfo.data.titleList[index]));
      
      // 处理多空填值题型，加载内容到multiFillContent
      if (info.questionsType === QUESTIONS_TYPE.MULTI_FILL_BLANK.value && info.optionList && info.optionList.length > 0) {
        this.setState({
          multiFillContent: info.optionList[0].optionContent || ''
        });
      }
      
      info.optionList.map(item => {
        if (item.haveRemarkFrame == 1 && item.frameDefaultText) {
          item.extInputList = item.frameDefaultText.split(';');
        }
        
        // 处理二级选项
        const hasOptionType = item.optionType !== undefined || item.option_type !== undefined;
        if (hasOptionType || item.enableSecondLevel || item.secondOptionContent || item.second_option_content) {
          // 获取optionType，确保为字符串类型
          let optionType = null;
          if (item.optionType !== undefined) {
            optionType = item.optionType.toString();
          } else if (item.option_type !== undefined) {
            optionType = item.option_type.toString();
          } else {
            // 默认为"1"
            optionType = '1';
          }
          
          // 获取secondOptionContent
          const secondOptionContent = item.secondOptionContent || item.second_option_content || '';
          
          // 删除旧的字段
          delete item.option_type;
          delete item.second_option_content;
          delete item.enableSecondLevel;
          delete item.enable_second_level;
          
          // 添加新的字段
          item.optionType = optionType;
          item.secondOptionContent = secondOptionContent;
          
          console.log('处理后的选项:', item);
        }
      });
      
      info.jsonToOption = info.optionList;
      
      // 确保附件相关字段的类型正确
      if (info.has_attachment) {
        info.has_attachment = info.has_attachment === 1 || info.has_attachment === true || info.has_attachment === '1';
      }
      
      console.log('编辑题目数据:', info);
      
      // 清除选中状态，确保二级选项不会显示
      this.setState({
        questionList: [info],
        addQuestionPanle: true,
        questionType: info.questionsType,
        selectedOptions: {}, // 清除选中状态
      });
    };
    this.provinceChange = value => {
      console.log('地址数据', value);
    };
  }
  componentDidMount() {
    this.examId = utils.queryStringToJson(this.props.location.search).id;
    const { dispatch, rootData = {} } = this.props;
    dispatch({
      type: 'trait/fetchQuestionInfo',
      payload: {
        examId: this.examId,
      },
    });
    const { loginUserInfo = {} } = rootData;
    dispatch({
      type: 'trait/getQuestionType',
      payload: {
        hisId: loginUserInfo.hisId,
      },
    });
  }
  componentWillReceiveProps(nextProps) {
    const { trait } = nextProps;
    const { data } = trait.question.questionInfo;
    if (data) {
      if (data.titleList) {
        let lastData = {};
        data.titleList.forEach((item, index) => {
          if (item.questionsType === QUESTIONS_TYPE.CHULI_YIJIAN.value) {
            lastData = item;
            data.titleList.splice(index, 1);
          }
        });
        if (Object.keys(lastData).length > 0) {
          data.titleList.push(lastData);
          lastData = {};
        }
      }
      this.setState({
        phoneInfo: data,
      });
    }
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'trait/clear',
    });
  }

  // 添加以下方法
  
  // 处理单选题选择
  handleRadioChange = (titleId, optionId) => {
    const { selectedOptions } = this.state;
    
    // 清除该题目之前的选择
    const newSelectedOptions = {
      ...selectedOptions,
      [titleId]: {
        [optionId]: true
      }
    };
    
    this.setState({ selectedOptions: newSelectedOptions });
  }
  
  // 处理多选题选择
  handleCheckboxChange = (titleId, optionId, checked) => {
    const { selectedOptions } = this.state;
    
    // 获取当前题目的选择状态
    const currentTitleSelections = selectedOptions[titleId] || {};
    
    // 更新选择状态
    const newSelectedOptions = {
      ...selectedOptions,
      [titleId]: {
        ...currentTitleSelections,
        [optionId]: checked
      }
    };
    
    this.setState({ selectedOptions: newSelectedOptions });
  }
  
  // 检查选项是否被选中
  isOptionSelected = (titleId, optionId) => {
    const { selectedOptions } = this.state;
    return selectedOptions[titleId] && selectedOptions[titleId][optionId];
  }
  
  // 检查选项是否启用了二级选项
  isSecondLevelEnabled = (option) => {
    // 如果选项类型为"0"，则表示没有启用二级选项
    const optionType = option.optionType || option.option_type;
    if (optionType === '0') {
      return false;
    }
    
    // 必须同时存在二级选项类型和内容才认为启用了二级选项
    return !!(optionType || option.secondOptionContent || option.second_option_content);
  }

  // 恢复使用API返回的题型列表，但保留空值检查和日志输出
  getQuestionTypeList = (mapList, questionInfoData) => {
    const typeList = [];
    const existXGflag = questionInfoData.titleList && questionInfoData.titleList.some(item => item.questionsType === QUESTIONS_TYPE.XIN_GUAN.value);
    const existCHULI_YIJIANflag = questionInfoData.titleList && questionInfoData.titleList.some(item => item.questionsType === QUESTIONS_TYPE.CHULI_YIJIAN.value);

    // 排除单选-二级选择和多选-二级选择这两个选项
    const excludedLabels = ['单选-二级选择', '多选-二级选择'];

    // 恢复使用API返回的mapList
    console.log('API返回的mapList:', mapList);
    for (const key in mapList) {
      const obj = {};
      obj.questionsType = key;
      obj.questionsName = mapList[key];
      // 只添加不在排除列表中的选项
      if (!excludedLabels.includes(obj.questionsName)) {
        typeList.push(obj);
      }
    }

    console.log('题型列表数据:', typeList);

    const list =
      typeList.length > 0
        ? typeList.map((item, idx) => (
            <Radio
              key={idx}
              style={{ marginTop: 10 }}
              value={item.questionsType}
              disabled={(existXGflag && item.questionsType === QUESTIONS_TYPE.XIN_GUAN.value) || (existCHULI_YIJIANflag && item.questionsType === QUESTIONS_TYPE.CHULI_YIJIAN.value)}
            >
              {item.questionsName}
            </Radio>
          ))
        : null;
    console.log('题型列表组件:', list);
    return list;
  };
  // 编辑单选或多选附加项
  editFrame = (index, value, inputIndex) => {
    const { questionList } = this.state;
    if (value == 0) {
      questionList[0].jsonToOption[index].extInputList.splice(inputIndex, 1);
    } else if (questionList[0].jsonToOption[index].extInputList) {
      questionList[0].jsonToOption[index].extInputList.push('');
    } else {
      questionList[0].jsonToOption[index].extInputList = [''];
    }
    if (questionList[0].jsonToOption[index].extInputList && questionList[0].jsonToOption[index].extInputList.length) {
      questionList[0].jsonToOption[index].haveRemarkFrame = 1;
    } else {
      questionList[0].jsonToOption[index].haveRemarkFrame = value;
    }
    questionList[0].jsonToOption[index].frameDefaultText = questionList[0].jsonToOption[index].extInputList.join(';');
    this.setState({
      questionList,
    });
  };
  // 添加单选附加项文本
  extInputFrame = (index, value, inputIndex) => {
    const { questionList } = this.state;
    questionList[0].jsonToOption[index].extInputList[inputIndex] = value;
    questionList[0].jsonToOption[index].frameDefaultText = questionList[0].jsonToOption[index].extInputList.join(';');
    this.setState({
      questionList,
    });
  };
  // 添加附加项文本
  inputFrame = (index, value) => {
    const { questionList } = this.state;
    questionList[0].jsonToOption[index].frameDefaultText = value;
    this.setState({
      questionList,
    });
  };
  render() {
    const { trait = {} } = this.props;
    const { question = {} } = trait;
    const { questionInfo = {}, questionTypeList = {} } = question;
    const { questionType, questionList, addQuestionPanle, phoneInfo } = this.state;
    const questionInfoData = questionInfo.data ? questionInfo.data : {};
    
    // 添加日志
    console.log('问题类型列表:', questionTypeList);
    console.log('QUESTIONS_TYPE:', QUESTIONS_TYPE);
    console.log('QUESTIONS_TYPE_ARR:', QUESTIONS_TYPE_ARR);

    const questionHtml =
      questionInfoData.examId && questionInfoData.titleList
        ? questionInfoData.titleList.map((item, key) => (
            <div className="questionlist-option-item" key={item.titleId}>
              <p className="title">
                {item.questionsType !== QUESTIONS_TYPE.DUANLUO_SHUOMING.value && (
                  <span>
                    {[QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(item.questionsType) ? '' : '问题'}
                    {item.questionsType === QUESTIONS_TYPE.CHULI_YIJIAN.value ? '' : `${key + 1}：`}
                  </span>
                )}
                {item.questionsType === QUESTIONS_TYPE.MULTI_FILL_BLANK.value && item.optionList && item.optionList.length > 0 ? (
                  <span style={{ color: '#333', fontWeight: 'normal' }}>
                    {item.optionList[0].optionContent.replace(/\{val\}/g, "___")}
                  </span>
                ) : (
                  <span>{item.questionsTitle}</span>
                )}
                {![QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(item.questionsType) ? (
                  <span className="questions-type">{QUESTIONS_TYPE_ARR[item.questionsType] || ''}</span>
                ) : null}

                {![QUESTIONS_TYPE.DUANLUO_SHUOMING.value, QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(item.questionsType) && (
                  <span className="questions-required">{item.required == '1' ? '必填' : '非必填'}</span>
                )}
                <a style={{ float: 'right' }} className="item-margin" onClick={() => this.editQuestion(key, item.titleId)}>
                  编辑
                </a>
                <a style={{ float: 'right' }} onClick={() => this.deleteQuestion(item.titleId)}>
                  删除
                </a>
              </p>

                  {/* 填空题内容已直接显示在题目中，不再单独显示 */}
              
              {'578'.indexOf(item.questionsType) === -1 && item.questionsType !== QUESTIONS_TYPE.MULTI_FILL_BLANK.value && (
                <RadioGroup name={`question_${item.titleId}`} style={{ width: '100%' }}>
                  {item.optionList && item.optionList.length
                    ? item.optionList.map((item1, idx) => {
                        const type = item.questionsType;
                        const extInputList = item1.frameDefaultText ? item1.frameDefaultText.split(';') : [];
                        return type === QUESTIONS_TYPE.DAN_XUAN.value ? (
                          <div key={idx} className="option-list-item list-item-flex">
                            <Radio 
                              value={item1.optionContent}
                              onChange={() => this.handleRadioChange(item.titleId, idx)}
                              checked={this.isOptionSelected(item.titleId, idx)}
                            >
                              {item1.optionContent}
                            </Radio>
                            {/* 显示二级选项 - 只有当选项被选中且启用了二级选项时才显示 */}
                            {this.isOptionSelected(item.titleId, idx) && this.isSecondLevelEnabled(item1) && (
                              <div className="second-level-options" style={{ marginLeft: 10 }}>
                                {(item1.optionType === '1') ? (
                                  <Input style={{ width: 160 }} value={item1.secondOptionContent} disabled placeholder="二级填空" />
                                ) : item1.optionType === '2' ? (
                                  <div>
                                    {item1.secondOptionContent && item1.secondOptionContent.trim() !== '' && 
                                     item1.secondOptionContent.split(',').filter(opt => opt.trim() !== '').map((option, optIdx) => (
                                      <Checkbox key={optIdx} disabled>{option}</Checkbox>
                                    ))}
                                  </div>
                                ) : (
                                  <div 
                                    style={{ 
                                      marginTop: 5, 
                                      maxWidth: 300,
                                      padding: '5px',
                                      border: '1px solid #f0f0f0',
                                      backgroundColor: '#fafafa',
                                      color: '#333',
                                      fontSize: '14px',
                                      fontWeight: 'normal',
                                      display: 'block',
                                      position: 'relative',
                                      zIndex: 100
                                    }}
                                    className="second-level-multifill-preview"
                                  >
                                    {/* <div style={{ color: 'red', marginBottom: '5px' }}>多值填空内容:</div> */}
                                    <div style={{ color: 'black' }}>
                                      {item1.secondOptionContent && item1.secondOptionContent.replace(/\{val\}/g, "___")}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                            {item1.haveRemarkFrame == 1 && extInputList && extInputList.length
                              ? extInputList.map((ext, extIndex) => {
                                  return <Input key={extIndex} style={{ width: 160 }} value={ext} disabled />;
                                })
                              : null}
                            {item1.imageUrl ? (
                              <div>
                                <img className="preview-img" width={80} height={80} src={item1.imageUrl} alt="" />
                              </div>
                            ) : null}
                            {/* 附件上传区域 - 只在最后一个选项后显示 */}
                            {idx === item.optionList.length - 1 && (item.has_attachment === 1 || item.has_attachment === '1') && (
                              <div className="attachment-upload-area" style={{ marginTop: 10, width: '100%', borderTop: '1px dashed #ccc', paddingTop: 10 }}>
                                <p style={{ marginBottom: 5 }}><b>附件上传：</b> {item.attachment_desc}</p>
                                <Button disabled>
                                  <Icon type="upload" /> 上传附件
                                </Button>
                              </div>
                            )}
                          </div>
                        ) : type === QUESTIONS_TYPE.DUO_XUAN.value ? (
                          <div key={idx} className="option-list-item list-item-flex">
                            <Checkbox
                              onChange={(e) => this.handleCheckboxChange(item.titleId, idx, e.target.checked)}
                              checked={this.isOptionSelected(item.titleId, idx)}
                            >
                              {item1.optionContent}
                            </Checkbox>
                            {/* 显示二级选项 - 只有当选项被选中且启用了二级选项时才显示 */}
                            {this.isOptionSelected(item.titleId, idx) && this.isSecondLevelEnabled(item1) && (
                              <div className="second-level-options" style={{ marginLeft: 10 }}>
                                {(item1.optionType === '1') ? (
                                  <Input style={{ width: 160 }} value={item1.secondOptionContent} disabled placeholder="二级填空" />
                                ) : item1.optionType === '2' ? (
                                  <div>
                                    {item1.secondOptionContent && item1.secondOptionContent.trim() !== '' && 
                                     item1.secondOptionContent.split(',').filter(opt => opt.trim() !== '').map((option, optIdx) => (
                                      <Checkbox key={optIdx} disabled>{option}</Checkbox>
                                    ))}
                                  </div>
                                ) : (
                                  <div 
                                    style={{ 
                                      marginTop: 5, 
                                      maxWidth: 300,
                                      padding: '5px',
                                      border: '1px solid #f0f0f0',
                                      backgroundColor: '#fafafa',
                                      color: '#333',
                                      fontSize: '14px',
                                      fontWeight: 'normal',
                                      display: 'block',
                                      position: 'relative',
                                      zIndex: 100
                                    }}
                                    className="second-level-multifill-preview"
                                  >
                                    <div style={{ color: 'black' }}>
                                      {item1.secondOptionContent && item1.secondOptionContent.replace(/\{val\}/g, "___")}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                            {item1.haveRemarkFrame == 1 ? <Input style={{ width: 200 }} value={item1.frameDefaultText} disabled /> : null}
                            {item1.imageUrl ? (
                              <div>
                                <img className="preview-img" width={80} height={80} src={item1.imageUrl} alt="" />
                              </div>
                            ) : null}
                            {/* 附件上传区域 - 只在最后一个选项后显示 */}
                            {idx === item.optionList.length - 1 && (item.has_attachment === 1 || item.has_attachment === '1') && (
                              <div className="attachment-upload-area" style={{ marginTop: 10, width: '100%', borderTop: '1px dashed #ccc', paddingTop: 10 }}>
                                <p style={{ marginBottom: 5 }}><b>附件上传：</b> {item.attachment_desc}</p>
                                <Button disabled>
                                  <Icon type="upload" /> 上传附件
                                </Button>
                              </div>
                            )}
                          </div>
                        ) : [QUESTIONS_TYPE.DUOHANG_TIANKONG.value, QUESTIONS_TYPE.QIAN_MING.value].includes(type) ? (
                          <Input.TextArea key={idx} maxLength="200" className="option-list-item" style={{ width: '100%' }} rows={6} placeholder="请输入问题答案（最多200字）" />
                        ) : type === QUESTIONS_TYPE.MULTI_FILL_BLANK.value ? (
                          <div key={idx} style={{ width: '100%' }}>
                            <div style={{ marginBottom: 10 }}>
                              <Button disabled>插入占位符</Button>
                            </div>
                            <Input.TextArea
                              className="option-list-item" 
                              style={{ width: '100%' }} 
                              rows={6} 
                              placeholder="这里显示填空题内容，插入的{val}表示填空位置"
                              disabled
                              value={item1.optionContent || ''}
                            />
                          </div>
                        ) : type === QUESTIONS_TYPE.SHOUJI_HAOMA.value ? (
                          <Input key={idx} maxLength="11" className="option-list-item" style={{ width: '100%' }} rows={6} placeholder="请输入手机号码" />
                        ) : type === QUESTIONS_TYPE.SHENFEN_ZHENG.value ? (
                          <Input key={idx} maxLength="18" className="option-list-item" style={{ width: '100%' }} rows={6} placeholder="请输入身份证号" />
                        ) : type === QUESTIONS_TYPE.DANHANG_TIANKONG.value ? (
                          <Input key={idx} maxLength="10" className="option-list-item" style={{ width: '100%' }} rows={6} placeholder="请输入" />
                        ) : type === QUESTIONS_TYPE.DI_ZHI.value ? (
                          <Cascader
                            key={idx}
                            style={{ marginTop: '15px', width: '100%' }}
                            options={provinceData}
                            onChange={() => {
                              this.provinceChange();
                            }}
                            placeholder="请选择地址"
                          />
                        ) : type === QUESTIONS_TYPE.XIA_QU.value ? (
                          <Cascader
                            key={idx}
                            style={{ marginTop: '15px', width: '100%' }}
                            options={provinceData}
                            onChange={() => {
                              this.provinceChange();
                            }}
                            placeholder="请选择辖区"
                          />
                        ) : type === QUESTIONS_TYPE.RIQI_XUANZE.value ? (
                          <DatePicker key={idx} style={{ marginTop: '15px', width: '100%' }} onChange={() => {}} placeholder="请选择日期" />
                        ) : [QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(type) ? null : (
                          <Icon
                            key={idx}
                            className="option-list-item"
                            style={{
                              fontSize: 24,
                              marginRight: 10,
                              color: '#FFC028',
                            }}
                            type="star"
                          />
                        );
                      })
                    : null}
                </RadioGroup>
              )}
              {'78'.indexOf(item.questionsType) > -1 ? (
                <Select mode={item.questionsType === QUESTIONS_TYPE.XIALA_DUOXUAN.value ? 'multiple' : ''} style={{ width: 200, marginTop: 10 }} placeholder="请选择">
                  {(item.optionList || []).map((opt, key2) => {
                    return <Option key={key2}>{opt.optionContent}</Option>;
                  })}
                </Select>
              ) : null}
            </div>
          ))
        : null;
    return (
      <div className="create-survey-page">
        <div className="preview-window">
          <Phone phoneInfo={phoneInfo} />
        </div>
        <div className="create-survey-info">
          {questionHtml}

          {addQuestionPanle
            ? questionList.map((item, key) => {
                return (
                  <div className="question-list-item-mask" key={key}>
                    <div className="question-list-item">
                      <p className="title">
                        <span>问题（ 图片大小不超过1M，建议尺寸比例1：1 ）</span>
                      </p>
                      <Row type="flex" align="middle" className="question-option-item">
                        <Col className="question-option-item-label">题目：</Col>
                        <Col style={{ width: '42%', maxWidth: '315px' }}>
                          <Input
                            placeholder={item.questionsType === QUESTIONS_TYPE.MULTI_FILL_BLANK.value ? '此字段不会显示，填空内容请在下方编辑' : '请输入'}
                            onChange={e => this.questionTile(key, e)}
                            value={item.questionsTitle}
                            maxLength="2000"
                            disabled={item.questionsType === QUESTIONS_TYPE.MULTI_FILL_BLANK.value}
                          />
                        </Col>
                        {![QUESTIONS_TYPE.DUANLUO_SHUOMING.value, QUESTIONS_TYPE.XIN_GUAN.value, QUESTIONS_TYPE.CHULI_YIJIAN.value].includes(item.questionsType) && (
                          <Col className="item-margin">
                            <Checkbox checked={item.required == '1'} onChange={e => this.isRequire(key, e)}>
                              必填
                            </Checkbox>
                          </Col>
                        )}
                      </Row>
                      <Row type="flex" align="middle" className="question-option-item">
                        <Col className="question-option-item-label">序号：</Col>
                        <Col style={{ width: '42%', maxWidth: '315px' }}>
                          <Input placeholder="请输入" onChange={e => this.editQuestionItem(key, 'sorted', e)} value={item.sorted} maxLength="2" />
                        </Col>
                      </Row>
                      <div className={item.titleId ? 'f-none' : 'question-option-item'}>
                        <Row style={{ paddingTop: 0 }}>
                          <Col className="label-margin">
                            <RadioGroup value={questionType} onChange={e => this.questionOptionChange(key, e)}>
                              {this.getQuestionTypeList(questionTypeList, questionInfoData)}
                            </RadioGroup>
                          </Col>
                        </Row>
                      </div>
                      <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                        {[QUESTIONS_TYPE.DUOHANG_TIANKONG.value, QUESTIONS_TYPE.QIAN_MING.value].includes(questionType) ? (
                          <Row className="question-option-item">
                            <Col>
                              <Input.TextArea maxLength="200" style={{ marginLeft: 65, width: '80%' }} rows={6} placeholder="请输入问题答案（最多200字）" />
                            </Col>
                          </Row>
                        ) : [
                            QUESTIONS_TYPE.DAFEN_TI.value,
                            QUESTIONS_TYPE.DUANLUO_SHUOMING.value,
                            QUESTIONS_TYPE.SHOUJI_HAOMA.value,
                            QUESTIONS_TYPE.SHENFEN_ZHENG.value,
                            QUESTIONS_TYPE.DI_ZHI.value,
                            QUESTIONS_TYPE.DANHANG_TIANKONG.value,
                            QUESTIONS_TYPE.RIQI_XUANZE.value,
                            QUESTIONS_TYPE.XIN_GUAN.value,
                            QUESTIONS_TYPE.CHULI_YIJIAN.value,
                            QUESTIONS_TYPE.XIA_QU.value,
                            QUESTIONS_TYPE.MULTI_FILL_BLANK.value,
                          ].includes(questionType) ? (
                          questionType === QUESTIONS_TYPE.MULTI_FILL_BLANK.value ? (
                            <Row className="question-option-item">
                              <Col className="question-option-item-label">填空内容：</Col>
                              <Col span={18}>
                                <div style={{ marginBottom: 8 }}>
                                  <Button 
                                    type="primary" 
                                    size="small"
                                    onClick={() => {
                                      const currentContent = this.state.multiFillContent || '';
                                      this.setState({ multiFillContent: currentContent + '{val}' });
                                    }}
                                  >
                                    插入{'{val}'}占位符
                                  </Button>
                                  <span style={{ marginLeft: 8, fontSize: 12, color: '#999' }}>
                                    (每个{'{val}'}占位符表示一个需要填写的空白，此内容会作为题目标题显示)
                                  </span>
                                </div>
                                <Input.TextArea
                                  ref={this.textAreaRef}
                                  value={this.state.multiFillContent}
                                  onChange={(e) => this.setState({ multiFillContent: e.target.value })}
                                  style={{ width: '100%' }}
                                  rows={6}
                                  placeholder="请在此输入填空题内容，在需要填空的地方插入{val}占位符，此内容将显示为题目标题"
                                />
                              </Col>
                            </Row>
                          ) : null
                        ) : (
                          item.jsonToOption &&
                          item.jsonToOption.map((item1, key1) => (
                            <div key={key1}>
                              <Row type="flex" align="middle" className="question-option-item">
                                <Col className="question-option-item-label">选项{key1 + 1}：</Col>
                                <Col style={{ width: '42%', maxWidth: '315px' }}>
                                  <Input.TextArea value={item1.optionContent || ''} onChange={e => this.optionValue(key, key1, e)} placeholder="请输入描述" maxLength="500" />
                                </Col>
                                <Col>
                                  <Upload
                                    className="item-margin"
                                    name="upfile"
                                    action={`${CONSTANT.DOMAIN}/api/files/uploadpic`}
                                    data={{
                                      partnerId: 'merchant',
                                      serviceType: 'test',
                                    }}
                                    beforeUpload={file => {
                                      const isJPG = file.type == 'image/jpeg' || file.type == 'image/png';
                                      let uploadFalse = true;
                                      if (!isJPG) {
                                        message.error('请选择图片进行上传!');
                                        uploadFalse = false;
                                      }
                                      const size = file.size / 1024 / 1024 < 1;
                                      if (!size) {
                                        message.error('图片大小不能超过1M!');
                                        uploadFalse = false;
                                      }
                                      return uploadFalse;
                                    }}
                                    showUploadList={false}
                                    onChange={e => this.uploadOnChange(key1, e)}
                                  >
                                    <Button>{item1.imageUrl ? '更换图片' : '上传图片'}</Button>
                                  </Upload>
                                </Col>
                                <Col className="item-margin">
                                  <a onClick={() => this.deleteOption(key, key1)}>删除本项</a>
                                </Col>
                                {questionType == '1' && (
                                  <Col className="item-margin">
                                    <Checkbox
                                      checked={item1.optionGroup}
                                      onChange={e => {
                                        this.isMutex(key, key1, e.target.checked);
                                      }}
                                    >
                                      互斥项
                                    </Checkbox>
                                  </Col>
                                )}
                              </Row>

                              {/* 二级选项开关 */}
                              {[QUESTIONS_TYPE.DAN_XUAN.value, QUESTIONS_TYPE.DUO_XUAN.value].includes(questionType) && (
                                <Row className="question-option-item">
                                  <Col className="question-option-item-label" style={{ width: '80px' }}>
                                    二级选项：
                                  </Col>
                                  <Col>
                                    <Checkbox 
                                      checked={!!(item1.optionType && item1.optionType !== '0')}
                                      onChange={e => {
                                        const { questionList } = this.state;
                                        if (e.target.checked) {
                                          // 启用二级选项时，设置默认值
                                          questionList[key].jsonToOption[key1].optionType = '1'; // 默认为二级填空
                                          if (!questionList[key].jsonToOption[key1].secondOptionContent) {
                                            questionList[key].jsonToOption[key1].secondOptionContent = '';
                                          }
                                          // 确保不设置enableSecondLevel字段
                                          delete questionList[key].jsonToOption[key1].enableSecondLevel;
                                        } else {
                                          // 禁用二级选项时，设置为"0"表示没有二级选项
                                          questionList[key].jsonToOption[key1].optionType = '0';
                                          // 保留secondOptionContent字段但设为空字符串，避免验证失败
                                          questionList[key].jsonToOption[key1].secondOptionContent = '';
                                        }
                                        this.setState({ questionList });
                                      }}
                                    >
                                      启用二级选项
                                    </Checkbox>
                                  </Col>
                                </Row>
                              )}
                              
                              {/* 二级选项编辑区域 - 只有当optionType不为"0"且有值时才显示 */}
                              {[QUESTIONS_TYPE.DAN_XUAN.value, QUESTIONS_TYPE.DUO_XUAN.value].includes(questionType) && 
                               !!(item1.optionType && item1.optionType !== '0') && (
                                <Row className="question-option-item">
                                  <Col className="question-option-item-label" style={{ width: '80px' }}>
                                    二级内容：
                                  </Col>
                                  <Col style={{ width: '42%', maxWidth: '315px' }}>
                                    <Input.TextArea 
                                      value={item1.secondOptionContent || ''} 
                                      onChange={e => {
                                        const { questionList } = this.state;
                                        questionList[key].jsonToOption[key1].secondOptionContent = e.target.value;
                                        this.setState({ questionList });
                                      }} 
                                      placeholder={(item1.optionType || '1') === '1' ? "请输入描述" : 
                                                  (item1.optionType === '2' ? "请输入选项，用英文逗号分隔" : 
                                                  "请在此输入填空题内容，在需要填空的地方插入{val}占位符")} 
                                      maxLength="500"  
                                    />
                                  </Col>
                                  <Col className="item-margin">
                                    <Radio.Group 
                                      value={item1.optionType || '1'} 
                                      onChange={e => {
                                        const { questionList } = this.state;
                                        questionList[key].jsonToOption[key1].optionType = e.target.value;
                                        this.setState({ questionList });
                                      }}
                                    >
                                      <Radio value={'1'}>二级填空</Radio>
                                      <Radio value={'2'}>二级复选</Radio>
                                      <Radio value={'3'}>二级多值填空</Radio>
                                    </Radio.Group>
                                  </Col>
                                </Row>
                              )}
                              
                              {/* 二级多值填空的占位符插入按钮 - 只在optionType为"3"时显示 */}
                              {[QUESTIONS_TYPE.DAN_XUAN.value, QUESTIONS_TYPE.DUO_XUAN.value].includes(questionType) && 
                               !!(item1.optionType && item1.optionType === '3') && (
                                <Row className="question-option-item">
                                  <Col className="question-option-item-label" style={{ width: '80px' }}>
                                  </Col>
                                  <Col>
                                    <Button 
                                      type="primary" 
                                      size="small"
                                      onClick={() => {
                                        const { questionList } = this.state;
                                        const currentContent = questionList[key].jsonToOption[key1].secondOptionContent || '';
                                        questionList[key].jsonToOption[key1].secondOptionContent = currentContent + '{val}';
                                        this.setState({ questionList });
                                      }}
                                    >
                                      插入{'{val}'}占位符
                                    </Button>
                                    <span style={{ marginLeft: 8, fontSize: 12, color: '#999' }}>
                                      (每个{'{val}'}占位符表示一个需要填写的空白)
                                    </span>
                                  </Col>
                                </Row>
                              )}
                              
                              {/* 携带附件选项 - 只在每个问题的最后一个选项后显示一次 */}
                              {[QUESTIONS_TYPE.DAN_XUAN.value, QUESTIONS_TYPE.DUO_XUAN.value].includes(questionType) && 
                               key1 === item.jsonToOption.length - 1 && (
                                <Row className="question-option-item">
                                  <Col className="question-option-item-label" style={{ width: '80px' }}>
                                    携带附件：
                                  </Col>
                                  <Col>
                                    <Checkbox 
                                      checked={item.has_attachment === true || item.has_attachment === 1 || item.has_attachment === '1'}
                                      onChange={e => {
                                        const { questionList } = this.state;
                                        questionList[key].has_attachment = e.target.checked;
                                        this.setState({ questionList });
                                      }}
                                    >
                                      允许上传附件
                                    </Checkbox>
                                  </Col>
                                </Row>
                              )}
                              
                              {/* 附件描述框 */}
                              {[QUESTIONS_TYPE.DAN_XUAN.value, QUESTIONS_TYPE.DUO_XUAN.value].includes(questionType) && 
                               key1 === item.jsonToOption.length - 1 && 
                               (item.has_attachment === true || item.has_attachment === 1 || item.has_attachment === '1') && (
                                <Row className="question-option-item">
                                  <Col className="question-option-item-label" style={{ width: '80px' }}>
                                    附件描述：
                                  </Col>
                                  <Col style={{ width: '42%', maxWidth: '315px' }}>
                                    <Input.TextArea 
                                      value={item.attachment_desc || ''} 
                                      onChange={e => {
                                        const { questionList } = this.state;
                                        questionList[key].attachment_desc = e.target.value;
                                        this.setState({ questionList });
                                      }} 
                                      placeholder="请输入附件描述" 
                                      maxLength="500"  
                                    />
                                  </Col>
                                </Row>
                              )}

                              {/* 单选添加附加输入项  */}
                              {/* {[QUESTIONS_TYPE.DAN_XUAN.value].includes(questionType) && item1.haveRemarkFrame
                                ? item1.extInputList.map((ext, eIndex) => {
                                    return (
                                      <div className="question-option-item" key={eIndex}>
                                        <Row type="flex" align="middle">
                                          <Col className="item-margin-left">
                                            <Input style={{ width: 230 }} maxLength="100" value={ext} placeholder="请填充文本信息" onChange={e => this.extInputFrame(key1, e.target.value, eIndex)} />
                                          </Col>
                                          <Col className="item-margin">
                                            <a onClick={() => this.editFrame(key1, 0, eIndex)}>删除输入框</a>
                                          </Col>
                                        </Row>
                                      </div>
                                    );
                                  })
                                : null} */}
                              {/* 单选添加附加输入项 */}
                              {/* {[QUESTIONS_TYPE.DAN_XUAN.value].includes(questionType) && (
                                <div className="question-option-item">
                                  <Col className="item-margin-left">
                                    <a onClick={() => this.editFrame(key1, 1)}>选项后加输入框</a>
                                  </Col>
                                </div>
                              )} */}
                              <div className={item1.imageUrl ? 'question-option-item' : 'f-none'}>
                                <Row>
                                  <img width={80} style={{ verticalAlign: 'bottom' }} className="label-margin" src={item1.imageUrl} alt="" />
                                  <a className="item-margin" onClick={() => this.delImg(key1)}>
                                    删除
                                  </a>
                                </Row>
                              </div>
                              <div
                                className={
                                  [QUESTIONS_TYPE.DAN_XUAN.value, QUESTIONS_TYPE.DUO_XUAN.value, QUESTIONS_TYPE.XIALA_DANXUAN.value, QUESTIONS_TYPE.XIALA_DUOXUAN.value].includes(item.questionsType)
                                    ? 'question-option-item'
                                    : 'f-none'
                                }
                              >
                                {key1 === item.jsonToOption.length - 1 && (
                                  <Row>
                                    <Col>
                                      <a onClick={() => this.addOptionToQuestion(key, questionType)}>添加选项</a>
                                    </Col>
                                  </Row>
                                )}
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                      <div className={[QUESTIONS_TYPE.DUO_XUAN.value, QUESTIONS_TYPE.XIALA_DUOXUAN.value].includes(questionType) ? 'question-option-item' : 'f-none'}>
                        <Row type="flex" align="middle">
                          <Col className="question-option-item-label" style={{ width: '80px' }}>
                            最多可选：
                          </Col>
                          <Col span={12}>
                            <Input placeholder={`不填则默认为全部选项(${item.jsonToOption.length})`} value={item.maxAnswer} maxLength="2" onChange={e => this.maxOption(e.target.value, key)} />
                          </Col>
                        </Row>
                      </div>

                      {/* <Row
                      className={['14'].includes(questionType) ? 'question-option-item' : 'f-none'}
                      type="flex"
                      align="middle"
                    >
                      <Col className="question-option-item-label">
                        最多可选：
                      </Col>
                      <Col span={12}>
                        <Input
                          placeholder="请输入最多选择项数量"
                          value={item.maxAnswer}
                          maxLength="2"
                          onChange={e =>
                            this.maxOption(e.target.value, key)
                          }
                        />
                      </Col>
                    </Row> */}

                      <Row className="question-option-item">
                        <Button
                          onClick={() =>
                            this.setState({
                              addQuestionPanle: false,
                              questionList: [
                                {
                                  questionsTitle: '',
                                  required: '1',
                                  questionsType: QUESTIONS_TYPE.SHOUJI_HAOMA.value,
                                  sorted: '',
                                  jsonToOption: [
                                    {
                                      optionContent: '',
                                      imageUrl: '',
                                    },
                                  ],
                                },
                              ],
                              questionType: QUESTIONS_TYPE.SHOUJI_HAOMA.value,
                            })
                          }
                        >
                          取消
                        </Button>
                        <Button style={{ marginLeft: 15 }} type="primary" onClick={() => this.saveQuestion()}>
                          保存
                        </Button>
                      </Row>
                    </div>
                  </div>
                );
              })
            : null}
          <Row type="flex" justify="center" className="add-question-btn">
            <Col span={24} style={{ textAlign: 'center' }} onClick={this.addQuestion.bind(this)}>
              +添加问题
            </Col>
          </Row>
          <Row type="flex" justify="start">
            <Col span={24} className="btn-bar">
              <Button
                onClick={() =>
                  hashHistory.push({
                    pathname: '/trait/survey/create',
                    query: {
                      id: this.examId,
                    },
                  })
                }
              >
                上一步
              </Button>
              <Button className="ant-col-offset-1" onClick={() => this.saveQuestionToDrafts()}>
                保存至草稿箱
              </Button>
              <Button className="ant-col-offset-1" type="primary" onClick={() => this.releaseQuestion()}>
                发布
              </Button>
            </Col>
          </Row>
        </div>
      </div>
    );
  }
}
export default connect(state => {
  return {
    trait: state.trait,
    rootData: state.root,
  };
})(Form.create()(CreateQuestion));
