import { history } from 'umi';
import { message } from 'antd';
import breadcrumbNameMap from '../layouts/components/Breadcrumb';
import * as service from './service';

export default {
  namespace: 'root',
  state: {
    permissionData: {},
    typeData: undefined,
    siderDisplay: '',
    drawerStatus: false,
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
    closeDrawer(state) {
      return { ...state, drawerStatus: false };
    },
    openDrawer(state) {
      return { ...state, drawerStatus: true };
    },
    updateFormStore(state, { payload: resData }) {
      return { ...state, formStore: resData };
    },
    clear() {
      return {};
    },
  },
  effects: {
    *initApp({ payload }, { call, put }) {
      const { data } = yield call(service.loginUserInfo);
      if (data.code === 0) {
        yield put({
          type: 'authority',
          payload,
        });
        yield put({
          type: 'save',
          payload: {
            loginUserInfo: {
              ...data.data,
            },
          },
        });
      } else if (data.code != 999) {
        message.error(data.msg || '获取登录信息失败');
      }
    },
    *loginUserInfo({ payload }, { call, put }) {
      const { data } = yield call(service.loginUserInfo);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: {
            loginUserInfo: {
              ...data.data,
            },
          },
        });
      } else if (data.code != 999) {
        message.error(data.msg || '获取登录信息失败');
      }
    },
    *authority({ payload }, { select, call, put }) {
      if (payload != '/login' && payload != '/resetPwd') {
        let { permissionData, loginUserInfo } = yield select(state => {
          // eslint-disable-line
          return state.root;
        });
        if (!permissionData || JSON.stringify(permissionData) == '{}') {
          const { data } = yield call(service.getPermissionData);
          const menuData = yield call(service.getMenuTree);
          permissionData = data.data;

          if (data.code === 0) {
            const menuTreeProx = [];
            let menuTree = [];
            const menuApiRes = menuData.data || {};
            if (menuApiRes.code === 0) {
              menuTree = (menuApiRes.data || {}).resources;
              const thirdComList = [
                {
                  menu: {
                    icon: 'clockcircleo',
                    name: '体检中心',
                    url: '/exammerchant/index.html#/main',
                    children: [],
                  },
                  key: '/exam', // 平台关键字, url包含此关键字则判定为属于该平台
                  hasAuthority: false, // 是否有权限
                },
              ];
              for (let i = 0; i < menuTree.length; i++) {
                // 对不同平台分类处理
                const traitChild = menuTree[i].children || [];
                for (let j = 0; j < traitChild.length; j++) {
                  const { protocol, host } = window.location;
                  if (traitChild[j].url.startsWith('/merchant/umi/index.html#')) {
                    // traitChild[j].url = `${protocol}//${host}${traitChild[j].url}`;
                    traitChild[j].url = (traitChild[j].url || '').split('#')[1] || traitChild[j].url;
                  }
                }

                thirdComList.forEach((item, idx) => {
                  if (menuTree[i].url && menuTree[i].url.indexOf(item.key) === 0) {
                    thirdComList[idx].hasAuthority = true;
                  } else {
                    menuTreeProx.push(menuTree[i]);
                  }
                });
              }
              thirdComList.forEach(item => {
                if (item.hasAuthority) {
                  menuTreeProx.push(item.menu);
                }
              });
            }

            yield put({
              type: 'save',
              payload: {
                permissionData,
                menuTree: menuTreeProx,
              },
            });
          } else if (data.code != 999) {
            message.error(data.msg || '获取权限信息失败');
          }
        }
        // const { menus = false, btns = false } = permissionData;
        // if (payload != '/' && payload != '/main' && payload != '/trait/survey/createQuestion'
        //   && ((!menus || !menus[payload]) && (!btns || !btns[payload]))) {
        //   message.error('没有访问该页面的权限');
        //   if (loginUserInfo && JSON.stringify(loginUserInfo) != '{}') {
        //     history.push('/main');
        //   } else {
        //     history.push('/login');
        //   }
        // }
      }
    },
    *getExtraMenu({ payload }, { call, put }) {
      const { data = {} } = yield call(service.getExtraMenu, {});
      if (data.code === 0) {
        const url = (data.data || {}).url;
        if (url) {
          location.href = url;
        }
      } else {
        message.error(data.msg);
      }
    },
    *logout({ payload }, { call }) {
      yield call(service.logout);
      history.push({
        pathname: '/login',
      });
    },
    /* 保存表单信息 */
    *saveFormStore({ payload: reqData }, { put }) {
      yield put({ type: 'updateFormStore', payload: reqData });
    },
    clear({ payload }, { put }) {
      put({
        type: 'clear',
        payload: {},
      });
    },
  },
  subscriptions: {
    setup({ dispatch, history }) {
      return history.listen(({ pathname }) => {
        document.title = breadcrumbNameMap[pathname] || '管理后台';
        dispatch({
          type: 'authority',
          payload: pathname,
        });
      });
    },
  },
};
