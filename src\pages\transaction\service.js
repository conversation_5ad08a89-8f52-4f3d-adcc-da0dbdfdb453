import * as utils from '../../utils/utils';
/**
 * 获取医院渠道列表
 * @param {object} param
 */
export function loadChannelTypeGroup(param) {
  // 此接口命名不符合后台接口规范，遗留接口
  return utils.request('/api/order/getHisBusTypeList', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 根据医院的渠道类型，获取渠道下的业务列表，多个渠道时type用,分隔
 * @param {object} param
 */
export function loadBusinessTypeGroup(param) {
  const api = param.type ? '/api/order/getbiztypebychannel' : '/api/order/getbustype';
  return utils.request(api, {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 生成订单报表
 * @param {object} param
 */
export function genarateReport(param) {
  return utils.request('/api/order/generatereport', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 获取报表下载地址
 * @param {object} param
 */
export function getReportUrl(param) {
  return utils.request('/api/order/getreporturl', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 下载报表
 * @param {string} url
 */
export function downloadReport(url) {
  return utils.download(url, {
    method: 'POST',
  });
}
/* chen_yang */
/**
 * 获取医院列表
 * @param {object} param
 */
export function getHisList() {
  return utils.request('/api/userinfo/gethislist', {
    method: 'GET',
  });
}
/**
 * 查询业务办理通道
 */
export function getBusChannel() {
  return utils.request('/api/order/getbuschannel', {
    method: 'GET',
  });
}
/**
 * 查询支付渠道及方式
 */
export function getPayChannelWay(param) {
  return utils.request('/api/order/getpaychannelway', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 查询支付渠道及方式
 */
export function getBusStatus(reqData) {
  return utils.request(`/api/order/getbusstatus?busType=${reqData.busType}`, {
    method: 'GET',
  });
}
/**
 * 查询医院开通的所有业务类型
 */
export function getBusType() {
  return utils.request('/api/order/getbustype', {
    method: 'GET',
  });
}
/**
 * 获取昨日对账页面操作初始化数据
 * @param {*} param
 */
export function getLastOptionData(param) {
  return utils.request('/api/bill/yesterdaybill', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 查询订单列表
 */
export function getOrderLst(reqData) {
  return utils.request('/api/order/getordersbypage', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...reqData, sort: 'desc', orderBy: 'create_time' }),
  });
}
/**
 * 获取自动对账页面操作初始化数据
 * @param {*} param
 */
export function getAutomaticOptionData(param) {
  return utils.request('/api/bill/normalbill', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 查询订单详情
 */
export function getOrderInfo(reqData) {
  return utils.request('/api/order/getorderdetail', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...reqData }),
  });
}
/**
 * 获取对账详情
 * @param {*} param
 */
export function getFinancialDetail(param) {
  return utils.request('/api/bill/differbill', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 更新订单详情
 */
export function updateOrderInfo(reqData) {
  const url = `busType=${reqData.busType}&id=${reqData.id}&isSuccStatus=${reqData.isSuccStatus}`;
  return utils.request(`/api/order/updateorderstatus?${url}`, {
    method: 'GET',
  });
}
/**
 * 获取对账总览
 * @param {*} param
 */
export function getFinancialSummary(param) {
  return utils.request('/api/bill/searchbill', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 退款
 */
export function refundOrder(reqData) {
  const url = `busType=${reqData.busType}&id=${reqData.id}&reason=${reqData.reason}&refundFee=${reqData.refundFee}&rundPwd=${reqData.rundPwd}`;
  return utils.request(`/api/order/refund?${url}`, {
    method: 'GET',
  });
}
/**
 * 获取对账差异
 * @param {*} param
 */
export function getFinancialDiff(param) {
  return utils.request('/api/bill/diffdetail', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 异常订单列表查询
 */
export function getErrorOrdersByPage(reqData) {
  return utils.request('/api/order/geterrorordersbypage', {
    method: 'post',
    body: utils.jsonToQueryString({ ...reqData }),
  });
}
/**
 * 获取对账进度
 * @param {*} param
 */
export function getFinancialProcess(param) {
  return utils.request('/api/bill/processlog', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 快速查询订单列表
 */
export function queryListSoon(reqData) {
  return utils.request('/api/order/querylistsoon', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...reqData }),
  });
}
/**
 * 异常订单下载
 * @param {*} param
 */
export function getDownloadErrorOrders(reqData) {
  return utils.request('/api/order/downloaderrororders', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...reqData }),
  });
}
/* * 下载对账单
 * @param {*} param
 */
export function downloadFinancial(param) {
  return utils.request('/api/bill/billdownload', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 处理记录列表查询
 */
export function findOrderByInputs(reqData) {
  return utils.request('/api/ordermanager/findorderbyinputs', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...reqData, sort: 'desc', orderBy: 'update_time' }),
  });
}
/**
 * 通过id查询处理记录详情
 */
export function findorderbyorderId(id) {
  return utils.request(`/api/ordermanager/findorderbyorderid?orderId=${id}`, {
    method: 'GET',
  });
}
/* * 生成对账单
 * @param {*} param
 */
export function generateFinancial(param) {
  return utils.request('/api/bill/generatorbill', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/* * 手动刷新订单
 * @param {*} param
 */
export function getorderRefresh(param) {
  return utils.request('/api/order/getorderRefresh', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 获取对账日志商户号
 * @param {*} param
 */
export function getLogMerchant(param) {
  return utils.request('/api/bill/merchantlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 获取对账日志列表
 * @param {*} param
 */
export function getLogLst(param) {
  return utils.request('/api/bill/processbillresult', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 获取对账日志详情
 * @param {*} param
 */
export function getLogProcess(param) {
  return utils.request('/api/bill/processlog', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 重新对账
 * @param {*} param
 */
export function getRepeatbill(param) {
  return utils.request('/api/bill/repeatbill', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
