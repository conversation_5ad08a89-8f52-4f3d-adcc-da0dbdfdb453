import * as request from '@/utils/request';

export async function query() {
  return request('/api/users');
}

export async function queryCurrent() {
  return request.post('/api/userinfo/merchantuser');
}

export async function queryResource() {
  return request.post('/api/userinfo/resources');
}

export async function login(param) {
  return request.post('/api/ehis/user/login/authorization', { data: param });
}

export async function userpermission(param) {
  return request.post('/api/userpatient/userpermission', { data: param });
}

export async function logout(param) {
  return request.post('/api/logout', { data: param });
}
