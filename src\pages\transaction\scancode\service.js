import * as utils from '../../../utils/utils';
import { download } from '@/utils/request';

/**
 * 获取医院配置信息
 * @param {object} param
 */
export function getHisBusTypeList(param) {
  return utils.request('/api/order/getHisBusTypeList', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取所有产品信息
 * @param {object} param
 */
export function getByPage(param) {
  return utils.request('/api/product/get-products', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取合作客户
 * @param {object} param
 */
export function getByList(param) {
  return utils.request('/api/institution/get-by-list', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取来源医生
 * @param {object} param
 */
export function findAllUser(param) {
  return utils.request('/api/userinfo/findAllUser', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取列表分页
 * @param {object} param
 */
export function getOrdersByPage(param) {
  return utils.request('/api/order/getordersbypage', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 查询订单明细
 * @param {object} param
 */
export function getOrderDetail(param) {
  return utils.request('/api/order/getorderdetail', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 退款
 * @param {object} param
 */
export function refund(param) {
  return utils.request('/api/order/refund', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function queryChargeList(param) {
  return utils.request('/api/customize/queryChargeList?_route=h242', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

// 导出所有合同
export const exportOrder = (param = {}) => download('/api/order/exportOrder', { data: param });
