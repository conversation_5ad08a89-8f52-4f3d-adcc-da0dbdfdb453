import React, { Component, Fragment } from 'react';
import { Tooltip, Modal, Input, Select, Radio, message, notification } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import * as Api from './api';

import styles from './groupoper.less';

@Form.create()
class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      visible: false,
      busType: [],
      sendToHis: 0,
      allUser: [],
      moveType: '3',
    };

    this.formItemLayout = {
      labelCol: {
        span: 8,
      },
      wrapperCol: {
        span: 14,
      },
    };
  }

  componentDidMount() {
    this.getBusType();
    this.getAllUsrs();
  }

  componentWillReceiveProps(nextProp) {
    const { chatInfo = {} } = this.props;
    if (nextProp.type === 'justModal' && nextProp.chatInfo.id !== chatInfo.id) {
      this.setState({ visible: true });
    }
  }

  getBusType = async () => {
    const { code, data = [] } = await Api.getBusType();
    if (code == 0) {
      this.setState({ busType: data });
    }
  };

  getAllUsrs = async () => {
    const { code, data = [] } = await Api.getAllUser();
    if (code == 0) {
      this.setState({ allUser: data });
    }
  };

  handleOk = async () => {
    const {
      form: { validateFields },
    } = this.props;
    const { moveType } = this.state;
    validateFields(async (err, values) => {
      if (!err) {
        const { chatInfo = {}, getChatList = () => {} } = this.props;
        values.bz = values.bz || '';
        if (moveType === '1') {
          values.transFlg = '2';
        }
        if (moveType === '2') {
          values.transFlg = '1';
        }
        const { code, msg = '' } = await Api.handMove({
          ...values,
          groupId: chatInfo.id,
          pid: chatInfo.pid,
        });
        if (code == 0) {
          message.success('转移成功');
          this.handleCancel();
          getChatList({ removeId: chatInfo.id });
        } else {
          notification.error({
            message: msg,
          });
        }
      }
    });
  };

  visilist = () => {
    this.setState({
      visible: true,
    });
  };

  handleCancel = () => {
    this.setState({
      visible: false,
    });
  };

  switchType = e => {
    const moveType = e.target.value;
    if (moveType == 3) {
      const {
        form: { setFieldsValue },
      } = this.props;
      setFieldsValue({ periodId: 'GL100' });
    }
    this.setState({ moveType });
  };

  render() {
    const { visible, confirmLoading, busType = [], sendToHis = 1, allUser = [], moveType } = this.state;
    const { form, type = '', chatInfo } = this.props;
    const { getFieldDecorator } = form;
    return (
      <Fragment>
        {type !== 'justModal' && chatInfo.chatFilterType != '11' && chatInfo.chatFilterType != '12' ? (
          <Tooltip title="手动转移">
            <div className={`${styles.chartHradBtn} ${styles.chbtn1}`} onClick={this.visilist} />
          </Tooltip>
        ) : null}
        <Modal title="手动转移" visible={visible} onOk={this.handleOk} confirmLoading={confirmLoading} onCancel={this.handleCancel} destroyOnClose width="640px">
          <Form>
            <Form.Item {...this.formItemLayout} label="设置">
              {getFieldDecorator('sendToHis', {
                rules: [
                  {
                    required: true,
                    message: '请选择',
                  },
                ],
                initialValue: sendToHis,
              })(
                <Radio.Group>
                  <Radio value={1}>自动转移生效</Radio>
                  <Radio value={0}>自动转移不生效</Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            <Form.Item {...this.formItemLayout} label="选择转移类型">
              <Radio.Group onChange={this.switchType} defaultValue="3">
                <Radio value="1">转移到阶段</Radio>
                <Radio value="2">转移到个人</Radio>
                <Radio value="3">转移普通门诊</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item {...this.formItemLayout} label="选择目标阶段">
              {getFieldDecorator('periodId', {
                initialValue: 'GL100', // chatInfo.periodId
                rules: [
                  {
                    required: true,
                    message: '请选择',
                  },
                ],
              })(
                <Select placeholder="请选择" disabled={moveType == '3'}>
                  {busType.map(item => {
                    return <Select.Option key={item.dictKey}>{item.dictValue}</Select.Option>;
                  })}
                </Select>,
              )}
            </Form.Item>
            <Form.Item {...this.formItemLayout} label="选择目标个人">
              {getFieldDecorator('account', {
                rules: [
                  {
                    required: true,
                    message: '请选择',
                  },
                ],
              })(
                <Select
                  placeholder="请选择"
                  showSearch
                  onChange={this.setPersonSel}
                  filterOption={(ipt, node) => {
                    if (!ipt && !ipt.length) {
                      return true;
                    }
                    if (node.props.label) {
                      return node.props.label.indexOf(ipt) >= 0;
                    }
                    return (node.props.value || '').indexOf(ipt) >= 0 || (node.props.children || '').indexOf(ipt) >= 0;
                  }}
                >
                  {allUser.map(item => {
                    return (
                      <Select.OptGroup label={item.identityName} key={item.identityId}>
                        {(item.userList || []).map(user => {
                          return <Select.Option key={user.account}>{user.name}</Select.Option>;
                        })}
                      </Select.OptGroup>
                    );
                  })}
                </Select>,
              )}
            </Form.Item>
            <Form.Item {...this.formItemLayout} label="备注">
              {getFieldDecorator('bz')(<Input.TextArea rows="2" placeholder="请输入" maxLength={100} />)}
            </Form.Item>
          </Form>
        </Modal>
      </Fragment>
    );
  }
}

export default Index;
