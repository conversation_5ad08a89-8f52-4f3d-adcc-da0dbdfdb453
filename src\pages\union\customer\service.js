import { post } from '@/utils/request';

export const fetchList = (param = {}) => post('/api/institution/get-by-page', { data: param });

export const fetchListPerson = (param = {}) => post('/api/institution/get-by-page-person', { data: param });

export const fetchListTeam = (param = {}) => post('/api/institution/get-by-page-team', { data: param });

export const findAllUser = (param = {}) => post('/api/userinfo/findAllUser', { data: param });

export const getDetail = (param = {}) => post('/api/institution/get-by-id', { data: param });

export const addCustomer = (param = {}) => post('/api/institution/add-institution', { data: param });

export const updateCustomer = (param = {}) => post('/api/institution/update', { data: param });

export const deleteCustomer = (param = {}) => post('/api/institution/delete', { data: param });
