/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select, DatePicker } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { qwxzzcxtemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, qwxzzcxtemplate: { ...qwxzzcxtemplate, ...payload } },
      },
    });
  };

  const changeTableData = (val, ind, key) => {
    const { field6 = [] } = qwxzzcxtemplate;
    field6[ind][key] = val;
    changeData({ field6 });
  };

  const changeTableData7 = (val, ind, key) => {
    const { field7 = [] } = qwxzzcxtemplate;
    field7[ind][key] = val;
    changeData({ field7 });
  };

  useEffect(() => {
    if (!qwxzzcxtemplate.field7?.length) {
      changeData({ field7: [{}] });
    }
  }, [qwxzzcxtemplate.field7]);

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="检测信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="检测类型">
                <Checkbox.Group value={qwxzzcxtemplate.field1 ? qwxzzcxtemplate.field1.split(',') : []} onChange={v => changeData({ field1: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="单独先证者">单独先证者</Checkbox>
                    </Col>
                    <Col span={9}>
                      <Checkbox value="核心家系（一家三口及以上）">核心家系（一家三口及以上）</Checkbox>
                    </Col>
                    <Col span={9}>
                      <Checkbox value="其他家系情况（夫妻双方等）">其他家系情况（夫妻双方等）</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="送检备注">
                <Checkbox.Group value={qwxzzcxtemplate.field2 ? qwxzzcxtemplate.field2.split(',') : []} onChange={v => changeData({ field2: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="加做">加做</Checkbox>
                    </Col>
                    <Col span={9}>
                      <Checkbox value="重送样">重送样</Checkbox>
                    </Col>
                    <Col span={9}>
                      <div className="flex-box">
                        <Checkbox value="再分析">再分析</Checkbox>
                        <div className="flex-box">
                          <div style={{ flexShrink: 0 }}>原因</div>
                          <Input placeholder="请输入" value={qwxzzcxtemplate.field3} onChange={e => changeData({ field3: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div className="flex-box">
                        <Checkbox value="加急">加急</Checkbox>
                        <div className="flex-box">
                          <div style={{ flexShrink: 0 }}>原因</div>
                          <Input placeholder="请输入" value={qwxzzcxtemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="亲属（包括本人）做过此检测">
                <div className="flex-box">
                  <Radio.Group value={qwxzzcxtemplate.field5} onChange={e => changeData({ field5: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24} style={{ marginBottom: '24px' }}>
              <Table
                dataSource={qwxzzcxtemplate.field6 || []}
                columns={[
                  {
                    title: '亲属检测编号',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field1} onChange={e => changeTableData(e.target.value, index, 'field1')} />;
                    },
                  },
                  {
                    title: '亲属姓名',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field2} onChange={e => changeTableData(e.target.value, index, 'field2')} />;
                    },
                  },
                  {
                    title: '亲属关系',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field3} onChange={e => changeTableData(e.target.value, index, 'field3')} />;
                    },
                  },
                  {
                    title: '亲属检测结果',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field4} onChange={e => changeTableData(e.target.value, index, 'field4')} />;
                    },
                  },
                  {
                    title: '操作',
                    width: 60,
                    render: (cur, col, index) => (
                      <Button type="link" onClick={() => changeData({ field6: qwxzzcxtemplate.field6.filter((item, ind) => index != ind) })}>
                        删除
                      </Button>
                    ),
                  },
                ]}
                pagination={false}
              />
              <Button type="dashed" block onClick={() => changeData({ field6: [...(qwxzzcxtemplate.field6 || []), {}] })}>
                +添加亲属
              </Button>
            </Col>
            <Col span={24} style={{ marginBottom: '24px' }}>
              <Table
                className="xzz-table"
                scroll={{ x: 'max-content' }}
                dataSource={qwxzzcxtemplate.field7 || []}
                columns={[
                  {
                    title: '样本编号',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field1} onChange={e => changeTableData7(e.target.value, index, 'field1')} />;
                    },
                  },
                  {
                    title: '样本类型',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field2} onChange={e => changeTableData7(e.target.value, index, 'field2')} />;
                    },
                  },
                  {
                    title: '与先证者关系',
                    render: (cur, col, index) => {
                      if (index === 0) return '先证者';
                      return <Input placeholder="请输入" value={col.field3} onChange={e => changeTableData7(e.target.value, index, 'field3')} />;
                    },
                  },
                  {
                    title: '姓名',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field4} onChange={e => changeTableData7(e.target.value, index, 'field4')} />;
                    },
                  },
                  {
                    title: '性别',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field5} onChange={e => changeTableData7(e.target.value, index, 'field5')} />;
                    },
                  },
                  {
                    title: '年龄',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field6} onChange={e => changeTableData7(e.target.value, index, 'field6')} />;
                    },
                  },
                  {
                    title: '孕周（产前必填）',
                    render: (cur, col, index) => {
                      return <Input placeholder="请输入" value={col.field7} onChange={e => changeTableData7(e.target.value, index, 'field7')} />;
                    },
                  },
                  {
                    title: '临床表型',
                    render: (cur, col, index) => {
                      if (index === 0) return '——';
                      return (
                        <div className="flex-box">
                          <Radio.Group value={col.field8} onChange={e => changeTableData7(e.target.value, index, 'field8')}>
                            <Radio value="无">无</Radio>
                            <Radio value="与先证者类似">与先证者类似</Radio>
                            <Radio value="其他">其他</Radio>
                          </Radio.Group>
                          <div className="flex-box">
                            <Input placeholder="请输入" value={col.field9} onChange={e => changeTableData7(e.target.value, index, index, 'field9')} />
                          </div>
                        </div>
                      );
                    },
                  },
                  {
                    title: '操作',
                    width: 60,
                    render: (cur, col, index) => {
                      if (index === 0) return '——';
                      return (
                        <Button type="link" onClick={() => changeData({ field7: qwxzzcxtemplate.field7.filter((item, ind) => index != ind) })}>
                          删除
                        </Button>
                      );
                    },
                  },
                ]}
                pagination={false}
              />
              <Button type="dashed" block style={{ marginTop: '8px' }} onClick={() => changeData({ field7: [...(qwxzzcxtemplate.field7 || []), {}] })}>
                +添加受检者
              </Button>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="先证者（患者）" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="主诉">
                <TextArea placeholder="请输入" defaultValue={qwxzzcxtemplate.field8} onChange={e => changeData({ field8: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="家系病史">
                <div className="flex-box">
                  <Radio.Group value={qwxzzcxtemplate.field9} onChange={e => changeData({ field9: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">（描述成员）</div>
                    <Input placeholder="请输入" value={qwxzzcxtemplate.field10} onChange={e => changeData({ field10: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="临床(疑似)诊断">
                <TextArea placeholder="请输入" defaultValue={qwxzzcxtemplate.field11} onChange={e => changeData({ field11: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="其他临床信息描述">
                <TextArea placeholder="请输入" defaultValue={qwxzzcxtemplate.field12} onChange={e => changeData({ field12: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="先证者（流产物/胎儿）" key="1">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item label="孕次">
                <Input placeholder="请输入" value={qwxzzcxtemplate.field13} onChange={e => changeData({ field13: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="产次">
                <Input placeholder="请输入" value={qwxzzcxtemplate.field14} onChange={e => changeData({ field14: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="孕周（周、天）">
                <div className="flex-box">
                  <Input placeholder="请输入周" value={qwxzzcxtemplate.field15} onChange={e => changeData({ field15: e.target.value })} />
                  <Input placeholder="请输入天" value={qwxzzcxtemplate.field16} onChange={e => changeData({ field16: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="试管婴儿">
                <div className="flex-box">
                  <Radio.Group value={qwxzzcxtemplate.field17} onChange={e => changeData({ field17: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={16}>
              <Form.Item label="送检标本">
                <Checkbox.Group value={qwxzzcxtemplate.field18 ? qwxzzcxtemplate.field18.split(',') : []} onChange={v => changeData({ field18: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="羊水">羊水</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="脐血">脐血</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="DNA">DNA</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="流产物">流产物</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="妊娠情况">
                <div className="flex-box">
                  <Radio.Group value={qwxzzcxtemplate.field20} onChange={e => changeData({ field20: e.target.value })}>
                    <Radio value="单胎">单胎</Radio>
                    <Radio value="双胎">双胎</Radio>
                    <Radio value="其他">其他</Radio>
                  </Radio.Group>
                  {qwxzzcxtemplate.field20 == '其他' ? (
                    <div className="flex-box">
                      <Input placeholder="请输入" value={qwxzzcxtemplate.field21} onChange={e => changeData({ field21: e.target.value })} />
                    </div>
                  ) : null}
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="不良孕产史">
                <div className="flex-box">
                  <Radio.Group value={qwxzzcxtemplate.field22} onChange={e => changeData({ field22: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  {qwxzzcxtemplate.field22 == '有' ? (
                    <div className="flex-box">
                      <div className="flex-shrink">描述</div>
                      <Input placeholder="请输入" value={qwxzzcxtemplate.field23} onChange={e => changeData({ field23: e.target.value })} />
                    </div>
                  ) : null}
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="超声检查">
                <Checkbox.Group value={qwxzzcxtemplate.field24 ? qwxzzcxtemplate.field24.split(',') : []} onChange={v => changeData({ field24: v.join(',') })}>
                  <Checkbox value="未见异常">未见异常</Checkbox>
                  <Checkbox value="胎儿结构异常">胎儿结构异常</Checkbox>
                  <Checkbox value="软指标异常">软指标异常</Checkbox>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="超声报告描述">
                <TextArea placeholder="请输入" defaultValue={qwxzzcxtemplate.field25} onChange={e => changeData({ field25: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="临床(疑似)诊断">
                <TextArea placeholder="请输入" defaultValue={qwxzzcxtemplate.field26} onChange={e => changeData({ field26: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="家系病史">
                <div className="flex-box">
                  <Radio.Group value={qwxzzcxtemplate.field27} onChange={e => changeData({ field27: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">（描述成员）</div>
                    <Input placeholder="请输入" value={qwxzzcxtemplate.field28} onChange={e => changeData({ field28: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
