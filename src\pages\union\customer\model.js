import * as Api from './service';

export default {
  namespace: 'customer',
  state: {
    userList: [],
    detail: {},
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
  },
  effects: {
    *getAllUser({ payload }, { call, put }) {
      const data = yield call(Api.findAllUser, payload);
      if (data.code == 0) {
        yield put({
          type: 'save',
          payload: { userList: data.data || [] },
        });
      }
    },
    *getDetail({ payload }, { call, put }) {
      const data = yield call(Api.getDetail, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { detail: { ...data?.data, contactsJson: JSON.parse(data?.data?.contactsJson) } },
        });
      }
    },
  },
};
