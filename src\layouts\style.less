@charset "utf-8";

@import '~@/resources/styles/mixins';

.g-sider {
  position: fixed !important;
  z-index: 10;
  height: 100vh;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 0px;
  }
}
.g-menu {
  .iconfont {
    min-width: 14px;
    margin-right: 8px;
    transition: font-size 0.15s cubic-bezier(0.215, 0.61, 0.355, 1), margin 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  img {
    width: 14px;
    height: 14px;
    margin-right: 8px;
  }
  .ant-menu-submenu-arrow {
    color: #f2f4f4 !important;
  }
}
.merchant-header {
  width: 100%;
  padding: 0 21px 0 24px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  background: #fff;
  box-shadow: 0px -1px 0px 0px #f0f0f0 inset;
  .opt-cell {
    height: 100%;
    text-align: right;
    font-size: 14px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -moz-box-flex: 1;
    -ms-flex: 1;
    flex: 1;

    .separate-line {
      display: inline-block;
      padding: 0 8px;
    }
    .opt-cell-custom {
      display: inline-block;
      cursor: default;
      color: red;
      margin-right: 15px;
    }
    .opt-cell-user {
      display: inline-block;
      cursor: default;
    }

    .opt-cell-off {
      display: inline-block;
      cursor: pointer;
    }
  }
}
