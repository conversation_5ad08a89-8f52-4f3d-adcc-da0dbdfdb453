@import '~antd/lib/style/themes/default.less';

.ellipsis {
  display: inline-block;
  max-width: 4em;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-wrap: normal;
}

.tableCol {
  padding-top: 10px;

  &:first-child {
    padding-top: 0;
  }
}
.col-item {
  :global {
    .ant-legacy-form-item {
      flex: auto;
      margin-bottom: 0;
      .ant-picker {
        width: 100%;
      }
      .text-right {
        text-align: right;
        .ant-btn:not(:last-child) {
          margin-right: 8px;
        }
      }
    }
  }
}
.table-box {
  width: 100%;
  overflow: auto;
  
  .table-content {
    min-width: 1300px; // 根据实际列宽调整
  }
}
