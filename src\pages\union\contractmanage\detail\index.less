.g-page.page-contractmanage-detail {
  .container {
    padding: 0 56px 24px;
    background: transparent;
    .ant-card {
      margin-bottom: 24px;
    }
    .my-form-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      .label {
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.85);
        &.required::before {
          display: inline-block;
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
    }
    .files-list {
      display: flex;
      // align-items: center;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 16px;
      .image-box {
        position: relative;
        border-radius: 2px;
        border: 1px solid #d9d9d9;
        overflow: hidden;
        > img {
          width: 104px;
          height: 104px;
        }
        .action-box {
          position: absolute;
          z-index: 2;
          background-color: rgba(55, 55, 55, 0.6);
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          display: none;
          .anticon {
            font-size: 20px;
            color: #fff;
            cursor: pointer;
            &:not(:last-child) {
              margin-right: 16px;
            }
          }
        }
        &:hover {
          .action-box {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
      .file-box {
        display: flex;
        align-items: center;
        .anticon {
          font-size: 20px;
          cursor: pointer;
          margin-left: 8px;
        }
      }
      .ant-upload-picture-card-wrapper {
        padding: 0;
        width: auto;
        .ant-upload {
          margin: 0;
        }
      }
    }
    .danger-link {
      color: #ff4d4f;
    }
  }
  .footer {
    text-align: center;
    .ant-btn {
      margin-left: 8px;
    }
  }
}
.add-yiyuan-product {
  .ant-select-selector {
    max-height: 100px;
    overflow: auto;
  }
}
