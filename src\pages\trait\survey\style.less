@charset "utf-8";
@import '../../../resources/styles/mixins';

.survey-drawer,
.transaction-drawer {
  min-width: 700px;
}
.trait-modal-formitem {
  margin: 0 12px 24px;
}

.page-survey {
  .question-list-container {
    margin: 24px;
    padding: 24px;
    background-color: #fff;
  }
  .opt-area {
    color: @link-color;
  }
}
.page-survey-tabs {
  padding: 24px 24px 0 24px;
  background: #fff;
  overflow: hidden;
  .active {
    border-color: @primary-color;
    color: @primary-color;
  }
  input[type='radio'] {
    display: none;
    // &:checked {
    //   + label {
    //     border-color: @primary-color;
    //     color: @primary-color;
    //   }
    // }
    // &{
    //   + label {
    //     border-color:red;
    //     color: red;
    //   }
    // }
  }
  label {
    float: left;
    padding: 0 0 10px;
    margin-right: 55px;
    border-bottom: 2px transparent solid;
    cursor: pointer;
    font-size: 14px;
  }
}

.survey-threeLevel-page {
  margin: 24px 170px;
}

/* 问卷详情 */
.survey-detail-container {
  position: relative;

  .survey-detail-header,
  .survey-detail-panle {
    padding: 16px 24px 10px;
    background-color: #fff;
  }
  .survey-detail-form {
    background-color: #fff;
    margin-top: 10px;
    padding: 16px 24px;
    .col-item {
      margin-left: 24px;
      margin-bottom: 10px;
      &:first-child {
        margin-left: 0;
      }
    }
    .ant-form-item {
      margin-bottom: 0;
    }
    .tips {
      font-size: 14px;
      margin: 3px 0 0 10px;
    }
  }
  .survey-detail-btn {
    background-color: #fff;
    margin-top: 10px;
    padding: 16px 24px 24px;
  }
  .survey-title {
    font-size: 18px;
    color: rgba(0, 0, 0, 0.75);
  }

  .ant-badge-status-success {
    background-color: #00a854 !important;
  }

  .ant-badge {
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-left: 20px;
    padding: 4px 20px;
  }

  .survey-item {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
    &.dimensionTit {
      padding-bottom: 5px;
      margin-top: 16px;
      border-bottom: 1px solid #e8e5e5;
    }
    .survey-item-descript {
      word-wrap: break-word;
      word-break: break-all;
    }
    .survey-item-label {
      display: inline-block;
      min-width: 85px;
    }
  }

  .end-voting-btn {
    position: absolute;
    right: 24px;
    top: 16px;
  }

  .ticket-panle {
    float: right;
    background-color: #f9f9f9;
    overflow: hidden;

    .ticket-item-panle {
      float: left;
      padding: 20px 30px;
      text-align: center;
    }

    .ticket-value {
      font-size: 42px;
      color: rgba(0, 0, 0, 0.75);
    }
  }

  .survey-detail-panle {
    margin-top: 24px;
    padding: 0;

    .survey-detail-header {
      padding: 12px 30px;
      border-bottom: 1px @border-color solid;
      background-color: #f9f9f9;
      .title {
        word-wrap: break-word;
        word-break: break-all;
      }
    }

    .survey-detail-item {
      padding: 12px 30px 12px 52px;
    }

    .option-img {
      padding: 0 52px 10px;
      box-sizing: content-box;
    }

    .questions-type {
      color: #51c488;
      padding: 2px 9px;
      border-radius: 4px;
      background-color: #ceefdf;
      margin-left: 8px;
      white-space: nowrap;
    }

    .questions-required {
      background-color: #fee3ce;
      color: #f8811a;
      padding: 2px 9px;
      border-radius: 4px;
      margin-left: 8px;
      white-space: nowrap;
    }
    .questions-average {
      background-color: #108ee9;
      color: #ffffff;
      padding: 2px 9px;
      border-radius: 4px;
      margin-left: 8px;
    }
    .questions-percent {
      background-color: #3f969d;
      color: #ffffff;
      padding: 2px 9px;
      border-radius: 4px;
      margin-left: 8px;
    }
  }
}

/*答题 抽屉*/
.answer-user-list {
  .info-header {
    padding: 0 25px;
    height: 54px;
    line-height: 54px;
    font-size: 16px;
    border-bottom: 1px #e9e9e9 solid;
  }

  .close-icon {
    float: right;
  }

  .list-container {
    margin: 0 24px;
  }

  .table-header-title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.75);
    word-wrap: break-word;
    word-break: break-all;
  }

  .answer-content {
    width: 40%;
  }
}

/* 手机预览 */
.question-phone {
  .question-title {
    margin: 10px 0;
    text-align: center;
    font-size: 16px;
    word-wrap: break-word;
    word-break: break-all;
    padding: 15px;
  }

  .question-phone-list {
    margin: 10px 15px;
    color: rgba(0, 0, 0, 0.6);
    .mydescript {
      word-wrap: break-word;
      word-break: break-all;
    }
  }
}
.close-icon {
  cursor: pointer;
}

.survey-patient-list {
  margin: 24px 140px;
  padding: 20px 20px 20px 0;
  background: #fff;
  border-radius: 4px;
  padding-top: 25px;
  .ant-table-thead > tr > th {
    text-align: center;
    span {
      display: inline-block;
      max-width: 150px;
    }
    p {
      width: 100%;
      overflow: hidden;
      white-space: normal;
      text-overflow: ellipsis;
    }
  }
  .ant-table-tbody {
    text-align: center;
  }
}
.export-time-modal {
  width: 400px !important;
  .ant-modal-body {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .ant-progress-text {
    display: block;
    width: 100%;
    margin: 10px 0 0 0;
    text-align: center;
  }
  .col-item {
    .ant-form-item {
      margin-bottom: 10px;
    }
  }
}

.triangle-border-down {
  //style={{ fontSize: 12, lineHeight: '20px',transform: 'rotate(-90deg)' }}
  font-size: 12;
  line-height: 20px;
  transform: rotate(-90deg);
}
.survey-notice {
  padding: 10px 24px;
  background-color: #fff;
}
.create-survey-notice {
  border: 1px solid #51c488;
  background: rgb(234, 253, 244);
  margin-bottom: 10px;
  line-height: 35px;
  display: flex;
  align-items: center;
  padding-left: 10px;
}
.notice-img {
  line-height: 35px;
  margin-right: 6px;
  width: 20px;
  height: 20px;
}
.survey-detail-entirety {
  background-color: #fff;
  margin-top: 10px;
  padding: 16px 24px 24px;
}
