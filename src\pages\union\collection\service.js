import { post } from '@/utils/request';

export const fetchList = (param = {}) => post('/api/returnMoney/contractReturnMoney', { data: param });

export const fetchTotalList = (param = {}) => post('/api/sampleStat/productAllCount', { data: param });

export const findAllUser = (param = {}) => post('/api/userinfo/findAllUser', { data: param });

export const getInstitution = (param = {}) => post('/api/institution/get-by-list', { data: param });

export const getProduct = (param = {}) => post('/api/product/get-products', { data: param });

export const selectListByParam = (param = {}) => post('/api/returnMoney/selectListByParam', { data: param });

export const addMoney = (param = {}) => post('/api/returnMoney/add', { data: param });

export const updateMoney = (param = {}) => post('/api/returnMoney/update', { data: param });

export const deleteMoney = (param = {}) => post('/api/returnMoney/delete', { data: param });
