import { message } from 'antd';
import { history } from 'umi';
import * as Api from './service';

import * as utils from '../../utils/utils';

const mountChildren = (pid, list = [], children, activeId) => {
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    if (item.active) {
      item.indeterminate = true;
      item.active = false;
    }
    if (activeId && item.no == activeId) {
      item.active = true;
    }
    if (item.no === pid) {
      for (let j = 0; j < children.length; j++) {
        const tmp = children[j];
        if (activeId && tmp.no == activeId) {
          tmp.active = true;
          break;
        }
      }
      item.children = children;
      item.hasChild = 0;
      break;
    } else if (item.hasChild == 0) {
      mountChildren(pid, item.children, children, activeId);
    }
  }
};

export default {
  namespace: 'register',
  state: {
    deptList: [],
    branchDeptLst: [],
    hisList: [],
    smart: {
      illnessLst: [],
      diseasesList: [],
      bodyparts: [],
      bodypartsDetail: {},
      diseaseDetail: {},
      diseaseInfoToDrawer: {},
    },
    imgData: {},
    department: {
      list: [],
      subList: [],
      detail: false,
      tree: [],
    },
    hospital: {
      list: [],
      detail: {},
    },
    doctor: {
      list: {},
      detail: {},
    },
    sortable: {
      backIds: [],
      dept: [],
      doctor: [],
    },
    manualObj: {},
    resultStatus: {},
  },
  reducers: {
    updateHisLst(state, { payload: resData }) {
      return { ...state, hisList: resData };
    },
    saveResult(state, { payload: resData }) {
      return { ...state, resultStatus: resData };
    },
    saveMannual(state, { payload: resData }) {
      return { ...state, manualObj: resData };
    },
    updateIllnessLst(state, { payload: resData }) {
      return {
        ...state,
        smart: {
          ...state.smart,
          illnessLst: resData,
        },
      };
    },
    updateDiseasesList(state, { payload: resData }) {
      return {
        ...state,
        smart: {
          ...state.smart,
          diseasesList: resData,
        },
      };
    },
    updateBodyPartsList(state, { payload: resData }) {
      return {
        ...state,
        smart: {
          ...state.smart,
          bodyparts: resData,
        },
      };
    },
    updateBodyPartsDetail(state, { payload: resData }) {
      return {
        ...state,
        smart: {
          ...state.smart,
          bodypartsDetail: resData,
        },
      };
    },
    updateDiseaseDetail(state, { payload: resData }) {
      return {
        ...state,
        smart: {
          ...state.smart,
          diseaseDetail: resData,
        },
      };
    },
    updateDiseaseDetailDrawer(state, { payload: resData }) {
      return {
        ...state,
        smart: {
          ...state.smart,
          diseaseInfoToDrawer: resData,
        },
      };
    },
    updateDeptList(state, { payload: resData }) {
      return { ...state, deptList: resData };
    },
    updateBranchDeptList(state, { payload: resData }) {
      return { ...state, branchDeptLst: resData };
    },
    saveDepartment(state, { payload }) {
      return {
        ...state,
        department: {
          ...state.department,
          ...payload,
        },
      };
    },
    saveDoctor(state, { payload }) {
      return {
        ...state,
        doctor: {
          ...state.doctor,
          ...payload,
        },
      };
    },
    saveHospital(state, { payload }) {
      return {
        ...state,
        hospital: {
          ...state.hospital,
          ...payload,
        },
      };
    },
    saveSortable(state, { payload }) {
      return {
        ...state,
        sortable: {
          ...state.sortable,
          ...payload,
        },
      };
    },
    saveImg(state, { payload: resData }) {
      return {
        ...state,
        imgData: resData,
      };
    },
    clear(state) {
      return {
        ...state,
        busChannelLst: [],
        smart: { ...state.smart, diseaseInfoToDrawer: {}, bodyparts: [] },
      };
    },
    clearIlls(state) {
      return {
        ...state,
        smart: { ...state.smart, diseaseInfoToDrawer: {}, bodypartsDetail: {} },
      };
    },
  },
  effects: {
    *departmentList({ payload, success }, { call, put, select }) {
      const { pid = 0, activeId = false } = payload;
      const { data } = yield call(Api.departmentList, payload);
      if (data.code === 0) {
        let list = [];
        let subList = [];
        if (pid && pid != 0) {
          list = yield select(state => {
            return state.register.department.list;
          });
          const children = utils.generateLabelValueList({
            list: data.data || [],
            labelIndex: 'name',
            valueIndex: 'no',
            isLeafIndex: 'hasChild',
            childIndex: 'children',
          });
          mountChildren(pid, list, children, activeId);
        } else {
          list = utils.generateLabelValueList({
            list: data.data || [],
            labelIndex: 'name',
            valueIndex: 'no',
            isLeafIndex: 'hasChild',
            childIndex: 'children',
          });
          list.forEach(item => {
            if (item.no == activeId) {
              item.active = true;
            }
          });
        }
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          if (item.active || item.indeterminate) {
            subList = item.children || [];
            break;
          }
        }
        yield put({
          type: 'saveDepartment',
          payload: {
            list,
            subList,
          },
        });
        if (success) {
          success();
        }
      } else {
        message.error(data.msg);
      }
    },
    *departmentTree({ payload }, { call, put }) {
      const { data } = yield call(Api.departmentTree, payload);
      if (data.code === 0) {
        const tree = utils.generateLabelValueList({
          list: data.data || [],
          labelIndex: 'name',
          valueIndex: 'no',
          keyIndex: 'no',
          isLeafIndex: 'hasChild',
          childIndex: 'children',
        });
        yield put({
          type: 'saveDepartment',
          payload: {
            tree,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *departmentDelete({ payload }, { call }) {
      const { data } = yield call(Api.departmentDelete, payload);
      if (data.code === 0) {
        message.success('删除成功');
      } else {
        message.error(data.msg);
      }
    },
    *batchDeleteDepts({ payload }, { call, put }) {
      const { data } = yield call(Api.batchDeleteDepts, payload);
      if (data.code === 0) {
        message.success('批量删除科室成功');
        yield put({
          type: 'saveDepartment',
          payload: {
            list: [],
            subList: [],
            detail: {},
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *departmentDetail({ payload }, { call, put }) {
      const { data } = yield call(Api.departmentDetail, payload);
      if (data.code === 0) {
        const detail = data.data;
        // let summary = {};
        // if (detail.summary) {
        //   summary = yield call(Api.loadStaticJSONData, {url: detail.summary});
        // }
        // detail.summary = (summary && summary.content) || '';
        yield put({
          type: 'saveDepartment',
          payload: {
            detail,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *submitDepartmentDetail({ payload, success }, { call, put }) {
      const { data } = yield call(Api.submitDepartmentDetail, payload);
      if (data.code === 0) {
        message.success('保存成功');
        const dept = data.data;
        yield put({
          type: 'departmentList',
          success,
          payload: {
            hisId: dept.hisId,
            activeId: dept.no,
            pid: dept.pid,
          },
        });
        yield put({
          type: 'saveDepartment',
          payload: {
            detail: dept,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *syncDepartmentToMicro({ payload }, { call }) {
      const { data } = yield call(Api.syncDepartmentToMicro, payload);
      if (data.code === 0) {
        message.success('同步成功');
      } else {
        message.error(data.msg);
      }
    },
    *doctorList({ payload }, { call, put }) {
      const { data } = yield call(Api.doctorList, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveDoctor',
          payload: {
            list: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *doctorDelete({ payload }, { call, put }) {
      const { data } = yield call(Api.doctorDelete, payload);
      if (data.code === 0) {
        message.success('删除成功');
        yield put({
          type: 'doctorList',
          payload: {
            ...payload.listQueryParam,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *batchDelDoctor({ payload }, { call, put }) {
      const { data } = yield call(Api.batchDelDoctor, { ids: payload.ids, type: payload.type, hisId: payload.hisId });
      if (data.code === 0) {
        message.success('批量删除医生成功');
        yield put({
          type: 'doctorList',
          payload: {
            ...payload.listQueryParam,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *doctorDetail({ payload }, { call, put }) {
      const { data } = yield call(Api.doctorDetail, payload);
      if (data.code === 0) {
        const detail = data.data;
        // let summary = {};
        // if (detail.summary) {
        //   summary = yield call(Api.loadStaticJSONData, {url: detail.summary});
        // }
        // detail.summary = (summary && summary.content) || '';
        yield put({
          type: 'saveDoctor',
          payload: {
            detail,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *submitDoctorDetail({ payload }, { call, put }) {
      const listQueryParam = Object.assign({}, payload.listQueryParam);
      const { data } = yield call(Api.submitDoctorDetail, payload);
      if (data.code === 0) {
        message.success('保存成功');
        yield put({
          type: 'root/closeDrawer',
        });
        yield put({
          type: 'saveDoctor',
          payload: {
            detail: {},
            list: [],
          },
        });
        yield put({
          type: 'doctorList',
          payload: listQueryParam,
        });
      } else {
        message.error(data.msg);
      }
    },
    *syncDoctorToMicro({ payload }, { call }) {
      const { data } = yield call(Api.syncDoctorToMicro, payload);
      if (data.code === 0) {
        message.success('同步成功');
      } else {
        message.error(data.msg);
      }
    },
    *hospitalList({ payload, next }, { call, put }) {
      const { data } = yield call(Api.hospitalList, payload);
      if (data.code === 0) {
        const list = data.data;
        yield put({
          type: 'saveHospital',
          payload: {
            list,
          },
        });
        const hisId = (payload || {}).hisIdStr || list[0].hisId;
        if (list && list.length > 0 && next) {
          if (typeof next == 'string') {
            yield put({
              type: next,
              payload: {
                hisId,
              },
            });
          } else if (next instanceof Array) {
            for (let i = 0; i < next.length; i++) {
              const effect = next[i];
              yield put({
                type: effect,
                payload: {
                  hisId,
                },
              });
            }
          }
        }
      } else {
        message.error(data.msg);
      }
    },
    // *hospitalDetail({ payload }, { call, put }) {
    //   const { data } = yield call(Api.hospitalDetail, payload);
    //   if (data.code === 0) {
    //     const detail = data.data;
    //     // let introduction = {};
    //     // if (detail.introduction) {
    //     //   introduction = yield call(Api.loadStaticJSONData, {url: detail.introduction});
    //     // }
    //     // detail.introduction = (introduction && introduction.content) || '';
    //     yield put({
    //       type: 'saveHospital',
    //       payload: {
    //         detail,
    //       },
    //     });
    //   } else {
    //     message.error(data.msg);
    //     yield put({
    //       type: 'saveHospital',
    //       payload: {
    //         detail: {},
    //       },
    //     });
    //   }
    // },
    // *submitHospitalDetail({ payload }, { call }) {
    //   const { data } = yield call(Api.submitHospitalDetail, payload);
    //   if (data.code === 0) {
    //     message.success('保存成功');
    //     history.push('/register/hospital');
    //   } else {
    //     message.error(data.msg);
    //   }
    // },
    *deptSortable({ payload }, { call, put }) {
      yield put({
        type: 'saveSortable',
        payload: {
          dept: false,
        },
      });
      const { data } = yield call(Api.deptSortable, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveSortable',
          payload: {
            dept: data.data,
          },
        });
      } else {
        message.error(data.msg);
        yield put({
          type: 'saveSortable',
          payload: {
            dept: [],
          },
        });
      }
    },
    *submitDeparmentSortable({ payload }, { call, put }) {
      const { data } = yield call(Api.submitDeparmentSortable, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveSortable',
          payload: {
            dept: false,
          },
        });
        message.success('保存成功');
        yield put({
          type: 'deptSortable',
          payload: {
            hisId: payload.hisId,
            pid: payload.deptNo,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *doctorSortable({ payload }, { call, put }) {
      const { data } = yield call(Api.doctorSortable, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveSortable',
          payload: {
            doctor: data.data,
          },
        });
      } else {
        message.error(data.msg);
        yield put({
          type: 'saveSortable',
          payload: {
            doctor: [],
          },
        });
      }
    },
    *submitDoctorSortable({ payload }, { call, put }) {
      const { data } = yield call(Api.submitDoctorSortable, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveSortable',
          payload: {
            doctor: false,
          },
        });
        message.success('保存成功');
        yield put({
          type: 'doctorSortable',
          payload: {
            hisId: payload.hisId,
            deptNo: payload.deptNo,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    // /* 获取医院列表 */
    // *fetchHisLst({ payload: reqData }, { call, put }) {
    //   const { data } = yield call(Api.getHisList, reqData);
    //   yield put({ type: 'updateHisLst', payload: data });
    // },

    // /* 获取病症列表 */
    // *fetchIllnessLst({ payload: reqData }, { call, put }) {
    //   const { data } = yield call(Api.getHisList, reqData);
    //   yield put({ type: 'updateIllnessLst', payload: data });
    // },

    /* 新增his需要的身体部位 */
    *fetchAddHisBodyPartRes({ payload: reqData }, { call, put }) {
      const res = {};
      let data;
      if (reqData.reqData.id) {
        // 编辑
        res.msg = '更新成功';
        data = yield call(Api.updateHisBodyPart, reqData.reqData);
      } else {
        data = yield call(Api.addHisBodyPart, reqData.reqData);
      }
      if (data.data && data.data.code == 0) {
        message.success(res.msg || '添加成功');
        yield put({ type: 'clearIlls' });
        yield put({ type: 'clear' });
        reqData.resolve();
      } else {
        message.error(data.data ? data.data.msg : '新增失败' || '新增失败');
      }
    },

    /* 删除身体部位 */
    *deleteHisBodyPart({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.deleteHisBodyPart, reqData.reqData);
      if (data.code == 0) {
        yield put({ type: 'clear' });
        message.success('删除成功');
        reqData.resolve();
      } else {
        message.error('删除失败');
      }
    },

    /* 获取部位所有病历 */
    *fetchDiseasesList({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getDiseasesList, reqData);
      yield put({ type: 'updateDiseasesList', payload: data });
    },
    /* 获取所有部位 */
    *fetchBodyPartsList({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getBodyPartsList, reqData);
      yield put({ type: 'updateBodyPartsList', payload: data });
    },

    /* 获取部位详情 */
    *getBodypartDetail({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getBodypartDetail, reqData);
      yield put({ type: 'updateBodyPartsDetail', payload: data });
    },

    /* 获取病症详情 */
    *fetchDiseaseDetail({ payload: reqData }, { call, put, select }) {
      const { data } = yield call(Api.getDiseaseDetail, reqData);
      const diseasesList = yield select(state => state.register.smart.diseasesList);
      const id = reqData.record.id; // 病症id
      if (diseasesList && diseasesList.data && diseasesList.data.length) {
        diseasesList.data.forEach(item => {
          if (item.id == id) {
            item.depts = data.data ? data.data.depts : [];
          }
        });
      }
      yield put({ type: 'updateDiseasesList', payload: diseasesList });
    },

    /* 获取病症详情给抽屉组件 和上面的区分开 */
    *fetchDiseaseDetailToDrawer({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getDiseaseDetail, reqData);
      yield put({ type: 'updateDiseaseDetailDrawer', payload: data });
      yield put({
        type: 'root/openDrawer',
      });
    },

    /* 新增或者更新推荐科室 */
    *fetchSaveRecommendDeptRes({ payload: reqData }, { call, put, select }) {
      // const loginUserInfo = yield select(state => state.root.loginUserInfo);
      // if (loginUserInfo.userType == 'platform') {
      //   console.log('111111111')
      // }
      // console.log(loginUserInfo)
      const resData = yield call(Api.saveRecommendDept, reqData.reqData);
      if (resData.data && resData.data.code == 0) {
        message.success('编辑成功');
        yield put({
          type: 'clearIlls',
        });
        const { data } = yield call(Api.getDiseasesList, {
          bodyPartId: reqData.bodyPartId.bodyPartId ? reqData.bodyPartId.bodyPartId : '',
          parentDisId: reqData.bodyPartId.parentDisId || '',
        });
        yield put({ type: 'updateDiseasesList', payload: data });
        yield put({
          type: 'root/closeDrawer',
        });
      } else {
        message.error('修改失败');
      }
    },

    /* 新增或者更新推荐科室 */
    *fetchSaveOrUpdateDiseaseRes({ payload: reqData }, { call }) {
      const { data } = yield call(Api.saveOrUpdateDisease, reqData.reqData);
      if (data.code == 0) {
        reqData.resolve(data);
      } else {
        message.error('修改失败');
      }
    },

    /* 删除病症 */
    *fetchdelDiseaseRes({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.delDisease, reqData);
      if (data.code == 0) {
        message.success('删除成功');
        const listData = yield call(Api.getDiseasesList, {
          bodyPartId: reqData.bodypartId.bodyPartId ? reqData.bodypartId.bodyPartId : '',
          parentDisId: reqData.bodypartId.parentDisId || '',
        });
        yield put({ type: 'updateDiseasesList', payload: listData.data });
      } else {
        message.error('删除失败');
      }
    },

    /* 删除推荐的科室 */
    *fetchRecommendDeptRes({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.delRecommendDept, reqData.reqData);
      if (data && data.code == 0) {
        yield put({
          type: 'clearIlls',
        });
        message.success('删除成功');
        reqData.resolve(data);
      } else {
        message.error('删除失败');
      }
    },

    /* 科室查询 */
    *fetchDeptList({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.departmentTree, reqData);
      if (data.code == 0) {
        const tree = utils.generateLabelValueList({
          list: data.data || [],
          labelIndex: 'name',
          valueIndex: 'name',
          keyIndex: 'no',
          isLeafIndex: 'hasChild',
          childIndex: 'children',
        });
        yield put({
          type: 'updateDeptList',
          payload: {
            tree,
          },
        });
      }
    },

    /* 科室查询 */
    *fetchBranchDeptList({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.departmentTree, reqData);
      if (data.code == 0) {
        const tree = utils.generateLabelValueList({
          list: data.data || [],
          labelIndex: 'name',
          valueIndex: 'name',
          keyIndex: 'no',
          isLeafIndex: 'hasChild',
          childIndex: 'children',
        });
        yield put({
          type: 'updateBranchDeptList',
          payload: {
            tree,
          },
        });
        setTimeout(() => {
          reqData.resolve(data);
        });
      }
    },
    /* 批量导出 */
    *fetchExportDisExcel({ payload: reqData }) {
      utils.download('/api/guide/exportdisexcel', reqData);
    },

    /* 病症特定搜索 */
    *queryDiseases({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.queryDiseases, reqData);
      yield put({ type: 'updateDiseasesList', payload: data });
    },

    /* 身体部位导出 */
    *exportDisBodyExcel({ payload: reqData }) {
      utils.download('/api/guide/exportbodyxls', reqData);
    },

    /* 季节病症删除 */
    *removeDisSeason({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.removeDisSeason, reqData);
      if (data && data.code == 0) {
        message.success('删除成功');
        const listData = yield call(Api.getDiseasesList, { parentDisId: reqData.seasonId });
        yield put({ type: 'updateDiseasesList', payload: listData.data });
      }
    },

    /* 同步科室医生 */
    *syncInfo({ payload }, { call }) {
      const { type } = payload;
      const dialogText = type === 'doctor' ? '医生' : '科室';
      const hide = message.loading('同步中', 0);
      const { data } = yield call(type === 'doctor' ? Api.syncDoctorInfo : Api.syncDeptInfo, payload);
      hide();
      if (data.code === 0) {
        message.success(`同步${dialogText}成功`);
      } else {
        message.error(`同步${dialogText}失败`);
      }
    },
    /* 同步科室医生头像 */
    *syncDocImg({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.syncDocImg, reqData.reqData);
      // hide();
      if (data.code === 0) {
        reqData.resolve();
        yield put({ type: 'saveImg', payload: data.data });
      } else {
        message.error('上传失败');
      }
    },
    *asyncManual({ payload: reqData }, { call, put }) {
      const hide = message.loading('同步中', 0);
      const dialogText = reqData.reqData.syncType === 'doctor' ? '医生' : '科室';
      const { data } = yield call(Api.asyncManual, reqData.reqData);
      if (data.code === 0) {
        reqData.resolve();
        yield put({ type: 'saveMannual', payload: data.data });
      } else {
        message.error(`同步${dialogText}失败`);
      }
      hide();
    },
    *getManualStatus({ payload: reqData }, { call, put }) {
      const dialogText = reqData.reqData.syncType === 'doctor' ? '医生' : '科室';
      const { data } = yield call(Api.getManualStatus, reqData.reqData);
      if (data.code === 0) {
        reqData.resolve();
        yield put({ type: 'saveResult', payload: data.data });
      } else {
        message.error(`同步${dialogText}失败`);
      }
    },
  },
};
