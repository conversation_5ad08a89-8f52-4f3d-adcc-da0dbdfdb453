import { buildConstMap } from '@/utils/utils';

export const TYPE = [
  { value: 'CNV', label: 'CNV-知情同意书' },
  { value: 'JHCAH', label: 'JHCAH知情同意书' },
  { value: 'JHDPSDDP', label: 'JHDP三代地贫知情同意书' },
  { value: 'JHYSYS', label: 'JHYS叶酸申请单' },
  { value: 'NIPTJHBB', label: 'NIPT家辉版本-知情同意书' },
  { value: 'PLUSJHBB', label: 'PLUS家辉版本-知情同意书' },
  { value: 'PEJHBB', label: '子痫前期申请单' },
  { value: 'JSXJWSZ', label: '脊髓性肌萎缩症基因检测知情同意书' },
  { value: 'FXS', label: '脆性X综合征（FXS）基因检测知情同意书' },
  { value: 'QWXZZCX', label: '全外显子组测序知情同意书' },
  { value: 'SMA', label: '脊髓性肌萎缩症（SMA）知情同意' },
  { value: 'DJYJWDJC', label: '单基因及位点检测知情同意书' },
  { value: 'RSTFISH', label: '染色体、Fish知情同意书' },
  { value: 'YCXER', label: '遗传性耳聋基因检测知情同意书' },
  { value: 'PANEL', label: 'panel单基因病诊断、产前诊断知情同意书' },
  { value: 'XNXGJKPG', label: '心脑血管健康风险评估知情同意书' },
  { value: 'XB', label: '星博知情同意书' },
  { value: 'KZMZBZY', label: '康泽门诊部专用同意书' },
];

export const CUSTOMER_TYPE = [
  { value: 1, label: '医院' },
  { value: 2, label: '代理' },
];

export const CUSTOMER_TYPE_MAP = buildConstMap(CUSTOMER_TYPE);
