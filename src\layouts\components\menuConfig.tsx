import React from 'react';
import { HomeOutlined, SettingOutlined, CreditCardOutlined, UserOutlined } from '@ant-design/icons';

const menuConfig = [
  {
    breadcrumb: '医院管理',
    path: '/hospital',
    icon: <i className="iconfont iconyiyuanguanli" />,
  },
  {
    breadcrumb: '专业知识库',
    path: '/dictionary',
    icon: <i className="iconfont iconzidian" />,
    routes: [
      {
        breadcrumb: '病种字典',
        path: '/dictionary/disease',
      },
      {
        breadcrumb: '问诊表题库',
        path: '/dictionary/crf',
      },
    ],
  },
  {
    breadcrumb: '问诊表',
    path: '/crfform',
    icon: <i className="iconfont iconCRFbiaodan" />,
  },
  {
    breadcrumb: '患教资料',
    path: '/PEduData',
    icon: <i className="iconfont iconhuanjiaoziliao" />,
  },
  {
    breadcrumb: '随访计划模板',
    path: '/template',
    icon: <i className="iconfont iconsuifangmoban" />,
  },
  {
    breadcrumb: '系统设置',
    path: '/syssetting',
    icon: <i className="iconfont iconshezhi" />,
    routes: [
      {
        breadcrumb: '资源管理',
        path: '/syssetting/resourcemng',
      },
    ],
  },
];

export default menuConfig;
