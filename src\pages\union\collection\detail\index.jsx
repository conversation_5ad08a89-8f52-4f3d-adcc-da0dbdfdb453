import React, { useEffect, useState } from 'react';
import { Form, Input, InputNumber, Button, Table, DatePicker, Modal, message, Popconfirm } from 'antd';
import queryString from 'query-string';
import { useAntdTable } from 'ahooks';
import { merge } from 'lodash';
import moment from 'moment';
import { connect } from 'umi';
import * as Api from '../service';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const Index = (props) => {
  // 调试代码：打印传入的props
  console.log('组件接收的props:', props);
  console.log('是否包含layout属性:', props.hasOwnProperty('layout'));
console.log('路由匹配信息:', props.route);
  const [addForm] = Form.useForm();
  const queryParams = queryString.parse(props.location.search);
  console.log('路由查询参数:', queryParams);
  
  const { 
    institutionId, 
    contractId, 
    contractNumber, 
    institutionName,
    institutionCode,
    contactsName,
    totalSettleMoney, 
    unSettleMoney,
    menuType = '' 
  } = queryParams;

  const { permissionData = {} } = props;
  const { btns = {} } = permissionData;
  console.log('权限数据:', btns);

  const [id, setId] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [list, setList] = useState([]);

  useEffect(() => {
    fetchList();
  }, []);

  const handleCancel = () => {
    setIsModalOpen(false);
    setId('');
    addForm.resetFields();
  };

  const handleEdit = async record => {
    addForm.setFieldsValue({
      ...record,
      returnDate: moment(record.returnDate),
    });
    setId(record.id);
    setIsModalOpen(true);
  };

  const onAddFinish = async values => {
    const url = id ? 'updateMoney' : 'addMoney';
    try {
      const data = await Api[url]({
        ...values,
        returnDate: moment(values.returnDate).format('YYYY-MM-DD'),
        institutionId,
        contractId,
        contractNumber,
        institutionName,
        id,
      });
      if (data.code === 0) {
        message.success('操作成功');
        addForm.resetFields();
        fetchList();
      }
    } catch (error) {
      console.error('提交出错:', error);
    }
    setIsModalOpen(false);
    setId('');
  };

  const handleDelete = async id => {
    const data = await Api.deleteMoney({
      id,
    });
    if (data.code === 0) {
      message.success(data.msg || '删除成功', 1, () => {
        fetchList();
      });
    }
  };

  const fetchList = async () => {
    try {
      console.log('获取列表数据，参数:', { institutionId, contractId, menuType });
      const data = await Api.selectListByParam({ institutionId, contractId, menuType });
      console.log('获取到的列表数据:', data.data);
      setList(data.data || []);
    } catch (error) {
      console.error('获取列表出错:', error);
    }
  };

  const columns = [
    {
      title: '回款日期',
      dataIndex: 'returnDate',
    },
    {
      title: '回款金额（元）',
      dataIndex: 'returnMoney',
      render: text => text.toFixed(2),
    },
    {
      title: '回款备注',
      dataIndex: 'notes',
    },
    {
      title: '更新人',
      dataIndex: 'updateUserName',
    },
    {
      title: '账号',
      dataIndex: 'updateUserAccount',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
    },
    {
      title: '操作',
      dataIndex: 'id',
      fixed: 'right',
      render: (id, record) => (
        <div className="edit-btn">
          {btns['/union/collection/edit'] && (
            <span onClick={() => handleEdit(record)}>
              编辑
            </span>
          )}
          {btns['/union/collection/delete'] && (
            <Popconfirm 
              title="确定要删除当前记录吗？" 
              onConfirm={() => handleDelete(id)} 
              okText="确认" 
              cancelText="取消"
            >
              <a href="#" style={{ color: '#FF4D4F' }}>删除</a>
            </Popconfirm>
          )}
        </div>
      ),
    },
  ];

  if (menuType && !['team'].includes(menuType)) {
    columns.pop();
  }

  return (
    <div className="g-page p-product-list">
      <div className="p-product-table">
        <div className="flex-box" style={{ justifyContent: 'space-between' }}>
          <div style={{ display: 'flex' }}>
            <h3 style={{ marginRight: '40px' }}>合同编号: {contractNumber}</h3>
            <h3 style={{ marginRight: '40px' }}>客户编号: {institutionCode}</h3>
            <h3 style={{ marginRight: '40px' }}>客户单位: {institutionName}</h3>
            <h3 style={{ marginRight: '40px' }}>销售负责人: {contactsName}</h3>
            <h3 style={{ marginRight: '40px', color: 'red' }}>累计待结算金额: ￥{totalSettleMoney}元</h3>
            <h3 style={{ marginRight: '40px', color: 'red' }}>累计待汇款金额: ￥{unSettleMoney}元</h3>
          </div>
          <div>
            {(!menuType || menuType === 'team') && btns['/union/collection/add'] && (
              <Button
                type="primary"
                onClick={() => setIsModalOpen(true)}
                style={{ marginRight: 10 }}
              >
                增加回款
              </Button>
            )}
            {(!menuType || menuType === 'team') && btns['/union/collection/export'] && (
              <Button type="primary" onClick={() => setIsModalOpen(true)}>
                导出明细
              </Button>
            )}
          </div>
        </div>
        
        <Table 
          rowKey="id" 
          dataSource={list} 
          columns={columns} 
          pagination={false} 
          scroll={{ x: 'max-content' }} 
        />
      </div>

      <Modal
        title={`${id ? '编辑' : '添加'}回款`}
        open={isModalOpen}
        onOk={() => addForm.submit()}
        onCancel={handleCancel}
        className="add-sampling-modal"
      >
        <Form form={addForm} onFinish={onAddFinish} {...formItemLayout}>
          <Form.Item name="returnDate" label="回款日期" rules={[{ required: true }]}>
            <DatePicker allowClear={false} />
          </Form.Item>
          <Form.Item name="returnMoney" label="回款金额" rules={[{ required: true }]}>
            <InputNumber min={0} placeholder="请输入" addonAfter="¥" precision={2} />
          </Form.Item>
          <Form.Item name="notes" label="备注">
            <TextArea placeholder="请输入" rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default connect(state => ({
  permissionData: state.root.permissionData,
}))(Index);