import { message } from 'antd';
import * as api from './api';

export default {
  namespace: 'roleMng',
  state: {},
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
    clear() {
      return {};
    },
  },
  effects: {
    *getrolesbypage({ payload }, { call, put }) {
      const data = yield call(api.getrolesbypage, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: { listData: data.data || [] },
        });
      }
    },
    *updaterole({ payload }, { call }) {
      const data = yield call(api.updaterole, payload);
      return data;
    },
    *deleteRole({ payload }, { call }) {
      const data = yield call(api.deleteRole, payload);
      return data;
    },
    *addrole({ payload }, { call }) {
      const data = yield call(api.addrole, payload);
      return data;
    },
    *getmenutree({ payload }, { call }) {
      const data = yield call(api.getmenutree, payload);
      return data;
    },
    *getrolebyid({ payload }, { call }) {
      const data = yield call(api.getrolebyid, payload);
      return data;
    },
  },
};
