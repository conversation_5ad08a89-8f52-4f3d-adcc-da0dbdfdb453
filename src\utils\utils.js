// @ts-ignore
import { fetch } from 'whatwg-fetch';
import { history } from 'umi';
import { message } from 'antd';
import queryString from 'query-string';
import sm4, { genKey } from '@haici/gmsm4';
import { DOMAIN, REQUEST_PARAM } from '../config/constant/constant';

const CryptoJS = require('./aes');
let loadingItem = null;

// 初始化loading队列
if (typeof window.HC_LOADING_QUEUE === 'undefined') {
  window.HC_LOADING_QUEUE = [];
}

export async function request(url = '', options = {}, showLoading = true, showError = false) {
  const backurl = encodeURIComponent(window.location.href);
  if (showLoading) {
    // 全局加载中遮盖可以在这里添加
    if (window.HC_LOADING_QUEUE.length <= 0) {
      loadingItem = message.loading('加载中', 0);
    }
    window.HC_LOADING_QUEUE.push('');
  }

  let data = {};
  let response;
  try {
    response = await fetch(`${DOMAIN}${url}`, {
      ...options,
      credentials: 'include',
      headers: {
        Accept: 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      },
    });
  } catch (e) {
    console.log('catch error in request', e);
    response = {
      status: -1,
    };
  }
  if (showLoading) {
    window.HC_LOADING_QUEUE.pop();
    if (window.HC_LOADING_QUEUE.length <= 0 && loadingItem) {
      loadingItem();
    }
  }

  if (response.status >= 200 && response.status < 300) {
    try {
      data = await response.json();
    } catch (e) {
      data = {
        code: -1,
        msg: '响应数据语法错误',
      };
    }
  } else {
    data = {
      code: -1,
      msg: `抱歉，请求失败，请检查网络后重试 (CODE:${response.status})`,
    };
  }
  if (data.code === 999) {
    // 添加登录失效的处理代码
    if (history.location.pathname !== '/login' && history.location.pathname !== '/download') {
      history.push({ pathname: '/login', search: queryString.stringify({ backurl }) });
    }
    // sleep等待
    // await sleep(10000);
  }

  if (data.code === 996) {
    history.push(`/resetPwd?resetCode=${data.data.resetCode}`);
  }

  if (showError && data.code !== 0 && data.code !== 999 && data.code !== 996) {
    // 全局报错可以在这里处理
    message.error(data.msg, 3);
  }

  return { data };
}

/**
 * Requests a URL, returning a promise.
 *
 * @param  {string} url       The URL we want to request
 * @param  {object} [options] The options we want to pass to "fetch"
 * @return {object}           An object containing either "data" or "err"
 */
export async function upload(url, options) {
  message.loading('加载中', 0);
  const response = await fetch(`${DOMAIN}${url}`, {
    ...options,
    credentials: 'include',
  });
  message.destroy();
  let data = {
    code: -1,
  };
  if (response.status >= 200 && response.status < 300) {
    data = await response.json();
  } else {
    data = {
      code: -1,
      msg: `发生未知错误(code:${response.status})`,
    };
  }
  if (data.code === 999) {
    history.push('/login');
  }
  if (data.code === 996) {
    history.push(`/resetPwd?resetCode=${data.data.resetCode}`);
  }
  return { data };
}

export async function download(url, param) {
  const queryStr = jsonToQueryString(param.data || {});
  const response = await fetch(`${DOMAIN}${url}${url.indexOf('?') >= 0 ? '&' : '?'}${queryStr}`, {
    credentials: 'include',
    method: 'GET',
    headers: {
      Accept: 'application/json, text/javascript, */*; q=0.01',
    },
  });
  let data = {
    code: -1,
  };

  if (response.status >= 200 && response.status < 300) {
    const contentType = response.headers.get('content-type');
    if (contentType.indexOf('application/json') > 0) {
      data = await response.json();
    } else {
      const a = document.createElement('a'); // eslint-disable-line
      response.blob().then(blob => {
        const $url = window.URL.createObjectURL(blob); // eslint-disable-line
        a.href = $url;
        a.download = `${new Date().getTime()}`;
        a.click();
        window.URL.revokeObjectURL($url); // eslint-disable-line
      });
      data = {
        code: 0,
      };
    }
  } else {
    data = {
      code: -1,
      error: response.statusText,
    };
  }

  return { data };
}
export async function downloadImg(url, param, filename) {
  const queryStr = jsonToQueryString(param.data || {});
  const response = await fetch(`${url}${url.indexOf('?') >= 0 ? '&' : '?'}${queryStr}`, {
    credentials: 'include',
    method: 'GET',
    headers: {
      Accept: 'application/json, text/javascript, */*; q=0.01',
    },
  });
  let data = {
    code: -1,
  };

  if (response.status >= 200 && response.status < 300) {
    const contentType = response.headers.get('content-type');
    if (contentType.indexOf('application/json') > 0) {
      data = await response.json();
    } else {
      const a = document.createElement('a'); // eslint-disable-line
      response.blob().then(blob => {
        const $url = window.URL.createObjectURL(blob); // eslint-disable-line
        a.href = $url;
        a.download = filename || `${new Date().getTime()}`;
        a.click();
        window.URL.revokeObjectURL($url); // eslint-disable-line
      });
      data = {
        code: 0,
      };
    }
  } else {
    data = {
      code: -1,
      error: response.statusText,
    };
  }

  return { data };
}

export async function downloadReport(url, options) {
  const response = await fetch(`${DOMAIN}${url}`, {
    ...options,
    credentials: 'include',
    headers: {
      mode: 'no-cors',
      Accept: 'application/json, text/javascript, */*; q=0.01',
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
  let data = {
    code: -1,
  };
  if (response.status >= 200 && response.status < 300) {
    const contentType = response.headers.get('content-type');
    const contentDisposition = decodeURIComponent(response.headers.get('content-disposition'));
    if (contentType.indexOf('application/json') >= 0 && contentDisposition.indexOf('filename') == -1) {
      data = await response.json();
      data.code != 0 ? message.error(data.msg || '下载失败') : message.success(data.msg || '下载失败');
    } else {
      const filename = contentDisposition.match(/filename=([^;]*)/)[1];
      const a = document.createElement('a'); // eslint-disable-line
      response.blob().then(blob => {
        const $url = window.URL.createObjectURL(blob); // eslint-disable-line
        a.href = $url;
        a.download = filename || `${new Date().getTime()}`;
        a.click();
        window.URL.revokeObjectURL($url); // eslint-disable-line
      });
      data = {
        code: 0,
      };
    }
  } else {
    data = {
      code: -1,
      error: response.statusText,
    };
  }
  if (data.code === 999) {
    history.push('/login');
  }
  return { data };
}

/**
 * 等待
 * @param time
 * @returns {Promise}
 */
export function sleep(time) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve();
    }, time);
  });
}

/**
 * 分转元
 * @param moneyString
 * @param mark
 * @returns {*}
 */
export const formatMoney = (moneyString = '0', mark = 100) => {
  try {
    const moneyNumber = parseFloat(moneyString);
    if (typeof moneyNumber === 'number' && typeof mark === 'number') {
      return parseFloat(String(moneyNumber / mark)).toFixed(2);
    }
    return 0;
  } catch (e) {
    console.log('error', e); // 缺失全局异常处理
    return 0;
  }
};

/**
 * 对象转键值对
 * @param json
 * @returns {*|number}
 */
export function jsonToQueryString(json = {}) {
  return queryString.stringify(json);
}

/**
 * search转对象
 * @param qs
 * @returns {*|number}
 */
export function queryStringToJson(qs = '') {
  return queryString.parse(qs);
}

/**
 * 修改键值对数组的键值名
 * 因为antd中的select、checkbox等组件的数据源仅支持label、value、children为键值名
 *
 * @param {*} param
 */
export function generateLabelValueList(param) {
  const { list, labelIndex, valueIndex, keyIndex, isLeafIndex, childIndex } = param;
  const resultList = [];
  if (list && list.length > 0) {
    list.forEach((item, key) => {
      resultList.push({
        ...item,
        label: labelIndex && item[labelIndex],
        value: valueIndex && item[valueIndex],
        key: keyIndex && item[keyIndex],
        isLeaf: !!(isLeafIndex && item[isLeafIndex]),
      });
      if (childIndex && item[childIndex] && item[childIndex].length > 0) {
        const child = generateLabelValueList({
          list: item[childIndex],
          labelIndex,
          valueIndex,
          keyIndex,
          isLeafIndex,
          childIndex,
        });
        if (child && child.length > 0) {
          resultList[key].children = child;
        }
      }
    });
  }
  return resultList;
}

/**
 * 给数值添加逗号（千分位表示法）
 * @param {*} value
 */
export function comma(value = '') {
  value = (value / 100).toFixed(2);
  const tmp = value.split('.');
  tmp[0] = tmp[0].replace(/\d{1,3}(?=(\d{3})+$)/g, i => `${i},`);
  return tmp.join('.');
}
/**
 * 给数值添加逗号（千分位表示法）
 * @param {*} value
 */
export function stringComma(value = '') {
  value = value.toString().replace(/(\d)(?=(\d{3})+$)/g, '$1,');
  return value;
}

/**
 * 异步加载json数据（静态JSON文件，跨域请求，只可以用于请求静态资源）
 * @param {*} url
 */
export async function loadStaticJSONData(url) {
  const response = await fetch(url, {
    mode: 'cors',
    headers: {
      Accept: 'application/json, text/javascript, */*; q=0.01',
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
  let data = {};
  if (response.status >= 200 && response.status < 300) {
    data = await response.json();
  }
  return data;
}

/**
 * sm4的加密方法
 * @param {*} hex
 */
export const sm4Encrypt = (word = '') => {
  const SM4_KEY = genKey('hnjhyy242');
  const endData = sm4.encryptWithECB(SM4_KEY, word);
  return endData;
};
/**
 * md5的加密方法
 * @param {*} hex
 */
export const md5Encrypt = (word = '') => {
  return CryptoJS.MD5(word).toString();
};
/**
 * aes的加密方法
 * @param {*} hex
 */
export const aesEncrypt = (word = '') => {
  const iv = CryptoJS.enc.Utf8.parse('ab6b2dfd69f5fb91');
  const key = CryptoJS.enc.Utf8.parse('1f17417b2227b356');
  const srcs = CryptoJS.enc.Utf8.parse(word);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.ciphertext.toString().toUpperCase();
};

/**
 * aes的解密方法
 * @param {*} hex
 */
export const aesDecrypt = (hex = '') => {
  const iv = CryptoJS.enc.Utf8.parse('ab6b2dfd69f5fb91');
  const key = CryptoJS.enc.Utf8.parse('1f17417b2227b356');
  const encryptedHexStr = CryptoJS.enc.Hex.parse(hex);
  const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  const decrypt = CryptoJS.AES.decrypt(srcs, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
};

/* eslint-disable guard-for-in */
// 过滤空对象
export const filterObj = obj => {
  const res = Object.keys(obj)
    .filter(item => !!obj[item])
    .reduce((pre, cur) => ({ ...pre, [cur]: obj[cur] }), {});

  return res;
};

export const getDownload = (url, name) => {
  const el = document.createElement('a');
  el.href = url;
  el.download = name;
  el.target = '_blank';
  document.body.appendChild(el);
  el.click();
  document.body.removeChild(el);
};

/**
 * 根据原始常量枚举数据生成 map，方便通过 value 来快速查询对象
 * @param {Object} constObj - 常量结构 { CHANG_LIANG: { label: 'Label', value: 'value' } }
 * @return {Object} constMap 返回由 constObj 中 value 为 key 的对象
 * */
export function buildConstMap(constObj) {
  let constMap = {};
  for (let key in constObj) {
    constMap[constObj[key].value] = { ...constObj[key] };
  }
  return constMap;
}

/* 中华人民共和国居民身份证号码验证 */
export function validateIdNo(idNo = '') {
  let val = idNo.replace(/(^\s*)|(\s*$)/g, '');
  val = val.toUpperCase();
  const len = (val || '').length;
  if (len === 0) {
    return false;
  }
  if (len !== 18 && len !== 15) {
    return false;
  }
  // 15位的身份证，验证了生日是否有效
  if (len === 15) {
    const year = val.substring(6, 8);
    const month = val.substring(8, 10);
    const day = val.substring(10, 12);
    const tempDate = new Date(year, parseFloat(month) - 1, parseFloat(day));
    if (tempDate.getYear() !== parseFloat(year) || tempDate.getMonth() !== parseFloat(month) - 1 || tempDate.getDate() !== parseFloat(day)) {
      return false;
    }
    return true;
  }
  // 18位的身份证，验证最后一位校验位
  if (len === 18) {
    // 身份证的最后一为字符
    const endChar = val.charAt(len - 1);
    val = val.substr(0, 17);
    const table = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const table2 = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    const cNum = [];
    for (let i = 0; i < val.length; i++) {
      cNum[i] = val.charAt(i);
    }
    let sum = 0;
    for (let i = 0; i < cNum.length; i++) {
      // 其中(cNum[i]-48)表示第i位置上的身份证号码数字值，table[i]表示第i位置上的加权因子，
      const num = cNum[i].charCodeAt(0);
      const num1 = parseInt(table[i], 10);
      sum = sum * 1 + (num - 48) * num1;
    }
    // 以11对计算结果取模
    const index = Number(sum % 11);
    // 根据模的值得到对应的校验码,即身份证上的最后一位为校验码
    const verfiyCode = table2[index];
    if (endChar !== verfiyCode) {
      return false;
    }
    return true;
  }
}
