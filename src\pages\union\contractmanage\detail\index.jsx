/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect, history } from 'umi';
import { Form, Card, Row, Col, Input, Select, DatePicker, InputNumber, Button, Table, Divider, Radio, Popconfirm, Upload, message, Image, Modal } from 'antd';
import { PlusOutlined, DeleteOutlined, EyeOutlined, CloseCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import queryString from 'query-string';
import * as CONSTANT from '@/config/constant/constant';
import { CON_MODAL_ENUM } from '../_data';
import Productmodal from './components/productmodal';
import Hospitalmodal from './components/hospitalmodal';
import Stepmodal from './components/stepmodal';
import './index.less';

const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;

const Index = props => {
  const { location, dispatch, institutionList = [], allPersonnel = [], detail = {}, btns = {} } = props;
  const { id = '', menuType } = queryString.parse(location.search);
  const [form] = Form.useForm();
  const reportMailingType = Form.useWatch('reportMailingType', form); // 邮寄费用类型
  const cooperationMode = Form.useWatch('cooperationMode', form); // 销售渠道
  const contractTypeValue = Form.useWatch('contractType', form); // 合作类型

  const [contractType, setContractType] = useState([]); // 合同类型options
  const [payway, setPayway] = useState([]); // 付款方式options
  const [method, setMethod] = useState([]); // 结算方式options
  const [mastfilds, setMastfilds] = useState([]); // 添加产品字段
  const [showAddPro, setShowAddPro] = useState(false);
  const [showProData, setShowProData] = useState({});
  const [showAddStep, setShowAddStep] = useState(false);
  const [showStepData, setShowStepData] = useState({});
  const [showAddHos, setShowAddHos] = useState(false);
  const [showHosData, setShowHosData] = useState({});
  const [canEdit, setCanEdit] = useState(!!!id);
  const [visible, setVisible] = useState(false); // 合同图片预览

  useEffect(() => {
    dispatch({ type: 'contract/getinstitutionbylist', payload: { menuType } });
    dispatch({ type: 'contract/getAllUser', payload: { menuType } });
    dispatch({ type: 'contract/getproducts', payload: { menuType } });
  }, []);
  useEffect(() => {
    if (id) {
      dispatch({
        type: 'contract/getDetail',
        payload: { id },
      }).then(res => {
        if (res) {
          form.resetFields();
        }
      });
    }
  }, [id]);

  const getMethodOptions = (list, value, settlementType) => {
    const newMethod = list.filter(v => v.value === value)[0]?.method || [];
    setMethod(newMethod);
    if (newMethod.length === 1) {
      form.setFieldsValue({ settlementType: newMethod[0].value });
      const newMastfilds = newMethod.filter(v => v.value === newMethod[0].value)[0]?.mastfilds || [];
      setMastfilds(newMastfilds);
    } else if (settlementType) {
      // 改变结算方式
      const newMastfilds = newMethod.filter(v => v.value === settlementType)[0]?.mastfilds || [];
      setMastfilds(newMastfilds);
    }
  };

  useEffect(() => {
    if (detail.cooperationMode) {
      // 改变合作模式
      const newContractType = CON_MODAL_ENUM.filter(v => v.value === detail.cooperationMode)[0]?.type || [];
      setContractType(newContractType);
      if (detail.contractType) {
        // 改变合作类型
        const newPayway = newContractType.filter(v => v.value === detail.contractType)[0]?.payway || [];
        setPayway(newPayway);
        if (newPayway.length === 1) {
          form.setFieldsValue({ payType: newPayway[0].value });
          getMethodOptions(newPayway, newPayway[0].value, detail.settlementType);
        } else if (detail.payType) {
          // 改变付款方式
          getMethodOptions(newPayway, detail.payType, detail.settlementType);
        }
      }
    }
  }, [detail.cooperationMode, detail.contractType, detail.payType, detail.settlementType]);

  const saveDetail = values => {
    dispatch({
      type: 'contract/save',
      payload: {
        detail: { ...detail, ...values },
      },
    });
  };

  const deletePro = (index, productId, item) => {
    let stepPro = [];
    (detail.step || []).forEach(v => {
      stepPro = [...stepPro, ...(v.products || [])];
    });
    if (item.sonProductId) {
      if (stepPro.filter(v => item.sonProductId === v.sonProductId).length > 0) {
        message.warning('阶梯计价方式中存在该产品，请核对后再操作！');
        return false;
      }
    } else {
      if (stepPro.filter(v => item.productId === v.productId).length > 0) {
        message.warning('阶梯计价方式中存在该产品，请核对后再操作！');
        return false;
      }
    }
    if (id) {
      dispatch({
        type: 'contract/checkdelete',
        payload: { contractId: id, productId },
      }).then(res => {
        if (res) {
          const relProducts = JSON.parse(JSON.stringify(detail.relProducts || []));
          (relProducts || []).splice(index, 1);
          saveDetail({ relProducts });
        }
      });
    } else {
      const relProducts = JSON.parse(JSON.stringify(detail.relProducts || []));
      (relProducts || []).splice(index, 1);
      saveDetail({ relProducts });
    }
  };
  const deleteStep = index => {
    const step = JSON.parse(JSON.stringify(detail.step || []));
    (step || []).splice(index, 1);
    saveDetail({ step });
  };
  const getsubproducts = parentId => {
    dispatch({ type: 'contract/getproducts', payload: { parentId, menuType } });
  };

  const deleteHos = index => {
    const institutions = JSON.parse(JSON.stringify(detail.institutions || []));
    (institutions || []).splice(index, 1);
    saveDetail({ institutions });
  };

  const beforeUpload = file => {
    // const isImage = file.type && file.type.indexOf('image') > -1;
    // if (!isImage) {
    //   message.error('请选择图片进行上传!');
    // }
    const size = file.size / 1024 / 1024 <= 50;
    if (!size) {
      message.error('文件大小不能超过50MB!');
    }
    // return isImage && size;
    return size;
  };
  const uploadOnChange = e => {
    message.loading('加载中', 0);
    const { response } = e.file;
    if (response) {
      message.destroy();
      let img = '';
      if (response.code != 0) {
        message.error('上传文件失败');
        return false;
      } else {
        name = (response.data || {}).fileName;
        img = (response.data || {}).url;
        const files = JSON.parse(JSON.stringify(detail.files || []));
        files.push({ name, url: img });
        saveDetail({ files });
      }
    }
  };
  const deleteImg = index => {
    const files = JSON.parse(JSON.stringify(detail.files || []));
    (files || []).splice(index, 1);
    saveDetail({ files });
  };

  const exportGrant = () => {
    dispatch({
      type: 'contract/exportGrant',
      payload: { id },
    });
  };

  const submit = () => {
    form.validateFields().then(values => {
      if (!detail.relProducts?.length) {
        message.error('请添加合同签约产品后提交！');
        return false;
      }
      if (mastfilds.includes('tieredSettlementPrice')) {
        if (!detail.step?.length) {
          message.error('请添加阶梯计价方式后提交！');
          return false;
        }
        values.step = JSON.stringify(detail.step || []);
      }
      if (cooperationMode == 'CM2' && !detail.institutions?.length) {
        message.error('请添加合同授权医院后提交！');
        return false;
      }
      values.signingDate = values?.signingDate?.format('YYYY-MM-DD');
      values.startDate = values.startDate.format('YYYY-MM-DD');
      values.endDate = values.endDate.format('YYYY-MM-DD');
      const params = JSON.parse(JSON.stringify(detail));
      // values.files = (params.files || []).join(';');
      values.files = JSON.stringify(params.files || []);
      (params.relProducts || []).forEach(v => {
        if (v.tieredSettlementPrice) {
          v.tieredSettlementPrice = JSON.stringify(v.tieredSettlementPrice);
        }
      });
      (params.institutions || []).forEach(v => {
        if (v.relContractId) {
          v.relContractId = v.relContractId.join(';');
        }
      });
      values.relProductsJSON = JSON.stringify(params.relProducts || []);
      if (values.cooperationMode != 'CM1') {
        values.institutionsJSON = JSON.stringify(params.institutions || []);
      }
      if (id) {
        values.id = id;
      }
      if (!values.reportMailingType) {
        values.reportMailingType = '1';
      }

      console.log(values);
      dispatch({
        type: id ? 'contract/updatecontract' : 'contract/addcontract',
        payload: values,
      }).then(res => {
        if (res) {
          message.success('操作成功！', 1, () => {
            if (id) {
              setCanEdit(false);
              history.goBack();
            } else {
              dispatch({
                type: 'contract/save',
                payload: { detail: {} },
              });
              setTimeout(() => {
                form.resetFields();
              }, 0);
            }
          });
        }
      });
    });
  };

  // const limitDecimals = (value) => {
  //   const reg = /^(\-)*(\d+)\.(\d\d).*$/;
  //   if(typeof value === 'string') {
  //      return !isNaN(Number(value)) ? value.replace(reg,'$1$2.$3') : ''
  //  } else if (typeof value === 'number') {
  //      return !isNaN(value) ? String(value).replace(reg,'$1$2.$3') : ''
  //  } else {
  //     return ''
  //  }
  // };

  return (
    <div className="g-page page-contractmanage-detail">
      <Form className="container" layout="vertical" form={form} scrollToFirstError onValuesChange={changedValues => saveDetail(changedValues)} initialValues={detail} disabled={!canEdit}>
        {id && (!menuType || menuType === 'team') ? (
          <Button
            type="link"
            style={{ textAlign: 'right', width: '100%', marginBottom: '0px' }}
            disabled={false}
            onClick={() => history.push(`/union/changerecord?moduleCode=com.anyi.his.vo.ContractRespVo&moduleId=${id}`)}
          >
            变更记录
          </Button>
        ) : null}
        <Card title="基本信息">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <FormItem label="合同编号" name="contractNumber" rules={[{ required: true }]}>
                <Input placeholder="请输入" />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="合同单位" name="assignedUnit" rules={[{ required: true }]}>
                <Select
                  placeholder="请选择"
                  showSearch
                  filterOption={(input, option) => {
                    return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  {(institutionList || []).map(v => (
                    <Option value={v.id} key={v.id}>
                      {v.institutionName}
                    </Option>
                  ))}
                </Select>
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="签订单位" name="signingUnit" rules={[{ required: true }]}>
                <Select
                  placeholder="请选择"
                  showSearch
                  filterOption={(input, option) => {
                    return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  {(institutionList || [])
                    .filter(v => (!menuType ? (v.institutionName || '').includes('家辉') : true))
                    .map(v => (
                      <Option value={v.id} key={v.id}>
                        {v.institutionName}
                      </Option>
                    ))}
                </Select>
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="销售渠道" name="cooperationMode" rules={[{ required: true }]}>
                <Select placeholder="请选择" options={CON_MODAL_ENUM} onChange={() => form.setFieldsValue({ contractType: null, payType: null, settlementType: null })} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="合同类型" name="contractType" rules={[{ required: true }]}>
                <Select placeholder="请选择" options={contractType} onChange={() => form.setFieldsValue({ settlementType: null })} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="合同状态" name="contractStatus" rules={[{ required: true }]}>
                <Select placeholder="请选择">
                  <Option value="1">已签约</Option>
                  <Option value="2">签约中</Option>
                  <Option value="3">已过期</Option>
                  <Option value="4">未签约（B2C）</Option>
                  <Option value="5">已作废</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="合同签订日期" name="signingDate">
                <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="合同有效开始日期" name="startDate" rules={[{ required: true }]}>
                <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem
                label="合同有效截止日期"
                name="endDate"
                rules={[
                  { required: true },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (getFieldValue('startDate') && value && moment(getFieldValue('startDate').format('YYYY-MM-DD')).valueOf() > moment(value.format('YYYY-MM-DD')).valueOf()) {
                        return Promise.reject(new Error('合同有效截止日期不能早于合同有效开始日期'));
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="销售人员" name="salesPersonnel">
                <Select
                  placeholder="请选择"
                  allowClear
                  showSearch
                  filterOption={(input, option) => {
                    return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  }}
                >
                  {(allPersonnel || []).map(v => (
                    <Option value={v.id} key={v.id}>
                      {v.name}
                    </Option>
                  ))}
                </Select>
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="保证金（元）" name="margin">
                <InputNumber placeholder="请输入" style={{ width: '100%' }} min={0} precision={2} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="保证金支付状态" name="marginPayStatus">
                <Select placeholder="请选择" allowClear>
                  <Option value="0">未支付</Option>
                  <Option value="1">已支付</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="采样包押金（元）" name="cxgMargin">
                <InputNumber placeholder="请输入" style={{ width: '100%' }} min={0} precision={2} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="采样包押金支付状态" name="cxgMarginPayStatus">
                <Select placeholder="请选择" allowClear>
                  <Option value="0">未支付</Option>
                  <Option value="1">已支付</Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Card title="签约产品及结算信息">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <FormItem label="付款方式" name="payType" rules={[{ required: true }]}>
                <Select placeholder="请选择" options={payway} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="结算方式" name="settlementType" rules={[{ required: true }]}>
                <Select placeholder="请选择" options={method} />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="结算周期" name="payCycle" rules={[{ required: true }]}>
                <Select placeholder="请选择">
                  <Option value="P1">月度结算</Option>
                  <Option value="P2">季度结算</Option>
                  <Option value="P3">年度结算</Option>
                  <Option value="P4">其他</Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
          <div className="my-form-title">
            <div className="label required">合同签约产品线</div>
            {canEdit ? (
              <Button
                type="primary"
                ghost
                onClick={() => {
                  if (!form.getFieldValue('settlementType')) {
                    message.warning('请先选择结算方式！');
                    return false;
                  }
                  setShowAddPro(true);
                }}
              >
                + 添加产品
              </Button>
            ) : null}
          </div>
          <Table
            style={{ marginBottom: '24px' }}
            columns={[
              {
                title: '产品线',
                dataIndex: 'productName',
              },
              {
                title: '子产品',
                dataIndex: 'sonProductName',
              },
              {
                title: '固定结算单价',
                dataIndex: 'fixedSettlementPrice',
                render: v => (v || v === 0 ? v.toFixed(2) : ''),
              },
              {
                title: '终端价',
                dataIndex: 'terminalPrice',
                render: v => (v || v === 0 ? v.toFixed(2) : ''),
              },
              {
                title: '结算比例',
                dataIndex: 'settlementRatio',
              },
              {
                title: '底价',
                dataIndex: 'basePrice',
                render: v => (v || v === 0 ? v.toFixed(2) : ''),
              },
              // {
              //   title: '阶梯结算价',
              //   dataIndex: 'tieredSettlementPrice',
              //   render: tieredSettlementPrice => (tieredSettlementPrice || []).map((v, i) => <div key={i}>{`${v.start}至${v.end}结算${v.price}`}</div>),
              // },
              {
                title: '承诺销量',
                dataIndex: 'promisedSales',
              },
              {
                title: '采样包押金单价',
                dataIndex: 'cxgUnitPrice',
                render: v => (v || v === 0 ? v.toFixed(2) : ''),
              },
              canEdit
                ? {
                    title: '操作',
                    render: (item, col, index) => (
                      <>
                        <a
                          onClick={() => {
                            col.editIndex = index;
                            setShowProData(col);
                            setShowAddPro(true);
                            getsubproducts(item.productId);
                          }}
                        >
                          编辑
                        </a>
                        <Divider type="vertical" />
                        <Popconfirm title="确定要删除当前产品吗？" onConfirm={() => deletePro(index, item.productId, item)}>
                          <a className="danger-link">删除</a>
                        </Popconfirm>
                      </>
                    ),
                  }
                : {},
            ]}
            dataSource={detail.relProducts || []}
            pagination={false}
          />
          {showAddPro ? (
            <Productmodal
              menuType={menuType}
              mastfilds={mastfilds}
              data={showProData}
              onCancel={() => {
                setShowAddPro(false);
                setShowProData({});
              }}
            />
          ) : null}
          {detail.relProducts?.length > 0 && mastfilds.includes('tieredSettlementPrice') ? (
            <>
              <div className="my-form-title">
                <div className="label required">阶梯计价方式</div>
                {canEdit ? (
                  <Button type="primary" ghost onClick={() => setShowAddStep(true)}>
                    + 添加计价
                  </Button>
                ) : null}
              </div>
              <Table
                style={{ marginBottom: '24px' }}
                columns={[
                  {
                    title: '计价产品',
                    dataIndex: 'products',
                    render: products =>
                      (products || []).map((v, i) => (
                        <div key={i}>
                          {v.productName}
                          {v.sonProductName ? `——${v.sonProductName}` : ''}
                        </div>
                      )),
                  },
                  {
                    title: '计价阶梯',
                    dataIndex: 'rules',
                    render: rules => (rules || []).map((v, i) => <div key={i}>{`${v.start}至${v.end}`}</div>),
                  },
                  {
                    title: '结算价',
                    dataIndex: 'rules',
                    render: rules => (rules || []).map((v, i) => <div key={i}>{(v.price || []).map(p => `${p.productName}-${p.money}元`).join('；')}</div>),
                  },
                  canEdit
                    ? {
                        title: '操作',
                        render: (item, col, index) => (
                          <>
                            <a
                              onClick={() => {
                                col.editIndex = index;
                                setShowStepData(col);
                                setShowAddStep(true);
                              }}
                            >
                              编辑
                            </a>
                            <Divider type="vertical" />
                            <Popconfirm title="确定要删除当前阶梯吗？" onConfirm={() => deleteStep(index)}>
                              <a className="danger-link">删除</a>
                            </Popconfirm>
                          </>
                        ),
                      }
                    : {},
                ]}
                dataSource={detail.step || []}
                pagination={false}
              />
              {showAddStep ? (
                <Stepmodal
                  data={showStepData}
                  onCancel={() => {
                    setShowAddStep(false);
                    setShowStepData({});
                  }}
                />
              ) : null}
            </>
          ) : null}
          {cooperationMode == 'CM2' ? (
            <>
              <div className="my-form-title">
                <div className="label required">授权医院</div>
                {canEdit ? (
                  <Button type="primary" ghost onClick={() => setShowAddHos(true)}>
                    + 添加医院
                  </Button>
                ) : (
                  <Button type="primary" ghost onClick={exportGrant} disabled={false}>
                    导出表格
                  </Button>
                )}
              </div>
              <Table
                columns={[
                  {
                    title: '医院名称',
                    dataIndex: 'institutionName',
                  },
                  // {
                  //   title: '医院等级',
                  //   dataIndex: 'assignedUnit',
                  // },
                  {
                    title: '省',
                    dataIndex: 'institutionProvinceName',
                  },
                  {
                    title: '市',
                    dataIndex: 'institutionCityName',
                  },
                  {
                    title: '关联合同编号',
                    dataIndex: 'relContractName',
                    render: item => (contractTypeValue == 'CT7' ? (item ? item : '未关联合同，请维护') : contractTypeValue == 'CT6' ? '直接代理无关联' : ''),
                  },
                  canEdit
                    ? {
                        title: '操作',
                        render: (item, col, index) => (
                          <>
                            {contractTypeValue == 'CT7' ? (
                              <>
                                <a
                                  onClick={() => {
                                    col.editIndex = index;
                                    if (!col.relContractId) {
                                      delete col.relContractId;
                                    }
                                    setShowHosData(col);
                                    setShowAddHos(true);
                                    if (contractTypeValue == 'CT7') {
                                      dispatch({ type: 'contract/getcontractbyinstitution', payload: { id: col.institutionId } });
                                    }
                                  }}
                                >
                                  编辑
                                </a>
                                <Divider type="vertical" />
                              </>
                            ) : null}
                            <Popconfirm title="确定要删除当前授权医院吗？" onConfirm={() => deleteHos(index)}>
                              <a className="danger-link">删除</a>
                            </Popconfirm>
                          </>
                        ),
                      }
                    : {},
                ]}
                dataSource={detail.institutions || []}
                pagination={false}
              />
              {showAddHos ? (
                <Hospitalmodal
                  contractTypeValue={contractTypeValue}
                  data={showHosData}
                  onCancel={() => {
                    setShowAddHos(false);
                    setShowHosData({});
                  }}
                />
              ) : null}
            </>
          ) : null}
        </Card>
        <Card title="其他信息">
          {contractTypeValue != 'CT4' ? (
            <FormItem label="报告邮寄费用：" required>
              <FormItem name="reportMailingType" rules={[{ required: true, message: '请选择报告邮寄费用' }]} noStyle>
                <Radio.Group placeholder="请选择">
                  <Radio value="1">免费</Radio>
                  <Radio value="2">收费</Radio>
                </Radio.Group>
              </FormItem>
              {reportMailingType == 2 ? (
                <>
                  请输入每单快递收费金额
                  <FormItem name="reportMailingFees" rules={[{ required: true, message: '请输入每单快递收费金额' }]} noStyle>
                    <InputNumber placeholder="请输入" style={{ width: '106px', margin: '0 8px' }} min={0} precision={2} />
                  </FormItem>
                  元
                </>
              ) : null}
            </FormItem>
          ) : null}
          <FormItem label="移动端样本信息完善功能" name="xcxSamplePrefectFlag" required>
            <Radio.Group
              placeholder="请选择"
              onChange={e => {
                if (e.target.value == '1') {
                  Modal.confirm({
                    title: '温馨提示',
                    content: '开启后将支持移动端小程序在扫码缴费订单详情里录入样本信息，是否确定开启？',
                    onCancel: () => {
                      form.setFieldsValue({ xcxSamplePrefectFlag: '0' });
                    },
                  });
                }
              }}
            >
              <Radio value="0">关闭</Radio>
              <Radio value="1">开启</Radio>
            </Radio.Group>
          </FormItem>
          <FormItem label="移动端样本寄送功能" name="useMailFlag" required>
            <Radio.Group
              placeholder="请选择"
              onChange={e => {
                if (e.target.value == '1') {
                  Modal.confirm({
                    title: '温馨提示',
                    content: '开启后将支持移动端小程序样本寄送功能，是否确定开启？',
                    onCancel: () => {
                      form.setFieldsValue({ useMailFlag: '0' });
                    },
                  });
                }
              }}
            >
              <Radio value="0">关闭</Radio>
              <Radio value="1">开启</Radio>
            </Radio.Group>
          </FormItem>
          <FormItem label="扫码缴费门诊缴费方式" name="xcxOutpatientPaymentFlag" required>
            <Radio.Group
              placeholder="请选择"
              onChange={e => {
                if (e.target.value == '1') {
                  Modal.confirm({
                    title: '温馨提示',
                    content: '开启后将支持在扫码开单缴费时通过上传门诊缴费凭证的方式直接开单，是否确定开启？',
                    onCancel: () => {
                      form.setFieldsValue({ xcxOutpatientPaymentFlag: '0' });
                    },
                  });
                }
              }}
            >
              <Radio value="0">不支持</Radio>
              <Radio value="1">支持</Radio>
            </Radio.Group>
          </FormItem>
          <div className="my-form-title">
            <div className="label">其他附件（营业执照、法人身份证、纸质合同照片或文档上传）：</div>
          </div>
          <div className="files-list">
            {/* <div style={{ display: 'none' }}>
              <Image.PreviewGroup preview={{ visible: visible || visible === 0, current: visible, onVisibleChange: vis => setVisible(vis) }}>
                {(detail.files || []).map((v, i) => (
                  <Image src={v} key={i} />
                ))}
              </Image.PreviewGroup>
            </div> */}
            {/* {(detail.files || []).map((v, i) => (
              <div className="image-box" key={i}>
                <img src={v} />
                <div className="action-box">
                  <EyeOutlined title="预览" onClick={() => setVisible(i)} />
                  {canEdit ? <DeleteOutlined title="删除" onClick={() => deleteImg(i)} /> : null}
                </div>
              </div>
            ))} */}
            {(detail.files || []).map((v, i) => (
              <div className="file-box" key={i}>
                <a href={v.url} target="_blank" rel="noreferrer">
                  {v.name}
                </a>
                {canEdit ? <CloseCircleOutlined title="删除" onClick={() => deleteImg(i)} /> : null}
              </div>
            ))}
            {canEdit ? (
              <Upload
                name="upfile"
                // accept="image/*"
                listType="picture-card"
                showUploadList={false}
                action={`${CONSTANT.DOMAIN}/api/files/uploadpic`}
                data={{
                  partnerId: 'merchant',
                  serviceType: 'test',
                }}
                beforeUpload={beforeUpload}
                onChange={uploadOnChange}
              >
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>上传</div>
                </div>
              </Upload>
            ) : null}
          </div>
          <FormItem label="备注：" name="notes">
            <TextArea placeholder="请输入" rows={5} />
          </FormItem>
        </Card>
        {!!menuType && menuType !== 'team' ? null : !canEdit ? (
          btns['/union/contractmanage/edit'] ? (
            <div className="footer">
              <Button type="primary" htmlType="submit" onClick={() => setCanEdit(true)} disabled={false}>
                编辑
              </Button>
            </div>
          ) : null
        ) : (
          <div className="footer">
            <Button
              onClick={() => {
                dispatch({
                  type: 'contract/save',
                  payload: { detail: {} },
                });
                if (window.history.length > 1) {
                  history.goBack();
                } else {
                  window.opener = null;
                  window.close();
                }
              }}
            >
              取消
            </Button>
            <Button type="primary" htmlType="submit" onClick={submit}>
              确定
            </Button>
          </div>
        )}
      </Form>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.contract,
  };
})(Index);
