import React, { Component } from 'react';
import { connect } from 'dva';
import { Row, Col, Input, Button, Table, Divider, Modal } from 'antd';
import moment from 'moment';
import queryString from 'query-string';
import './index.less';

class Index extends Component {
  state = {
    searchTxt: '',
  };
  componentDidMount() {
    this.query(1);
  }
  query = pageNum => {
    const { searchTxt = '' } = this.state;
    this.props.dispatch({
      type: 'roleMng/getrolesbypage',
      payload: { pageNum, identityName: searchTxt },
    });
  };
  preDelete = id => {
    Modal.confirm({
      title: '确认删除',
      content: (
        <div>
          <p>确定要删除当前角色吗？</p>
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.doDelete(id);
      },
    });
  };
  doDelete = async id => {
    this.props
      .dispatch({
        type: 'roleMng/deleteRole',
        payload: { identityId: id },
      })
      .then(res => {
        if (res.code == 0) {
          const { listData = {} } = this.props;
          const { currentPage = 1, recordList = [] } = listData;
          let pageNum = currentPage;
          if (status === 0 && recordList.length == 1 && currentPage > 1) {
            pageNum -= 1;
          }
          this.query(pageNum);
        }
      });
  };
  render() {
    const { listData = {} } = this.props;
    const { recordList = [], currentPage = 1, numPerPage = 10, totalCount = 0 } = listData;
    const columns = [
      {
        title: '编号',
        dataIndex: 'idText',
      },
      {
        title: '角色名称',
        dataIndex: 'identityName',
      },
      {
        title: '角色代码',
        dataIndex: 'identityCode',
      },
      {
        title: '角色描述',
        dataIndex: 'description',
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
      },
      {
        title: '操作',
        render: record => {
          return (
            <>
              <a
                onClick={() => {
                  this.props.history.push({
                    pathname: '/system/roles/detail',
                    search: queryString.stringify({ id: record.idText }),
                  });
                }}
              >
                编辑
              </a>
              <Divider type="vertical" />
              <a onClick={() => this.preDelete(record.idText)}>删除</a>
            </>
          );
        },
      },
    ];
    return (
      <div className="page-rolemng g-page">
        <div className="g-query-box">
          <Row gutter={[16, 24]}>
            <Col span={8}>
              <span className="query-lable">角色名称：</span>
              <Input placeholder="请输入" allowClear value={this.state.searchTxt} onChange={e => this.setState({ searchTxt: e.target.value })} />
            </Col>
            <Col span={8}>
              <Button type="primary" onClick={() => this.query(1)}>
                查询
              </Button>
            </Col>
          </Row>
        </div>
        <div className="container">
          <Button
            type="primary"
            onClick={() =>
              this.props.history.push({
                pathname: '/system/roles/detail',
              })
            }
          >
            +新建角色
          </Button>
          <Table
            columns={columns}
            dataSource={recordList}
            rowKey="id"
            pagination={{
              showSizeChanger: false,
              showQuickJumper: true,
              current: currentPage,
              pageSize: numPerPage,
              total: totalCount,
              showTotal: total => `共 ${total} 条`,
              onChange: page => {
                this.query(page);
              },
            }}
          />
        </div>
      </div>
    );
  }
}

export default connect(state => {
  return {
    ...state.roleMng,
  };
})(Index);
