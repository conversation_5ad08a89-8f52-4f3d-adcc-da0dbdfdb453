import React from 'react';
import { history as hashHistory } from 'umi';
import { connect } from 'dva';
import { <PERSON><PERSON>, Badge, DatePicker, Row, Col, Progress, Modal, message } from 'antd';

import moment from 'moment';

import * as CONSTANT from '../../../../config/constant/constant';
import Drawer from '../../../../components/drawer/Drawer';
import AnswerUserList from '../../components/AnswerUserList';
import * as utils from '../../../../utils/utils';
import { revertPublish } from '../../service';

import '../style.less';

const RangePicker = DatePicker.RangePicker;

class Survey extends React.Component {
  constructor(props) {
    super(props);
    this.reqData = {
      pageNum: 1,
    };
    this.tabsChange = e => {
      this.getQuestionLst(e);
    };
    this.modifyTime = () => {
      const { id, beginTime, endTime } = this;
      const { dispatch } = this.props;
      const { trait } = this.props;
      Modal.confirm({
        title: '你确定要修改本问卷的时间吗?',
        onOk() {
          dispatch({
            type: 'trait/modifyQuestionTime',
            payload: {
              beginTime: beginTime || trait.question.questionInfo.data.beginTime,
              endTime: endTime || trait.question.questionInfo.data.endTime,
              examId: id,
            },
          });
        },
      });
      setTimeout(() => {
        // 这里没有做promise。。
        this.setState({
          modifyTime: false,
        });
      }, 1000);
    };

    this.timeChange = value => {
      this.beginTime = moment(value[0]).format('YYYY-MM-DD');
      this.endTime = moment(value[1]).format('YYYY-MM-DD');
    };

    this.state = {
      modifyTime: false,
    };

    this.editQuestion = () => {
      hashHistory.push({
        pathname: '/trait/survey/edit',
        query: {
          id: utils.queryStringToJson(this.props.location.search).id,
        },
      });
    };

    this.preEdit = () => {
      const { trait = {} } = this.props;
      const info = trait.question.questionInfo.data || {};
      const that = this;
      if (info.status == 1) {
        const conf = Modal.confirm({
          title: '警告',
          content: '该问卷正在进行中，若修改问卷内容则之前的问卷结果将全部清空（可先到详情页导出问卷结果），确定修改？',
          onOk: async () => {
            conf.destroy();
            const { data = {} } = await revertPublish({ examId: info.examId });
            if (data.code == 0) {
              that.editQuestion();
            } else {
              message.error(data.msg || '系统错误');
            }
          },
        });
      } else {
        this.editQuestion();
      }
    };

    this.endVote = () => {
      // 终止投票
      const { id } = this;
      const { dispatch } = this.props;
      Modal.confirm({
        title: '您确定要终止问卷或投票吗?',
        onOk() {
          dispatch({
            type: 'trait/saveQuestion',
            payload: {
              status: '3',
              examId: id,
              jumpLst: true, // 是否需要跳到列表页
              jumpTabs: '3', // 默认跳到tabs页的key
            },
          });
        },
      });
    };

    this.copyToDraft = () => {
      // 复制到草稿箱
      this.props.dispatch({
        type: 'trait/copyQuestion',
        payload: {
          examId: this.id,
          jumpTabs: '0',
          status: '0',
        },
      });
    };

    this.releaseQuestion = () => {
      const info = this.props.trait.question.questionInfo.data;
      const { beginTime, endTime, scopeInvestigation, pushText = [] } = info;
      this.props.dispatch({
        type: 'trait/releaseQuestion',
        payload: {
          data: {
            examId: this.id,
            beginTime,
            endTime,
            scopeInvestigation,
            pushText,
          },
          status: moment().isAfter(beginTime) ? '1' : '2',
        },
      });
    };

    this.getAnswer = (examId, titleId, title) => {
      const { dispatch } = this.props;
      dispatch({
        type: 'trait/getQuestionAnsweruserList',
        payload: {
          examId,
          questionsNum: titleId,
          queryBeginDate: this.queryBeginDate,
          queryEndDate: this.queryEndDate,
        },
      });
      this.title = title;
      dispatch({
        type: 'root/openDrawer',
      });
    };

    this.exportQuestion = () => {
      this.props.dispatch({
        type: 'trait/getQuestionExport',
        payload: {
          examId: this.id,
        },
      });
    };
  }

  componentDidMount() {
    this.getYesterDay();
    const { dispatch, location, trait, getToday } = this.props;
    const { yesterday } = this.state;
    const info = trait.question.questionInfo.data ? trait.question.questionInfo.data : {};
    const query = utils.queryStringToJson(location.search);
    this.id = query.id;
    this.queryBeginDate = moment(info.beginTime).format('YYYY-MM-DD');
    // this.queryEndDate = info.endTime > info.beginTime ? yesterday : moment(info.endTime).format('YYYY-MM-DD'); // 判断结束日期是否大于开始日期
    this.queryEndDate = yesterday > info.beginTime ? yesterday : getToday;
    dispatch({
      type: 'trait/fetchQuestionInfo',
      payload: {
        examId: this.id,
        queryBeginDate:
          query.beginTime ||
          moment()
            .add(-1, 'day')
            .format('YYYY-MM-DD'),
        queryEndDate: query.endTime,
      },
    });
  }

  queryQuestionInfo(value) {
    this.queryBeginDate = moment(value[0]).format('YYYY-MM-DD');
    this.queryEndDate = moment(value[1]).format('YYYY-MM-DD');
    const { dispatch, location } = this.props;
    this.id = utils.queryStringToJson(location.search).id;
    dispatch({
      type: 'trait/fetchQuestionInfo',
      payload: {
        examId: this.id,
        queryBeginDate: this.queryBeginDate,
        queryEndDate: this.queryEndDate,
      },
    });
  }

  componentWillUnmount() {
    this.props.dispatch({
      type: 'trait/clear',
    });
  }

  getQuestionLst(status) {
    this.props.dispatch({
      type: 'trait/fetchQuestionLst',
      payload: {
        status,
        ...this.reqData,
      },
    });
  }
  getYesterDay() {
    this.setState({
      yesterday: moment()
        .add(-1, 'd')
        .format('YYYY-MM-DD'),
    });
    this.setState({ getToday: moment().format('YYYY-MM-DD') });
  }

  render() {
    const { trait, drawerStatus } = this.props;
    const { modifyTime, yesterday, getToday } = this.state;
    const info = trait.question.questionInfo.data ? trait.question.questionInfo.data : {};
    const answerList = trait.question.answerUser && trait.question.answerUser.data ? trait.question.answerUser.data.recordList : [];
    const getYesterday = yesterday > info.beginTime ? yesterday : getToday;
    const beginTime = this.beginTime ? this.beginTime : info.beginTime;
    const endTime = this.endTime ? this.endTime : info.endTime;
    const pushText = info.pushText
      ? JSON.parse(info.pushText).map(item => {
          let pushTime = item.bizValue;
          switch (item.pushType) {
            case '0':
              pushTime = `${pushTime}, 立即接收`;
              break;
            case '2':
              pushTime = `${pushTime}, 定时接收`;
              break;
            case '1':
              pushTime = `${pushTime}, 延时接收`;
              break;
            default:
              break;
          }
          if (item.pushParam) {
            switch (item.pushParam) {
              case '10:00':
                pushTime = `${pushTime}(上午10点)`;
                break;
              case '15:00':
                pushTime = `${pushTime}(下午3点)`;
                break;
              case '19:00':
                pushTime = `${pushTime}(晚上7点)`;
                break;
              case '10':
                pushTime = `${pushTime}(10分钟后)`;
                break;
              case '60':
                pushTime = `${pushTime}(1小时后)`;
                break;
              default:
                // pushTime = `${pushTime}(${item.pushParam / 24 / 60}天后)`;
                pushTime = `${pushTime}${isNaN(item.pushParam / 24 / 60) ? '' : `(${item.pushParam / 24 / 60}天后)`}`;
                break;
            }
          }
          item.pushTime = pushTime;
          return item;
        })
      : '';
    const tempBeginTime = info.beginTime;
    const tempEndTime = getYesterday;

    console.log(tempBeginTime, tempEndTime);
    console.log(this.queryBeginDate, this.queryEndDate);
    return (
      <div className="survey-threeLevel-page survey-detail-container">
        <Row type="flex" align="middle" justify="space-between" className="survey-detail-header">
          <Col span={14}>
            <p style={{ marginBottom: 28 }}>
              <span className="survey-title">{info.examTitle || ''}</span>
              {info.status ? (
                info.status == 0 ? (
                  <Badge status="success" text="草稿" />
                ) : info.status == 1 ? (
                  <Badge status="processing" text="正在进行" />
                ) : info.status == 2 ? (
                  <Badge status="default" text="未进行" />
                ) : (
                  <Badge status="success" text="已结束" />
                )
              ) : null}
            </p>

            <p className="survey-item">
              <span className="survey-item-label">调查范围：</span>
              {info.scopeInvestigation == 0
                ? '所有用户'
                : pushText
                ? pushText.map(item => {
                    if (pushText.length == 1) {
                      return item.pushTime;
                    } else {
                      return `${item.pushTime}; `;
                    }
                  })
                : ''}
            </p>
            <p className="survey-item">
              <span className="survey-item-label">发布单位：</span>
              <span>{info.releaseCompany || ''}</span>
            </p>
            <p className={!modifyTime ? 'survey-item' : 'f-none'}>
              <span className="survey-item-label">有效期：</span>
              <span>{beginTime}</span>
              <span className="table-header-split">至</span>
              <span>{endTime}</span>
              <a style={{ marginLeft: 10 }} onClick={() => this.setState({ modifyTime: true })}>
                修改
              </a>
            </p>
            {info.beginTime ? (
              <p className={modifyTime ? 'survey-item' : 'f-none'}>
                <span className="survey-item-label">有效期：</span>
                <RangePicker
                  format="YYYY-MM-DD"
                  onChange={value => this.timeChange(value)}
                  defaultValue={[moment(info.beginTime, 'YYYY-MM-DD'), moment(info.endTime, 'YYYY-MM-DD')]}
                  allowClear={false}
                  disabledDate={date => {
                    return (
                      date.valueOf() <
                      moment()
                        .add(-1, 'd')
                        .valueOf()
                    );
                  }}
                />
                <a style={{ marginLeft: 10 }} onClick={() => this.modifyTime()}>
                  确定
                </a>
                <a style={{ marginLeft: 10 }} onClick={() => this.setState({ modifyTime: false })}>
                  取消
                </a>
              </p>
            ) : null}

            <Row className="survey-item" type="flex" align="top">
              <Col className="survey-item-label">问卷描述：</Col>
              <Col style={{ wordBreak: 'break-word' }} span={18}>
                {/* <pre>{info.examDesc || ''}</pre> */}
                {(info.examDesc || '').split('\n').map((v, i) => (
                  <div key={i} style={{ textIndent: '4ch' }}>
                    {v}
                  </div>
                ))}
              </Col>
            </Row>
          </Col>
          <Col span={10}>
            <div className="ticket-panle">
              <div className="ticket-item-panle">
                <p>投票人数</p>
                <p className="ticket-value">{info.personCount || 0}</p>
              </div>
              <div className="ticket-item-panle">
                <p>总票数</p>
                <p className="ticket-value">{info.ticketCount || 0}</p>
              </div>
            </div>
          </Col>
          {info.status && info.status == 1 ? (
            <Button type="primary" className="end-voting-btn" onClick={() => this.endVote()}>
              终止投票
            </Button>
          ) : info.status && info.status == 3 ? (
            <Button type="primary" className="end-voting-btn" onClick={() => this.copyToDraft()}>
              复制到草稿箱
            </Button>
          ) : null}
        </Row>
        <Row className="survey-detail-btn">
          <Col>
            {info.queryBeginDate && (info.status == 3 || info.status == 1) ? (
              <p className="survey-item">
                <span className="survey-item-label">问卷回答时间：</span>
                <RangePicker
                  format="YYYY-MM-DD"
                  defaultValue={[moment(info.beginTime, 'YYYY-MM-DD'), moment(getYesterday, 'YYYY-MM-DD')]}
                  onChange={value => this.queryQuestionInfo(value)}
                  allowClear={false}
                  disabledDate={date => {
                    return (
                      date.valueOf() >=
                      moment()
                        .add('d')
                        .valueOf()
                    );
                  }}
                />
              </p>
            ) : null}
            {info.status == 0 || info.status == 1 || info.status == 2 || info.status == 3 ? (
              <Row style={{ marginTop: 30 }}>
                <Col>
                  {info.status == 0 || info.status == 1 ? (
                    <Button type="primary" style={{ marginRight: '30px' }} onClick={() => this.preEdit()}>
                      编辑
                    </Button>
                  ) : null}
                  {info.status == 0 || info.status == 2 ? (
                    <Button style={{ marginRight: '30px' }} onClick={() => this.releaseQuestion()}>
                      发布
                    </Button>
                  ) : null}
                  {info.status == 1 || info.status == 3 ? (
                    <Button type="primary" style={{ marginRight: '30px' }}>
                      <a
                        href={`${CONSTANT.DOMAIN}/api/question/getquestionexport?examId=${this.id}&queryBeginDate=${
                          this.queryBeginDate && this.queryEndDate ? this.queryBeginDate : tempBeginTime
                        }&queryEndDate=${this.queryEndDate || tempEndTime}`}
                      >
                        导出问卷结果
                      </a>
                    </Button>
                  ) : null}
                </Col>
              </Row>
            ) : null}
          </Col>
        </Row>
        {info.titleList
          ? info.titleList.map((item, key) => {
              return (
                <div className="survey-detail-panle" key={item.titleId}>
                  <p className="survey-detail-header">
                    <span style={{ fontSize: 14 }}>
                      {item.questionsType != 5 && `${item.titleNum}、`}
                      {item.dimensionName && <span>{item.dimensionName || ''}——</span>}
                      <span className="title">{item.questionsTitle || ''}</span>
                    </span>
                    <span className="questions-type">
                      {item.questionsType == '0'
                        ? '单选'
                        : item.questionsType == '1'
                        ? '多选'
                        : item.questionsType == '2'
                        ? '多行填空'
                        : item.questionsType == '3'
                        ? '打分题'
                        : item.questionsType == '5'
                        ? '段落说明'
                        : item.questionsType == '6'
                        ? '签名'
                        : item.questionsType == '7'
                        ? '下拉单选'
                        : item.questionsType == '8'
                        ? '下拉多选'
                        : item.questionsType == '9'
                        ? '手机号码'
                        : item.questionsType == '10'
                        ? '身份证'
                        : item.questionsType == '11'
                        ? '地址'
                        : item.questionsType == '13'
                        ? '姓名'
                        : item.questionsType == '14'
                        ? '日期选择'
                        : item.questionsType == '17'
                        ? '辖区'
                        : item.questionsType == '12'
                        ? '单行填空'
                        : null}
                    </span>
                    {item.questionsType != '5' && <span className="questions-required">{item.required == '1' ? '必填' : '非必填'}</span>}
                    {item.averageScore && item.questionsType != '5' && <span className="questions-average">平均分：{item.averageScore}</span>}
                    {item.averageScorePercent && <span className="questions-percent">满意率：{item.averageScorePercent}</span>}
                    {item.questionsType != '5' && (
                      <a onClick={() => this.getAnswer(info.examId, item.titleId, item.questionsTitle)} style={{ float: 'right' }}>
                        查看详情
                      </a>
                    )}
                  </p>

                  {item.questionsType != '5' &&
                    item.questionsType != '9' &&
                    item.questionsType != '10' &&
                    item.questionsType != '11' &&
                    item.questionsType != '12' &&
                    item.questionsType != '2' &&
                    item.questionsType != '6' &&
                    item.questionsType != '13' &&
                    item.questionsType != '14' &&
                    item.questionsType != '17' && (
                      <Row type="flex" align="top" justify="space-between" className="survey-detail-item">
                        <Col span={12}>选项</Col>
                        <Col span={4}>{info.investigationType == 1 ? '' : '分值'}</Col>
                        <Col span={4}>小计</Col>
                        <Col span={4}>占比</Col>
                      </Row>
                    )}
                  {item.questionsType != '5' &&
                    item.questionsType != '9' &&
                    item.questionsType != '10' &&
                    item.questionsType != '11' &&
                    item.questionsType != '12' &&
                    item.questionsType != '2' &&
                    item.questionsType != '6' &&
                    item.questionsType != '13' &&
                    item.questionsType != '14' &&
                    item.questionsType != '17' &&
                    item.optionList &&
                    item.optionList.map((item1, optIdx) => {
                      return item.questionsType != 2 &&
                        item.questionsType != 5 &&
                        item.questionsType != 6 &&
                        item.questionsType != 9 &&
                        item.questionsType != 10 &&
                        item.questionsType != 11 &&
                        item.questionsType != 12 &&
                        item.questionsType != 17 &&
                        !item.isAverage ? (
                        <div style={{ borderBottom: '1px #e9e9e9 solid' }} key={optIdx}>
                          <Row type="flex" align="top" justify="space-between" className="survey-detail-item">
                            <Col span={12}>{item1.optionContent}</Col>
                            <Col span={4}>{info.investigationType == 1 ? '' : item1.scoreStatus == 0 ? '不计分' : item1.score}</Col>
                            <Col span={4}>{item1.optionCount || 0}票</Col>
                            <Col span={4}>
                              {item1.percentage || '0%'}
                              {/* <Progress percent={Number(item1.percentage.split('.')[0].replace(/%/g, ''))} /> */}
                            </Col>
                          </Row>
                          {item1.imageUrl ? <img className="option-img" width={76} height={76} src={item1.imageUrl} alt="" /> : null}
                        </div>
                      ) : null;
                    })}
                  {item.titleScore &&
                  info.investigationType == 2 &&
                  item.questionsType != '5' &&
                  item.questionsType != '9' &&
                  item.questionsType != '10' &&
                  item.questionsType != '11' &&
                  item.questionsType != '12' &&
                  item.questionsType != '2' &&
                  item.questionsType != '6' &&
                  item.questionsType != '13' &&
                  item.questionsType != '14' &&
                  item.questionsType != '17' ? (
                    <Row align="top" className="survey-detail-item">
                      <Col span={4}>本题平均分：</Col>
                      <Col span={4}>{item.titleScore}</Col>
                    </Row>
                  ) : (
                    ''
                  )}
                </div>
              );
            })
          : null}

        <Drawer open={drawerStatus} className="transaction-drawer">
          <AnswerUserList list={answerList} title={this.title} />
        </Drawer>
      </div>
    );
  }
}

export default connect(state => {
  return {
    trait: state.trait,
    drawerStatus: state.root.drawerStatus,
  };
})(Survey);
