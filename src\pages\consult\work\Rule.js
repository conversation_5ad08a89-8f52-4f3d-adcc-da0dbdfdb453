import React, { Component } from 'react';
import { Modal, message, Input } from 'antd';
import * as Api from './api';

import styles from './index.less';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      maxNumber: 0,
    };
  }

  componentDidMount() {
    this.getMaxReceiveNumber();
  }

  handleSubmit = async () => {
    const { maxNumber } = this.state;
    if (!Number.isInteger(maxNumber * 1)) {
      message.error('请输入整数患者');
      return false;
    }
    const { period = {}, cancel, getUserList } = this.props;
    const { code, msg } = await Api.updateMaxReceiveNumber({ maxReceiveNumber: maxNumber, periodId: period.dictKey });
    if (code == 0) {
      cancel();
      getUserList();
      message.success('设置成功');
    } else {
      // message.error(msg || '设置失败');
    }
  };

  getMaxReceiveNumber = async () => {
    const { period = {} } = this.props;
    const { code, data = {} } = await Api.getMaxReceiveNumber({ periodId: period.dictKey });
    if (code == 0) {
      this.setState({ maxNumber: data.maxReceiveNumber || 0 });
    }
  };

  render() {
    const { show, period = {}, cancel } = this.props;
    const { maxNumber } = this.state;
    return (
      <Modal
        title="设置接诊规则"
        visible={show}
        onOk={this.handleSubmit}
        onCancel={() => {
          cancel();
          this.getMaxReceiveNumber();
        }}
      >
        <div className={styles.flexLine}>
          <div style={{ width: 120, textAlign: 'right' }}>阶段：</div>
          <div className={styles.flexItem}>{period.dictValue}</div>
        </div>
        <div className={styles.flexLine} style={{ marginTop: 15 }}>
          <div style={{ width: 120, textAlign: 'right' }}>转接规则：</div>
          <div className={styles.flexItem}>
            接满&nbsp;&nbsp;
            <Input maxLength={4} onChange={e => this.setState({ maxNumber: e.currentTarget.value })} value={maxNumber} style={{ width: 80 }} />
            &nbsp;&nbsp;患者转移
          </div>
        </div>
      </Modal>
    );
  }
}

export default Index;
