import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Tabs, <PERSON>r, Button } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';

import IIHOCSortable from '../deptSortable/components/IIHOCSortable';
import Cell from '../deptSortable/components/Cell';

import '../../microwebsite/style.less';

import * as utils from '../../../utils/utils';

const TabPane = Tabs.TabPane;
const FormItem = Form.Item;

export default connect(state => {
  return {
    hisList: state.register.hospital.list,
    deptTree: state.register.department.tree,
    list: state.register.sortable.doctor,
  };
})(
  Form.create()(
    class DoctorSortable extends React.Component {
      componentDidMount() {
        const { dispatch } = this.props;
        dispatch({
          type: 'register/hospitalList',
          next: 'departmentTree',
        });
      }

      componentWillUnmount() {
        const { dispatch } = this.props;
        dispatch({
          type: 'register/saveSortable',
          payload: {
            doctor: [],
          },
        });
      }

      departmentTree = payload => {
        const { dispatch, form } = this.props;
        const { setFieldsValue } = form;
        setFieldsValue({
          deptNo: [],
        });
        dispatch({
          type: 'register/departmentTree',
          payload,
        });
      };

      switchHospital = hisId => {
        const { dispatch, form } = this.props;
        const { resetFields } = form;
        resetFields();
        this.departmentTree({
          hisId,
        });
        history.push(`/register/doctorSortable?hisId=${hisId}`);
        dispatch({
          type: 'register/saveSortable',
          payload: {
            doctor: false,
          },
        });
      };

      doctorSortable = payload => {
        const { dispatch } = this.props;
        dispatch({
          type: 'register/doctorSortable',
          payload,
        });
      };

      reset = () => {
        const { dispatch, form } = this.props;
        form.resetFields();
        dispatch({
          type: 'register/saveSortable',
          payload: {
            doctor: false,
          },
        });
      };

      render() {
        const { hisList = [], deptTree = [], list = [], location, form } = this.props;
        if (!hisList || hisList.length == 0) return null;
        const { getFieldDecorator, validateFields } = form;
        const hisId = utils.queryStringToJson(location.search).hisId || (hisList && hisList.length > 0 && hisList[0].hisId);

        const SortHtml = IIHOCSortable({
          hisId,
          list,
          WrappedComponent: Cell,
          submitEffectType: 'register/submitDoctorSortable',
          btnIdentity: '/register/doctorSortable/edit',
        });

        return (
          <div className="page-doctor">
            <Tabs
              animated={false}
              defaultActiveKey={`${hisId}`}
              style={{ display: 'flex', flex: 'auto', flexDirection: 'column' }}
              onChange={id => {
                this.switchHospital(id);
              }}
            >
              {hisList.map(item => {
                return (
                  <TabPane tab={item.hisName} key={`${item.hisId}`}>
                    <div className="doctor-body">
                      <div className="doctor-option">
                        <Form
                          layout="inline"
                          onSubmit={e => {
                            e.preventDefault();
                            const $this = this;
                            validateFields((err, values) => {
                              if (!err) {
                                $this.doctorSortable({
                                  hisId: item.hisId,
                                  deptNo: values.deptNo && values.deptNo[values.deptNo.length - 1],
                                });
                              }
                            });
                          }}
                        >
                          <FormItem>
                            {getFieldDecorator('deptNo', {
                              rules: [{ required: true, message: '请选择科室' }],
                            })(<Cascader style={{ width: 211 }} placeholder="请选择科室" options={deptTree} displayRender={label => label[label.length - 1]} changeOnSelect showSearch />)}
                          </FormItem>
                          <FormItem>
                            <Button type="primary" size="default" htmlType="submit">
                              查询
                            </Button>
                          </FormItem>
                          <FormItem>
                            <Button onClick={this.reset} size="default">
                              重置
                            </Button>
                          </FormItem>
                        </Form>
                      </div>
                      <div className="doctor-list">
                        <div style={{ padding: '6px 0 26px', display: 'flex', justifyContent: 'center' }}>
                          <div style={{ paddingTop: 1 }}>
                            <Icon type="infocircle" style={{ fontSize: 14, color: '#3F969D', marginRight: 8 }} />
                          </div>
                          <div>操作说明：1.点击上方科室选择框，选择对应的科室进行查询；2.拖动医生到对应的位置保存，手机端将同步更新排列顺序。</div>
                        </div>
                        <SortHtml />
                      </div>
                    </div>
                  </TabPane>
                );
              })}
            </Tabs>
          </div>
        );
      }
    },
  ),
);
