import React from 'react';
import { Tooltip } from 'antd';

import './style.less';

export default class Cell extends React.Component {
  render() {
    const { text = '', isDragging, onClick, connectDragSource, connectDropTarget } = this.props;
    const opacity = isDragging ? 0 : 1;

    return connectDragSource(
      connectDropTarget(
        <div className="sort-cell" style={{ opacity }} onClick={onClick}>
          {text.length > 7 ? (
            <Tooltip placement="top" overlayStyle={{ width: 140 }} title={text}>
              <div
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  padding: '8.5px 16px',
                }}
              >
                {text}
              </div>
            </Tooltip>
          ) : (
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                padding: '8.5px 16px',
              }}
            >
              {text}
            </div>
          )}
        </div>,
      ),
    );
  }
}
