import * as request from '@/utils/request';

export function getAllRecordList(param) {
  return request.post('/api/consultation/getAllRecordList', { data: param }) || {};
}

export function getApplyInfoAndFile(param) {
  return request.post('/api/consultation/getApplyInfoAndFile', { data: param }) || {};
}

export function getStatusLine(param) {
  return request.post('/api/consultation/getStatusLine', { data: param }) || {};
}

export function auditingAction(param) {
  return request.post('/api/consultation/review', { data: param }) || {};
}

export function getAllUser(param) {
  return request.post('/api/team/getAllUsersGroup', { data: param }) || {};
}

export function cacelApply(param) {
  return request.post('/api/consultation/cacelApply', { data: param }) || {};
}

export function creatConsultationChat(param) {
  return request.postjson('/api/consultation/chat/creatConsultationChat', { data: param }) || {};
}

export function getAllianceinfo(param) {
  return request.post('/api/institution/get-by-page', { data: param }) || {};
}

export function getAllResult(param) {
  return request.post('/api/consultation/getAllResult', { data: param }) || {};
}

export function getAllUsersGroup(param) {
  return request.post('/api/team/getAllUsersGroup', { data: param }) || {};
}
