import React from 'react';
import update from 'immutability-helper';
import { findDOMNode } from 'react-dom';
import { connect } from 'dva';
import { Button, Modal, Input, Select } from 'antd';
import { Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { DragDropContext, DragSource, DropTarget } from 'react-dnd';
import HTML5Backend from 'react-dnd-html5-backend';
import Card from './Card';
import ItemTypes from './ItemTypes';

const confirm = Modal.confirm;
const warning = Modal.warning;
const Option = Select.Option;

const cardSource = {
  beginDrag(props) {
    return {
      id: props.id,
      index: props.index,
    };
  },
};

const cardTarget = {
  hover(props, monitor, component) {
    const dragIndex = monitor.getItem().index;
    const hoverIndex = props.index;
    if (dragIndex === hoverIndex) {
      return;
    }
    const hoverBoundingRect = findDOMNode(component).getBoundingClientRect(); // eslint-disable-line
    const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
    const clientOffset = monitor.getClientOffset();
    const hoverClientY = clientOffset.y - hoverBoundingRect.top;
    if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
      return;
    }
    if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
      return;
    }
    props.moveCard(dragIndex, hoverIndex);
    monitor.getItem().index = hoverIndex;
  },
};

export default function IIHOCSortable({ list = [], disabled = false, form }) {
  const Container = DropTarget(ItemTypes.CARD, cardTarget, connectDrop => ({
    connectDropTarget: connectDrop.dropTarget(),
  }))(
    DragSource(ItemTypes.CARD, cardSource, (connectDrag, monitor) => ({
      connectDragSource: connectDrag.dragSource(),
      isDragging: monitor.isDragging(),
    }))(Card),
  );

  class Sortable extends React.Component {
    constructor(props) {
      super(props);
      this.state = {
        list,
        disabled: true,
        modalVisible: false,
        detail: {},
        type: 'edit',
        secondTypeName: '',
        secondTypeId: '',
        typeName: 1,
        typeId: 1,
        newList: [],
      };
    }

    componentDidMount() {
      const newList = [];
      list.map(item => {
        if (item.subTypeList.length > 0) {
          item.subTypeList.map(subItem => {
            let param = { ...item };
            param.subTypeName = subItem.typeName;
            param.typeRemark = subItem.typeRemark;
            param.key = subItem.typeId;
            newList.push(param);
          });
        } else {
          item.key = item.typeId;
          newList.push(item);
        }
      });
      this.setState({ newList });
    }

    moveCard = (dragIndex, hoverIndex) => {
      if (!disabled) {
        const items = this.state.list;
        const dragCard = items[dragIndex];
        this.setState({
          disabled: false,
        });
        this.setState(
          update(this.state, {
            list: {
              $splice: [
                [dragIndex, 1],
                [hoverIndex, 0, dragCard],
              ],
            },
          }),
        );
      }
    };

    submitSortable = () => {
      const items = this.state.list;
      const { dispatch } = this.props;
      dispatch({
        type: 'microwebsite/submitArticleTypeSortable',
        payload: {
          idArray: items.map(item => `${item.typeId}`),
        },
      });
    };

    articleTypeDelete = typeId => {
      const { dispatch } = this.props;
      dispatch({
        type: 'microwebsite/articleTypeDelete',
        payload: {
          typeId,
          listQueryParam: form.getFieldsValue(),
        },
      });
    };

    handleModalCancel = () => {
      this.setState({ modalVisible: false });
    };

    handleModalOk = () => {
      const { typeName, secondTypeName, detail, type, secondTypeId } = this.state;
      if (!secondTypeName && !typeName) {
        Modal.warning({
          title: '提示',
          content: '请输入类型名称！',
        });
        return;
      }
      let pid = '';
      if (type == 'new') {
        pid = detail.subTypeList && detail.subTypeList.length > 0 ? secondTypeId : typeName;
      }
      if (detail.subTypeList && detail.subTypeList.length > 0) {
        pid = detail.typeId;
        detail.typeId = detail.subTypeList && detail.subTypeList.length > 0 ? secondTypeId : typeName;
      }
      // if (type != "new") {
      //   delete detail["typeId"];
      // }
      detail.type = type;
      detail.pid = pid;
      detail.typeName = secondTypeName;
      if (detail.subTypeName) {
        delete detail['subTypeName'];
      }
      delete detail['subTypeList'];
      const { dispatch } = this.props;
      dispatch({
        type: 'microwebsite/submitArticleTypeDetail',
        payload: {
          ...detail,
          listQueryParam: form.getFieldsValue(),
        },
      });
    };

    render() {
      const { typeList } = this.props;
      const { detail, type, newList, typeName, typeId, list } = this.state;
      let newTypeList = [...typeList];
      if (type == 'new' && newTypeList[0]?.typeId != 0) {
        newTypeList.unshift({
          hisId: 242,
          pid: 1,
          subTypeList: [],
          typeId: 1,
          typeName: '无',
        });
      }
      return (
        <div
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Modal title={`${this.state.type == 'new' ? '新增' : '编辑'}文章类型`} visible={this.state.modalVisible} onOk={this.handleModalOk} onCancel={this.handleModalCancel} okText="保存">
            <div style={{ display: 'flex', marginTop: 24, padding: '0 30px' }}>
              <div style={{ width: 95, textAlign: 'right', marginRight: 8 }}>类型名称：</div>
              {/* {((detail.subTypeList && detail.subTypeList.length > 0) ||
                this.state.type == "new") && (
                <Select
                  showSearch
                  style={{ width: 160, marginRight: 10 }}
                  disabled={
                    detail.subTypeList && detail.subTypeList.length > 0
                      ? true
                      : false
                  }
                  placeholder="请选择类型名称"
                  options={newTypeList}
                  value={typeId || typeName}
                  onChange={(value) => {
                    this.setState({
                      detail: {
                        ...this.state.detail,
                      },
                      typeName: value,
                      typeId: "",
                    });
                  }}
                >
                  {newTypeList &&
                    newTypeList.map((item) => {
                      return (
                        <Option value={item.typeId} key={item.typeId}>
                          {item.typeName}
                        </Option>
                      );
                    })}
                </Select>
              )} */}
              <Input
                style={{ width: 160 }}
                value={this.state.secondTypeName}
                placeholder={this.state.typeName != '' && this.state.typeName != 1 ? '请输入二级类型名称' : '请输入类型名称'}
                maxLength="10"
                onChange={e => {
                  this.setState({
                    secondTypeName: e.target.value,
                  });
                }}
              />
            </div>
            <div
              style={{
                display: 'flex',
                marginTop: 16,
                padding: '0 30px 10px 30px',
              }}
            >
              <div style={{ width: 104, textAlign: 'right', marginRight: 8 }}>说明：</div>
              <Input.TextArea
                style={{ width: 354, height: 82, resize: 'none' }}
                value={this.state.detail.typeRemark}
                maxLength="30"
                onChange={e => {
                  const $this = this;
                  $this.setState({
                    detail: {
                      ...$this.state.detail,
                      typeRemark: e.target.value,
                    },
                  });
                }}
              />
            </div>
          </Modal>
          <div
            style={{
              flex: 1,
              display: 'flex',
              flexWrap: 'wrap',
              alignItems: 'flex-start',
              alignContent: 'flex-start',
            }}
          >
            <div
              className="sort-article-type-card"
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
              }}
              onClick={() => {
                this.setState({
                  modalVisible: true,
                  detail: {},
                  type: 'new',
                  secondTypeName: '',
                  typeName: '',
                  typeId: 1,
                });
              }}
            >
              <Icon type="plus" style={{ color: '#E9E9E9', fontSize: 42 }} />
            </div>
            {(list || []).map((item, idx) => {
              return (
                <Container
                  key={item.key}
                  index={idx}
                  id={item.key}
                  text={item.typeRemark}
                  title={`${item.typeName}${item.subTypeName ? '：' + item.subTypeName : ''}`}
                  moveCard={this.moveCard}
                  editClick={() => {
                    let secondTypeName = [];
                    let secondTypeId = '';
                    if (item.subTypeList.length > 0) {
                      // item.subTypeList.map((item) => {
                      //   secondTypeName.push(item.typeName);
                      // });
                      secondTypeId = item.subTypeList[0].typeId;
                      // secondTypeName = secondTypeName.join("、");
                      secondTypeName = item.subTypeName;
                    } else {
                      secondTypeName = item.typeName;
                    }
                    this.setState({
                      modalVisible: true,
                      detail: item,
                      typeName: item.typeId,
                      secondTypeName: secondTypeName,
                      type: 'edit',
                      secondTypeId,
                      typeId: '',
                    });
                  }}
                  deleteClick={() => {
                    const $this = this;
                    confirm({
                      title: (
                        <div>
                          确定删除文章类型
                          <span style={{ color: '#f57f17' }}>{item.typeName}</span>
                          吗？
                        </div>
                      ),
                      content: '删除后无法再恢复，请确认是否删除该文章类型。',
                      onOk() {
                        $this.articleTypeDelete(item.typeId);
                      },
                    });
                  }}
                />
              );
            })}
          </div>
          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Button onClick={this.submitSortable} type="primary" style={{ marginRight: 16 }} size="default" disabled={disabled}>
              保存当前顺序
            </Button>
            <Button
              size="default"
              disabled={this.state.disabled || disabled}
              onClick={() => {
                this.setState({
                  list: typeList,
                  disabled: true,
                });
              }}
            >
              取消
            </Button>
          </div>
        </div>
      );
    }
  }

  return DragDropContext(HTML5Backend)(
    connect(state => {
      return {
        backId: state.microwebsite.sortable.backId,
        typeList: state.microwebsite.article.typeList,
      };
    })(Sortable),
  );
}
