/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select, DatePicker } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { ycxertemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, ycxertemplate: { ...ycxertemplate, ...payload } },
      },
    });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="申请单信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="样本类型">
                <Checkbox.Group value={ycxertemplate.field1 ? ycxertemplate.field1.split(',') : []} onChange={v => changeData({ field1: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="干血片">干血片</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="外周血">外周血</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="腔拭子">腔拭子</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="唾液">唾液</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="DNA">DNA</Checkbox>
                    </Col>
                    <Col span={18}>
                      <div className="flex-box">
                        <Checkbox value="其他">其他</Checkbox>
                        <div className="flex-box">
                          <Input placeholder="请输入" value={ycxertemplate.field2} onChange={e => changeData({ field2: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="耳聋家族史">
                <div className="flex-box">
                  <Radio.Group value={ycxertemplate.field3} onChange={e => changeData({ field3: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  {ycxertemplate.field3 === '有' ? (
                    <>
                      <div className="flex-box">
                        <div className="flex-shrink">，其中耳聋患者共</div>
                        <Input placeholder="请输入" value={ycxertemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
                      </div>
                      <div className="flex-box">
                        <div className="flex-shrink">名，分别与受检者关系</div>
                        <Input placeholder="请输入" value={ycxertemplate.field5} onChange={e => changeData({ field5: e.target.value })} />
                      </div>
                    </>
                  ) : null}
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="母亲孕期感染">
                <div className="flex-box">
                  <Radio.Group value={ycxertemplate.field6} onChange={e => changeData({ field6: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="父母近亲结婚">
                <div className="flex-box">
                  <Radio.Group value={ycxertemplate.field7} onChange={e => changeData({ field7: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="受检者耳部畸形（包括小耳、内耳畸形等）">
                <div className="flex-box">
                  <Radio.Group value={ycxertemplate.field8} onChange={e => changeData({ field8: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="受检者用药史（氨基糖苷类药物）">
                <div className="flex-box">
                  <Radio.Group value={ycxertemplate.field9} onChange={e => changeData({ field9: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="检测类型">
                <Checkbox.Group value={ycxertemplate.field10 ? ycxertemplate.field10.split(',') : []} onChange={v => changeData({ field10: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="双耳通过">双耳通过</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="双耳不通过">双耳不通过</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="单耳通过">单耳通过</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="未做">未做</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="其他">
                <TextArea placeholder="请输入" defaultValue={ycxertemplate.field11} onChange={e => changeData({ field11: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
