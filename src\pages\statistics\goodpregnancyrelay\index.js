import React, { Component } from 'react';
import { history as router } from 'umi';
import moment from 'moment';
import { connect } from 'dva';
import { Select, Row, Col, Button, Table, DatePicker, Input, Modal, Upload, message } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { PlusOutlined } from '@ant-design/icons';
import user from '@/models/user';
import * as Api from './api';
import styles from './index.less';

let request = true;
@connect(({ user = {} }) => {
  const { currentUser = {} } = user;
  return { currentUser };
})
@Form.create()
class Index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tableData: [], // 表格数据
      beginDate: moment(new Date() - 24 * 60 * 60 * 1000).format('YYYY/MM/DD'),
      endDate: moment(new Date()).format('YYYY/MM/DD'),
      currentPage: 1,
      totalCount: 0,
    };
    // 表单布局样式
    this.formItemLayout = {
      labelCol: {
        span: 6,
      },
      wrapperCol: {
        span: 14,
      },
    };
    // 表格表头和每列的渲染
    this.tableColumns = [
      {
        title: '被推荐人姓名',
        dataIndex: 'patName',
      },
      {
        title: '被推荐人手机号码',
        dataIndex: 'patMobile',
      },
      {
        title: '被推荐人身份证号码',
        dataIndex: 'medicalCode',
      },
      {
        title: '推荐人姓名',
        dataIndex: 'recommendName',
      },
      {
        title: '推荐人家系号',
        dataIndex: 'pid',
      },
      {
        title: '推荐人手机号码',
        dataIndex: 'recommendMobile',
      },
      {
        title: '推荐日期',
        dataIndex: 'createTime',
      },
      {
        title: '推荐备注',
        dataIndex: 'extFields',
      },
    ];
  }

  componentDidMount() {
    this.getTableData(1);
  }

  // eslint-disable-next-line react/no-deprecated
  componentWillUpdate(newProps) {
    if (newProps.currentUser.id && request) {
      this.getTableData(1, newProps.currentUser.id);
    }
  }

  getTableData = async (pageNum, id, input) => {
    request = false;
    const timeQuantum = this.getQueryParam(); // 获取关联时间数据
    const { searchTxt, pageSize } = this.state;
    const param = {
      search: input ?? searchTxt,
      numPerPage: 10,
      pageNum: pageNum || 1,
    };
    if (timeQuantum.transferTimeStart) {
      param.beginDate = timeQuantum.transferTimeStart;
      param.endDate = timeQuantum.transferTimeEnd;
    } else {
      param.beginDate = moment(new Date() - 24 * 60 * 60 * 1000).format('YYYY-MM-DD');
      param.endDate = moment(new Date()).format('YYYY-MM-DD');
    }
    const { code, data = {} } = await Api.getRecommendList(param);
    if (code == 0) {
      const { recordList = [], totalCount } = data;
      this.setState({
        tableData: recordList,
        totalCount,
        currentPage: pageNum || 1,
      });
    }
  };

  // 获取查询参数
  getQueryParam = () => {
    const param = {};
    const {
      form: { getFieldsValue },
    } = this.props;
    const value = getFieldsValue();
    const { transferTime = ['', ''] } = value;
    if (transferTime && transferTime[0] && transferTime[1]) {
      param.transferTimeStart = transferTime[0] && moment(transferTime[0]).format('YYYY-MM-DD');
      param.transferTimeEnd = transferTime[1] && moment(transferTime[1]).format('YYYY-MM-DD');
      delete param.transferTime;
    }
    return param;
  };

  // 大于当前日期不能选
  disabledDate = time => {
    if (!time) {
      return false;
    }
    return time > moment();
  };

  render() {
    const {
      form,
      form: { getFieldDecorator },
    } = this.props;
    const { tableData = [], totalCount, currentPage, beginDate, endDate } = this.state;
    console.log(this.state.searchTxt);
    return (
      <div id="goodpregnancyrelay">
        <div className={styles.searchModule}>
          <Form>
            <Row>
              <Col span={9}>
                <Form.Item {...this.formItemLayout} label="推荐日期">
                  {getFieldDecorator('transferTime', {
                    initialValue: [moment(beginDate, 'YYYY/MM/DD'), moment(endDate, 'YYYY/MM/DD')],
                  })(<DatePicker.RangePicker allowClear={false} format="YYYY-MM-DD" disabledDate={this.disabledDate} style={{ width: '100%' }} />)}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Button type="primary" style={{ marginLeft: '12%' }} onClick={() => this.getTableData()}>
                  查询
                </Button>
                <Button
                  style={{ marginLeft: 30 }}
                  onClick={() => {
                    form.resetFields();
                    this.setState({ searchTxt: '' });
                    this.getTableData('', '', '');
                  }}
                >
                  重置
                </Button>
              </Col>
            </Row>
          </Form>
        </div>
        <div className="antd-pro-pages-statistics-goodpregnancyrelay-index-operArea">
          <div className="antd-pro-pages-statistics-goodpregnancyrelay-index-left">
            <Input.Search
              value={this.state.searchTxt}
              placeholder="请输入 家系号/姓名/被推荐人姓名"
              enterButton
              maxLength={10}
              style={{ width: 300 }}
              onSearch={() => {
                this.getTableData();
              }}
              onChange={val => this.setState({ searchTxt: val.target.value })}
            />
          </div>
        </div>
        <div className={styles.right}>
          <span>
            合计推荐：<span className={styles.selTip}>{totalCount}</span> 人
          </span>
        </div>
        <Table
          // key={tableData.currentPage}
          style={{ marginTop: 16, background: '#fff', margin: '20px' }}
          dataSource={tableData}
          columns={this.tableColumns}
          rowKey={(record, index) => {
            return record.id;
          }}
          pagination={{
            total: totalCount || 0,
            showTotal: total => `共 ${total} 条`,
            onChange: pageNum => {
              this.getTableData(pageNum);
            },
            current: currentPage,
          }}
        />
      </div>
    );
  }
}

export default Index;
