import React from 'react';
import { connect } from 'dva';
import { Button, Input } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import IIHOCSortable from './components/IIHOCSortable';
import '../style.less';

const FormItem = Form.Item;

export default connect(state => {
  return {
    list: state.microwebsite.article.typeList,
  };
})(
  Form.create()(
    class ArticleTypeList extends React.Component {
      state = {
        disabled: false,
      };

      componentDidMount() {
        this.articleTypeList();
      }

      componentWillUnmount() {
        this.props.dispatch({
          type: 'microwebsite/saveArticle',
          payload: {
            typeList: [],
            list: {},
            detail: {},
          },
        });
      }

      articleTypeList = () => {
        const { dispatch, form } = this.props;
        const { typeName } = form.getFieldsValue();
        this.setState({
          disabled: !!typeName,
        });
        dispatch({
          type: 'microwebsite/articleTypeList',
          payload: {
            typeName,
          },
        });
      };

      reset = () => {
        this.props.form.resetFields();
        this.articleTypeList();
      };

      render() {
        const { list = [], form } = this.props;
        const { getFieldDecorator, getFieldsValue } = form;

        const SortHtml = IIHOCSortable({ list, form, disabled: this.state.disabled });

        return (
          <div className="page-article-type">
            <div className="article-type-body">
              <div className="article-type-option">
                <Form
                  layout="inline"
                  onSubmit={e => {
                    e.preventDefault();
                    this.articleTypeList({
                      ...getFieldsValue(),
                    });
                  }}
                >
                  <FormItem label="类型名称" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('typeName', {})(<Input style={{ width: 211 }} placeholder="请输入" />)}
                  </FormItem>
                  <FormItem>
                    <Button type="primary" size="default" htmlType="submit">
                      查询
                    </Button>
                  </FormItem>
                  <FormItem>
                    <Button onClick={this.reset} size="default">
                      重置
                    </Button>
                  </FormItem>
                </Form>
              </div>
              <div className="article-type-list">
                <div style={{ padding: '6px 0 26px', display: 'flex', justifyContent: 'center' }}>
                  <div style={{ paddingTop: 1 }}>
                    <Icon type="infocircle" style={{ fontSize: 14, color: '#3F969D', marginRight: 8 }} />
                  </div>
                  <div>操作说明：1、请拖动对象到相应的位置并保存，手机端将同步更新排列顺序。</div>
                </div>
                <SortHtml />
              </div>
            </div>
          </div>
        );
      }
    },
  ),
);
