import React, { Component, Fragment } from 'react';
import { List, Input, Radio, Modal, message, Button } from 'antd';
import * as Api from './api';

import styles from './consultationopinion.less';
const { TextArea } = Input;
class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      opinionText: '', //填写内容
    };
  }

  shouldComponentUpdate(nextProps, nextState) {
    const { content, status } = this.props;
    const { opinionText } = this.state;
    if (nextProps.content != content || nextProps.status != status || nextState.opinionText != opinionText) {
      return true;
    }
    return false;
  }

  changeInput = e => {
    this.setState({ opinionText: e.target.value });
  };

  cancel = val => {
    const { opinionText } = this.state;
    this.props.onCancel(val, opinionText); //取消回调
    this.setState({
      opinionText: '',
    });
  };

  footer = () => {
    const { content } = this.props;
    if (content != '') {
      return [
        <Button type="primary" onClick={() => this.cancel(false)}>
          关闭
        </Button>,
      ];
    } else {
      return [
        <Button onClick={() => this.cancel(false)}>取消</Button>,
        <Button type="primary" onClick={() => this.cancel(true)}>
          提交
        </Button>,
      ];
    }
  };

  render() {
    const { viewStyle, title, name, content, status } = this.props;
    const { opinionText } = this.state;
    return (
      <Modal title={title} visible={status} footer={this.footer()} onCancel={() => this.cancel(false)}>
        <div className={styles.title}>
          <span>*</span>
          {name}
        </div>
        <TextArea rows={10} maxLength={1000} value={content != '' ? content : opinionText} onChange={this.changeInput} disabled={content && content.length > 0 ? true : false} />
        <div className={styles.textNum}>{content != '' ? content.length : opinionText.length}/1000</div>
      </Modal>
    );
  }
}

export default Index;
