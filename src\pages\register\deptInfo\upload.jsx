import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Tabs, Upload, Button } from 'antd';
import { Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import loading from '../../../components/loading/Loading';
import * as CONSTANT from '../../../config/constant/constant';
import * as utils from '../../../utils/utils';

const TabPane = Tabs.TabPane;
const Dragger = Upload.Dragger;

export default connect(state => {
  return {
    hisList: state.register.hospital.list,
  };
})(
  class DepartmentUpload extends React.Component {
    state = {
      status: 1,
      errorText: '操作失败',
    };

    componentDidMount() {
      const { dispatch, hisList = [] } = this.props;
      if (!hisList || hisList.length == 0) {
        dispatch({
          type: 'register/hospitalList',
        });
      }
    }

    render() {
      const { hisList = [], location, dispatch } = this.props;
      if (!hisList || hisList.length == 0) return null;
      const hisId = utils.queryStringToJson(location.search).hisId;
      return (
        <div className="page-department">
          <Tabs
            animated={false}
            defaultActiveKey={`${hisId || (hisList[0] && hisList[0].hisId)}`}
            style={{ display: 'flex', flex: 'auto', flexDirection: 'column' }}
            onChange={() => {
              this.setState({
                status: 1,
              });
            }}
          >
            {hisList.map(item => {
              return (
                <TabPane tab={item.hisName} key={`${item.hisId}`}>
                  {this.state.status == 1 ? (
                    <div style={{ flex: 1, margin: 24, borderRadius: 4, background: '#fff', textAlign: 'center' }}>
                      <div style={{ padding: '26px 0', display: 'flex', justifyContent: 'center' }}>
                        <div style={{ paddingTop: 1 }}>
                          <Icon type="infocircle" style={{ fontSize: 14, color: '#3F969D', marginRight: 8 }} />
                        </div>
                        <div>
                          此功能只支持使用科室模板编辑的内容，如果您还没有模板，请
                          <a
                            href="./rBAEiFmWZPWAfkpWAAAkaLuPTN855.xlsx" // eslint-disable-line
                          >
                            点击下载
                          </a>
                        </div>
                      </div>
                      <div style={{ margin: '116px 0 180px', width: 380, height: 194, display: 'inline-block' }}>
                        <Dragger
                          name="idFile"
                          multiple={false}
                          showUploadList={false}
                          action={`${CONSTANT.DOMAIN}/api/deptinfo/batchuploadlist?hisId=${item.hisId}&showMode=register`} // eslint-disable-line
                          onChange={info => {
                            loading.show();
                            const { response } = info.file;
                            if (response) {
                              loading.destroy();
                              if (response.code == 0) {
                                this.setState({
                                  status: 2,
                                });
                              } else {
                                this.setState({
                                  status: 3,
                                  errorText: response.msg || '操作失败',
                                });
                              }
                            }
                          }}
                        >
                          <div>
                            <Icon type="inbox" style={{ fontSize: 40, color: '#3F969D' }} />
                          </div>
                          <p style={{ marginTop: 24 }} className="ant-upload-text">
                            点击这里上传
                          </p>
                        </Dragger>
                      </div>
                    </div>
                  ) : this.state.status == 2 ? (
                    <div style={{ flex: 1, margin: 24, borderRadius: 4, background: '#fff', textAlign: 'center' }}>
                      <div style={{ margin: '168px 0 155px', width: 126, height: 240, display: 'inline-block' }}>
                        <div>
                          <Icon type="hook-circle-o" style={{ fontSize: 126, color: '#3F969D' }} />
                        </div>
                        <div style={{ marginTop: 37, fontSize: 18, color: '#404040' }}>成功提交</div>
                        <div style={{ marginTop: 19 }}>
                          <Button
                            onClick={() => {
                              dispatch({
                                type: 'register/saveDepartment',
                                payload: {
                                  list: [],
                                  subList: [],
                                  detail: {},
                                },
                              });
                              history.push(`/register/deptInfo?hisId=${item.hisId}`);
                            }}
                          >
                            返回科室列表
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : this.state.status == 3 ? (
                    <div style={{ flex: 1, margin: 24, borderRadius: 4, background: '#fff', textAlign: 'center' }}>
                      <div style={{ margin: '168px 0 155px', width: 150, height: 240, display: 'inline-block' }}>
                        <div>
                          <Icon type="sign-circle-o" style={{ fontSize: 126, color: '#F56A00' }} />
                        </div>
                        <div style={{ marginTop: 44, fontSize: 18, color: '#404040' }}>文件异常</div>
                        <div style={{ marginTop: 19, fontSize: 14, color: '#F56A00', textAlign: 'center' }}>
                          <div style={{ display: 'inline-block', textAlign: 'left' }}>{this.state.errorText}</div>
                        </div>
                        <div style={{ marginTop: 23 }}>
                          <Button
                            onClick={() => {
                              this.setState({
                                status: 1,
                              });
                            }}
                          >
                            重新添加
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : null}
                </TabPane>
              );
            })}
          </Tabs>
        </div>
      );
    }
  },
);
