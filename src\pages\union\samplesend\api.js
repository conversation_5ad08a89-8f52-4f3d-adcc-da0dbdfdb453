import * as request from '@/utils/request';

//样本寄送列表
export const queryMailOrderList = (param = {}) => request.post('/api/mail/getmailorderlist', { data: param });

// 客户列表
export const getinstitutionbylist = (param = {}) => request.post('/api/institution/get-by-list', { data: param });
// 产品列表
export const getproducts = (param = {}) => request.post('/api/product/get-products', { data: param });


// 模拟图片中的分页数据
export const getDetailData = () => ({
    code: 0,
    data: {
      records: [
        {
          productLine: "NIPT",
          subproductLine: "NIPT-1",
          sampleNumber: "JH21321322",
          
          name: "张三",
          createTime: "2025-02-05 09:15"
        },{
          productLine: "NIPT",
          subproductLine: "NIPT-1",
          sampleNumber: "JH21321326",
          name: "张三",
          createTime: "2025-02-05 09:15"
        },
        ,{
          productLine: "NIPT",
          subproductLine: "NIPT-1",
          sampleNumber: "JH21321325",
          name: "张三",
          createTime: "2025-02-05 09:15"
        },
        // 添加更多符合图片的数据...
      ]
     
    }
  });
