/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select, DatePicker } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { kzmzbzytemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, kzmzbzytemplate: { ...kzmzbzytemplate, ...payload } },
      },
    });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="其他信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item label="送检方条码">
                <Input placeholder="请输入" value={kzmzbzytemplate.field1} onChange={e => changeData({ field1: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="孕周（周、天）">
                <div className="flex-box">
                  <Input placeholder="请输入周" value={kzmzbzytemplate.field3} onChange={e => changeData({ field3: e.target.value })} />
                  <Input placeholder="请输入天" value={kzmzbzytemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="临床诊断">
                <TextArea placeholder="请输入" defaultValue={kzmzbzytemplate.field2} onChange={e => changeData({ field2: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="唐筛信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={12}>
              <Form.Item label="出生日期">
                <DatePicker
                  format="YYYY-MM-DD"
                  style={{ width: '100%' }}
                  value={kzmzbzytemplate.field5 ? moment(kzmzbzytemplate.field5) : null}
                  onChange={(date, dateString) => changeData({ field5: dateString })}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="B超日期">
                <DatePicker
                  format="YYYY-MM-DD"
                  style={{ width: '100%' }}
                  value={kzmzbzytemplate.field6 ? moment(kzmzbzytemplate.field6) : null}
                  onChange={(date, dateString) => changeData({ field6: dateString })}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="CRL(cm)">
                <Input placeholder="请输入" value={kzmzbzytemplate.field7} onChange={e => changeData({ field7: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="BPD(mm)">
                <Input placeholder="请输入" value={kzmzbzytemplate.field8} onChange={e => changeData({ field8: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="胎儿数">
                <Input placeholder="请输入" value={kzmzbzytemplate.field9} onChange={e => changeData({ field9: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="末次月经">
                <DatePicker
                  format="YYYY-MM-DD"
                  style={{ width: '100%' }}
                  value={kzmzbzytemplate.field10 ? moment(kzmzbzytemplate.field10) : null}
                  onChange={(date, dateString) => changeData({ field10: dateString })}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="是否吸烟">
                <div className="flex-box">
                  <Radio.Group value={kzmzbzytemplate.field11} onChange={e => changeData({ field11: e.target.value })}>
                    <Radio value="是">是</Radio>
                    <Radio value="否">否</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="送检单位代码">
                <Input placeholder="请输入" value={kzmzbzytemplate.field12} onChange={e => changeData({ field12: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
