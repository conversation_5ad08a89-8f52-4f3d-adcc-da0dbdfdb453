import React, { Component, Fragment } from 'react';
import { Tooltip, Modal, Input, message, Tag, Timeline } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import * as Api from './api';

import styles from './groupoper.less';

@Form.create()
class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      visible: false,
      tagList: [],
      activeTag: -1,
      historyList: [],
    };

    this.formItemLayout = {
      labelCol: {
        span: 0,
      },
      wrapperCol: {
        span: 24,
      },
    };
  }

  componentDidMount() {}

  queryTagList = async () => {
    const { code, data = {} } = await Api.queryTagList({ tagType: 'visit', numPerPage: 100 });
    if (code == 0) {
      const { recordList = [] } = data;
      this.setState({ tagList: recordList });
    }
  };

  queryHistory = async () => {
    const { chatInfo } = this.props;
    const { code, data = [] } = await Api.queryTagHistory({
      type: '2',
      groupId: chatInfo.id,
      pid: chatInfo.pid,
    });
    if (code == 0) {
      this.setState({ historyList: data });
    }
  };

  handleOk = async () => {
    const { activeTag, tagList = [] } = this.state;
    if (activeTag === -1) {
      message.error('请选择回访标签');
      return false;
    }
    const {
      form: { validateFields },
    } = this.props;
    validateFields(async (err, values) => {
      if (!err) {
        const { chatInfo = {} } = this.props;
        const tag = tagList[activeTag];
        const { code } = await Api.addTag({
          ...values,
          groupId: chatInfo.id,
          pid: chatInfo.pid,
          type: 2,
          patTagId: tag.id,
          patTagName: tag.tagName,
        });
        if (code == 0) {
          message.success('提交成功');
          this.setState({ visible: false });
        }
      }
    });
  };

  visilist = () => {
    this.setState({
      visible: true,
    });
    this.queryTagList();
    this.queryHistory();
  };

  handleCancel = () => {
    this.setState({
      visible: false,
      activeTag: -1,
    });
  };

  handleTagClick = tagIndex => {
    this.setState({ activeTag: tagIndex });
  };

  render() {
    const { visible, confirmLoading, tagList = [], activeTag, historyList = [] } = this.state;
    const { form, chatInfo = {} } = this.props;
    const { getFieldDecorator } = form;

    return (
      <Fragment>
        {chatInfo.chatFilterType != '11' && (
          <Tooltip title="回访记录">
            <div className={`${styles.chartHradBtn} ${styles.chbtn2}`} onClick={this.visilist} />
          </Tooltip>
        )}
        <Modal title="回访记录" visible={visible} destroyOnClose onOk={this.handleOk} confirmLoading={confirmLoading} onCancel={this.handleCancel}>
          <Form>
            <div>
              <div>回访标签</div>
              <div>
                {tagList.map((item, key) => {
                  return (
                    <Tag style={{ cursor: 'pointer', marginTop: 10 }} key={item.id} color={key === activeTag ? PRIMARY_COLOR : ''} onClick={() => this.handleTagClick(key)}>
                      {item.tagName}
                    </Tag>
                  );
                })}
              </div>
            </div>
            <div style={{ marginTop: 10 }}>
              <div style={{ marginBottom: 4 }}>沟通记录</div>
              <Form.Item {...this.formItemLayout}>{getFieldDecorator('content')(<Input.TextArea rows="2" placeholder="请输入" maxLength={100} />)}</Form.Item>
            </div>
          </Form>
          <div style={{ maxHeight: 250, overflow: 'auto', paddingTop: 5 }}>
            <Timeline>
              {historyList.map(item => {
                return (
                  <Timeline.Item color={PRIMARY_COLOR} key={item.updateTime}>
                    <p>
                      {item.updateTime}&nbsp;&nbsp;{item.patTagName}
                    </p>
                    <p>{item.content}</p>
                  </Timeline.Item>
                );
              })}
            </Timeline>
          </div>
        </Modal>
      </Fragment>
    );
  }
}

export default Index;
