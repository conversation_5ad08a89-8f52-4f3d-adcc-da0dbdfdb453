import React from 'react';

import styles from './emoji.less';

const ExtraConfig = {
  13: {
    style: { transform: 'scale(1.3)' },
  },
  15: {
    style: { transform: 'scale(1.15)' },
  },
};

const { protocol, host } = window.location;

export default callback => (
  <div className={styles.emojiBox}>
    {new Array(43).fill(0).map((item, key) => {
      const extra = ExtraConfig[key] || {};
      return (
        <div
          className={styles.emoji}
          key={key}
          style={extra.style || {}}
          // onClick={() => callback(`https://zxxymp.med.gzhc365.com/merchant/umi/emoji/emijo-${key+1}.png`)}
          onClick={() => callback(key + 1)}
        >
          <img src={`./emoji/emijo-${key + 1}.png`} alt="" />
        </div>
      );
    })}
  </div>
);
