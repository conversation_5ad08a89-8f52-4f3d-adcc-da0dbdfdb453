/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio } from 'antd';
import '../../index.less';

const { TextArea } = Input;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { jhysysTemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, jhysysTemplate: { ...jhysysTemplate, ...payload } },
      },
    });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="申请单信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="送检原因" style={{ marginBottom: 0 }}>
                <Radio.Group value={jhysysTemplate.field1} onChange={e => changeData({ field1: e.target.value })}>
                  <Radio value="备孕">备孕</Radio>
                  <div className="flex-box" style={{ display: 'inline-flex', gap: 0, marginRight: '16px' }}>
                    <div className="flex-shrink">
                      <Radio value="已怀孕">已怀孕</Radio>
                    </div>
                    <Input placeholder="请输入孕周" value={jhysysTemplate.field2} onChange={e => changeData({ field2: e.target.value })} />
                  </div>
                  <Radio value="其他">其他</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="病史">
                <div className="flex-box">
                  <Radio.Group value={jhysysTemplate.field3} onChange={e => changeData({ field3: e.target.value })}>
                    <Radio value="无">无</Radio>
                    <Radio value="有">有</Radio>
                  </Radio.Group>
                  <div className="flex-box">
                    <div className="flex-shrink">请说明</div>
                    <Input placeholder="请输入" value={jhysysTemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="临床诊断">
                <TextArea placeholder="请输入" defaultValue={jhysysTemplate.field5} onChange={e => changeData({ field5: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="家族史">
                <TextArea placeholder="请输入" defaultValue={jhysysTemplate.field6} onChange={e => changeData({ field6: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
