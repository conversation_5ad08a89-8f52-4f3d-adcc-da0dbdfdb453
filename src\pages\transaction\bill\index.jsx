/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect, history } from 'umi';
import { Form, Row, Col, Select, DatePicker, Input, Button, Table, Divider, Popconfirm, message } from 'antd';
import moment from 'moment';
import queryString from 'query-string';
import { formatMoney, filterObj, getDownload } from '@/utils/utils';
import './index.less';

const { RangePicker } = DatePicker;

const Index = props => {
  const { dispatch, listData = [], btns = {} } = props;
  const [form] = Form.useForm();
  const [queryParam, setQueryParam] = useState({});

  const query = pageNum => {
    const values = form.getFieldsValue();
    if (values.billDate) {
      values.beginDate = values.billDate[0].format('YYYY-MM-DD');
      values.endDate = values.billDate[1].format('YYYY-MM-DD');
      delete values.billDate;
    }
    setQueryParam(values);
    dispatch({
      type: 'bill/queryByPage',
      payload: { ...values, pageNum },
    });
  };

  useEffect(() => {
    query(1);
  }, []);

  const dataExport = async record => {
    const url = '/merchant/api/bill/exportByPage';
    const paramStr = queryString.stringify({
      ...filterObj(queryParam),
    });
    getDownload(`${url}?${paramStr}`);
  };

  const columns = [
    {
      title: '账单日期',
      dataIndex: 'date',
      fixed: 'left',
    },
    {
      title: '交易笔数',
      dataIndex: 'num',
      fixed: 'left',
    },
    {
      title: '退款总金额',
      dataIndex: 'refundFee',
      render: v => formatMoney(v),
    },
    {
      title: '手续费总金额',
      dataIndex: 'serviceFee',
      render: v => formatMoney(v),
    },
    {
      title: '订单总金额',
      dataIndex: 'totalFee',
      render: v => formatMoney(v),
    },
    {
      title: '操作',
      fixed: 'right',
      render: record => {
        return btns['/transaction/bill/detail'] ? (
          <a
            onClick={() => {
              history.push({
                pathname: '/transaction/bill/detail',
                search: queryString.stringify({ billDate: record.date }),
              });
            }}
          >
            账单明细
          </a>
        ) : null;
      },
    },
  ];

  return (
    <div className="g-page page-bill">
      <Form className="g-query-box" form={form}>
        <Row gutter={[16, 24]}>
          <Col span={8} className="col-item">
            <Form.Item label="账单日期" name="billDate" initialValue={[moment().subtract(1, 'month'), moment()]}>
              <RangePicker format="YYYY-MM-DD" allowClear={false} />
            </Form.Item>
          </Col>
          <Col span={16} style={{ textAlign: 'right' }}>
            <Button type="primary" onClick={() => query(1)}>
              查询
            </Button>
            <Button
              onClick={() => {
                form.resetFields();
                query(1);
              }}
              style={{ marginLeft: 10 }}
            >
              重置
            </Button>
          </Col>
        </Row>
      </Form>
      <div className="container">
        <Col span={24} style={{ textAlign: 'right', marginBottom: '16px' }}>
          <Button type="primary" onClick={dataExport}>
            下载账单
          </Button>
        </Col>
        <Table columns={columns} dataSource={listData} rowKey="id" scroll={{ x: 'max-content' }} pagination={false} />
      </div>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.bill,
  };
})(Index);
