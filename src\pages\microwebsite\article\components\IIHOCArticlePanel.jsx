import React from 'react';
import { Input, Upload, Button, Select, message, Checkbox, Radio, DatePicker } from 'antd';
import moment from 'moment';
import { connect } from 'dva';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import TinymceEditor from '../../../../components/editor/TinymceEditor';
import Phone from './Phone';

import * as CONSTANT from '../../../../config/constant/constant';
import '../../style.less';

const FormItem = Form.Item;
const Option = Select.Option;

export default function IIHOCArticlePanel({ type = 'detail', article = {}, typeList = [] }) {
  class ArticlePanel extends React.Component {
    state = {
      detail: { contentType: 1, ...article },
      secondTypeList: [],
    };

    articleDetail = hisId => {
      const { dispatch } = this.props;
      dispatch({
        type: 'microwebsite/articleDetail',
        payload: {
          hisId,
        },
      });
    };

    beforeUpload = file => {
      const isImage = file.type && file.type.indexOf('image') > -1;
      if (!isImage) {
        message.error('请选择图片进行上传!');
      }
      const size = file.size / 1024 / 1024 <= 1;
      if (!size) {
        message.error('图片大小不能超过1MB!');
      }
      return isImage && size;
    };

    uploadOnChange = e => {
      const { response } = e.file;
      if (response) {
        let picUrl = '';
        if (response.code != 0) {
          message.error('上传文件失败');
        } else {
          picUrl = response.data;
        }
        this.saveArticleDetail({
          picUrl,
        });
      }
    };

    validateHandler = (rule, value, callback) => {
      const { field } = rule;
      let content = '';
      switch (field) {
        case 'title':
          if (!value || value == '') {
            content = '请输入文章标题!';
          } else if (!/^[\S\s]{1,50}$/.test(value)) {
            content = '最多输入50个字符!';
          }
          break;
        case 'typeId':
          if (!value || value.length == 0) {
            content = '请选择文章类型!';
          }
          break;
        case 'position':
          if (!value || value.length == 0) {
            content = '请选择文章展示位置!';
          }
          break;
        case 'createTime':
          if (!value || value == '') {
            content = '请选择文章发布时间!';
          }
          break;
        case 'contentType':
          if (!value || value.length == 0) {
            content = '请选择文章内容形式!';
          }
          break;
        case 'picUrl':
          if (!value || value == '') {
            // content = '请上传封面图片!';
          } else {
            const image = new Image(); // eslint-disable-line
            image.src = value;
            // if (
            //   !image.width ||
            //   image.width == 0 ||
            //   !image.height ||
            //   image.height == 0
            // ) {
            //   content = "图片资源无法加载或加载过慢!";
            // } else {
            // const ratio = image.width / image.height;
            // if (ratio > 2.2 || ratio < 1.8) {
            //   content = '图片尺寸与推荐尺寸350*174相差过大!';
            // }
            // }
          }
          break;
        // case "content":
        //   if (!value || value == "") {
        //     content = "请输入文章内容!";
        //   } else if (!/^[\s\S]{1,30000}$/.test(value)) {
        //     content = "最多输入30000个字符!";
        //   }
        //   break;
        default:
          break;
      }
      if (content && content != '') {
        callback(content);
      } else {
        callback();
      }
    };

    saveArticleDetail = param => {
      const $this = this;
      const { form } = $this.props;
      const { detail = {} } = $this.state;
      const { setFieldsValue } = form;
      setFieldsValue({
        ...param,
      });
      $this.setState({
        detail: {
          ...detail,
          ...param,
        },
      });
    };

    submitArticleDetail = status => {
      const $this = this;
      $this.state.detail.typeName = $this.state.detail.secondTypeName || $this.state.detail.typeName;
      $this.state.detail.typeId = $this.state.detail.secondTypeId || $this.state.detail.typeId;
      const {
        dispatch,
        form: { validateFieldsAndScroll },
      } = this.props;
      validateFieldsAndScroll(err => {
        if (!err) {
          console.log(moment($this.state.detail.createTime).valueOf());
          dispatch({
            type: 'microwebsite/submitArticleDetail',
            payload: {
              ...$this.state.detail,
              createTime: $this.state.detail.createTime ? moment($this.state.detail.createTime).format('YYYY-MM-DD HH:mm:ss') : moment($this.state.detail.createTime).format('YYYY-MM-DD HH:mm:ss'),
              position: ($this.state.detail.position || []).join(','),
              status,
            },
          });
        }
      });
    };

    render() {
      const { detail = {}, secondTypeList = [] } = this.state;
      const { form } = this.props;
      const { getFieldDecorator } = form;

      return (
        <div className="page-article">
          <div className="article-detail" style={{ margin: '24px 170px' }}>
            <div style={{ padding: '20px 71.6px 0 0' }}>
              <Phone detail={detail} />
            </div>
            <div className="article-content">
              <Form style={{ marginTop: 12 }}>
                <FormItem
                  label="文章标题"
                  labelCol={{ span: 4 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {getFieldDecorator('title', {
                    rules: [
                      {
                        required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.title,
                    onChange: e => {
                      this.saveArticleDetail({ title: e.target.value });
                    },
                  })(<Input style={{ width: 347 }} disabled={type == 'detail'} placeholder="请输入文章标题" />)}
                </FormItem>
                <FormItem
                  label="文章类型"
                  labelCol={{ span: 4 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {getFieldDecorator('typeId', {
                    rules: [
                      {
                        required: type != 'detail',
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: type == 'new' ? [] : [`${detail.typeId}`],
                    onChange: typeId => {
                      const typeTmp = typeList.filter(item => item.typeId == typeId);
                      this.setState({
                        secondTypeList: typeTmp[0].subTypeList,
                      });
                      this.saveArticleDetail({
                        typeId: typeTmp[0].typeId,
                        typeName: typeTmp[0].typeName,
                      });
                    },
                  })(
                    <Select
                      style={{ width: 231 }}
                      disabled={type != 'new'}
                      placeholder="请选择文章类型"
                      // options={typeList}
                      filterOption={(input, option) => {
                        return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }}
                    >
                      {typeList &&
                        typeList.map(item => {
                          // if (item.subTypeList.length > 0) {
                          //   return (
                          //     <Option
                          //       value={`${item.subTypeList[0].typeId}`}
                          //       key={item.subTypeList[0].typeId}
                          //     >
                          //       {item.subTypeList[0].typeName}
                          //     </Option>
                          //   );
                          // } else {
                          return (
                            <Option value={`${item.typeId}`} key={item.typeId}>
                              {item.typeName}
                            </Option>
                          );
                          // }
                        })}
                    </Select>,
                  )}
                </FormItem>
                {secondTypeList.length > 0 && (
                  <FormItem
                    label="二级目录"
                    labelCol={{ span: 4 }}
                    // wrapperCol={{ offset: 4 }}
                  >
                    {getFieldDecorator('secondTypeId', {
                      rules: [
                        {
                          required: type != 'detail',
                          validator: this.validateHandler,
                        },
                      ],
                      initialValue: type == 'new' ? [] : [`${detail.typeId}`],
                      onChange: typeId => {
                        const typeTmp = secondTypeList.filter(item => item.typeId == typeId);
                        this.saveArticleDetail({
                          secondTypeId: typeTmp[0].typeId,
                          secondTypeName: typeTmp[0].typeName,
                        });
                      },
                    })(
                      <Select
                        showSearch
                        style={{ width: 231 }}
                        disabled={type != 'new'}
                        placeholder="请选择文章类型"
                        // options={typeList}
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                      >
                        {secondTypeList.map(item => {
                          return (
                            <Option value={`${item.typeId}`} key={item.typeId}>
                              {item.typeName}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                )}
                <FormItem
                  label="展示位置"
                  labelCol={{ span: 4 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {getFieldDecorator('position', {
                    rules: [
                      {
                        required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.position,
                    onChange: v => {
                      this.saveArticleDetail({ position: v });
                    },
                  })(
                    <Checkbox.Group
                      disabled={type == 'detail'}
                      options={[
                        { label: '患者端科普', value: '1' },
                        { label: '医院动态', value: '2' },
                        { label: '医生端科普', value: '3' },
                      ]}
                    />,
                  )}
                </FormItem>
                <FormItem
                  label="发布时间"
                  labelCol={{ span: 4 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {getFieldDecorator('createTime', {
                    rules: [
                      {
                        required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.createTime ? moment(detail.createTime) : moment(),
                    onChange: v => {
                      this.saveArticleDetail({ createTime: v });
                    },
                  })(<DatePicker allowClear={false} showTime disabledDate={current => current && current > moment().endOf('day')} />)}
                </FormItem>
                <FormItem
                  label="封面图片"
                  labelCol={{ span: 4 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {type == 'detail' ? (
                    <div style={{ display: 'flex' }}>
                      <img src={detail.picUrl} width="350px" height="174px" alt="" className="avatar" />
                    </div>
                  ) : detail.picUrl ? (
                    <div style={{ display: 'flex' }}>
                      <img src={detail.picUrl} width="350px" height="174px" alt="" className="avatar" />
                      <div style={{ padding: '146px 0 0 10px' }}>
                        <Upload name="idFile" action={`${CONSTANT.DOMAIN}/api/article/upfile`} beforeUpload={this.beforeUpload} showUploadList={false} onChange={this.uploadOnChange}>
                          <a>更换图片</a>
                        </Upload>
                      </div>
                    </div>
                  ) : (
                    <Upload
                      className="avatar-uploader"
                      name="idFile"
                      action={`${CONSTANT.DOMAIN}/api/article/upfile`}
                      beforeUpload={this.beforeUpload}
                      showUploadList={false}
                      onChange={this.uploadOnChange}
                    >
                      <div className="avatar-uploader-trigger">
                        <Icon
                          type="cloudupload"
                          style={{
                            fontSize: 32,
                            color: '#3F969D',
                            marginBottom: 14,
                          }}
                        />
                        <div
                          style={{
                            color: '#404040',
                            lineHeight: 1.5,
                            marginBottom: 7,
                          }}
                        >
                          点击此区域上传图片
                        </div>
                        <div style={{ color: '#919191', lineHeight: 1.5 }}>大小不超过1M，建议尺寸350*174</div>
                      </div>
                    </Upload>
                  )}
                  {getFieldDecorator('picUrl', {
                    rules: [
                      {
                        // required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.picUrl,
                  })(<Input style={{ display: 'none' }} />)}
                </FormItem>
                <FormItem
                  label="内容形式"
                  labelCol={{ span: 4 }}
                  // wrapperCol={{ offset: 4 }}
                >
                  {getFieldDecorator('contentType', {
                    rules: [
                      {
                        required: type != 'detail',
                        whitespace: true,
                        validator: this.validateHandler,
                      },
                    ],
                    initialValue: detail.contentType,
                    onChange: e => {
                      this.saveArticleDetail({ contentType: e.target.value, content: '' });
                    },
                  })(
                    <Radio.Group
                      disabled={type == 'detail'}
                      options={[
                        { label: '文本编辑', value: 1 },
                        { label: '跳转链接', value: 2 },
                      ]}
                    />,
                  )}
                </FormItem>
                {detail.contentType == 1 ? (
                  <FormItem style={{ marginBottom: 0 }}>
                    {type != 'detail' ? (
                      <TinymceEditor
                        id={`update-article-${detail.id}-editor`}
                        content={detail.content || ' '}
                        url={`${CONSTANT.DOMAIN}/api/article/upfile`}
                        onChange={content => {
                          this.saveArticleDetail({ content });
                        }}
                      />
                    ) : (
                      <div dangerouslySetInnerHTML={{ __html: detail.content }} />
                    )}
                    {getFieldDecorator('content', {
                      rules: [{ validator: this.validateHandler }],
                      initialValue: detail.content,
                    })(<Input style={{ display: 'none' }} />)}
                  </FormItem>
                ) : (
                  <FormItem
                    label="跳转链接"
                    labelCol={{ span: 4 }}
                    // wrapperCol={{ offset: 4 }}
                  >
                    {getFieldDecorator('content', {
                      rules: [
                        {
                          required: type != 'detail',
                          whitespace: true,
                          validator: this.validateHandler,
                        },
                      ],
                      initialValue: detail.content,
                      onChange: e => {
                        this.saveArticleDetail({ content: e.target.value });
                      },
                    })(<Input style={{ width: 347 }} disabled={type == 'detail'} placeholder="请输入跳转链接" />)}
                  </FormItem>
                )}
                {type != 'detail' ? (
                  <FormItem style={{ marginTop: 24 }}>
                    <Button
                      type="primary"
                      size="default"
                      onClick={() => {
                        this.submitArticleDetail(1);
                      }}
                    >
                      保存并发布
                    </Button>
                    <Button
                      size="default"
                      style={{ marginLeft: 16 }}
                      onClick={() => {
                        this.submitArticleDetail(0);
                      }}
                    >
                      保存为草稿
                    </Button>
                  </FormItem>
                ) : null}
              </Form>
            </div>
          </div>
        </div>
      );
    }
  }

  return connect()(Form.create()(ArticlePanel));
}
