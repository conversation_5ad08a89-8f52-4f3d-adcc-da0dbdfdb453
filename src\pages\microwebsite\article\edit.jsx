import React from 'react';
import { connect } from 'dva';
import { history } from 'umi';

import IIHOCArticlePanel from './components/IIHOCArticlePanel';

import * as utils from '../../../utils/utils';

export default connect(state => {
  return {
    detail: state.microwebsite.article.detail,
    typeList: state.microwebsite.article.typeList,
  };
})(
  class ArticleEdit extends React.Component {
    componentDidMount() {
      const { dispatch, location, typeList } = this.props;
      const articleId = utils.queryStringToJson(location.search).id;
      if (articleId) {
        dispatch({
          type: 'microwebsite/articleTypeList',
        });
        dispatch({
          type: 'microwebsite/articleDetail',
          payload: {
            articleId,
          },
        });
      } else {
        history.push('/microwebsite/article');
      }
    }

    render() {
      const { detail, typeList } = this.props;
      const ArticleEditPanel = IIHOCArticlePanel({ type: 'modify', article: detail, typeList });
      return <ArticleEditPanel />;
    }
  },
);
