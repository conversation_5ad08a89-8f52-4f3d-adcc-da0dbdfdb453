import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Tabs, Input, Button, message, Modal, Upload } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';

import TinymceEditor from '../../../components/editor/TinymceEditor';

import '../../microwebsite/style.less';
import '../../microwebsite/department/index.less';

import * as CONSTANT from '../../../config/constant/constant';
import * as utils from '../../../utils/utils';

const TabPane = Tabs.TabPane;
const FormItem = Form.Item;
const confirm = Modal.confirm;

class Department extends React.Component {
  state = {
    type: 'detail',
    pid: 0,
    hisId: '',
  };

  componentDidMount() {
    const $this = this;
    const { dispatch, location, hisList = [] } = $this.props;

    const hisId = utils.queryStringToJson(location.search).hisId || (hisList && hisList.length > 0 && hisList[0].hisId);
    if (hisId) {
      dispatch({
        type: 'register/hospitalList',
      });
      dispatch({
        type: 'register/departmentList',
        payload: {
          hisId,
        },
      });
    } else {
      dispatch({
        type: 'register/hospitalList',
        next: 'departmentList',
      });
    }
  }

  componentWillUnmount() {
    const $this = this;
    const { dispatch, deptList = [] } = $this.props;
    clearTimeout($this.timeoutId);
    dispatch({
      type: 'register/saveDepartment',
      payload: {
        list: deptList.forEach(item => {
          delete item.active;
          delete item.indeterminate;
        }),
        detail: false,
        subList: [],
      },
    });
  }

  departmentList = hisId => {
    const $this = this;
    const { dispatch } = $this.props;
    this.setState({
      hisId,
    });
    dispatch({
      type: 'register/saveDepartment',
      payload: {
        list: [],
        detail: {},
        subList: [],
      },
    });
    dispatch({
      type: 'register/departmentList',
      payload: {
        hisId,
      },
    });
  };

  deptClickHandler = (idx, dept) => {
    const $this = this;
    const { dispatch, deptList = [], deptSubList } = $this.props;
    const { hasChild = 1, active = false, indeterminate = false } = dept;
    if (active || indeterminate) {
      deptSubList.forEach(item => {
        item.active = false;
        if (item.children && item.children instanceof Array) {
          item.children.forEach(subItem => (subItem.active = false));
        }
      });
      deptList[idx].active = true;
      dispatch({
        type: 'register/saveDepartment',
        payload: {
          list: [...deptList],
          subList: [...deptSubList],
          detail: {},
        },
      });
    } else {
      deptList.forEach(item => {
        delete item.active;
        delete item.indeterminate;
      });
      deptList[idx].active = true;
      dispatch({
        type: 'register/saveDepartment',
        payload: {
          list: [...deptList],
          subList: [],
          detail: {},
        },
      });
      if (hasChild == 0) {
        dispatch({
          type: 'register/departmentList',
          payload: {
            hisId: dept.hisId,
            activeId: dept.no,
            pid: dept.no,
          },
        });
      }
    }
    $this.departmentDetail({
      no: dept.no,
      hisId: dept.hisId,
    });
  };

  subDeptClickHandler = (idx, dept) => {
    const $this = this;
    const { dispatch, deptList = [], deptSubList = [] } = $this.props;
    const { hasChild = 1 } = dept;
    for (let i = 0; i < deptList.length; i++) {
      const item = deptList[i];
      if (item.active) {
        delete item.active;
        item.indeterminate = true;
      }
    }
    for (let i = 0; i < deptSubList.length; i++) {
      const item = deptSubList[i];
      if (item.no == dept.no) {
        item.indeterminate = true;
        item.active = true;
      } else {
        item.active = false;
        item.indeterminate = false;
      }
      if (item.children && item.children.length > 0) {
        item.children.map(tmp => delete tmp.active);
      }
    }
    dispatch({
      type: 'register/saveDepartment',
      payload: {
        list: [...deptList],
        subList: [...deptSubList],
        detail: {},
      },
    });
    $this.departmentDetail({
      no: dept.no,
      hisId: dept.hisId,
    });
    if (hasChild == 0) {
      dispatch({
        type: 'register/departmentList',
        payload: {
          hisId: dept.hisId,
          activeId: dept.no,
          pid: dept.no,
        },
      });
    }
  };

  thirdDeptClickHandler = (idx = -1, dept) => {
    const $this = this;
    const { dispatch, deptList = [], deptSubList = [] } = $this.props;
    if (idx >= 0 && dept) {
      $this.departmentDetail({
        no: dept.no,
        hisId: dept.hisId,
      });
    }
    for (let i = 0; i < deptList.length; i++) {
      const item = deptList[i];
      if (item.active) {
        item.indeterminate = true;
      }
      item.active = false;
    }
    for (let i = 0; i < deptSubList.length; i++) {
      const item = deptSubList[i];
      if (item.active) {
        item.indeterminate = true;
      }
      item.active = false;
      if (item.children) {
        item.children.forEach(tmp => delete tmp.active);
      }
      if (dept && item.no == dept.pid) {
        item.children[idx].active = true;
      }
    }
    dispatch({
      type: 'register/saveDepartment',
      payload: {
        subList: [...deptSubList],
      },
    });
  };

  departmentDetail = payload => {
    const $this = this;
    const { dispatch } = $this.props;
    dispatch({
      type: 'register/departmentDetail',
      payload,
    });
  };

  validateHandler = (rule, value, callback) => {
    const { field } = rule;
    let content = '';
    switch (field) {
      case 'no':
        if (!value || value == '') {
          content = '请输入科室编号!';
        }
        break;
      case 'name':
        if (!value || value == '') {
          content = '请输入科室名称!';
        } else if (!/^[\s\S]{1,30}$/.test(value)) {
          content = '最多输入30个字符!';
        }
        break;
      case 'tel':
        if (!value || value == '') {
          content = '请输入咨询电话!';
        } else if (!/^[\s\S]{1,30}$/.test(value)) {
          content = '最多输入30个字符!';
        }
        break;
      case 'address':
        if (!value || value == '') {
          content = '请输入科室地址!';
        } else if (!/^[\s\S]{1,50}$/.test(value)) {
          content = '最多输入50个字符!';
        }
        break;
      case 'summary':
        if (!value || value == '') {
          content = '请输入科室介绍!';
        } else if (!/^[\s\S]{1,30000}$/.test(value)) {
          content = '最多输入30000个字符!';
        }
        break;
      default:
        break;
    }
    if (content && content != '') {
      callback(content);
    } else {
      callback();
    }
  };

  /**
   * 递归查找，修改列表中的某一科室信息
   */
  updateDept = (dept, list = []) => {
    const $this = this;
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (item.no == dept.no) {
        for (const key in dept) {
          if ({}.hasOwnProperty.call(dept, key)) {
            item[key] = dept[key];
          }
        }
        break;
      } else if (item.hasChild == 0) {
        $this.updateDept(dept, item.children);
      }
    }
  };

  /**
   * 递归查找，删除列表中的某一科室
   */
  deleteDept = (dept, list = []) => {
    const $this = this;
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (item.no == dept.no) {
        list.splice(i, 1);
        break;
      } else if (item.hasChild == 0) {
        $this.deleteDept(dept, item.children);
      }
    }
  };

  submitDeleteDeprtment = dept => {
    const $this = this;
    const { deptList = [], deptSubList = [], dispatch } = $this.props;
    let { detail } = $this.props;
    if (detail.no == dept.no) {
      detail = {};
    }
    if (deptList.some(item => item.no == dept.no)) {
      // 删除一级科室
      $this.deleteDept(dept, deptList);
    } else {
      // 删除二、三级科室
      $this.deleteDept(dept, deptSubList);
    }
    dispatch({
      type: 'register/departmentDelete',
      payload: {
        hisId: dept.hisId,
        no: dept.no,
      },
    });
    dispatch({
      type: 'register/saveDepartment',
      payload: {
        list: [...deptList],
        subList: [...deptSubList],
        detail: {
          ...detail,
        },
      },
    });
  };
  // 批量删除科室
  submitDeleteAllDeprtment = hisId => {
    const $this = this;
    const { deptList = [], dispatch } = $this.props;
    const ids = [];
    deptList.map(item => {
      ids.push(item.no);
    });
    dispatch({
      type: 'register/batchDeleteDepts',
      payload: {
        hisId,
        ids: ids.join(','),
        type: 'register',
      },
    });
  };

  submitDepartmentDetail = () => {
    const $this = this;
    const {
      detail,
      form: { validateFieldsAndScroll, getFieldsValue },
      dispatch,
    } = $this.props;
    validateFieldsAndScroll(err => {
      if (!err) {
        const { pid, type } = $this.state;
        const dept = {
          hisId: detail.hisId || $this.state.hisId,
          pid,
          method: type,
          ...getFieldsValue(),
        };
        dispatch({
          type: 'register/submitDepartmentDetail',
          payload: dept,
          success: () => {
            $this.setState({
              type: 'detail',
            });
            clearTimeout($this.timeoutId);
            $this.timeoutId = setTimeout(() => {
              const element = $this.refs.active;
              if (element) {
                element.scrollIntoView();
              }
            }, 100);
          },
        });
      }
    });
  };

  syncDepartmentToMicro = hisId => {
    const { dispatch } = this.props;
    dispatch({
      type: 'register/syncDepartmentToMicro',
      payload: {
        hisId,
      },
    });
  };

  beforeUpload = file => {
    const isImage = file.type && file.type.indexOf('image') > -1;
    if (!isImage) {
      message.error('请选择图片进行上传!');
    }
    const size = file.size / 1024 / 1024 <= 1;
    if (!size) {
      message.error('图片大小不能超过1MB!');
    }
    return isImage && size;
  };
  uploadOnChange = e => {
    const { response } = e.file;
    if (response) {
      let img = '';
      if (response.code != 0) {
        message.error('上传文件失败');
      } else {
        img = (response.data || {}).url;
      }
      const { form, dispatch, detail = {} } = this.props;
      form.setFieldsValue({ img });
      dispatch({
        type: 'register/saveDepartment',
        payload: {
          detail: { ...detail, img },
        },
      });
    }
  };

  clearImg() {
    const { form, dispatch, detail = {} } = this.props;
    form.setFieldsValue({ img: '' });
    dispatch({
      type: 'register/saveDepartment',
      payload: {
        detail: { ...detail, img: '' },
      },
    });
  }

  render() {
    const $this = this;
    const { hisList = [], deptList = [], deptSubList = [], detail = {}, form, dispatch, location, permissionData = {} } = $this.props;
    if (!hisList || hisList.length == 0) return null;
    const { btns = {} } = permissionData;
    const hisId = utils.queryStringToJson(location.search).hisId || (hisList && hisList.length > 0 && hisList[0].hisId);
    const { getFieldDecorator, setFieldsValue } = form;
    const rules = [{ required: true, whitespace: true, validator: $this.validateHandler }];
    const { type } = $this.state;

    return (
      <div className="page-department">
        <Tabs
          animated={false}
          defaultActiveKey={`${hisId}`}
          style={{ display: 'flex', flex: 'auto', flexDirection: 'column' }}
          onChange={id => {
            $this.setState({
              type: 'detail',
            });
            $this.departmentList(id);
            history.push(`/register/deptInfo?hisId=${id}`);
          }}
        >
          {hisList.map(item => {
            return (
              <TabPane tab={item.hisName} key={`${item.hisId}`}>
                <div className="department-detail">
                  <div className="dept-panel">
                    <div className="dept-panel-title">
                      <div style={{ flex: 1 }}>科室列表</div>
                      {btns['/register/deptInfo/upload'] ? (
                        <Button
                          size="small"
                          onClick={() => {
                            history.push(`/register/deptInfo/upload?hisId=${item.hisId}`);
                          }}
                        >
                          批量添加
                        </Button>
                      ) : null}
                      {btns['/register/deptInfo/batchdelete'] ? (
                        <Button
                          style={{ marginLeft: '24px' }}
                          onClick={e => {
                            e.stopPropagation();
                            confirm({
                              title: <div>确定要删除全部科室吗？</div>,
                              content: '删除后无法再恢复，请确认是否删除全部科室。',
                              onOk() {
                                $this.submitDeleteAllDeprtment(hisId);
                              },
                            });
                          }}
                          disabled={deptList && deptList.length === 0}
                        >
                          批量删除
                        </Button>
                      ) : null}
                      {btns['/register/deptInfo/sync'] ? (
                        <a
                          style={{ margin: '0 0 0 24px', fontSize: 14, fontWeight: 400 }}
                          onClick={() => {
                            confirm({
                              title: <div>确定将所有科室信息同步到微网站吗？</div>,
                              content: '同步后将覆盖微网站已有科室的信息，请仔细确认是否同步。',
                              onOk() {
                                $this.syncDepartmentToMicro(item.hisId);
                              },
                            });
                          }}
                        >
                          同步到微网站
                        </a>
                      ) : null}
                    </div>
                    <div className="dept-panel-body">
                      <div className="dept-panel-list">
                        <div className="panel-list-left">
                          <div className="list-items">
                            {deptList &&
                              deptList.map((dept, idx) => {
                                return (
                                  <div ref={dept.active ? 'active' : ''} className={`list-item${dept.active ? ' active' : dept.indeterminate ? ' indeterminate' : ''}`} key={`dept-${dept.no}`}>
                                    <div
                                      className="item-name"
                                      onClick={() => {
                                        $this.setState({
                                          type: 'detail',
                                        });
                                        $this.deptClickHandler(idx, dept, deptList);
                                      }}
                                    >
                                      <span>{dept.name}</span>
                                      {btns['/register/deptInfo/edit'] ? (
                                        <a
                                          onClick={e => {
                                            e.stopPropagation();
                                            $this.setState({
                                              pid: dept.no,
                                              type: 'modify',
                                            });
                                            $this.deptClickHandler(idx, dept, deptList);
                                          }}
                                        >
                                          编辑
                                        </a>
                                      ) : null}
                                      {btns['/register/deptInfo/edit'] ? (
                                        <a
                                          onClick={e => {
                                            e.stopPropagation();
                                            confirm({
                                              title: (
                                                <div>
                                                  确定删除科室
                                                  <span style={{ color: '#f57f17' }}>{dept.name}</span>
                                                  吗？
                                                </div>
                                              ),
                                              content: '删除后无法再恢复，请确认是否删除该科室。',
                                              onOk() {
                                                $this.submitDeleteDeprtment(dept);
                                              },
                                            });
                                          }}
                                        >
                                          删除
                                        </a>
                                      ) : null}
                                    </div>
                                  </div>
                                );
                              })}
                          </div>
                          <div className="list-button">
                            {btns['/register/deptInfo/new'] ? (
                              <Button
                                onClick={() => {
                                  $this.setState({
                                    hisId: item.hisId,
                                    pid: 0,
                                    type: 'new',
                                  });
                                  deptList.forEach(tmp => {
                                    if (tmp.active) {
                                      tmp.active = false;
                                      tmp.indeterminate = true;
                                    }
                                  });
                                  dispatch({
                                    type: 'register/saveDepartment',
                                    payload: {
                                      detail: {},
                                      list: deptList,
                                    },
                                  });
                                }}
                              >
                                添加一级科室
                              </Button>
                            ) : null}
                          </div>
                        </div>
                        <div className="panel-list-right">
                          <div className="list-items">
                            {deptSubList &&
                              deptSubList.map((dept, idx) => {
                                return (
                                  <div ref={dept.active ? 'active' : ''} className={`list-item${dept.active ? ' active' : ''}${dept.indeterminate ? ' indeterminate' : ''}`} key={`dept-${dept.no}`}>
                                    <div
                                      className="item-name"
                                      onClick={() => {
                                        $this.setState({
                                          type: 'detail',
                                        });
                                        $this.subDeptClickHandler(idx, dept, deptSubList);
                                      }}
                                    >
                                      <span>{dept.name}</span>
                                      {btns['/register/deptInfo/edit'] ? (
                                        <a
                                          onClick={e => {
                                            e.stopPropagation();
                                            $this.setState({
                                              pid: dept.no,
                                              type: 'modify',
                                            });
                                            $this.subDeptClickHandler(idx, dept, deptList);
                                          }}
                                        >
                                          编辑
                                        </a>
                                      ) : null}
                                      {btns['/register/deptInfo/delete'] ? (
                                        <a
                                          onClick={e => {
                                            e.stopPropagation();
                                            confirm({
                                              title: (
                                                <div>
                                                  确定删除科室
                                                  <span style={{ color: '#f57f17' }}>{dept.name}</span>
                                                  吗？
                                                </div>
                                              ),
                                              content: '删除后无法再恢复，请确认是否删除该科室。',
                                              onOk() {
                                                $this.submitDeleteDeprtment(dept);
                                              },
                                            });
                                          }}
                                        >
                                          删除
                                        </a>
                                      ) : null}
                                    </div>
                                    <div className="list-item list-sub-items">
                                      {dept.children &&
                                        dept.children.map((subDept, key) => {
                                          return (
                                            <div
                                              ref={subDept.active ? 'active' : ''}
                                              className={`list-item${subDept.active ? ' active' : ''}`}
                                              key={`dept-${subDept.no}`}
                                              onClick={() => {
                                                $this.setState({
                                                  type: 'detail',
                                                });
                                                $this.thirdDeptClickHandler(key, subDept);
                                              }}
                                            >
                                              <div className="item-name">
                                                <span>{subDept.name}</span>
                                                {btns['/register/deptInfo/edit'] ? (
                                                  <a
                                                    onClick={e => {
                                                      e.stopPropagation();
                                                      $this.setState({
                                                        pid: subDept.no,
                                                        type: 'modify',
                                                      });
                                                      $this.thirdDeptClickHandler(key, subDept);
                                                    }}
                                                  >
                                                    编辑
                                                  </a>
                                                ) : null}
                                                {btns['/register/deptInfo/delete'] ? (
                                                  <a
                                                    onClick={e => {
                                                      e.stopPropagation();
                                                      confirm({
                                                        title: (
                                                          <div>
                                                            确定删除科室
                                                            <span style={{ color: '#f57f17' }}>{subDept.name}</span>
                                                            吗？
                                                          </div>
                                                        ),
                                                        content: '删除后无法再恢复，请确认是否删除该科室。',
                                                        onOk() {
                                                          $this.submitDeleteDeprtment(subDept);
                                                        },
                                                      });
                                                    }}
                                                  >
                                                    删除
                                                  </a>
                                                ) : null}
                                              </div>
                                            </div>
                                          );
                                        })}
                                      <div className="item-option">
                                        {btns['/register/deptInfo/new'] ? (
                                          <a
                                            onClick={() => {
                                              $this.setState({
                                                hisId: item.hisId,
                                                type: 'new',
                                                pid: dept.no,
                                              });
                                              $this.thirdDeptClickHandler();
                                              dispatch({
                                                type: 'register/saveDepartment',
                                                payload: {
                                                  detail: {},
                                                  subList: deptSubList,
                                                },
                                              });
                                            }}
                                          >
                                            添加三级科室
                                          </a>
                                        ) : null}
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                          </div>
                          <div className="list-button">
                            {btns['/register/deptInfo/new'] ? (
                              <Button
                                onClick={() => {
                                  const tmpArr = deptList.filter(tmp => tmp.active || tmp.indeterminate);
                                  if (!tmpArr || tmpArr.length == 0) {
                                    message.error('请选择一级科室');
                                  } else {
                                    $this.setState({
                                      type: 'new',
                                      pid: tmpArr[0].no,
                                    });
                                    $this.clearImg();
                                  }
                                }}
                              >
                                添加二级科室
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </div>
                      <div className="dept-edit">
                        {type == 'new' || type == 'modify' ? (
                          <div className="dept-detail-panel">
                            <Form style={{ margin: '30px 47px 0 20px' }}>
                              <FormItem label="科室编号" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                                {getFieldDecorator('no', {
                                  rules,
                                  initialValue: (type == 'modify' && detail.no) || '',
                                })(<Input style={{ width: 347 }} disabled={type != 'new'} placeholder="请输入科室编号" maxLength="50" />)}
                              </FormItem>
                              <FormItem label="科室名称" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                                {getFieldDecorator('name', {
                                  rules,
                                  initialValue: (type == 'modify' && detail.name) || '',
                                })(<Input style={{ width: 347 }} placeholder="请输入科室名称" />)}
                              </FormItem>
                              <FormItem label="咨询电话" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                                {getFieldDecorator('tel', {
                                  rules,
                                  initialValue: (type == 'modify' && detail.tel) || '',
                                })(<Input style={{ width: 347 }} placeholder="请输入咨询电话" />)}
                              </FormItem>
                              <FormItem label="科室图片" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                                <Upload
                                  className={`${detail.img ? 'deptlogo-has-img' : ''} deptlogo-uploader`}
                                  name="upfile"
                                  action={`${CONSTANT.DOMAIN}/api/files/uploadpic`}
                                  beforeUpload={this.beforeUpload}
                                  showUploadList={false}
                                  onChange={this.uploadOnChange}
                                  data={{
                                    partnerId: 'merchant',
                                    serviceType: 'test',
                                  }}
                                >
                                  <div className="deptlogo-uploader-trigger">
                                    <Button size="large">
                                      <Icon type="upload" />
                                      选择图片
                                    </Button>
                                  </div>
                                  <div className="upload-img">
                                    <img src={detail.img} alt="" />
                                  </div>
                                  <div className="upload-img-tip">重新上传</div>
                                </Upload>
                                {getFieldDecorator('img', {
                                  initialValue: (type == 'modify' && detail.img) || '',
                                })(<Input style={{ display: 'none' }} />)}
                              </FormItem>
                              <FormItem label="科室地址" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                                {getFieldDecorator('address', {
                                  rules,
                                  initialValue: (type == 'modify' && detail.address) || '',
                                })(<Input style={{ width: 347 }} placeholder="请输入科室地址" />)}
                              </FormItem>
                              <FormItem label="科室简介" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
                                <TinymceEditor
                                  id={`update-dept-${hisId}-${type == 'modify' ? detail.no : 'new'}-editor`}
                                  content={(type == 'modify' && detail.summary) || ' '}
                                  height="300"
                                  url={`${CONSTANT.DOMAIN}/api/his/upfile?hisId=${hisId}`}
                                  onChange={summary => {
                                    setFieldsValue({ summary });
                                  }}
                                />
                                {getFieldDecorator('summary', {
                                  rules: [{ required: true, validator: $this.validateHandler }],
                                  initialValue: (type == 'modify' && detail.summary) || '',
                                })(<Input style={{ display: 'none' }} />)}
                              </FormItem>
                              <FormItem style={{ marginBottom: 17 }} wrapperCol={{ span: 18, offset: 4 }}>
                                <Button type="primary" onClick={$this.submitDepartmentDetail}>
                                  保存
                                </Button>
                                <Button
                                  style={{ marginLeft: 16 }}
                                  onClick={() => {
                                    $this.setState({
                                      type: 'detail',
                                    });
                                  }}
                                >
                                  取消
                                </Button>
                              </FormItem>
                            </Form>
                          </div>
                        ) : type == 'detail' && detail && JSON.stringify(detail) != '{}' ? (
                          <div className="dept-detail-panel">
                            <div className="detail-panel-title">
                              <span>{detail.name}</span>
                            </div>
                            <div className="detail-panel-body">
                              <div className="detail-list">
                                <div className="detail-label">科室编号:</div>
                                <div className="detail-intro">{detail.no}</div>
                              </div>
                              <div className="detail-list">
                                <div className="detail-label">科室名称:</div>
                                <div className="detail-intro">{detail.name}</div>
                              </div>
                              <div className="detail-list">
                                <div className="detail-label">咨询电话:</div>
                                <div className="detail-intro">{detail.tel}</div>
                              </div>
                              <div className="detail-list">
                                <div className="detail-label">科室图片:</div>
                                <div className="detail-intro">{detail.img ? <img src={detail.img} className="detail-intro-logoimgview" alt="" /> : null}</div>
                              </div>
                              <div className="detail-list">
                                <div className="detail-label">科室地址:</div>
                                <div className="detail-intro">{detail.address}</div>
                              </div>
                              <div className="detail-list">
                                <div className="detail-label">科室简介:</div>
                                <div
                                  className="detail-intro"
                                  dangerouslySetInnerHTML={{ __html: detail.summary }} // eslint-disable-line
                                />
                              </div>
                            </div>
                          </div>
                        ) : !detail ? (
                          <div className="no-dept">
                            <div className="no-dept-img" alt="" />
                            <div className="no-dept-text">{!deptList || deptList.length == 0 ? '请在左边区域添加科室' : '请在左边区域选择科室'}</div>
                          </div>
                        ) : null}
                      </div>
                    </div>
                  </div>
                </div>
              </TabPane>
            );
          })}
        </Tabs>
      </div>
    );
  }
}

export default connect(state => {
  return {
    hisList: state.register.hospital.list,
    deptList: state.register.department.list,
    deptSubList: state.register.department.subList,
    detail: state.register.department.detail,
    permissionData: state.root.permissionData,
  };
})(Form.create()(Department));
