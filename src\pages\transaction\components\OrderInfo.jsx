/* eslint-disable react/no-string-refs */
import React from 'react';
import { connect } from 'dva';
import { Row, Col, Button, Steps, Input } from 'antd';
import { Decimal } from 'decimal.js';
import * as utils from '../../../utils/utils';
import * as SearchItem from '../components/SearchItem';
import '../styles.less';

const Step = Steps.Step;

class OrderInfo extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      backReason: '',
      rundPwd: '',
      refundFee: false,
    };
    this.printPage = () => {
      const title = document.title || ''; // eslint-disable-line
      document.title = this.props.order && this.props.order.hisName ? this.props.order.hisName : title; // eslint-disable-line
      window.print(); // eslint-disable-line
      document.title = title; // eslint-disable-line
    };
  }
  closeDrawer() {
    this.props.dispatch({
      type: 'root/closeDrawer',
    });
    this.setState({
      backReason: '',
      rundPwd: '',
    });
  }
  refreshOrder = (item, data) => {
    const { hisId, refundFee, hisBachId } = item;
    const { hisOrderNo, agtOrdNum, id, busType } = data;
    this.props.dispatch({
      type: 'order/getorderRefresh',
      payload: {
        reqData: {
          hisRefundOrdNo: hisBachId,
          hisId,
          refundAmout: new Decimal(refundFee).div(new Decimal(100)).toNumber(),
          hisOrdNo: hisOrderNo,
          agtOrdNo: agtOrdNum,
        },
        queryData: {
          id,
          busType,
        },
      },
    });
  };

  rebackFee = reson => {
    const refundFee = this.refs?.userrefuntfeeinput?.input?.value;
    this.props.funcReBack(reson, refundFee, this.state.rundPwd);
  };

  render() {
    /**
     * operData  操作列表
     * data 整个数据
     * rebackStep 退款目前在哪一步
     * funcReBack 退款按钮事件
     * isOrderBack 是否是退款过来的
     */
    const { isOrderBack, data = {}, rebackStep, operData } = this.props;
    const { backReason, rundPwd } = this.state;
    let orderStatus = '';
    if (data.refundList && data.refundList.length) {
      data.refundList.forEach(item => {
        if (item.status == 0) {
          orderStatus = 1;
        }
      });
    }
    let maxRefundFee = data.totalRealFee || 0;
    if (data.totalRefundFee > 0) {
      maxRefundFee = new Decimal(maxRefundFee).sub(new Decimal(data.totalRefundFee)).toNumber();
    }
    return (
      <div className="order-info-panle" id="orderInfoPnale">
        <p className="info-header">
          <span>订单详情</span>
          <i className="merchanticon anticon-close close-icon" onClick={() => this.closeDrawer()} />
        </p>
        <div className="order-main">
          {/* {data.refundProgress == 0 ? null : (
            <div className="section reback-section last-section">
              <p className="info-item-header">退款信息</p>
              <Steps className="reback-steps" current={data.refundProgress == 0 ? 0 : data.refundProgress}>
                <Step title="退款发起" description={data.refundProgress > 0 ? '完成' : '受理中'} />
                <Step title="退款受理" description={data.refundProgress > 1 ? '完成' : '受理中'} />
                <Step title="退款完成" description={data.refundProgress >= 2 && data.refundProgressDesc} />
              </Steps>
            </div>
          )} */}
          {data.refundList && data.refundList.length ? (
            <div style={{ marginTop: 30 }}>
              <Row className="tsc-table-header">
                <Col span={3}>退款时间</Col>
                <Col span={2}>退款金额</Col>
                <Col span={3}>退款状态</Col>
                <Col span={4}>退款流水号</Col>
                <Col span={5}>退款原因</Col>
                <Col span={4}>退款发起方</Col>
                <Col span={2}>备注</Col>
                {orderStatus ? <Col span={1}>操作</Col> : null}
              </Row>
              {data.refundList.map(item => (
                <Row className="tsc-table-item" key={`${item.createTime}${item.id}`}>
                  <Col span={3}>{item.createTime}</Col>
                  <Col span={2}>{utils.comma(item.refundFee || 0)}</Col>
                  <Col span={3}>{item.status == 1 ? '退款中' : item.status == 0 ? '退款受理' : item.status == 3 ? '退款失败' : '退款成功'}</Col>
                  <Col span={4}>
                    <span className="f-wrap">{item.refundSerialNo}</span>
                    <span className="tsc-split" />
                  </Col>
                  <Col span={5}>
                    <span>{item.refundDesc}</span>
                    <span className="tsc-split" />
                  </Col>
                  <Col span={4}>
                    <span>{item.refundPlatformTypeDes}</span>
                    <span className="tsc-split" />
                  </Col>
                  <Col span={2}>
                    <span>{item.memo}</span>
                    <span className="tsc-split" />
                  </Col>
                  {item.status == 0 || (item.status == 1 && !item.refundSerialNo) ? (
                    <Col span={1}>
                      <a onClick={() => this.refreshOrder(item, data)}>刷新</a>
                    </Col>
                  ) : null}
                </Row>
              ))}
            </div>
          ) : null}
          <div className="section">
            <p className="info-item-header">付款人信息</p>
            <Row>
              <Col span={8}>
                <span className="row-label">姓名:</span>
                <span>{data.patientName || ''}</span>
              </Col>
              <Col span={8}>
                <span className="row-label">就诊卡号:</span>
                <span>{data.patCardNo || ''}</span>
              </Col>
              <Col span={8}>
                <span className="row-label">手机号:</span>
                <span>{data.patientMobile || ''}</span>
              </Col>
              <Col span={8}>
                <span className="row-label">身份证号:</span>
                <span>{data.patientIdNo || ''}</span>
              </Col>
              {data.admissionNum ? (
                <Col span={8}>
                  <span className="row-label">住院号:</span>
                  <span>{data.admissionNum || ''}</span>
                </Col>
              ) : null}
              {data.authCode ? (
                <Col span={8}>
                  <span className="row-label">授权码:</span>
                  <span>{data.authCode || ''}</span>
                </Col>
              ) : null}
            </Row>
          </div>
          <div className="section">
            <p className="info-item-header">订单信息</p>
            <Row>
              <Col span={8}>
                <span className="row-label">订单状态:</span>
                <span>{SearchItem.getStatusName(data.busType, data.status) || data.statusDesc}</span>
              </Col>
              <Col span={8}>
                <span className="row-label">业务类型:</span>
                <span>{data.busTypeDesc || ''}</span>
              </Col>
              <Col span={8}>
                <span className="row-label">科室:</span>
                <span>{data.deptName || ''}</span>
              </Col>
              <Col span={8}>
                <span className="row-label">医生:</span>
                <span>{data.doctorName || ''}</span>
              </Col>
              <Col span={8}>
                <span className="row-label">交易金额:</span>
                <span>{utils.comma(data.totalRealFee || 0)}元</span>
              </Col>
              {data.totalRefundFee > 0 && (
                <Col span={8}>
                  <span className="row-label">已退款金额:</span>
                  <span>{utils.comma(data.totalRefundFee || 0)}元</span>
                </Col>
              )}
              {data.payedTime ? (
                <Col span={8}>
                  <span className="row-label">缴费时间:</span>
                  <span>{data.payedTime || ''}</span>
                </Col>
              ) : null}
              {data.hisOrderNo ? (
                <Col span={8} style={{ display: 'flex' }}>
                  <span className="row-label">医院订单号:</span>
                  <span className="row-value-label">{data.hisOrderNo || ''}</span>
                </Col>
              ) : null}
              <Col span={8} style={{ display: 'flex' }}>
                <span className="row-label">订单号:</span>
                <span className="row-value-label">{data.id || ''}</span>
              </Col>
              {data.dllMac ? (
                <Col span={8} style={{ display: 'flex' }}>
                  <span className="row-label">客户端ID:</span>
                  <span className="row-value-label">{data.dllMac || ''}</span>
                </Col>
              ) : null}
              {data.agtOrdNum ? (
                <Col span={8} style={{ display: 'flex' }}>
                  <span className="row-label">付款流水号:</span>
                  <span className="row-value-label">{data.agtOrdNum || ''}</span>
                </Col>
              ) : null}
              {data.hisRecepitNo ? (
                <Col span={8} style={{ display: 'flex' }}>
                  <span className="row-label">医院收据号:</span>
                  <span>{data.hisRecepitNo}</span>
                </Col>
              ) : null}
            </Row>

            <Row>
              {data.createTime ? (
                <Col span={8}>
                  <span className="row-label">订单时间:</span>
                  <span>{data.createTime}</span>
                </Col>
              ) : null}
              {data.paydTime ? (
                <Col span={8}>
                  <span className="row-label">支付时间:</span>
                  <span>{data.paydTime}</span>
                </Col>
              ) : null}
            </Row>
            <Row>
              {data.visitDate || data.visitPeriodName || data.visitBeginTime || data.visitEndTime ? (
                <Col span={8}>
                  <span className="row-label">就诊时间:</span>
                  <span>
                    {data.visitDate} {data.visitPeriodName} {data.visitBeginTime}
                    {data.visitEndTime ? `~${data.visitEndTime}` : ''}
                  </span>
                </Col>
              ) : null}
            </Row>
            <Row>
              {data.errorMsg ? (
                <Col span={8}>
                  <span className="row-label">错误信息:</span>
                  <span>{data.errorMsg}</span>
                </Col>
              ) : null}
            </Row>
            <Row>
              {typeof data.selfFundedFee != 'undefined' ? (
                <Col span={8}>
                  <span className="row-label">自费金额:</span>
                  <span>{utils.comma(data.selfFundedFee || 0)}元</span>
                </Col>
              ) : null}
              {typeof data.medicalFee != 'undefined' ? (
                <Col span={24}>
                  <span className="row-label">医保金额:</span>
                  <span className="row-value">{utils.comma(data.medicalFee || 0)}元</span>
                  {typeof data.medFundFee != 'undefined' ? (
                    <span>
                      <span>（其中医保统筹:</span>
                      <span>{utils.comma(data.medFundFee || 0)}元，</span>
                      <span>个账支付:</span>
                      <span>{utils.comma(data.medSelfFee || 0)}元，</span>
                      <span>其他账户支付:</span>
                      <span>{utils.comma(data.medOtherFee || 0)}元）</span>
                    </span>
                  ) : null}
                </Col>
              ) : null}
            </Row>
          </div>
          {data.deviceNum ? (
            <div className="section">
              <p className="info-item-header">设备信息</p>
              <Row>
                <Col span={8}>
                  <span className="row-label">设备号:</span>
                  <span>{data.deviceNum || ''}</span>
                </Col>
                <Col span={8}>
                  <span className="row-label">设备摆放位置:</span>
                  <span>{data.placement || ''}</span>
                </Col>
                <Col span={8}>
                  <span className="row-label">系统:</span>
                  <span className="row-value">{data.systemType == 1 ? 'Windows' : data.systemType == 2 ? 'Android' : '未知'}</span>
                </Col>
              </Row>
            </div>
          ) : null}
          {operData && operData.length ? (
            <div className="section last-section">
              <p className="info-item-header">操作情况</p>
              <Row className="tsc-table-header">
                <Col span={5}>操作说明</Col>
                <Col span={5}>操作账号</Col>
                <Col span={6}>状态</Col>
                <Col span={8}>操作时间</Col>
              </Row>
              {/* 操作情况 */}
              {operData.map(item1 => {
                return (
                  <Row className="tsc-table-item" align="middle" type="flex" justify="space-around" key={Math.random()}>
                    <Col span={5}>{item1.operatorDesc}</Col>
                    <Col span={5}>{item1.operatorName}</Col>
                    <Col span={6}>{item1.status == '1' ? '成功' : '失败'}</Col>
                    <Col span={8}>
                      <span>{item1.updateTime}</span>
                      <span className="tsc-split" />
                    </Col>
                  </Row>
                );
              })}
            </div>
          ) : null}
          <div className="print-oper-person">退款人：_____________</div>

          {isOrderBack && data.totalRealFee > 0 ? (
            <div className="m-refund">
              <div className="refund-label">退款金额：</div>
              <div className="refund-input">
                <Input
                  placeholder="请输入最多两位小数退款金额"
                  defaultValue={new Decimal(maxRefundFee).div(new Decimal(100).toNumber())}
                  ref="userrefuntfeeinput"
                  maxLength="8"
                  key={`${data.hisOrderNo}${data.totalRefundFee}${data.createTime}`}
                />
              </div>
              <div>
                元 <span className="refund-tip">退款金额默认=交易金额-已退款金额</span>
              </div>
            </div>
          ) : null}

          {isOrderBack ? (
            <div className="section last-section">
              <p className="info-item-header">退款原因</p>
              <Input.TextArea placeholder="请输入退款原因（最多40字）" value={backReason} maxLength={40} rows={4} onChange={e => this.setState({ backReason: e.target.value })} />
              <p className="info-item-header">退款密码</p>
              <Input placeholder="请输入退款密码" type="password" value={rundPwd} onChange={e => this.setState({ rundPwd: e.target.value })} />
              <Button className="order-info-btn" onClick={() => this.rebackFee(backReason, new Decimal(maxRefundFee).div(new Decimal(100).toNumber()), rundPwd)} type="primary">
                确认退款
              </Button>
            </div>
          ) : (
            <div className="section last-section">
              <Button className="order-info-btn" onClick={() => this.printPage()} type="primary">
                打印凭证
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  }
}
export default connect(state => {
  return {
    order: state.order,
    drawerStatus: state.root.drawerStatus,
  };
})(OrderInfo);
