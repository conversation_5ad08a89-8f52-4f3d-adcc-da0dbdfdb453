import React from 'react';
import { connect } from 'dva';
import { Input, Row, Col, Button, DatePicker, Select, Cascader, message, Table, Modal } from 'antd';
import { history } from 'umi';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';
import { Decimal } from 'decimal.js';

import * as utils from '../../../utils/utils';
import Drawer from '../../../components/drawer/Drawer';
import OrderInfo from '../components/ScanCodeInfo';
import * as SearchItem from '../components/SearchItem';
import { provinceData } from './province';

import '../styles.less';
import './style.less';

const FormItem = Form.Item;
const { RangePicker } = DatePicker;
const Option = Select.Option;
let isInitRequest = true;
const today = moment().add(1, 'd');

class scanOrderExport extends React.Component {
  constructor(props) {
    super(props);
    this.pageNum = 1;
    this.selectItem = {}; /* 退款或者更新选中的item */
    this.tableHeaderTime = {};
    this.state = {
      footType: '1',
      showModal: true,
      pageSize: 10,
    };
  }
  componentDidMount() {
    this.props.dispatch({ type: 'scanOrder/getHisBusTypeList' });
    this.props.dispatch({ type: 'scanOrder/getByPage', payload: { menuType: this.props.menuType || '' } });
    this.props.dispatch({ type: 'scanOrder/findAllUser', payload: { menuType: this.props.menuType || '' } });
    this.props.dispatch({ type: 'scanOrder/getByList', payload: { menuType: this.props.menuType || '' } });
    this.handleSubmit(this.pageNum);
  }
  // eslint-disable-next-line react/no-deprecated
  componentWillReceiveProps(nextProps) {}

  componentWillUnmount() {}
  /* 更新状态 */
  promiseUpdate(item) {}
  /* 查看订单详情 */
  orderDetail(record) {
    this.props.dispatch({
      type: 'scanOrder/getOrderDetail',
      payload: {
        busType: record.busType,
        id: record.bizOrderId,
        userType: this.props.userType || '',
      },
    });
    this.props.dispatch({
      type: 'root/openDrawer',
    });
  }
  handleSubmit(pageNo, searchIpt) {
    const { userType = '' } = this.props;
    const { pageSize, inputData } = this.state;
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const pageNum = pageNo || 1;
        const reqData = {
          ...values,
          pageNum,
          busType: 'dna_gene_pay',
          inputData: searchIpt ?? inputData,
          numPerPage: pageSize,
          userType,
        };
        this.pageNum = pageNo;
        if (values.transferTime) {
          reqData.payBeginTime = moment(values.transferTime[0]).format('YYYY-MM-DD');
          reqData.payEndTime = moment(values.transferTime[1]).format('YYYY-MM-DD');
        }
        delete reqData.transferTime;
        //开票日期
        if (values.invoiceDate) {
          reqData.invoiceStartDate = moment(values.invoiceDate[0]).format('YYYY-MM-DD');
          reqData.invoiceEndDate = moment(values.invoiceDate[1]).format('YYYY-MM-DD');
        }
        delete reqData.invoiceDate;
        //退款时间
        if (values.refundDate) {
          reqData.refundBeginTime = moment(values.refundDate[0]).format('YYYY-MM-DD');
          reqData.refundEndTime = moment(values.refundDate[1]).format('YYYY-MM-DD');
        }
        delete reqData.refundDate;

        this.queryData = reqData; // 退款后 查询列表用

        this.props.dispatch({
          type: 'scanOrder/getOrdersByPage',
          payload: reqData,
        });
      }
    });
  }

  exportOrder = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        const { userType = '' } = this.props;
        const { inputData } = this.state;
        const reqData = {
          ...values,
          busType: 'dna_gene_pay',
          inputData: inputData,
          userType,
        };
        if (values.transferTime) {
          reqData.payBeginTime = moment(values.transferTime[0]).format('YYYY-MM-DD');
          reqData.payEndTime = moment(values.transferTime[1]).format('YYYY-MM-DD');
        }
        delete reqData.transferTime;
        if (values.invoiceDate) {
          reqData.invoiceStartDate = moment(values.invoiceDate[0]).format('YYYY-MM-DD');
          reqData.invoiceEndDate = moment(values.invoiceDate[1]).format('YYYY-MM-DD');
        }
        delete reqData.invoiceDate;

        //退款时间
        if (values.refundDate) {
          reqData.refundBeginTime = moment(values.refundDate[0]).format('YYYY-MM-DD');
          reqData.refundEndTime = moment(values.refundDate[1]).format('YYYY-MM-DD');
        }
        delete reqData.refundDate;

        this.queryData = reqData; // 退款后 查询列表用

        this.props.dispatch({
          type: 'scanOrder/exportOrder',
          payload: reqData,
        });
      }
    });
  };

  orderReBack(backReason, refundFee, id, busType, rundPwd) {
    this.setState({ showModal: true });
    const { scanOrder = {}, userType = '' } = this.props;
    const { orderInfo = {} } = scanOrder;
    if (!/^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(refundFee) || refundFee * 1 <= 0) {
      message.warn('请输入最多两位小数且大于0的退款金额');
      return false;
    }
    refundFee = new Decimal(refundFee).mul(new Decimal(100)).toNumber();
    let maxRefundFee = orderInfo.totalRealFee || 0;
    if (orderInfo.totalRefundFee > 0) {
      maxRefundFee = new Decimal(maxRefundFee).sub(orderInfo.totalRefundFee).toNumber();
    }
    if (refundFee > maxRefundFee) {
      message.warn(`本次最多可退费金额${new Decimal(maxRefundFee).div(100).toNumber()}元`);
      return false;
    }

    if (!backReason) {
      message.warn('请输入退款原因');
      return;
    }
    if (!rundPwd) {
      message.warn('请输入退款密码');
      return;
    }
    const { dispatch } = this.props;
    const params = {
      busType,
      id,
      reason: backReason,
      rundPwd,
      refundFee,
    };
    dispatch({
      type: 'scanOrder/refund',
      payload: params,
    }).then(res => {
      if (res.data.code == 0) {
        message.success('退款成功');
        this.setState({ showModal: false });
        this.props.dispatch({
          type: 'scanOrder/getOrderDetail',
          payload: {
            busType: params.busType,
            id: params.id,
            userType,
          },
        });
        this.props.dispatch({
          type: 'scanOrder/getOrdersByPage',
          payload: this.queryData,
        });
      } else {
        message.error(res.data.msg || '退款失败');
      }
    });
  }
  itemRender = (current, type, originalElement) => {
    if (type === 'prev') {
      return <a>上一页</a>;
    }
    if (type === 'next') {
      return <a>下一页</a>;
    }
    if (type === 'page') {
      return <a>{current}</a>;
    }
    return originalElement;
  };
  // 表格每页行数改变
  onShowSizeChange = (c, size) => {
    console.log(size, '=====155');
    this.setState({ pageSize: size }, this.handleSubmit);
  };

  handleReset = () => {
    this.props.form.resetFields();
    this.handleSubmit(this.pageNum);
  };
  render() {
    const { showModal, pageSize } = this.state;
    const { drawerStatus, scanOrder = {}, userType, menuType } = this.props;
    const { hisBusTypeList = [], tableData = {}, productList = [], doctorList = [], customerList = [] } = scanOrder;
    const { getFieldDecorator } = this.props.form;
    const formItemLayout = {
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    };

    let columns = [
      {
        title: '支付时间',
        width: 120,
        fixed: 'left',
        dataIndex: 'payTime',
      },
      {
        title: '患者姓名',
        // width: 100,
        fixed: 'left',
        dataIndex: 'patientName',
      },
      {
        title: '身份证号码',
        width: 190,
        dataIndex: 'idNumber',
      },
      // {
      //   title: '就诊卡号',
      //   width: 190,
      //   dataIndex: 'patCardNo',
      // },
      {
        title: '合作客户',
        // width: 100,
        dataIndex: 'deptName',
      },
      {
        title: '产品线',
        dataIndex: 'extFieldsSearch',
        render: v => <pre>{v}</pre>,
      },
      {
        title: '子产品',
        dataIndex: 'sunProduct',
        render: v => <pre>{v}</pre>,
        // render: record => {
        //   try {
        //     const extFieldsViews = JSON.parse(record.extFieldsViews);
        //     const products = JSON.parse(extFieldsViews.products);
        //     return products.map((v, i) => <p key={i}>{!v.productId ? `(${v.productName})${v.remark}` : v.productName}</p>);
        //   } catch (error) {
        //     return '';
        //   }
        // },
      },
      {
        title: '产品终端价',
        width: 120,
        dataIndex: 'productPrices',
        render: v => <pre>{v}</pre>,
        // render: record => {
        //   try {
        //     const extFieldsViews = JSON.parse(record.extFieldsViews);
        //     const products = JSON.parse(extFieldsViews.products);
        //     return products.map((v, i) => <p key={i}>{utils.comma(v.productprice || 0)}</p>);
        //   } catch (error) {
        //     return '';
        //   }
        // },
      },
      {
        title: '订单状态',
        width: 100,
        dataIndex: 'statusDesc',
      },
      {
        title: '支付金额',
        width: 100,
        render: record => {
          return <p>{utils.comma(record.totalRealFee || 0)}</p>;
        },
      },
      {
        title: '是否加急',
        width: 100,
        render: record => {
          return <span>{record.hisRecepitNo == 1 ? '是' : '否'}</span>;
        },
      },
      {
        title: '开单方式',
        width: 100,
        render: record => {
          return <span>{record.subSource == 1 ? '患者开单' : '客户开单'}</span>;
        },
      },
      {
        title: '来源医生',
        width: 100,
        dataIndex: 'hisSerialNo',
      },
      {
        title: '客户省份',
        width: 100,
        dataIndex: 'admissionNum',
      },
      {
        title: '支付方式',
        // width: 190,
        dataIndex: 'payMethod',
      },
      {
        title: '支付流水号',
        width: 190,
        dataIndex: 'agtOrdNum',
      },
      {
        title: '开票状态',
        dataIndex: 'invoiceNo',
        render: v => (v ? '已开票' : '未开票'),
      },
      {
        title: '开票日期',
        dataIndex: 'invoiceDate',
      },
      {
        title: '销售人员',
        dataIndex: 'salesName',
      },
      {
        title: '样本编号',
        dataIndex: 'sampleNumber',
      },
      {
        title: '快递单号',
        dataIndex: 'mailNo',
      },
      {
        title: '退款时间',
        dataIndex: 'refundSuccessTime',
      },
      {
        title: '操作',
        fixed: 'right',
        render: (text, record, index) => {
          return (
            <div className="opt-cloum">
              <a onClick={() => this.orderDetail(record)}>详情</a>
              {userType == 2 || userType == 3
                ? null
                : !!record.payTime && (
                    <a onClick={() => history.push(`/transaction/scancode/record?patIdNo=${record.idNumber}&patName=${record.patientName}&startTime=${record.payTime}`)} style={{ marginLeft: '8px' }}>
                      门诊记录
                    </a>
                  )}
            </div>
          );
        },
      },
    ];

    if (userType == 2 || userType == 3) {
      columns = columns.filter(item => {
        return !['salesName'].includes(item.dataIndex);
      });
    }
    return (
      <div className="page-transaction page-transaction-order order-all">
        <div className="tsc-top-panle">
          <Form
            hideRequiredMark={true}
            onSubmit={e => {
              e.preventDefault();
              this.handleSubmit();
            }}
          >
            <Row type="flex" justify="start">
              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="支付日期">
                  {getFieldDecorator('transferTime', {})(<DatePicker.RangePicker allowClear format="YYYY-MM-DD" disabledDate={this.disabledDate} style={{ width: '100%' }} />)}
                </FormItem>
              </Col>

              {userType == 3 || userType == 2 || userType == 1 ? null : (
                <Col span={8} className="col-item">
                  <FormItem {...formItemLayout} label="开单产品">
                    {getFieldDecorator('productName', {
                      initialValue: '',
                    })(
                      <Select
                        placeholder="请选择开单产品"
                        showSearch
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                      >
                        <Option value="">全部</Option>
                        {productList.map(item => {
                          return (
                            <Option value={`${item.productName}`} key={item.id}>
                              {item.productName}
                            </Option>
                          );
                        })}
                        ,
                      </Select>,
                    )}
                  </FormItem>
                </Col>
              )}

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="是否加急">
                  {getFieldDecorator('isMerge', {
                    initialValue: '',
                  })(
                    <Select>
                      <Option value="">全部</Option>
                      <Option value="1">是</Option>
                      <Option value="0">否</Option>
                    </Select>,
                  )}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="订单状态">
                  {getFieldDecorator('status', {
                    initialValue: 'S,F,H,Z,C,U',
                  })(
                    <Select
                      placeholder="请选择订单状态"
                      filterOption={(input, option) => {
                        return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      }}
                    >
                      {hisBusTypeList.map(item => {
                        return (
                          <Option value={`${item.dictKey}`} key={item.dictKey}>
                            {item.dictValue}
                          </Option>
                        );
                      })}
                    </Select>,
                  )}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="退款情况">
                  {getFieldDecorator('refundState', {
                    initialValue: '-1',
                    rules: [
                      {
                        required: 'Y',
                        message: '请选择退款情况',
                      },
                    ],
                  })(
                    <Select>
                      <Option value="-1">全部</Option>
                      <Option value="1">有退款</Option>
                      <Option value="0">无退款</Option>
                    </Select>,
                  )}
                </FormItem>
              </Col>

              {userType == 2 || userType == 3 ? null : (
                <Col span={8} className="col-item">
                  <FormItem {...formItemLayout} label="来源医生">
                    {getFieldDecorator('hisOrderNo', {
                      initialValue: '',
                    })(
                      <Select
                        showSearch
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                      >
                        <Option value="">全部</Option>
                        {doctorList.map(item => {
                          return (
                            <Option value={`${item.account}`} key={item.account}>
                              {item.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </Col>
              )}

              {userType == 2 || userType == 3 ? null : (
                <Col span={8} className="col-item">
                  <FormItem {...formItemLayout} label="合作客户">
                    {getFieldDecorator('deptId', {
                      initialValue: '',
                    })(
                      <Select
                        showSearch
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                      >
                        <Option value="">全部</Option>
                        {customerList.map(item => {
                          return (
                            <Option value={`${item.id}`} key={item.id}>
                              {item.institutionName}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </Col>
              )}

              {userType == 2 || userType == 3 ? null : (
                <Col span={8} className="col-item">
                  <FormItem {...formItemLayout} label="来源省份">
                    {getFieldDecorator('admissionNum', {
                      initialValue: '',
                    })(
                      <Select
                        placeholder="请选择"
                        showSearch
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                      >
                        <Option value="">全部</Option>
                        {provinceData.map((v, i) => (
                          <Option value={v.label} key={i}>
                            {v.label}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </FormItem>
                </Col>
              )}

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="支付方式">
                  {getFieldDecorator('payMethod', {
                    initialValue: '',
                  })(
                    <Select>
                      <Option value="">全部</Option>
                      <Option value="微信支付">微信支付</Option>
                      <Option value="门诊缴费">门诊缴费</Option>
                    </Select>,
                  )}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="支付流水号">
                  {getFieldDecorator('agtOrdNum', {
                    initialValue: '',
                    rules: [
                      {
                        message: '请输入正确的支付流水号',
                        pattern: /^[a-zA-Z0-9]*$/,
                      },
                    ],
                  })(<Input maxLength="64" placeholder="请输入支付流水号" />)}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="开单模式">
                  {getFieldDecorator('subSource', {
                    initialValue: '',
                  })(
                    <Select>
                      <Option value="">全部</Option>
                      <Option value="2">客户开单</Option>
                      <Option value="1">患者开单</Option>
                    </Select>,
                  )}
                </FormItem>
              </Col>

              {!menuType && (
                <Col span={8} className="col-item">
                  <FormItem {...formItemLayout} label="销售人员">
                    {getFieldDecorator('salesId', {
                      initialValue: '',
                    })(
                      <Select
                        showSearch
                        filterOption={(input, option) => {
                          return option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        }}
                      >
                        <Option value="">全部</Option>
                        {doctorList.map(item => {
                          return (
                            <Option value={`${item.id}`} key={item.account}>
                              {item.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </Col>
              )}

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="开票日期">
                  {getFieldDecorator('invoiceDate', {})(<DatePicker.RangePicker allowClear format="YYYY-MM-DD" disabledDate={this.disabledDate} style={{ width: '100%' }} />)}
                </FormItem>
              </Col>

              <Col span={8} className="col-item">
                <FormItem {...formItemLayout} label="退款时间">
                  {getFieldDecorator('refundDate', {})(<DatePicker.RangePicker allowClear format="YYYY-MM-DD" disabledDate={this.disabledDate} style={{ width: '100%' }} />)}
                </FormItem>
              </Col>

              <Col span={userType == 3 || userType == 2 || userType == 1 ? 24 : !!userType ? 16 : 8} className="btn-bar">
                <Button type="primary" htmlType="submit">
                  查询
                </Button>
                <Button onClick={() => this.handleReset()}>重置</Button>
              </Col>
            </Row>
          </Form>
        </div>

        <div className="tsc-main-panle" style={{ padding: '0 24px' }}>
          <div className="scancode-search-ipt">
            <div className="antd-pro-pages-statistics-goodpregnancyrelay-index-left">
              <Input.Search
                placeholder="请输入患者姓名或样本编号或快递单号"
                enterButton
                style={{ width: 350 }}
                onSearch={val => this.setState({ inputData: val }, this.handleSubmit(1, val))}
                allowClear
              />
            </div>
            {userType == 2 || userType == 3 ? null : (
              <Button className="ant-col-offset-1" type="primary" onClick={this.exportOrder}>
                数据导出
              </Button>
            )}
          </div>
          <Table
            columns={columns}
            dataSource={tableData.recordList}
            rowKey="bizOrderId"
            scroll={{ x: 'max-content' }}
            pagination={{
              showSizeChanger: false,
              showQuickJumper: true,
              current: tableData.currentPage,
              pageSize: tableData.numPerPage,
              total: tableData.totalCount,
              showTotal: total => `共 ${total} 条`,
              onChange: pageNum => {
                this.handleSubmit(pageNum, this.state.inputData);
              },
              // itemRender: this.itemRender,
              // pageSize,
              // onShowSizeChange: this.onShowSizeChange,
            }}
          />
        </div>

        <Drawer open={drawerStatus} className="transaction-drawer">
          <OrderInfo funcReBack={(backReason, refundFee, id, busType, rundPwd) => this.orderReBack(backReason, refundFee, id, busType, rundPwd)} showModal={showModal} userType={userType} />
        </Drawer>
      </div>
    );
  }
}

export default connect(state => {
  return {
    scanOrder: state.scanOrder,
    drawerStatus: state.root.drawerStatus,
  };
})(Form.create()(scanOrderExport));
