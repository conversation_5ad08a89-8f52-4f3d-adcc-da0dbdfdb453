/* eslint-disable react/jsx-no-duplicate-props */
import React, { useEffect, useMemo, useState } from 'react';
import { history, connect } from 'umi';
import { Form, Row, Col, Input, Select, Space, Button, Table, DatePicker, Popconfirm, message } from 'antd';
import { useAntdTable } from 'ahooks';
import { merge } from 'lodash';
import moment from 'moment';
import * as Api from './service';
import { NEED_SIGN, STATUS, NEED_SIGN_MAP, STATUS_MAP, SHOW_STATUS } from './_data';
import './style.less';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};
const { RangePicker } = DatePicker;

const Index = props => {
  const [form] = Form.useForm();
  const { permissionData = {} } = props;
  const { btns = {} } = permissionData;

  const handleDelete = async (id, record) => {
    if (record.signNum) {
      message.error('已有用户提交报名，不能直接删除');
      return;
    }
    const data = await Api.deleteTrain({
      id,
    });
    message.success(data.msg || '删除成功');
    submit();
  };

  const openQRCode = async id => {
    const data = await Api.getQRCode({
      scene: `t=2&id=${id}`,
      page: 'pages/guest/index',
    });
    if (data.code === 0) {
      const el = document.createElement('a');
      el.href = data.data;
      el.target = '_blank';
      document.body.appendChild(el);
      el.click();
    }
  };

  const fetchList = async ({ current = 1, pageSize = 10 }) => {
    try {
      const { time = [], ...rest } = form.getFieldsValue();
      const params = {
        pageNum: current,
        numPerPage: pageSize,
        publishBeginTime: time[0] && moment(time[0]).format('YYYY-MM-DD'),
        publishEndTime: time[0] && moment(time[1]).format('YYYY-MM-DD'),
        ...(rest || {}),
      };

      const data = await Api.fetchList({ ...params });

      return {
        total: Number(data.data.totalCount) || 0,
        list: data?.data?.recordList || [],
      };
    } catch (error) {
      console.log(error);
    }
  };

  const { loading, tableProps, search } = useAntdTable(fetchList, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
  });

  const tableRealProps = useMemo(
    () =>
      merge(tableProps, {
        pagination: {
          // showQuickJumper: true,
          // showSizeChanger: true,
          showTotal: total => `共 ${total} 条`,
        },
      }),
    [tableProps],
  );

  const { submit, reset } = search;

  const columns = [
    {
      title: '活动名称',
      dataIndex: 'title',
    },
    {
      title: '报名',
      dataIndex: 'needSign',
      render: v => NEED_SIGN_MAP[v]?.label,
    },
    {
      title: '开始日期',
      dataIndex: 'beginTime',
    },
    {
      title: '截止日期',
      dataIndex: 'endTime',
    },
    {
      title: '上下架',
      dataIndex: 'isShow',
      render: v => SHOW_STATUS[v],
    },
    {
      title: '活动状态',
      dataIndex: 'status',
      render: v => STATUS_MAP[v]?.label,
    },
    {
      title: '报名人数',
      dataIndex: 'signNum',
    },
    {
      title: '待审核',
      dataIndex: 'waitNum',
    },
    {
      title: '已通过',
      dataIndex: 'accessNum',
    },
    {
      title: '已驳回',
      dataIndex: 'failNum',
    },
    {
      title: '已取消',
      dataIndex: 'cancelNum',
    },
    {
      title: '活动发布时间',
      dataIndex: 'createTime',
    },
    {
      title: '操作',
      dataIndex: 'id',
      fixed: 'right',
      render: (id, record) => (
        <div className="edit-btn">
          <span onClick={() => openQRCode(id)}>二维码</span>
          <span
            onClick={() => {
              history.push(`/trait/train/edit?id=${id}`);
            }}
          >
            详情
          </span>
          {record.needSign === 1 && (
            <span
              onClick={() => {
                history.push(`/trait/train/detail?id=${id}`);
              }}
            >
              报名详情
            </span>
          )}
          {btns['/trait/train/delete'] && (
            <Popconfirm title="确定要删除当前活动吗？" onConfirm={() => handleDelete(id, record)} okText="确认" cancelText="取消">
              <a href="#" style={{ color: '#FF4D4F' }}>
                删除
              </a>
            </Popconfirm>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="g-page p-product-list">
      <div className="g-query-box">
        <Form form={form} {...formItemLayout} labelWrap>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="needSign" label="是否报名" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...NEED_SIGN]} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="status" label="活动状态" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...STATUS]} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="time" label="发布日期">
                <RangePicker />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="title" label="活动名称" initialValue="">
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col span={16} style={{ textAlign: 'right' }}>
              <Space>
                <Button type="primary" onClick={submit} disabled={loading}>
                  查询
                </Button>
                <Button onClick={reset} disabled={loading}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="p-product-table">
        <div className="flex-box">
          {btns['/trait/train/add'] && (
            <Button type="primary" disabled={loading} onClick={() => history.push('/trait/train/add')}>
              添加活动
            </Button>
          )}
        </div>
        <Table rowKey="id" {...tableRealProps} loading={loading} columns={columns} scroll={{ x: 'max-content' }} />
      </div>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Index);
