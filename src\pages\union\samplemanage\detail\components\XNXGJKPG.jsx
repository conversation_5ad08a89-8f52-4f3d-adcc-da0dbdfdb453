/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select, DatePicker } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { xnxgjkpgtemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, xnxgjkpgtemplate: { ...xnxgjkpgtemplate, ...payload } },
      },
    });
  };

  const changeTableData = (val, ind, key) => {
    const { field2 = [] } = xnxgjkpgtemplate;
    field2[ind][key] = val;
    changeData({ field2 });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="亲属信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="亲属（包括本人）做过此检测">
                <div className="flex-box">
                  <Radio.Group value={xnxgjkpgtemplate.field1} onChange={e => changeData({ field1: e.target.value })}>
                    <Radio value="否">否</Radio>
                    <Radio value="是">是</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            {xnxgjkpgtemplate.field1 === '是' ? (
              <Col span={24} style={{ marginBottom: '24px' }}>
                <Table
                  dataSource={xnxgjkpgtemplate.field2 || []}
                  columns={[
                    {
                      title: '亲属检测编号',
                      render: (cur, col, index) => {
                        return <Input placeholder="请输入" value={col.field1} onChange={e => changeTableData(e.target.value, index, 'field1')} />;
                      },
                    },
                    {
                      title: '亲属姓名',
                      render: (cur, col, index) => {
                        return <Input placeholder="请输入" value={col.field2} onChange={e => changeTableData(e.target.value, index, 'field2')} />;
                      },
                    },
                    {
                      title: '亲属关系',
                      render: (cur, col, index) => {
                        return <Input placeholder="请输入" value={col.field3} onChange={e => changeTableData(e.target.value, index, 'field3')} />;
                      },
                    },
                    {
                      title: '亲属检测结果',
                      render: (cur, col, index) => {
                        return <Input placeholder="请输入" value={col.field4} onChange={e => changeTableData(e.target.value, index, 'field4')} />;
                      },
                    },
                    {
                      title: '操作',
                      width: 60,
                      render: (cur, col, index) => (
                        <Button type="link" onClick={() => changeData({ field2: xnxgjkpgtemplate.field2.filter((item, ind) => index != ind) })}>
                          删除
                        </Button>
                      ),
                    },
                  ]}
                  pagination={false}
                />
                <Button type="dashed" block onClick={() => changeData({ field2: [...(xnxgjkpgtemplate.field2 || []), {}] })}>
                  +添加亲属
                </Button>
              </Col>
            ) : null}
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="生理生化指标检测" key="1">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item label="腰围(cm)">
                <Input placeholder="请输入" value={xnxgjkpgtemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="收缩压(mmHg)">
                <Input placeholder="请输入" value={xnxgjkpgtemplate.field5} onChange={e => changeData({ field5: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="舒张压(mmHg)">
                <Input placeholder="请输入" value={xnxgjkpgtemplate.field6} onChange={e => changeData({ field6: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="总胆固醇">
                <div className="flex-box">
                  <Input placeholder="请输入" value={xnxgjkpgtemplate.field8} onChange={e => changeData({ field8: e.target.value })} />
                  <div className="flex-shrink">
                    <Radio.Group value={xnxgjkpgtemplate.field9} onChange={e => changeData({ field9: e.target.value })}>
                      <Radio value="Mg/dL">Mg/dL</Radio>
                      <Radio value="mmol/L">mmol/L</Radio>
                    </Radio.Group>
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="高密度脂蛋白胆固醇">
                <div className="flex-box">
                  <Input placeholder="请输入" value={xnxgjkpgtemplate.field10} onChange={e => changeData({ field10: e.target.value })} />
                  <div className="flex-shrink">
                    <Radio.Group value={xnxgjkpgtemplate.field11} onChange={e => changeData({ field11: e.target.value })}>
                      <Radio value="Mg/dL">Mg/dL</Radio>
                      <Radio value="mmol/L">mmol/L</Radio>
                    </Radio.Group>
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="低密度脂蛋白胆固醇">
                <div className="flex-box">
                  <Input placeholder="请输入" value={xnxgjkpgtemplate.field12} onChange={e => changeData({ field12: e.target.value })} />
                  <div className="flex-shrink">
                    <Radio.Group value={xnxgjkpgtemplate.field13} onChange={e => changeData({ field13: e.target.value })}>
                      <Radio value="Mg/dL">Mg/dL</Radio>
                      <Radio value="mmol/L">mmol/L</Radio>
                    </Radio.Group>
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="脂蛋白a">
                <div className="flex-box">
                  <Input placeholder="请输入" value={xnxgjkpgtemplate.field14} onChange={e => changeData({ field14: e.target.value })} />
                  <div className="flex-shrink">
                    <Radio.Group value={xnxgjkpgtemplate.field15} onChange={e => changeData({ field15: e.target.value })}>
                      <Radio value="Mg/dL">Mg/dL</Radio>
                      <Radio value="Mg/L">Mg/L</Radio>
                      <Radio value="nmol/L">nmol/L</Radio>
                    </Radio.Group>
                  </div>
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="甘油三酯">
                <div className="flex-box">
                  <Input placeholder="请输入" value={xnxgjkpgtemplate.field16} onChange={e => changeData({ field16: e.target.value })} />
                  <div className="flex-shrink">
                    <Radio.Group value={xnxgjkpgtemplate.field17} onChange={e => changeData({ field17: e.target.value })}>
                      <Radio value="Mg/dL">Mg/dL</Radio>
                      <Radio value="mmol/L">mmol/L</Radio>
                    </Radio.Group>
                  </div>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="其他" key="1">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item label="是否患糖尿病">
                <div className="flex-box">
                  <Radio.Group value={xnxgjkpgtemplate.field18} onChange={e => changeData({ field18: e.target.value })}>
                    <Radio value="是">是</Radio>
                    <Radio value="否">否</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="是否吸烟">
                <div className="flex-box">
                  <Radio.Group value={xnxgjkpgtemplate.field19} onChange={e => changeData({ field19: e.target.value })}>
                    <Radio value="是">是</Radio>
                    <Radio value="否">否</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="您居住的区域">
                <div className="flex-box">
                  <Radio.Group value={xnxgjkpgtemplate.field20} onChange={e => changeData({ field20: e.target.value })}>
                    <Radio value="南方">南方</Radio>
                    <Radio value="北方">北方</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="您居住的城乡区域">
                <div className="flex-box">
                  <Radio.Group value={xnxgjkpgtemplate.field21} onChange={e => changeData({ field21: e.target.value })}>
                    <Radio value="城市">城市</Radio>
                    <Radio value="乡村">乡村</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="近两周是否服用降压药">
                <div className="flex-box">
                  <Radio.Group value={xnxgjkpgtemplate.field22} onChange={e => changeData({ field22: e.target.value })}>
                    <Radio value="是">是</Radio>
                    <Radio value="否">否</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="近两周是否服用降糖药">
                <div className="flex-box">
                  <Radio.Group value={xnxgjkpgtemplate.field23} onChange={e => changeData({ field23: e.target.value })}>
                    <Radio value="是">是</Radio>
                    <Radio value="否">否</Radio>
                  </Radio.Group>
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="家族史情况">
                <Checkbox.Group value={xnxgjkpgtemplate.field24 ? xnxgjkpgtemplate.field24.split(',') : []} onChange={v => changeData({ field24: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="父母患脑卒中">父母患脑卒中</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="兄弟姐妹患脑卒中">兄弟姐妹患脑卒中</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="父母患冠心病">父母患冠心病</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="兄弟姐妹患冠心病">兄弟姐妹患冠心病</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="主诉">
                <TextArea placeholder="请输入" defaultValue={xnxgjkpgtemplate.field28} onChange={e => changeData({ field28: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="送检材料">
                <Checkbox.Group value={xnxgjkpgtemplate.field30 ? xnxgjkpgtemplate.field30.split(',') : []} onChange={v => changeData({ field30: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={12}>
                      <Checkbox value="外周血（EDTA抗凝管塑料材质紫色盖子）">外周血（EDTA抗凝管塑料材质紫色盖子）</Checkbox>
                    </Col>
                    <Col span={12}>
                      <Checkbox value="外周血（黄色促凝管）">外周血（黄色促凝管）</Checkbox>
                    </Col>
                    <Col span={24}>
                      <div className="flex-box">
                        <Checkbox value="其他">其他</Checkbox>
                        <div className="flex-box">
                          <Input placeholder="请输入" value={xnxgjkpgtemplate.field31} onChange={e => changeData({ field31: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="样品总量">
                <Input placeholder="请输入" value={xnxgjkpgtemplate.field32} onChange={e => changeData({ field32: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="样品管数">
                <Input placeholder="请输入" value={xnxgjkpgtemplate.field33} onChange={e => changeData({ field33: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="医院编号">
                <Input placeholder="请输入" value={xnxgjkpgtemplate.field34} onChange={e => changeData({ field34: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="特殊情况备注">
                <TextArea placeholder="请输入" defaultValue={xnxgjkpgtemplate.field35} onChange={e => changeData({ field35: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
