import React, { useEffect, useState } from 'react';
import { Form, Input, Upload, message, Radio, DatePicker, Button, Space, InputNumber } from 'antd';
import { PictureOutlined, PlusOutlined } from '@ant-design/icons';
import { history, connect } from 'umi';
import * as CONSTANT from '@/config/constant/constant';
import TinymceEditor from '@/components/editor/TinymceEditor';
import moment from 'moment';
import '../style.less';
import * as Api from '../service';

const { RangePicker } = DatePicker;

const Panel = props => {
  const { permissionData = {}, id } = props;
  const { btns = {} } = permissionData;
  const [form] = Form.useForm();
  const needSign = Form.useWatch('needSign', form);
  const isLimit = Form.useWatch('isLimit', form);
  const feeFlg = Form.useWatch('feeFlg', form);
  const feeType = Form.useWatch('feeType', form);
  const [imgUrl, setImgUrl] = useState('');
  const [detail, setDetail] = useState({});

  const [disabled, setDisabled] = useState(!!id);

  useEffect(() => {
    if (id) {
      getDetail();
    }
  }, []);

  const getDetail = async () => {
    try {
      const { data } = await Api.getDetail({ id });
      data.feePrice = (data.feePrice / 100).toFixed(2);
      form.setFieldsValue({ ...data, time: data.beginTime ? [moment(data.beginTime), moment(data.endTime)] : [], signTime: [moment(data.signBegin), moment(data.signEnd)] });
      setDetail(data);
      setImgUrl(data.img);
    } catch (error) {
      console.log(error);
    }
  };

  const beforeUpload = file => {
    const isImage = file.type && file.type.indexOf('image') > -1;
    if (!isImage) {
      message.error('请选择图片进行上传!');
    }
    const size = file.size / 1024 / 1024 <= 2;
    if (!size) {
      message.error('图片大小不能超过2MB!');
    }
    return isImage && size;
  };

  const uploadOnChange = e => {
    const { response } = e.file;
    if (response) {
      let picUrl = '';
      if (response.code != 0) {
        message.error('上传文件失败');
      } else {
        picUrl = response.data;
      }
      setImgUrl(picUrl);
    }
  };

  const onFinish = async values => {
    try {
      const { time = [], signTime = [], feePrice, ...rest } = values;
      const data = await Api[`${id ? 'update' : 'add'}`]({
        id,
        ...rest,
        feePrice: feePrice * 100,
        img: imgUrl,
        beginTime: time[0] && moment(time[0]).format('YYYY-MM-DD'),
        endTime: time[0] && moment(time[1]).format('YYYY-MM-DD'),
        signBegin: signTime[0] && moment(signTime[0]).format('YYYY-MM-DD'),
        signEnd: signTime[0] && moment(signTime[1]).format('YYYY-MM-DD'),
      });
      if (data.code === 0) {
        message.success(data.msg);
        history.goBack();
      }
    } catch (error) {
      console.log(error);
    }
  };

  console.log('detail', detail);
  console.log(feeType);

  return (
    <div className="page-train-add">
      <Form labelCol={{ span: 6 }} wrapperCol={{ span: 10 }} form={form} onFinish={onFinish} disabled={disabled}>
        {/* <h3>报名介绍</h3> */}
        <Form.Item name="title" label="标题" rules={[{ required: true }]}>
          <Input placeholder="请输入" width="240px" />
        </Form.Item>
        <Form.Item name="img" label="封面图片">
          {imgUrl ? (
            <div style={{ display: 'flex', width: '600px' }}>
              <img src={imgUrl} alt="" className="avatar" />
              <div style={{ padding: '146px 0 0 10px' }}>
                <Upload name="idFile" action={`${CONSTANT.DOMAIN}/api/article/upfile`} beforeUpload={beforeUpload} showUploadList={false} onChange={uploadOnChange}>
                  <a disabled={disabled}>更换图片</a>
                </Upload>
              </div>
            </div>
          ) : (
            <Upload
              name="idFile"
              className="avatar-uploader"
              action={`${CONSTANT.DOMAIN}/api/article/upfile`}
              beforeUpload={beforeUpload}
              showUploadList={false}
              listType="picture"
              onChange={uploadOnChange}
            >
              <div className="avatar-uploader-trigger">
                <PictureOutlined style={{ fontSize: '48px' }} />
                <div style={{ color: '#404040', lineHeight: 1.5, marginBottom: 7 }}>点击此区域上传图片</div>
                <div style={{ color: '#919191', lineHeight: 1.5 }}>大小不超过2M，建议尺寸460*270</div>
              </div>
            </Upload>
          )}
        </Form.Item>
        <Form.Item name="contetn" label="正文" rules={[{ required: true }]} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
          {disabled ? (
            <div dangerouslySetInnerHTML={{ __html: detail.contetn || '' }} style={{ width: '650px', overflow: 'auto' }} />
          ) : (
            <TinymceEditor
              id={`update-article-${detail.id}-editor`}
              content={detail.contetn || ' '}
              url={`${CONSTANT.DOMAIN}/api/article/upfile`}
              onChange={content => {
                // log('content', content);
                setDetail({ ...detail, contetn: content });
              }}
              disabled={disabled}
            />
          )}
        </Form.Item>
        <Form.Item name="sort" label="序号" rules={[{ required: true }]}>
          {/* <Input placeholder="请输入" width="240px" /> */}
          <InputNumber placeholder="请输入" min={0} max={2000000000} />
        </Form.Item>
        <Form.Item name="isShow" label="活动上下架" rules={[{ required: true }]} initialValue={1}>
          <Radio.Group>
            <Radio value={1}>上架</Radio>
            <Radio value={0}>下架</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item name="needSign" label="是否需要线上报名" rules={[{ required: true }]}>
          <Radio.Group>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </Form.Item>
        {needSign === 1 ? (
          <>
            <Form.Item name="signTime" label="活动报名起止日期" rules={[{ required: true }]}>
              <RangePicker />
            </Form.Item>
            <Form.Item name="isLimit" label="报名人数限制" rules={[{ required: true }]} initialValue={0}>
              <Radio.Group>
                <Radio value={0}>不限制</Radio>
                <Radio value={1}>限制</Radio>
              </Radio.Group>
            </Form.Item>
            {isLimit === 1 && (
              <Form.Item name="limit" label="请输入报名人数上限" rules={[{ required: true }]}>
                <InputNumber placeholder="请输入" min={0} />
              </Form.Item>
            )}
            <Form.Item name="docs" label="报名上传其他资料要求文字编辑">
              <Input placeholder="请输入" width="240px" />
            </Form.Item>
            <Form.Item name="feeFlg" label="是否需要在线缴纳报名费" rules={[{ required: true }]}>
              <Radio.Group>
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </Radio.Group>
            </Form.Item>
            {feeFlg == 1 ? (
              <>
                <Form.Item name="feeType" label="收费模式" rules={[{ required: true }]}>
                  <Radio.Group>
                    <Radio value={1}>按月</Radio>
                    <Radio value={2}>按场</Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item name="feePrice" label="收费金额" rules={[{ required: true }]}>
                  <InputNumber placeholder="请输入" min={0} precision={2} addonAfter={feeType == 1 || (!feeType && detail.feeType == 1) ? '元/月' : '元'} />
                </Form.Item>
              </>
            ) : null}
          </>
        ) : needSign === 0 ? (
          <Form.Item name="time" label="活动起止日期">
            <RangePicker />
          </Form.Item>
        ) : (
          ''
        )}

        <div style={{ display: 'flex', justifyContent: 'center' }}>
          {disabled ? (
            btns['/trait/train/edit'] && (
              <Button type="primary" onClick={() => setDisabled(false)} disabled={false}>
                编辑
              </Button>
            )
          ) : (
            <Space>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
              <Button onClick={() => history.goBack()}>取消</Button>
            </Space>
          )}
        </div>
      </Form>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Panel);
