import { connect } from 'dva';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';

export default function IIHOCDoctorDrawer({ WrappedComponent, detail = {}, type, hisId, listQueryParam }) {
  class Doctor<PERSON><PERSON>er extends WrappedComponent {
    constructor(props) {
      super(props);
      this.state = {
        detail,
        type,
        hisId,
        listQueryParam,
        ...this.state,
      };
    }

    closeDrawer = () => {
      const { dispatch } = this.props;
      dispatch({
        type: 'root/closeDrawer',
      });
    };

    render() {
      return super.render();
    }
  }

  return connect(state => {
    return {
      deptTree: state.microwebsite.department.tree,
    };
  })(Form.create()(<PERSON><PERSON><PERSON><PERSON>));
}
