@charset "utf-8";
@import '../../../resources/styles/mixins';

.page-department {
  .deptlogo-uploader-trigger,
  .deptlogo {
    max-width: 120px;
    max-height: 120px;
    text-align: center;
  }

  .deptlogo-upload-icon {
    font-size: 32px;
    color: @primary-color;
    margin-bottom: 14px;
  }

  .deptlogo-uploader {
    display: block;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    max-width: 120px;

    .upload-img {
      display: none;
    }

    .upload-img-tip {
      display: none;
    }

    &.deptlogo-has-img {
      border: none;
      .deptlogo-uploader-trigger {
        display: none;
      }
      .upload-img {
        max-width: 120px;
        max-height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        img {
          max-width: 100%;
          max-height: 100%;
        }
      }
      .upload-img-tip {
        text-align: center;
        font-size: 12px;
        color: @primary-color;
        margin-top: 10px;
        display: block;
      }
    }
  }

  .deptlogo-uploader-trigger {
    display: table-cell;
    vertical-align: middle;
    color: #999;
  }

  .detail-intro-imgview {
    max-width: 375px;
    max-height: 160px;
  }

  .detail-intro-logoimgview {
    max-width: 120px;
    max-height: 120px;
  }
}
