.phone {
  width: 375px;
  height: 667px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 5px;
  overflow: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  margin: 0 auto;

  .phone-head {
    height: 60px;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    color: white;
    background-color: #000000;
    border-bottom: 1px solid #ddd;
    position: relative;
  }

  .phone-body {
    padding: 15px;
    height: calc(100% - 60px);
    overflow-y: auto;

    .question-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 15px;
      text-align: center;
    }

    .question-phone-list {
      margin-bottom: 10px;
      line-height: 1.5;
    }

    .phone-question-list {
      margin-top: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;

      p {
        margin-bottom: 10px;
        font-weight: 500;
        
        .title {
          margin-right: 5px;
        }
        
        .bold {
          font-weight: bold;
        }
        
        .questions-type {
          display: inline-block;
          padding: 2px 8px;
          background-color: #f0f9eb;
          color: #67c23a;
          font-size: 12px;
          border-radius: 3px;
          margin-right: 5px;
        }
        
        .questions-required {
          display: inline-block;
          padding: 2px 8px;
          background-color: #fff0f0;
          color: #f56c6c;
          font-size: 12px;
          border-radius: 3px;
        }
      }

      // 填空题（多空填值）样式
      .multi-fill-blank {
        padding: 8px 0;
        line-height: 1.8;
        font-size: 15px;
        
        input {
          display: inline-block;
          width: 80px;
          border-top: none;
          border-left: none;
          border-right: none;
          border-radius: 0;
          border-bottom: 1px solid #333;
          padding: 0 4px;
          margin: 0 4px;
          text-align: center;
          
          &:focus {
            box-shadow: none;
            border-bottom: 1px solid #1890ff;
          }
        }
      }

      .option-list-item {
        margin-bottom: 10px;
        display: flex;
        flex-direction: column;
        align-items: flex-start; // 确保选项靠左对齐
        
        &.list-item-flex {
          display: flex;
          flex-direction: column;
          align-items: flex-start; // 确保选项靠左对齐
        }
      }
      
      // 二级选择样式
      .second-level-options {
        margin-left: 22px;
        margin-top: 8px;
        padding: 8px;
        background-color: #f9f9f9;
        border-radius: 4px;
        border-left: 2px solid #1890ff;
        
        // 二级选项中的checkbox对齐
        .ant-checkbox-wrapper {
          margin-left: 0;
          margin-bottom: 4px;
        }
        
        // 二级复选框列表
        .second-level-checkbox-list {
          .second-level-checkbox-item {
            margin-bottom: 8px;
            
            .ant-checkbox-wrapper {
              display: flex;
              align-items: center;
            }
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
      
      // 附件上传区域样式
      .attachment-upload-area {
        margin-top: 15px;
        padding: 12px;
        background-color: #f9f9f9;
        border-radius: 4px;
        border-left: 2px solid #1890ff;
        
        p {
          margin-bottom: 10px;
          font-size: 14px;
          
          b {
            color: #1890ff;
          }
        }
        
        .ant-upload-list {
          margin-top: 8px;
        }
        
        .ant-upload-list-picture .ant-upload-list-item,
        .ant-upload-list-picture-card .ant-upload-list-item {
          padding: 6px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          margin-top: 8px;
        }
        
        .ant-btn {
          background-color: #fff;
          border: 1px solid #d9d9d9;
          
          &:hover {
            color: #1890ff;
            border-color: #1890ff;
          }
        }
      }
      
      // Radio和Checkbox样式优化，确保一级选项文字对齐
      .ant-radio-wrapper {
        display: flex;
        align-items: center;
        width: 100%;
        
        .ant-radio {
          margin-right: 8px;
          margin-top: 0;
        }
        
        span + span {
          padding-right: 8px;
        }
      }
      
      .ant-checkbox-wrapper {
        display: flex;
        align-items: center;
        width: 100%;
        
        .ant-checkbox {
          margin-right: 8px;
          margin-top: 0;
        }
        
        span + span {
          padding-right: 8px;
        }
      }
    }
  }
}

.mydescript {
  white-space: pre-wrap;
  word-break: break-all;
}

.submit-area {
  margin-top: 30px;
  text-align: center;
  padding-bottom: 20px;
  
  button {
    width: 180px;
    height: 40px;
    font-size: 16px;
  }
} 

// 调整Ant Design原生checkbox和radio样式
:global {
  .ant-checkbox-wrapper {
    display: flex !important;
    align-items: flex-start !important; // 确保靠左上对齐
  }

  .ant-checkbox {
    top: 0 !important;
  }

  // 确保原生checkbox对齐显示
  .ant-checkbox + span {
    padding-left: 8px;
    padding-right: 8px;
    line-height: 1.5;
  }
  
  // 调整Radio样式
  .ant-radio-wrapper {
    display: flex !important;
    align-items: flex-start !important; // 确保靠左上对齐
  }
  
  .ant-radio {
    top: 0 !important;
  }
  
  // 确保原生radio对齐显示
  .ant-radio + span {
    padding-left: 8px;
    padding-right: 8px;
    line-height: 1.5;
  }
} 

/* 二级选项样式 */
.second-level-options {
  margin-top: 8px;
  margin-left: 22px;
  padding: 8px;
  border-left: 2px solid #1890ff;
  background-color: #f5f5f5;
  width: calc(100% - 22px);
}

.second-level-checkbox-list {
  display: flex;
  flex-wrap: wrap;
}

.second-level-checkbox-item {
  margin-right: 16px;
  margin-bottom: 8px;
}

/* 附件上传区域样式 */
.attachment-upload-area {
  margin-top: 10px;
  width: 100%;
  border-top: 1px dashed #ccc;
  padding-top: 10px;
}

/* 选项列表样式 */
.option-list-item.list-item-flex {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  
  .ant-radio-wrapper,
  .ant-checkbox-wrapper {
    width: 100%;
    margin-left: 0;
    text-align: left;
  }
}

/* 预览窗口样式 */
.phone.question-phone {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.phone-head {
  background-color: #1890ff;
  color: white;
  padding: 10px;
  text-align: center;
  font-weight: bold;
}

.phone-body {
  padding: 16px;
}

.question-title {
  margin-bottom: 16px;
  font-weight: bold;
}

.question-phone-list {
  margin-bottom: 12px;
}

.phone-question-list {
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.phone-question-list .title {
  font-weight: bold;
  margin-bottom: 8px;
}

.questions-type {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.questions-required {
  font-size: 12px;
  color: #ff4d4f;
  margin-left: 8px;
}

.submit-area {
  margin-top: 20px;
  text-align: center;
} 
 