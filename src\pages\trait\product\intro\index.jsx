/* eslint-disable react/jsx-no-duplicate-props */
import React, { useEffect, useState } from 'react';
import { connect, history } from 'umi';
import { Button, message } from 'antd';
import queryString from 'query-string';
import TinymceEditor from '@/components/editor/TinymceEditor';

import * as CONSTANT from '@/config/constant/constant';
import * as Api from '../service';
import './style.less';

const Index = props => {
  const { id = '' } = queryString.parse(props.location.search);
  const [detail, setDetail] = useState({});

  const fetchDetail = async () => {
    const { data = {} } = await Api.fetchDetail({ id });
    if (data.code === 0) {
      setDetail(data.data || {});
    }
  };

  useEffect(() => {
    fetchDetail();
  }, []);

  const onFinish = async releaseFlag => {
    const { data = {} } = await Api.updateProduct({
      id,
      introduction: detail.introduction,
      releaseFlag: !detail.introduction ? 0 : releaseFlag,
    });
    if (data.code !== 0) {
      message.error(data.msg);
      return;
    }
    if (data.code === 0) {
      message.success(data.msg, 1.5, () => {
        history.goBack();
      });
    }
  };

  return detail.id ? (
    <div className="g-page p-product-intro">
      <div className="intro-container">
        <TinymceEditor
          id={`product-intro-editor-${id}`}
          content={detail.introduction || ' '}
          url={`${CONSTANT.DOMAIN}/api/article/upfile`}
          onChange={introduction => {
            setDetail({ ...detail, introduction });
          }}
        />
      </div>
      <div className="footer">
        <Button type="primary" onClick={() => onFinish(1)}>
          保存并发布
        </Button>
        <Button onClick={() => onFinish(0)}>保存至草稿箱</Button>
        <Button onClick={() => history.goBack()}>取消</Button>
      </div>
    </div>
  ) : null;
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Index);
