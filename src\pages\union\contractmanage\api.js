import { post, download } from '@/utils/request';

// 客户列表
export const getinstitutionbylist = (param = {}) => post('/api/institution/get-by-list', { data: param });

// 账号列表
export const getAllUser = (param = {}) => post('/api/userinfo/findAllUser', { data: param });

// 团队列表
export const getAllGroup = (param = {}) => post('/api/team/paging', { data: param });

// 产品列表
export const getproducts = (param = {}) => post('/api/product/get-products', { data: param });

// 合同列表
export const getcontractbypage = (param = {}) => post('/api/contract/get-by-page', { data: param });

// 导出所有合同
export const exportcontract = (param = {}) => download('/api/contract/export', { data: param });

// 导合同中的授权医院
export const exportGrant = (param = {}) => download('/api/contract/exportGrant', { data: param });

// 删除合同
export const deletecontract = (param = {}) => post('/api/contract/delete', { data: param });

// 新增合同
export const addcontract = (param = {}) => post('/api/contract/add', { data: param });

// 更新合同
export const updatecontract = (param = {}) => post('/api/contract/update', { data: param });

// 合同详情
export const getDetail = (param = {}) => post('/api/contract/get-by-id', { data: param });

// 检查合同的产品是否可以删除
export const checkdelete = (param = {}) => post('/api/contract/check-contract-product-delete', { data: param });

// 获取合作客户的所有有效合同
export const getcontractbyinstitution = (param = {}) => post('/api/contract/get-contract-by-institution', { data: param });
