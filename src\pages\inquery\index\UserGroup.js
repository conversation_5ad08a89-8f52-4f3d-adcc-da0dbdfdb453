import React, { Component, Fragment } from 'react';
import { withRouter } from 'dva/router';
import { connect } from 'dva';
import { Layout, Button, DatePicker, Dropdown, Menu, Radio, Modal, Tag, Tooltip, Tabs, Spin, Select, Divider, Input, Transfer, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

import moment from 'moment';
import Ellipsis from '@/components/Ellipsis';
import AllGroupUserAdd from './AllGroupUserAdd';
import * as Api from './api';

import styles from './userGroup.less';

const { Option } = Select;
const options = [];
@connect(({ user }) => ({ currentUser: user.currentUser }))
class Index extends Component {
  constructor(prop) {
    super(prop);
    this.state = {
      activeOperType: 0,
      busTypes: [],
      activeBusType: -1,
      list: [],
      activePChat: -1,
      childChat: [],
      activeCChat: -1,
      activeFeeChat: -1,
      activeFeeDateIdx: -1,
      feeChat: [],
      contextMenuStyle: {
        display: 'none',
      },
      contextItem: {},
      showFilterModal: false,
      tagListuser: [],
      tagListvisit: [],
      activeTaguser: [],
      activeTagvisit: [],
      registStartTime: '',
      registEndTime: '',
      userIpt: '',
      showAllGroupUserAdd: false,
      feeChatLoading: false,
      sortKey: 0,
      userListPageNum: 1,
      usersEndPageIndex: 0, // 用户列表结束页码
      remindFilterDate: moment().format('YYYY-MM-DD'),
      remindBelongType: 'myslfe',
      pidTimeStart: moment().add(-1, 'days'),
      pidTimeEnd: moment(),
      addConsultation: false,
      consulationArr: [
        { pid: '132', num: '20' },
        { pid: '132', num: '1265899' },
      ],
      patientPid: 10140540, // 关联病友id
      patientName: '',
      talkPatient: '0', // 是否邀请病友
      filter: '',
      allUser: [], // 所有用户
      rightKey: [], // 已选择医生
      orderNo: '', // 会诊单号
      plannedTime: '', // 计划会诊时间
      selectDoctorList: [], // 已选择医生
      mainDoctorId: '', // 主诊医生id
      mainDoctorName: '', // 主诊医生姓名
      unreadNum: 0,
      status: '2', // 会诊类型
    };

    const {
      location: { query = {} },
    } = this.props;
    this.query = query;
    this.clientHeight = document.documentElement.clientHeight;

    const now = moment().add(-4, 'day');
    this.netBusDates = Array.from('111111111111111111').map(() => {
      const tDate = now.add(1, 'day');
      return tDate.format('YYYY-MM-DD');
    });

    this.leftOperConfig = [
      {
        type: 'nofree',
        value: '1',
        title: '免费咨询',
      },
      // {
      //   type: 'net',
      //   value: '6',
      //   title: '网络门诊',
      // },
      // {
      //   type: 'report',
      //   value: '2',
      //   title: '报告处理',
      // },
      {
        type: 'alluser',
        value: '8',
        title: '全部病友',
        // auth: '/inquery/select/alluser',
      },
      {
        type: 'session',
        value: '7',
        title: '会话邀请',
      },
      // {
      //   type: 'end',
      //   value: '3',
      //   title: '已结束问诊',
      // },
      {
        type: 'move',
        value: '4',
        title: '已转移',
      },
      {
        type: 'remind',
        value: '5',
        title: '日志提醒',
      },
      {
        type: 'consultation',
        value: '10',
        title: '会诊',
      },
      // {
      //   type: 'service',
      //   value: '11',
      //   title: '客服',
      // },
      // {
      //   type: 'vip',
      //   value: '12',
      //   title: 'VIP咨询群聊',
      // },
      {
        type: '',
        value: '9',
        title: '全部未读消息',
      },
    ];
    this.vipTag = [
      { dictValue: '服务中', dictKey: '0' },
      { dictValue: '已结束', dictKey: '1' },
    ];

    this.feeChatFilterTag = [
      { dictValue: '接诊中', dictKey: '0' },
      { dictValue: '已结束', dictKey: '1' },
    ];

    this.consultationTag = [
      { dictValue: '进行中', dictKey: '2' },
      { dictValue: '已结束', dictKey: '3' },
      { dictValue: '已取消', dictKey: '6' },
    ];

    this.sortPool = [
      {
        key: 'sortByUserTime',
        name: '患者发送时间',
        showTimeKey: 'messageTime',
      },
      {
        key: 'sortByDefault',
        name: '未读消息排序',
        showTimeKey: 'messageTime',
      },
      {
        key: 'sortBySelfTime',
        name: '医生发送时间',
        showTimeKey: 'doctorMessageTime',
      },
    ];
  }

  componentDidMount() {
    if (this.query.type == 'consultation') {
      const unReadUserAllNum = this.unReadUserAllNum();
      this.leftOper({ key: 7, unReadUserAllNum });
    } else {
      this.queryChatList(1, this.query.pid);
    }
    this.getBusType();
    this.queryTagForUserList('user');
    this.queryTagForUserList('visit');

    const { setChatListCall } = this.props;
    if (typeof setChatListCall === 'function') {
      setChatListCall(this.setOutQueryChatListCall);
    }

    this.listenDocSwitch();
    this.loopMessage();
    for (let i = 0; i < 100000; i++) {
      const value = `${i.toString(36)}${i}`;
      options.push({
        value,
        disabled: i === 10,
      });
    }
    this.getAllUsrs();
    this.setToActiveUserEvent();
    this.getUnreadNum();
  }

  componentWillUnmount() {
    clearTimeout(this.chatListTimer);
    clearTimeout(this.feeChatTimer);
    clearTimeout(this.loopMessageTimer);
    clearTimeout(this.userListScrollTimer);

    this.unListenDocSwitch();
    this.removeToActiveUserEvent();
  }

  setToActiveUserEvent = () => {
    document.addEventListener('keydown', this.toActiveUserEvent, false);
  };

  removeToActiveUserEvent = () => {
    document.removeEventListener('keydown', this.toActiveUserEvent, false);
  };

  toActiveUserEvent = event => {
    if (event && event.ctrlKey && event.altKey) {
      const { activePChat } = this.state;
      this[`userNode${activePChat}`].scrollIntoView({ block: 'nearest', inline: 'start' });
    }
  };

  getAllUsrs = async () => {
    const { code, data = [] } = await Api.getAllUser();
    if (code == 0) {
      const allUser = [];
      data.forEach((item, key) => {
        (item.userList || []).map(user => {
          allUser.push({
            label: `${item.identityName}-${user.name}`,
            value: `${user.name}___${user.account}`,
          });
        });
      });
      // 对象数组去重
      const result = {};
      for (let i = 0; i < allUser.length; i++) {
        result[allUser[i].value] = allUser[i];
      }
      const finalArr = [];
      for (const item in result) {
        finalArr.push(result[item]);
      }
      this.setState({ allUser: finalArr });
    }
  };

  setOutQueryChatListCall = (param = {}) => {
    if (param.removeId) {
      const { list = [] } = this.state;
      list.some((o, key) => {
        if (o.id === param.removeId) {
          list.splice(key, 1);
          return true;
        }
        return false;
      });
      if (list.length) {
        this.userListNode.scrollTop = 0;
        this.cardClick(list[0], 0, true);
      }
      this.setState({ list });
    }
    this.queryChatList();
  };

  queryTagForUserList = async tagType => {
    const { code, data = {} } = await Api.queryTagList({ tagType, numPerPage: 100 });
    if (code == 0) {
      const { recordList = [] } = data;
      this.setState({ [`tagList${tagType}`]: recordList });
    }
  };

  getBusType = async () => {
    const { code, data = [] } = await Api.getBusType();
    if (code == 0) {
      data.unshift({ dictKey: '', dictValue: '全部' });
      this.setState({ busTypes: data, activeBusType: 0 });
    }
  };

  loopMessage = async () => {
    // this.getUnreadNum('0');//获取未读消息条数
    clearTimeout(this.loopMessageTimer);
    const { code, data } = await Api.loopMessage();
    if (code == 0) {
      const { activeOperType, list = [], activePChat } = this.state;
      // const filterObj = this.leftOperConfig[activeOperType];
      let filterObj = {};
      if (this.leftOperConfig.length === 1) {
        filterObj = this.leftOperConfig[0];
      } else {
        filterObj = this.leftOperConfig[activeOperType];
      }
      if (filterObj.value === '6') {
        this.setFeeData(data);
      } else if (list.length && activePChat !== -1) {
        this.setNormalData(data, '', 'loopmessage');
      }
    }
    this.loopMessageTimer = setTimeout(() => {
      this.loopMessage();
    }, 4000);
  };

  proxyData = (oldData, newData) => {
    oldData.forEach(o => {
      o.unreadNum = 0;
      o.dealFlg = 0;
    });
    const hasArr = [];
    const newDataArr = [];
    newData.forEach((item, k) => {
      let isInOldList = false;
      for (let i = 0; i < oldData.length; i++) {
        const o = oldData[i];
        if (o.id === item.id) {
          o.unreadNum = item.unreadNum;
          o.dealFlg = item.dealFlg;
          o.doctorTime = moment(item.doctorTime || '2010-01-01').valueOf();
          o.patTime = moment(item.patTime || '2010-01-01').valueOf();
          o.messageTime = item.messageTime;
          hasArr.push(o.id);
          isInOldList = true;
        }
      }
      if (!isInOldList && (item.name || item.patName)) {
        // if (oldData.length && !item.name) { // 存在不存在于用户列表的新消息, 重新加载用户列表
        //   this.queryChatList();
        // }
        item.doctorTime = moment(item.doctorTime || '2010-01-01').valueOf();
        item.patTime = moment(item.patTime || '2010-01-01').valueOf();
        newDataArr.push(item);
      }
    });
    oldData.push(...newDataArr);
    return oldData;
  };

  sortByUserTime = users => {
    users.sort((a, b) => {
      return b.patTime - a.patTime;
    });
  };

  sortBySelfTime = users => {
    users.sort((a, b) => {
      return b.doctorTime - a.doctorTime;
    });
  };

  sortByDefault = users => {
    this.sortByUserTime(users);
    const tmpUnReadNum = [];
    for (let i = users.length - 1; i >= 0; i--) {
      if (users[i].unreadNum > 0) {
        const tmp = users[i];
        users.splice(i, 1);
        tmpUnReadNum.push(tmp);
      }
    }
    users.unshift(...tmpUnReadNum.reverse());
  };

  getRelations = async (item, key) => {
    const { code, data = [] } = await Api.queryRelationPatients({
      pid: item.pid,
      userId: item.userId,
    });
    if (code == 0) {
      this.setRelationData(data, key);
    }
  };

  getNextPageUsers = () => {
    const { userListPageNum } = this.state;
    this.setState({ userListPageNum: userListPageNum + 1 }, this.queryChatList);
  };

  queryChatList = async (key, pid) => {
    if (this.chatListTimer !== undefined) {
      clearTimeout(this.chatListTimer);
    }
    const {
      location: { pathname = '' },
    } = this.props;
    if (pathname !== '/inquery/select') {
      return false;
    }
    const { activeOperType } = this.state;
    // const filterObj = this.leftOperConfig[activeOperType];
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    if (filterObj.value === '6') {
      this.getFeeChatList(key);
    } else {
      this.getNormalChatList(key, pid);
    }
    this.chatListTimer = setTimeout(() => {
      this.queryChatList(key);
    }, 120000);
  };

  componentWillReceiveProps(nextProps) {
    const { activeBusType } = this.state;
    const {
      currentUser: { allianceName },
    } = nextProps;
    if (activeBusType == -1 && allianceName && allianceName.indexOf('家辉医遗传') == -1) {
      this.tagChange('2', '2');
    }
  }

  getNormalChatList = async (key, queryPid) => {
    clearTimeout(this.loopMessageTimer);
    const {
      registStartTime = '',
      registEndTime = '',
      userIpt = '',
      activeOperType,
      busTypes = [],
      activeBusType = '',
      activeTaguser = [],
      activeTagvisit = [],
      tagListvisit = [],
      tagListuser = [],
      userListPageNum,
      remindFilterDate,
      remindBelongType,
      pidTimeStart,
      pidTimeEnd,
    } = this.state;

    const tagListvisitIds = activeTagvisit.map(item => {
      return tagListvisit[item].id;
    });
    const tagListuserIds = activeTaguser.map(item => {
      return tagListuser[item].id;
    });
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    // const filterObj = this.leftOperConfig[activeOperType];
    const bysType = (busTypes[activeBusType] || {}).dictKey;
    // const bysType = activeBusType;
    const reqData = {
      pageNum: userListPageNum,
      numPerPage: 100,
      registStartTime,
      registEndTime,
      pid: userIpt,
      chatFilterType: filterObj.value,
      periodId: bysType,
      visitTagIdStr: tagListvisitIds.join('|'),
      userTagIdStr: tagListuserIds.join('|'),
      remindFilterDate,
      remindBelongType,
    };
    if (filterObj.value === '6') {
      reqData.isEndFeeType = bysType;
    }
    if (filterObj.value === '8') {
      reqData.pidTimeStart = pidTimeStart ? pidTimeStart.format('YYYY-MM-DD') : '';
      reqData.pidTimeEnd = pidTimeEnd ? pidTimeEnd.format('YYYY-MM-DD') : '';
    }
    if (filterObj.value === '10' || this.query.type === 'consultation') {
      if (this.query.type === 'consultation') {
        this.query = '';
      }
      if (activeBusType == -1) {
        //   this.tagChange('', '2');
        reqData.consultationStatus = '2';
      } else {
        reqData.consultationStatus = activeBusType;
      }
    }
    if (filterObj.value === '12') {
      if (activeBusType == '0' || activeBusType == '') {
        reqData.vipChatStatus = '1';
      } else {
        reqData.vipChatStatus = '0';
        reqData.periodId = '';
      }
    }
    const { code, data: listData = {} } = await Api.queryChatList(reqData);
    this.loopMessage(); // 查询未读
    const { userIpt: nowIpt } = this.state;
    if (userIpt !== nowIpt) {
      // 可能存在用户输入较快时候后端请求返回顺序不一致问题
      return false;
    }
    if (code == 0) {
      const { recordList: data = [], endPageIndex = 0 } = listData;
      this.setState({ usersEndPageIndex: endPageIndex });
      this.setNormalData(data, queryPid);
    }
  };

  setNormalData = (data, queryPid, dataFrom) => {
    const { list = [], activePChat = 0, sortKey, activeOperType } = this.state;
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    // const filterObj = this.leftOperConfig[activeOperType];
    const nList = this.proxyData(list, data, activePChat);
    this[this.sortPool[sortKey].key](nList);
    let newActivePChat = nList.findIndex(item => {
      return item.active && activePChat !== -1;
    });
    if (!(newActivePChat >= 0)) {
      newActivePChat = -1;
    }
    this.setState({ list: nList, activePChat: newActivePChat });
    // () => {
    //   if (newActivePChat !== -1 && !this.userListScrollIng) {
    //     this[`userNode${newActivePChat}`].scrollIntoView({ block: 'nearest', inline: 'start' });
    //   }
    // }

    if (nList.length > 0) {
      let queryPidIndex = 0;
      const { type = '', chatGroupId = '' } = this.query;
      nList.forEach((item, key) => {
        if (type == 'consultation') {
          if (chatGroupId && chatGroupId == item.id) {
            // 处理从会诊管理过来的聊天
            queryPidIndex = key;
          }
        } else if (queryPid && queryPid == item.pid) {
          // 处理从患者管理过来的聊天
          queryPidIndex = key;
        }
        if (document.hidden && item.unreadNum > 0) {
          this.setDocTitle();
        }
      });
      if (newActivePChat === -1) {
        this.cardClick(nList[queryPidIndex], queryPidIndex, true);
      } else if (dataFrom === 'loopmessage') {
        this.setRelationData(data, newActivePChat);
      } else {
        this.getRelations(nList[newActivePChat], newActivePChat);
      }
    } else {
      const { onChat } = this.props;
      onChat({ chatFilterType: filterObj.value });
    }
  };

  setRelationData = (data, key) => {
    const { activePChat, childChat = [] } = this.state;
    if (activePChat !== key) {
      return false;
    }
    const nList = this.proxyData(childChat, data);
    nList.forEach(obj => {
      if (document.hidden && obj.unreadNum > 0) {
        this.setDocTitle();
      }
    });
    this.setState({ childChat: nList });
  };

  getFeeChatList = (window.feeChatDateOrderList = async key => {
    const { activeFeeDateIdx, activeBusType } = this.state;
    const registerDate = this.netBusDates[activeFeeDateIdx];
    const isEndFeeType = (this.feeChatFilterTag[activeBusType] || {}).dictKey;
    this.setState({ feeChat: [] });
    const { code, data = [] } = await Api.queryFeeChatListForDoc({ registerDate, isEndFeeType });
    if (code == 0) {
      this.setFeeData(data, key || activeFeeDateIdx);
    }
  });

  setFeeData = (data, key) => {
    const { feeChat = [], activeFeeDateIdx: activeFeeDateIdxNow, activeFeeChat, sortKey } = this.state;
    if (key !== undefined && activeFeeDateIdxNow !== key) {
      return false;
    }
    const nList = this.proxyData(feeChat, data);

    this[this.sortPool[sortKey].key](nList);
    nList.forEach(obj => {
      if (document.hidden && obj.unreadNum > 0) {
        this.setDocTitle();
      }
    });
    this.setState({ feeChat: nList, feeChatLoading: false });
    if (activeFeeChat === -1) {
      this.feeChatClick(nList[0], 0);
    }
  };

  unReadUserAllNum = () => {
    const { activeOperType, list = [], feeChat = [] } = this.state;
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    let countList = [];
    if (filterObj.value === '6') {
      countList = feeChat;
    } else {
      countList = list;
    }
    return countList.filter(item => item.unreadNum > 0).length;
  };

  feeChatDateClick = idx => {
    const { activeFeeDateIdx } = this.state;
    if (activeFeeDateIdx !== idx) {
      this.setState({ activeFeeDateIdx: idx, activeFeeChat: -1, feeChat: [], feeChatLoading: true }, () => this.queryChatList(idx));
    } else {
      this.setState({ activeFeeDateIdx: -1, activeFeeChat: -1 });
    }
  };

  feeChatClick = async (item, key) => {
    const { activeFeeChat = -1, activeOperType, feeChat } = this.state;
    if (activeFeeChat === key) {
      return false;
    }
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    // const filterObj = this.leftOperConfig[activeOperType];
    const { onChat } = this.props;
    onChat({ ...item, chatFilterType: filterObj.value });
    if (item && item.id) {
      clearTimeout(this.loopMessageTimer); // 为了防止阅读消息过快，后台设置未读缓慢，所以暂停轮训
      item.unreadNum = 0;
      await Api.readMessage({ groupId: item.id });
      this.loopMessage();
    }
    this.setState({ activeFeeChat: key, feeChat });
  };

  cardClick = async (item, key, isUnSetRead) => {
    const { list = [], activePChat, activeOperType } = this.state;
    list.forEach((i, k) => {
      i.active = false;
      if (key === k) {
        i.active = true;
      }
    });
    const { onChat } = this.props;
    // const filterObj = this.leftOperConfig[activeOperType];
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    onChat({ ...item, chatFilterType: filterObj.value });
    if (key !== activePChat) {
      this.getRelations(item, key);
      this.setState({ childChat: [] });
    }
    if (item && !isUnSetRead) {
      clearTimeout(this.loopMessageTimer); // 为了防止阅读消息过快，后台设置未读缓慢，所以暂停轮训
      item.unreadNum = 0;
      await Api.readMessage({ groupId: item.id });
      this.loopMessage();
    }
    this.setState({ activePChat: key, activeCChat: -1, list });
  };

  childChatClick = async (item, key) => {
    const { activeCChat = -1, activeOperType } = this.state;
    if (activeCChat === key) {
      return false;
    }
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    // const filterObj = this.leftOperConfig[activeOperType];
    const { onChat } = this.props;
    onChat({ ...item, chatFilterType: filterObj.value });
    this.setState({ activeCChat: key });
    if (item) {
      clearTimeout(this.loopMessageTimer); // 为了防止阅读消息过快，后台设置未读缓慢，所以暂停轮训
      item.unreadNum = 0;
      await Api.readMessage({ groupId: item.id });
      this.loopMessage();
    }
  };

  contextMenu = item => {
    window.event.returnValue = false;
    window.event.cancelBubble = true;
    this.setState({
      contextMenuStyle: {
        left: window.event.clientX + 10,
        top: window.event.clientY,
        display: 'block',
      },
      contextItem: item,
    });
    return false;
  };

  hideContextMenu = () => {
    this.setState({
      contextMenuStyle: {
        display: 'none',
      },
      contextItem: {},
    });
  };

  contextMenuBody = () => {
    const { contextMenuStyle = {}, contextItem = {} } = this.state;
    return (
      <div className={styles.contextMenu} style={contextMenuStyle}>
        {contextItem.id ? (
          <Button type="primary" size="small" onClick={this.setUnReadMessage}>
            标为未读
          </Button>
        ) : (
          <Button type="primary" size="small" onClick={this.startChat}>
            发起聊天
          </Button>
        )}
      </div>
    );
  };

  startChat = async () => {
    this.hideContextMenu();
    const { contextItem = {} } = this.state;
    const { code, data } = await Api.creatChat({
      chatType: 1,
      userId: contextItem.userId,
      pid: contextItem.pid,
      name: contextItem.name,
    });
    if (code == 0) {
      const { onChat } = this.props;
      onChat({ ...contextItem, ...data });
    }
  };

  getMyRecordList = async () => {
    const param = {
      pageNum: 1,
      numPerPage: 10,
    };
    const data = await Api.getAllRecordList(param);
  };

  setUnReadMessage = async () => {
    this.hideContextMenu();
    const { contextItem = {}, list = [], feeChat = [], activeOperType } = this.state;
    contextItem.unreadNum = 1;
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    // const filterObj = this.leftOperConfig[activeOperType];
    if (filterObj.value === '6') {
      this.setState({ feeChat });
    } else {
      this.setState({ list });
    }
    clearTimeout(this.loopMessageTimer);
    await Api.unReadMessage({ groupId: contextItem.id });
    this.loopMessage();
  };

  leftOper = (param = {}) => {
    if (this.chatListTimer !== undefined) {
      clearTimeout(this.chatListTimer);
    }
    if (this.feeChatTimer !== undefined) {
      clearTimeout(this.feeChatTimer);
    }
    const { key, unReadUserAllNum } = param;
    const { activeOperType } = this.state;
    if (activeOperType === key) {
      this.nextUnReadUser(unReadUserAllNum);
      return false;
    }
    const { onChat } = this.props;
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[key];
      console.log('filterObj', filterObj);
    }
    // const filterObj = this.leftOperConfig[key];
    onChat({ chatFilterType: filterObj.value });
    if (filterObj.value === '6') {
      this.setState(
        {
          activeOperType: key,
          activeFeeDateIdx: -1,
          activeFeeChat: -1,
          activePChat: -1,
          activeCChat: -1,
          activeBusType: 0,
          feeChat: [],
        },
        () => this.feeChatDateClick(0),
      );
    } else if (this.query.type == 'consultation') {
      const { status = '2' } = this.query;
      this.setState(
        {
          status,
          activeOperType: key,
          activeFeeDateIdx: -1,
          activeFeeChat: -1,
          activePChat: -1,
          activeCChat: -1,
          activeBusType: status,
          list: [],
        },
        () => {
          this.queryChatList(7, this.query.pid);
        },
      );
    } else {
      this.setState(
        {
          activeOperType: key,
          activeFeeDateIdx: -1,
          activeFeeChat: -1,
          activePChat: -1,
          activeCChat: -1,
          activeBusType: filterObj.value === '10' ? '2' : 0,
          list: [],
        },
        this.queryChatList,
      );
    }
    this.setState({ usersEndPageIndex: 0, userListPageNum: 1 });
    // if (filterObj.value) {
    this.getUnreadNum(filterObj.value);
    // }
  };

  handleTagClick = (tagIndex, type) => {
    const activeTag = this.state[`activeTag${type}`];
    activeTag.length = 0;
    activeTag.push(tagIndex);
    this.setState({ [`activeTag${type}`]: activeTag });
  };

  onDateChange = (val = []) => {
    if (val && val.length) {
      this.setState({
        registStartTime: val[0].format('YYYY-MM-DD'),
        registEndTime: val[1].format('YYYY-MM-DD'),
      });
    } else {
      this.setState({ registStartTime: '', registEndTime: '' });
    }
  };

  consulationTime = plannedTime => {
    this.setState({ plannedTime });
  };

  changeUserIpt = e => {
    if (e.keyCode === 13) {
      this.setState({ userIpt: e.currentTarget.value, usersEndPageIndex: 0, userListPageNum: 1, list: [] }, this.queryChatList);
    }
  };

  resetDocTitle = () => {
    const isHidden = document.hidden;
    if (!isHidden) {
      if (this.setTitleTimer !== undefined) {
        clearTimeout(this.setTitleTimer);
        this.setTitleTimer = undefined;
      }
      window.document.title = '在线问诊';
    }
  };

  listenDocSwitch = () => {
    window.document.addEventListener('visibilitychange', this.resetDocTitle);
  };

  unListenDocSwitch = () => {
    window.document.removeEventListener('visibilitychange', this.resetDocTitle);
  };

  setDocTitle = () => {
    clearTimeout(this.setTitleTimer);
    this.setTitleTimer = setTimeout(() => {
      if (document.hidden) {
        if (window.document.title.indexOf('消息') > -1) {
          window.document.title = '【　　　　　　】';
        } else {
          window.document.title = '【您有未读消息】';
        }
        this.setDocTitle();
      } else {
        this.resetDocTitle();
      }
    }, 500);
  };

  switchAllGroupUserAdd = () => {
    const { showAllGroupUserAdd } = this.state;
    this.setState({ showAllGroupUserAdd: !showAllGroupUserAdd });
  };

  createConsultation = () => {
    this.setState({ addConsultation: true });
  };

  setUserListNode = node => {
    if (node) {
      // clearTimeout(this.userListScrollTimer);
      this.userListNode = node;
      // this.userListNode.onscroll = () => {
      //   this.userListScrollTimer = setTimeout(() => {
      //     this.userListScrollIng = false;
      //   }, 15000);
      //   this.userListScrollIng = true;
      // }
    }
  };

  renderNormalChatList = val => {
    const { list = [], childChat = [], activePChat, activeCChat, sortKey } = this.state;
    const { authResource = {} } = this.props;
    const { btns = {} } = authResource;

    return (
      <div className={styles.userListScroll} style={{ marginBottom: val == '10' ? '50px' : 0 }}>
        <div className={styles.userList} ref={ref => (this.userListNode = ref)}>
          {/* {val == '10' && <div className={styles.createConsultation} onClick={this.createConsultation}>创建会诊</div>} */}
          {list.length > 0 ? (
            list.map((item, key) => {
              return (
                <div className={styles.userGroup} key={item.id} ref={ref => (this[`userNode${key}`] = ref)}>
                  <div
                    className={`${styles.userInfo} ${activePChat === key ? styles.active : ''} ${activePChat === key && activeCChat < 0 ? styles.chatActive : ''}`}
                    onClick={() => this.cardClick(item, key)}
                    onContextMenu={() => this.contextMenu(item)}
                  >
                    <div className={styles.userBody}>
                      <div className={styles.userName}>
                        <Tooltip title={item.name}>
                          <Ellipsis lines={1}>{item.name}</Ellipsis>
                        </Tooltip>
                      </div>
                    </div>
                    <div className={styles.userFoot}>
                      <div id={item.unreadNum > 99 ? '99+' : item.unreadNum} className={`${styles.msgIcon} ${item.unreadNum > 0 ? styles.hasNewMsg : ''}`} />
                      {item.dealFlg != 0 ? <div className={`${styles.reportIcon} ${item.dealFlg == 1 ? styles.hasNewReport : ''}`} /> : null}
                      <div className={styles.date}>{item[this.sortPool[sortKey].showTimeKey]}</div>
                    </div>
                  </div>
                  {activePChat === key && childChat.length ? (
                    <div className={styles.fmList}>
                      {childChat.map((p, k) => {
                        return (
                          <div
                            className={`${styles.userDetail} ${p.id ? '' : styles.disable} ${activeCChat === k ? `${styles.active} ${styles.chatActive}` : ''}`}
                            key={`${p.id}${k}`}
                            onClick={() => {
                              this.childChatClick(p, k);
                            }}
                            onContextMenu={() => this.contextMenu(p)}
                          >
                            <div className={`${styles.dBody} ${styles[`sex${p.patSex === '男' ? 'M' : 'F'}`]}`}>
                              <Ellipsis lines={1}>
                                {p.familyMemberName}
                                {p.name ? `：${p.name}` : null}
                              </Ellipsis>
                            </div>
                            <div id={p.unreadNum > 99 ? '99+' : p.unreadNum} className={`${styles.dFoot} ${p.unreadNum > 0 ? styles.newMsg : ''}`}>
                              {p[this.sortPool[sortKey].showTimeKey]}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : null}
                </div>
              );
            })
          ) : val == '10' ? (
            <div style={{ textAlign: 'center', paddingTop: '50%' }}>暂无会诊</div>
          ) : null}
        </div>
      </div>
    );
  };

  renderNetChatList = () => {
    const { feeChat = [], activeFeeChat, activeFeeDateIdx, feeChatLoading } = this.state;

    return (
      <div className={styles.userListScroll}>
        <div className={styles.userList}>
          {this.netBusDates.map((item, key) => {
            return (
              <Fragment key={item}>
                <div className={`${styles.userListDate} ${activeFeeDateIdx === key ? styles.open : ''}`} onClick={() => this.feeChatDateClick(key)}>
                  {item}
                </div>
                {activeFeeDateIdx === key ? (
                  <div className={`${styles.netList} ${styles.fmList}`}>
                    {feeChat.length ? (
                      feeChat.map((p, k) => {
                        return (
                          <div
                            className={`${styles.userDetail} ${p.id ? '' : styles.disable} ${activeFeeChat === k ? `${styles.active} ${styles.chatActive}` : ''}`}
                            key={`${p.id}${k}`}
                            onClick={() => {
                              this.feeChatClick(p, k);
                            }}
                            onContextMenu={() => this.contextMenu(p)}
                          >
                            <div className={`${styles.dBody} ${styles.feechatHead}`}>
                              <Ellipsis lines={1}>
                                {p.pid}:{p.patName}
                              </Ellipsis>
                            </div>
                            <div id={p.unreadNum > 99 ? '99+' : p.unreadNum} className={`${styles.dFoot} ${p.unreadNum > 0 ? styles.newMsg : ''}`}>
                              {p.messageTime}
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className={styles.noChatList}>{feeChatLoading ? <Spin size="small" /> : '暂无数据'}</div>
                    )}
                  </div>
                ) : null}
              </Fragment>
            );
          })}
        </div>
      </div>
    );
  };

  renderChatList = () => {
    const { activeOperType } = this.state;
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    // const filterObj = this.leftOperConfig[activeOperType];
    if (filterObj.value === '6') {
      return this.renderNetChatList();
    }
    return this.renderNormalChatList(filterObj.value);
  };

  // 状态切换按钮
  tagChange = (key, status = '') => {
    this.setState({ status, activeBusType: key, usersEndPageIndex: 0, userListPageNum: 1, list: [] }, this.queryChatList);
  };

  getHeaderTag = () => {
    const { activeOperType, busTypes = [], activeBusType, status } = this.state;
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    // const filterObj = this.leftOperConfig[activeOperType];
    let renderData = busTypes;
    if (filterObj.value === '6') {
      renderData = this.feeChatFilterTag;
    }
    if (filterObj.value === '12') {
      renderData = this.vipTag;
    }
    if (filterObj.value === '10') {
      renderData = this.consultationTag;
      return (
        <Tabs onChange={this.tagChange} tabBarGutter={0} activeKey={String(status) || activeBusType}>
          {renderData.map(item => {
            return <Tabs.TabPane tab={item.dictValue} key={item.dictKey} />;
          })}
        </Tabs>
      );
    }
    return (
      <Tabs onChange={this.tagChange} tabBarGutter={0}>
        {renderData.map(item => {
          return <Tabs.TabPane tab={item.dictValue} key={item.dictKey} />;
        })}
      </Tabs>
    );
  };

  changeSort = idx => {
    const { list = [], feeChat = [], activeOperType } = this.state;
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    // const filterObj = this.leftOperConfig[activeOperType];
    if (filterObj.value === '6') {
      this[this.sortPool[idx].key](feeChat);
      this.setState({ feeChat });
    } else {
      this[this.sortPool[idx].key](list);
      this.setState({ list });
    }
    this.setState({ sortKey: idx });
  };

  nextUnReadUser = (unreadNum = 0) => {
    if (unreadNum <= 0) {
      return false;
    }
    const { list = [], activePChat } = this.state;
    list.some((o, k) => {
      if (k > activePChat && o.unreadNum > 0) {
        this.cardClick(o, k, true);
        if (this[`userNode${k}`]) {
          this[`userNode${k}`].scrollIntoView();
        }
        return true;
      }
    });
  };

  filterByPidTime = (val = []) => {
    const [pidTimeStart, pidTimeEnd] = val;
    this.setState({ pidTimeStart, pidTimeEnd, usersEndPageIndex: 0, userListPageNum: 1, list: [] }, this.queryChatList);
  };

  handleChange = e => {
    this.setState({ orderNo: e.target.value });
  };

  selectAttendingDoctor = (value, option, judge) => {
    const param = {};
    const {
      props: { id = '' },
    } = option;
    if (judge == 'hospital') {
      param.mainDoctorId = id;
    } else {
      param.mainDoctorName = value;
    }
    this.setState(param);
  };

  addPatient = e => {
    this.setState({ talkPatient: e.target.value });
  };

  setTransfer = rightKey => {
    const selectDoctorList = rightKey.map(item => {
      let arr = [];
      arr = item.split('___');
      return {
        account: arr[1],
        name: arr[0],
      };
    });
    this.setState({ rightKey, selectDoctorList });
  };

  getTransferItem = item => {
    const { filter = '' } = this.state;
    let filterStr;
    if (!filter && !filter.length) {
      filterStr = item.label;
    } else {
      filterStr = `${item.label}`.replace(new RegExp(filter, 'g'), `<span style="color: ${PRIMARY_COLOR}">${filter}</span>`);
    }
    const customLabel = (
      <span
        dangerouslySetInnerHTML={{
          __html: filterStr,
        }}
      />
    );
    return {
      label: customLabel, // for displayed item
      value: item.key, // for title and filter matching
    };
  };

  searchContent = val => {
    const { filter = '' } = this.state;
    if (filter !== val) {
      this.setState({ filter: val });
    }
  };

  consultationSubmit = async () => {
    const { orderNo, patientPid, patientName, plannedTime, mainDoctorId, mainDoctorName, talkPatient, selectDoctorList } = this.state;
    const param = {
      orderNo,
      patientPid,
      patientName,
      plannedTime,
      mainDoctorId,
      mainDoctorName,
      isInvitePat: talkPatient,
      list: selectDoctorList,
    };
    const { code, msg = '' } = await Api.creatConsultationChat(param);
    if (code == 0) {
      message.success({ content: '创建会诊间成功！', duration: 2 });
      this.setState({ addConsultation: false });
    } else {
      message.error({ content: '创建会诊间失败！', duration: 2 });
    }
  };

  getUnreadNum = async val => {
    const {
      registStartTime = '',
      registEndTime = '',
      userIpt = '',
      activeOperType,
      busTypes = [],
      activeBusType = '',
      activeTaguser = [],
      activeTagvisit = [],
      tagListvisit = [],
      tagListuser = [],
      userListPageNum,
      remindFilterDate,
      remindBelongType,
      pidTimeStart,
      pidTimeEnd,
      unreadNum,
    } = this.state;

    const tagListvisitIds = activeTagvisit.map(item => {
      return tagListvisit[item].id;
    });
    const tagListuserIds = activeTaguser.map(item => {
      return tagListuser[item].id;
    });
    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }
    // const filterObj = this.leftOperConfig[activeOperType];
    const bysType = (busTypes[activeBusType] || {}).dictKey;
    // const bysType = activeBusType;
    const reqData = {
      pageNum: userListPageNum,
      numPerPage: 100,
      registStartTime,
      registEndTime,
      pid: userIpt,
      chatFilterType: '9',
      periodId: bysType,
      visitTagIdStr: tagListvisitIds.join('|'),
      userTagIdStr: tagListuserIds.join('|'),
      remindFilterDate,
      remindBelongType,
    };
    const { code, data: listData = {} } = await Api.queryChatList(reqData);
    if (code == 0) {
      const { recordList: data = [] } = listData;
      if (val === '9') {
        this.setNormalData(data);
      }
      this.setState({
        unreadNum: listData.totalCount,
      });
    }
  };

  refresh = () => {
    this.getUnreadNum('9');
  };

  render() {
    const {
      filter,
      activeTaguser = [],
      activeTagvisit = [],
      userListPageNum,
      usersEndPageIndex,
      activeOperType,
      showFilterModal,
      sortKey,
      orderNo,
      patientName,
      patientPid,
      tagListuser = [],
      tagListvisit = [],
      showAllGroupUserAdd,
      remindBelongType,
      remindFilterDate,
      unreadNum,
      pidTimeStart,
      pidTimeEnd,
      addConsultation,
      patient,
      consulationArr,
      talkPatient,
      allUser,
      rightKey,
    } = this.state;
    const {
      authResource = {},
      currentUser: { allianceName },
    } = this.props;
    const { btns = {} } = authResource;
    const rightShow = rightKey.length ? rightKey : [];

    let filterObj = {};
    if (this.leftOperConfig.length === 1) {
      filterObj = this.leftOperConfig[0];
    } else {
      filterObj = this.leftOperConfig[activeOperType];
    }

    const unReadUserAllNum = this.unReadUserAllNum();

    const consulationOptions = consulationArr.map(d => <Option key={d.num}>{d.num}</Option>);

    const showData = allUser.filter(item => {
      // if (item.value) {
      return (!filter && !filter.length) || (item.label || '').indexOf(filter) > -1;
      // }
    });

    if (allianceName && allianceName.indexOf('家辉医遗传') == -1) {
      this.leftOperConfig = [
        {
          type: 'consultation',
          value: '10',
          title: '会诊',
        },
      ];
    }

    return (
      <Layout className={styles.leftContent}>
        <Layout.Sider width={80} className={styles.leftOper}>
          <div style={{ height: this.clientHeight - 64 - 32 }}>
            {this.leftOperConfig.map((item, key) => {
              if (item.auth && !btns[item.auth]) {
                return null;
              }
              return (
                <Tooltip key={item.type} title={item.title} placement="right">
                  <div className={`${styles.oper} ${styles[item.type]} ${activeOperType === key ? styles.on : ''}`} onClick={() => this.leftOper({ key, unReadUserAllNum })}>
                    {item.value === '9' && unreadNum > 0 ? <div className={styles.unReadUsers}>{unreadNum > 99 ? '99+' : unreadNum}</div> : null}
                    {item.value === '9' ? (
                      <div className={`${styles.unread} ${activeOperType === key ? styles.on : ''}`}>
                        全部
                        <br />
                        未读
                      </div>
                    ) : null}
                    {activeOperType === key && unReadUserAllNum > 0 ? <div className={styles.unReadUsers}>{unReadUserAllNum}</div> : null}
                  </div>
                </Tooltip>
              );
            })}
          </div>
        </Layout.Sider>
        <Layout.Content onClick={this.hideContextMenu} className={styles.userContent}>
          <div className={styles.search} style={{ marginRight: filterObj.value === '6' || filterObj.value === '10' ? 8 : 0 }}>
            <input className={styles.ipt} placeholder="输入病友姓名或PID搜索" onKeyDown={this.changeUserIpt} />
            {filterObj.value !== '6' && filterObj.value !== '10' ? <div className={styles.filter} onClick={() => this.setState({ showFilterModal: true })} /> : null}
          </div>
          {this.getHeaderTag()}
          {filterObj.value === '8' ? (
            <div className={styles.allUserAddBtn} onClick={this.switchAllGroupUserAdd}>
              +搜索添加
            </div>
          ) : null}
          <div style={{ display: 'flex' }}>
            {filterObj.value != '10' && (
              <Dropdown
                trigger={['click']}
                overlay={
                  <Menu>
                    {this.sortPool.map((item, idx) => {
                      return (
                        <Menu.Item key={item.key} onClick={() => this.changeSort(idx)}>
                          {item.name}
                        </Menu.Item>
                      );
                    })}
                  </Menu>
                }
              >
                <div className={styles.allUserAddBtn} style={{ width: filterObj.value === '9' ? '70%' : '100%' }}>
                  {this.sortPool[sortKey].name}
                </div>
              </Dropdown>
            )}
            {filterObj.value === '9' && (
              <div onClick={this.refresh} style={{ width: '20%' }} className={styles.allUserAddBtn}>
                刷新
              </div>
            )}
          </div>
          {filterObj.value === '5' ? (
            <Fragment>
              <div className={styles.filterDateBox}>
                <div className={styles.filterLabel}>筛选日期</div>
                <div className={styles.filterDate}>
                  <DatePicker
                    defaultValue={moment(remindFilterDate)}
                    onChange={date => {
                      this.setState(
                        {
                          remindFilterDate: date.format('YYYY-MM-DD'),
                          usersEndPageIndex: 0,
                          userListPageNum: 1,
                          list: [],
                        },
                        this.queryChatList,
                      );
                    }}
                    format="YYYY-MM-DD"
                  />
                </div>
              </div>
              <div className={styles.fLabel}>
                <Button
                  ghost={remindBelongType !== 'myslfe'}
                  shape="round"
                  size="small"
                  className={styles.labelItem}
                  onClick={() =>
                    this.setState(
                      {
                        remindBelongType: 'myslfe',
                        usersEndPageIndex: 0,
                        userListPageNum: 1,
                        list: [],
                      },
                      this.queryChatList,
                    )
                  }
                >
                  当前负责
                </Button>
                <Button
                  ghost={remindBelongType !== 'out'}
                  shape="round"
                  size="small"
                  className={styles.labelItem}
                  onClick={() =>
                    this.setState(
                      {
                        remindBelongType: 'out',
                        usersEndPageIndex: 0,
                        userListPageNum: 1,
                        list: [],
                      },
                      this.queryChatList,
                    )
                  }
                >
                  已转移
                </Button>
                <Button
                  ghost={remindBelongType !== ''}
                  shape="round"
                  size="small"
                  className={styles.labelItem}
                  onClick={() =>
                    this.setState(
                      {
                        remindBelongType: '',
                        usersEndPageIndex: 0,
                        userListPageNum: 1,
                        list: [],
                      },
                      this.queryChatList,
                    )
                  }
                >
                  全部病友
                </Button>
              </div>
            </Fragment>
          ) : null}
          {filterObj.value === '8' ? (
            <Fragment>
              <div className={styles.filterDateBox} style={{ margin: '0 15px' }}>
                <div className={styles.filterDate}>
                  <DatePicker.RangePicker
                    allowClear
                    value={[pidTimeStart, pidTimeEnd]}
                    onChange={this.filterByPidTime}
                    format="YYYY-MM-DD"
                    disabledDate={current => current && current > moment().endOf('day')}
                  />
                </div>
              </div>
            </Fragment>
          ) : null}
          {this.renderChatList()}
          {usersEndPageIndex >= userListPageNum ? (
            <div className={styles.allUserAddBtn} style={{ marginTop: 10 }} onClick={this.getNextPageUsers}>
              下一页
            </div>
          ) : null}
          {this.contextMenuBody()}
        </Layout.Content>
        <Modal
          title="筛选"
          visible={showFilterModal}
          onCancel={() => this.setState({ showFilterModal: false })}
          footer={
            <Fragment>
              <Button
                type="primary"
                onClick={() => {
                  this.setState({
                    showFilterModal: false,
                    usersEndPageIndex: 0,
                    userListPageNum: 1,
                    list: [],
                  });
                  this.queryChatList();
                }}
              >
                筛选
              </Button>
              <Button
                style={{ marginLeft: 30 }}
                onClick={() =>
                  this.setState({
                    registStartTime: '',
                    registEndTime: '',
                    activeTaguser: [],
                    activeTagvisit: [],
                  })
                }
              >
                重置
              </Button>
            </Fragment>
          }
        >
          <div style={{ marginBottom: 10 }}>
            <div>注册时间：</div>
            <div style={{ marginTop: 10 }}>
              <DatePicker.RangePicker onChange={this.onDateChange} className={styles.date} format="YYYY-MM-DD" />
            </div>
          </div>
          <div style={{ marginBottom: 10 }}>
            <div>患者标签：</div>
            <div>
              {tagListuser.map((item, key) => {
                return (
                  <Tag style={{ cursor: 'pointer', marginTop: 6 }} key={item.id} color={activeTaguser.indexOf(key) > -1 ? PRIMARY_COLOR : ''} onClick={() => this.handleTagClick(key, 'user')}>
                    {item.tagName}
                  </Tag>
                );
              })}
            </div>
          </div>
          <div style={{ marginBottom: 10 }}>
            <div>回访标签：</div>
            <div>
              {tagListvisit.map((item, key) => {
                return (
                  <Tag style={{ cursor: 'pointer', marginTop: 6 }} key={item.id} color={activeTagvisit.indexOf(key) > -1 ? PRIMARY_COLOR : ''} onClick={() => this.handleTagClick(key, 'visit')}>
                    {item.tagName}
                  </Tag>
                );
              })}
            </div>
          </div>
        </Modal>
        <Modal title="创建会诊" visible={addConsultation} onCancel={() => this.setState({ addConsultation: false })} onOk={this.consultationSubmit} width="50%">
          <div className={styles.consultationRow}>
            <div>
              <text>*</text>会诊单号：
            </div>
            <div style={{ flex: 1 }}>
              <Input value={orderNo} onChange={this.handleChange} />
            </div>
          </div>
          <div className={styles.consultationRow}>
            <div>
              <text>*</text>关联病友：
            </div>
            <div style={{ flex: 1 }}>
              <Input disabled value={patientName} />
            </div>
          </div>
          <div className={styles.consultationRow}>
            <div>是否邀请病友：</div>
            <div style={{ flex: 1 }}>
              <Radio.Group onChange={this.addPatient} value={talkPatient}>
                <Radio value="0">否</Radio>
                <Radio value="1">是</Radio>
              </Radio.Group>
            </div>
          </div>
          <div className={styles.consultationRow}>
            <div>计划会诊时间：</div>
            <div>
              <DatePicker onChange={(date, dateString) => this.consulationTime(dateString)} className={styles.date} showTime />
            </div>
          </div>
          <div style={{ marginBottom: 10 }}>
            <div style={{ marginBottom: '10px' }}>
              <text style={{ color: 'red' }}>*</text>添加会诊医生：
            </div>
            <Input.Search placeholder="搜索" onSearch={this.searchContent} style={{ width: 203, marginBottom: 4 }} />
            <Transfer
              rowKey={record => record.value}
              dataSource={showData}
              targetKeys={rightShow}
              listStyle={{
                width: '43%',
                height: 400,
              }}
              render={this.getTransferItem}
              onChange={this.setTransfer}
              titles={['可选', '已选']}
              notFoundContent="暂无数据"
            />
          </div>
          <div className={styles.consultationRow}>
            <div>
              <text>*</text>选择主诊医生：
            </div>
            <div style={{ flex: 1 }}>
              <Select
                // mode="tags"
                style={{ width: '100%' }}
                placeholder="请选择主诊医生"
                onChange={this.selectAttendingDoctor}
                showSearch
              >
                {consulationOptions}
              </Select>
            </div>
          </div>
        </Modal>
        <AllGroupUserAdd show={showAllGroupUserAdd} onCancel={this.switchAllGroupUserAdd} onSuccess={this.queryChatList} />
      </Layout>
    );
  }
}

export default connect(({ user = {} }) => {
  return { authResource: user.authResource || {} };
})(withRouter(Index));
