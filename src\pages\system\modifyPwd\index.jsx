import React from 'react';
import { connect } from 'dva';
import { Input, Button } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';

import './style.less';

const FormItem = Form.Item;

export default connect()(
  Form.create()(({ dispatch, form }) => {
    const { getFieldDecorator, getFieldsValue, getFieldValue, validateFields } = form;

    const validateHandler = (rule, value, callback) => {
      const { field } = rule;
      switch (field) {
        case 'newPwd':
          if (!value || value == '') {
            callback('请输入新密码!');
          } else if (!/^(?=.*?[A-Za-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,18}$/.test(value)) {
            callback('长度为8-18位字符,需包含数字+字母+符号，空格除外!');
          } else {
            callback();
          }
          break;
        case 'confirmNewPwd':
          if (!value || value == '') {
            callback('请再次输入新密码!');
          } else if (!/^(?=.*?[A-Za-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,18}$/.test(value)) {
            callback('长度为8-18位字符,需包含数字+字母+符号，空格除外!');
          } else if (value != getFieldValue('newPwd')) {
            callback('密码不一致!');
          } else {
            callback();
          }
          break;
        default:
          callback();
          break;
      }
    };

    const modifyPassword = e => {
      e.preventDefault();
      validateFields(err => {
        if (!err) {
          dispatch({
            type: 'system/modifyPassword',
            payload: {
              ...getFieldsValue(),
            },
          });
        }
      });
    };

    return (
      <div className="page-modify-password">
        <Form style={{ marginTop: 64 }}>
          <FormItem label="原密码" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('oldPwd', {
              rules: [{ required: true, whitespace: true, message: '请输入原密码!' }],
            })(<Input type="password" style={{ width: 347 }} placeholder="请输入原密码" />)}
          </FormItem>
          <FormItem label="新密码" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('newPwd', {
              rules: [{ required: true, whitespace: true, validator: validateHandler }],
            })(<Input type="password" style={{ width: 347 }} placeholder="请输入新密码" />)}
          </FormItem>
          <FormItem label="确认新密码" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('confirmNewPwd', {
              rules: [{ required: true, whitespace: true, validator: validateHandler }],
            })(<Input type="password" style={{ width: 347 }} placeholder="请再次输入新密码" />)}
          </FormItem>
          <FormItem wrapperCol={{ span: 8, offset: 8 }}>
            <Button type="primary" onClick={modifyPassword}>
              保存
            </Button>
          </FormItem>
        </Form>
      </div>
    );
  }),
);
