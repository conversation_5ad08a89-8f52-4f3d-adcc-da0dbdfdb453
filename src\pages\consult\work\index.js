import React, { Component, Fragment } from 'react';
import { connect } from 'dva';
import { Tabs, Table, Button, Modal, Select, message, Tooltip, Input, Transfer } from 'antd';
import Rule from './Rule';
import * as Api from './api';

import styles from './index.less';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      tabs: [],
      localTabs: [
        { key: '1', val: '按个人分配' },
        { key: '2', val: '按医生分配' },
      ],
      activeLocalTab: '1',
      activeTab: 0,
      tempVals: [], // 临时的receiveNum值集合
      userList: [],
      doctorList: [],
      modalType: false,
      modalDoctor: false, // 添加医生弹窗
      modalNurse: false,
      userSel: [], // 添加成员的临时数据
      doctorSel: {}, // 添加医生的临时数据
      nurseSel: {}, // 编辑护士的临时数据--当前编辑的行
      nurseTargetKeys: [], // 编辑护士的临时数据--当前选择的护士account
      showRule: false,
      allUser: [],
      allNurse: [], // 所有护士数据
      switchMode: {},
    };

    this.statusConfig = {
      0: '不接收',
      1: '接收中',
      2: '排队中',
    };

    this.doctorColumns = [
      {
        title: '序号',
        width: 60,
        key: (record, item, key) => `${key + 1}`,
        render: (record, item, key) => {
          return (
            <Tooltip title="拖动排序">
              <div onDragStart={this.dragStart} onDragEnd={this.dragEnd} draggable="true" id={key} onDrop={e => this.drop(key, e)} onDragOver={this.dragOver} className={styles.sortItem}>
                {key + 1}
              </div>
            </Tooltip>
          );
        },
      },
      {
        title: '医生姓名',
        dataIndex: 'doctorName',
      },
      {
        title: '登录账号',
        dataIndex: 'doctorId',
      },
      {
        title: '跟诊护士',
        className: styles.maxWidth600,
        render: (text, { list = [] }) => {
          const accounts = list.map(item => {
            return `${item.name}(${item.account})`;
          });
          return accounts.map((item, index) => {
            return (
              <span className={styles.nowrap} key={item}>
                {item}
                {accounts.length == index + 1 ? '' : '；'}
              </span>
            );
          });
        },
      },
      {
        title: '操作',
        render: (text, record) => {
          return (
            <Fragment>
              <Button type="link" onClick={() => this.showModalNurse(record)}>
                编辑护士
              </Button>
              <Button type="link" onClick={() => this.doctorDelete(record.id)}>
                删除
              </Button>
            </Fragment>
          );
        },
      },
    ];
  }

  componentDidMount() {
    this.getBusType();
    this.getAllUsrs();
  }
  // 获取阶段列表
  getBusType = async () => {
    const { code, data = [] } = await Api.getBusType({
      type: this.props.roleType === 'doc' ? 1 : 0,
    });
    if (code == 0) {
      this.setState({ tabs: data }, this.getUserList);
    }
  };
  // 分配目标可编辑
  receiveNumDefaultCanEdit = rowIndex => {
    let { userList = [], tempVals = [] } = this.state;
    tempVals[rowIndex] = userList[rowIndex].receiveNumDefault;
    userList[rowIndex].isEditReceiveNumDefault = true;
    this.setState({
      userList: userList,
      tempVals: tempVals,
    });
  };
  // 分配目标输入
  tempValChange = (e, rowIndex) => {
    let { tempVals = [] } = this.state;
    tempVals[rowIndex] = e.target.value;
    this.setState({
      tempVals: tempVals,
    });
  };
  // 保存分配目标的编辑结果
  saveReceiveNumDefault = (rowIndex, cancel = false) => {
    let { userList = [], tempVals = [] } = this.state;
    userList[rowIndex].isEditReceiveNumDefault = false;
    userList[rowIndex].receiveNumDefault = tempVals[rowIndex] || userList[rowIndex].receiveNumDefault;
    if (cancel) {
      this.setState({
        tempVals: tempVals,
        userList: userList,
      });
      return;
    }
    if (this.changeReceiveNumDefault(userList[rowIndex])) {
      this.setState({
        tempVals: tempVals,
        userList: userList,
      });
    }
  };
  // 修改某行的receiveNum
  changeReceiveNumDefault = async rowData => {
    const { code, data = {}, msg } = await Api.changeReceiveNumDefault({
      id: rowData.id,
      receiveNumDefault: Number(rowData.receiveNumDefault),
    });
    if (code == 0) {
      return true;
    }
    return false;
  };
  // 按个人分配列表
  getUserList = async () => {
    const { activeTab, tabs } = this.state;
    const idx = activeTab * 1;
    const param = {
      periodId: tabs[idx]?.no,
      periodName: tabs[idx]?.name,
      numPerPage: 1000,
      pageNum: 1,
    };
    const { code, data = [] } = await Api.getUserList(param);
    if (code == 0) {
      const userList = data.map(item => {
        if (item.list && item.list.length) {
          const loopData = item.list.reduce(
            (h, user) => {
              h.name.push(user.name);
              h.account.push(user.account);
              return h;
            },
            { name: [], account: [] },
          );
          item.name = loopData.name.join();
          item.account = loopData.account.join();
        }
        return item;
      });
      this.setState({ userList });
    }
  };
  // 按医生分配列表
  getDoctorList = async () => {
    const { activeTab, tabs } = this.state;
    const idx = activeTab * 1;
    const param = {
      periodId: tabs[idx].dictKey,
      periodName: tabs[idx].dictValue,
      numPerPage: 1000,
      pageNum: 1,
      queryType: 'doctor',
    };
    const { code, data = [] } = await Api.getUserList(param);
    if (code == 0) {
      const doctorList = data.map(item => {
        if (item.list && item.list.length) {
          const loopData = item.list.reduce(
            (h, user) => {
              h.name.push(user.name);
              h.account.push(user.account);
              return h;
            },
            { name: [], account: [] },
          );
          item.name = loopData.name.join();
          item.account = loopData.account.join();
        }
        return item;
      });
      this.setState({ doctorList });
    }
  };
  // 获取所有医护人员
  getAllUsrs = async () => {
    const { code, data = [] } = await Api.getAllUser();
    let allNurse = [];
    for (let i in data) {
      const item = data[i];
      if (item.identityId == 2 || item.identityName == '护士') {
        allNurse = item.userList;
      }
    }
    if (code == 0) {
      this.setState({ allUser: data, allNurse });
    }
  };

  changeTab = e => {
    this.setState({ activeTab: e }, () => {
      this.getUserList();
      // this.getDoctorList();
    });
  };
  changeLocalTab = key => {
    if (key == '2') {
      this.getDoctorList();
    }
    this.setState({ activeLocalTab: key });
  };

  // 新增成员
  handleSubmit = async () => {
    const { modalType, activeTab, tabs, userSel } = this.state;
    const idx = activeTab * 1;
    if (!userSel || !userSel.length) {
      message.error('请选择成员');
      return false;
    }
    const selList = userSel.map(item => {
      return {
        account: item.key,
        name: item.label,
      };
    });
    const param = {
      periodId: tabs[idx].no,
      periodName: tabs[idx].name,
      list: selList,
    };
    const { code, msg } = await Api.addReceiveUserGroup(param);
    if (code == 0) {
      message.success('添加成功');
      this.getUserList();
      this.setState({ modalType: false, userSel: [] });
    }
  };
  setPersonSel = val => {
    console.log(this.state.userSel);
    this.setState({ userSel: val });
  };
  // 新增医生
  addDoctor = async () => {
    const { modalDoctor, activeTab, tabs, doctorSel = {} } = this.state;
    const idx = activeTab * 1;
    if (!doctorSel || !doctorSel.key || !doctorSel.label) {
      message.error('请选择成员');
      return false;
    }
    const param = {
      periodId: tabs[idx].dictKey,
      periodName: tabs[idx].dictValue,
      doctorId: doctorSel.key,
      doctorName: doctorSel.label,
    };
    const { code, msg } = await Api.addDoctor(param);
    if (code == 0) {
      message.success('添加成功');
      this.getDoctorList();
      this.setState({ modalDoctor: false, doctorSel: {} });
    }
  };
  setDoctorSel = val => {
    this.setState({
      doctorSel: val,
    });
  };
  // 编辑护士弹窗
  showModalNurse = row => {
    const { list = [] } = row;
    const nurseTargetKeys = list.map(item => item.account);
    this.setState({
      nurseSel: row,
      modalNurse: true,
      nurseTargetKeys,
    });
  };
  // 编辑护士--跟诊护士穿梭框数据改变
  nurseSelChange = targetKeys => {
    this.setState({ nurseTargetKeys: targetKeys });
  };
  // 编辑护士-提交
  updateNurse = async () => {
    const { nurseSel = {}, nurseTargetKeys = [], allNurse = [] } = this.state;
    const { id = '', doctorId = '', doctorName = '' } = nurseSel;
    const list = allNurse.reduce((result, item) => {
      if (nurseTargetKeys.includes(item.account)) {
        result.push({ account: item.account, name: item.name });
      }
      return result;
    }, []);
    const { code, data = {} } = await Api.updateNurse({ id, doctorId, doctorName, list });
    if (code == 0) {
      message.success('编辑护士成功');
      this.setState(
        {
          nurseSel: {},
          nurseTargetKeys: [],
          modalNurse: false,
        },
        this.getDoctorList,
      );
    }
  };
  // 删除成员
  preDelete = id => {
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除该成员?',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.doDelete(id);
      },
    });
  };
  doDelete = async id => {
    const { code, msg = '' } = await Api.deletePerson({ id });
    if (code == 0) {
      message.success('删除成功');
      this.getUserList();
    } else {
      message.error(msg);
    }
  };
  // 删除医生
  doctorDelete = id => {
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除该医生?',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.doDeleteDoctor(id);
      },
    });
  };
  doDeleteDoctor = async id => {
    const { code, msg } = await Api.deletePerson({ id });
    if (code == 0) {
      message.success('删除成功');
      this.getDoctorList();
    }
  };
  preSwitch = record => {
    if (record.status == 1) {
      return false;
    }
    this.setState({ switchMode: record });
  };

  doSwitch = async () => {
    const { switchMode = {}, switchStatus } = this.state;
    const { code, msg } = await Api.switchStatus({ id: switchMode.id, periodId: switchMode.periodId, status: switchStatus });
    if (code == 0) {
      message.success('切换成功');
      this.setState({ switchMode: {} });
      this.getUserList();
    } else {
      message.error(msg || '切换失败');
    }
  };
  /***********拖拽排序开始************/
  dragStart = event => {
    //dataTransfer.setData()方法设置数据类型和拖动的数据
    event.dataTransfer.setData('eleid', event.target.id);
    //修改拖动元素的透明度
    // event.target.style.opacity = "0.4";
  };

  dragEnd = event => {
    event.preventDefault();
    //修改拖动元素的透明度
    // event.target.style.opacity = "1";
  };

  dragOver = event => {
    event.preventDefault();
    //event.effectAllowed = true;
  };

  drop = async (sourceSortNo, event) => {
    event.preventDefault();
    const targetSortNo = event.dataTransfer.getData('eleid') * 1;
    const { userList = [], doctorList = [], activeLocalTab } = this.state;
    if (activeLocalTab == '1') {
      const targetId = (userList[targetSortNo] || {}).id;
      const sourceId = (userList[sourceSortNo] || {}).id;
      const { code } = await Api.sortData({ targetId, targetSortNo, sourceId, sourceSortNo });
      if (code == 0) {
        this.getUserList();
      }
    }
    if (activeLocalTab == '2') {
      const targetId = (doctorList[targetSortNo] || {}).id;
      const sourceId = (doctorList[sourceSortNo] || {}).id;
      const { code } = await Api.sortData({ targetId, targetSortNo, sourceId, sourceSortNo });
      if (code == 0) {
        this.getDoctorList();
      }
    }
  };
  /***********拖拽排序结束************/

  render() {
    const {
      activeTab,
      tabs = [],
      localTabs = [],
      activeLocalTab,
      userList = [],
      doctorList,
      modalDoctor,
      modalType,
      modalNurse,
      showRule,
      nurseTargetKeys,
      allNurse = [],
      allUser = [],
      switchMode,
    } = this.state;
    const tabIdx = activeTab * 1;

    this.tableColumns = [
      {
        title: '序号',
        width: 60,
        key: (record, item, key) => `${key + 1}`,
        render: (record, item, key) => {
          return (
            <Tooltip title="拖动排序">
              <div onDragStart={this.dragStart} onDragEnd={this.dragEnd} draggable="true" id={key} onDrop={e => this.drop(key, e)} onDragOver={this.dragOver} className={styles.sortItem}>
                {key + 1}
              </div>
            </Tooltip>
          );
        },
      },
      {
        title: '姓名',
        dataIndex: 'name',
        className: styles.tabColumMax,
      },
      {
        title: '登录账号',
        dataIndex: 'account',
        className: styles.tabColumMax,
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: (text, record) => {
          return (
            <span className={styles[`status${text}`]}>
              {this.statusConfig[text]}&nbsp;&nbsp;
              {text == 1 ? (
                <span>
                  {record.receiveNum}/{record.receiveNumDefault}
                </span>
              ) : null}
            </span>
          );
        },
      },
      {
        title: '分配目标',
        dataIndex: 'receiveNumDefault',
        align: 'center',
        className: styles.tabColumReceiveNum,
        render: (text, { receiveNumDefault = 0, isEditReceiveNumDefault = false }, index) => {
          const { tempVals = [] } = this.state;
          return (
            <Fragment>
              {isEditReceiveNumDefault ? (
                <Fragment>
                  <Input value={tempVals[index]} onChange={e => this.tempValChange(e, index)} className={styles.tableReceiveNum} />
                  <Button type="link" onClick={() => this.saveReceiveNumDefault(index)}>
                    保存
                  </Button>
                  <Button type="link" onClick={() => this.saveReceiveNumDefault(index, true)}>
                    取消
                  </Button>
                </Fragment>
              ) : (
                <Fragment>
                  <span className={styles.tableReceiveNum}>{receiveNumDefault}</span>
                  <Button type="link" onClick={() => this.receiveNumDefaultCanEdit(index)}>
                    编辑
                  </Button>
                </Fragment>
              )}
            </Fragment>
          );
        },
      },
      {
        title: '患者总数',
        dataIndex: 'totalReceiveNum',
      },
      {
        title: '操作',
        render: record => {
          return (
            <Fragment>
              <span className={record.status != 1 ? styles.tableOperText : styles.tableOperTextDisable} onClick={() => this.preSwitch(record)}>
                切换状态
              </span>
              <span className={styles.tableOperText} onClick={() => this.preDelete(record.id)}>
                删除
              </span>
            </Fragment>
          );
        },
      },
    ];
    return (
      <div className={styles.connect}>
        {tabs.length > 0 ? (
          <Tabs defaultActiveKey="0" onChange={this.changeTab}>
            {tabs.map((item, key) => {
              return <Tabs.TabPane tab={item.name} key={key} />;
            })}
          </Tabs>
        ) : null}
        {/* {
          <Tabs activeKey={activeLocalTab} onChange={this.changeLocalTab} type="card">
            {
              localTabs.map(item => {
                return (
                  <Tabs.TabPane tab={item.val} key={item.key} />
                )
              })
            }
          </Tabs>
        } */}
        {activeLocalTab == '1' && (
          <Fragment>
            <div className={styles.flexLine}>
              <div className={styles.flexItem}>
                <Button style={{ margin: '24px' }} type="primary" onClick={() => this.setState({ modalType: true })}>
                  添加成员
                </Button>
              </div>
              {/* <div><Button type="primary" style={{ marginRight: 30 }} onClick={() => this.setState({ showRule: true })}>就诊规则</Button></div> */}
            </div>
            <Table
              style={{ margin: '0 24px 24px' }}
              dataSource={userList}
              columns={this.tableColumns}
              rowKey="id"
              locale={{
                emptyText: '暂无数据',
              }}
              pagination={false}
            />
          </Fragment>
        )}
        {activeLocalTab == '2' && (
          <Fragment>
            <div className={styles.flexLine}>
              <div className={styles.flexItem}>
                <Button style={{ marginLeft: 30 }} type="primary" onClick={() => this.setState({ modalDoctor: true })}>
                  添加医生
                </Button>
              </div>
            </div>
            <Table
              style={{ marginTop: 16 }}
              dataSource={doctorList}
              columns={this.doctorColumns}
              rowKey="id"
              locale={{
                emptyText: '暂无数据',
              }}
              pagination={false}
            />
          </Fragment>
        )}
        <Modal title="添加成员" visible={!!modalType} onOk={this.handleSubmit} onCancel={() => this.setState({ modalType: false, userSel: [] })}>
          <div className={styles.flexLine}>
            <div style={{ width: 120, textAlign: 'right' }}>选择用户：</div>
            <div className={styles.flexItem} key={modalType}>
              <Select
                placeholder="请选择"
                showSearch
                value={this.state.userSel}
                labelInValue
                mode="multiple"
                style={{ width: 300 }}
                onChange={this.setPersonSel}
                filterOption={(ipt, node) => {
                  if (!ipt && !ipt.length) {
                    return true;
                  }
                  if (node.props.label) {
                    return node.props.label.indexOf(ipt) >= 0;
                  }
                  return (node.props.value || '').indexOf(ipt) >= 0 || (node.props.children || '').indexOf(ipt) >= 0;
                }}
              >
                {allUser.map(item => {
                  return (
                    <Select.OptGroup label={item.identityName} key={item.identityId}>
                      {(item.userList || []).map(user => {
                        return <Select.Option key={user.account}>{user.name}</Select.Option>;
                      })}
                    </Select.OptGroup>
                  );
                })}
              </Select>
            </div>
          </div>
        </Modal>
        <Modal title="状态切换" visible={!!switchMode.id} onOk={this.doSwitch} onCancel={() => this.setState({ switchMode: {} })}>
          <div className={styles.flexLine}>
            <div style={{ width: 120, textAlign: 'right' }}>选择状态：</div>
            <div className={styles.flexItem}>
              <Select placeholder="请选择" style={{ width: 300 }} onChange={val => this.setState({ switchStatus: val })}>
                {Object.entries(this.statusConfig).map(item => {
                  if (item[0] == switchMode.status) {
                    return null;
                  }
                  return <Select.Option key={item[0]}>{item[1]}</Select.Option>;
                })}
              </Select>
            </div>
          </div>
        </Modal>
        <Modal title="添加医生" visible={modalDoctor} onOk={this.addDoctor} onCancel={() => this.setState({ modalDoctor: false })}>
          <div className={styles.flexLine}>
            <div style={{ width: 120, textAlign: 'right' }}>选择用户：</div>
            <div className={styles.flexItem} key={modalType}>
              <Select
                placeholder="请选择"
                showSearch
                labelInValue
                style={{ width: 300 }}
                onChange={this.setDoctorSel}
                filterOption={(ipt, node) => {
                  if (!ipt && !ipt.length) {
                    return true;
                  }
                  if (node.props.label) {
                    return node.props.label.indexOf(ipt) >= 0;
                  }
                  return (node.props.value || '').indexOf(ipt) >= 0 || (node.props.children || '').indexOf(ipt) >= 0;
                }}
              >
                {allUser.map(item => {
                  return (
                    <Select.OptGroup label={item.identityName} key={item.identityId}>
                      {(item.userList || []).map(user => {
                        return <Select.Option key={user.account}>{user.name}</Select.Option>;
                      })}
                    </Select.OptGroup>
                  );
                })}
              </Select>
            </div>
          </div>
        </Modal>
        <Modal title="编辑护士" visible={modalNurse} width="680px" onOk={this.updateNurse} onCancel={() => this.setState({ modalNurse: false })}>
          <Transfer
            dataSource={allNurse}
            titles={['未跟诊护士', '已跟诊护士']}
            listStyle={{
              width: 260,
              height: 500,
            }}
            showSearch
            operations={['批量跟诊', '取消跟诊']}
            filterOption={this.filterOption}
            targetKeys={nurseTargetKeys}
            onChange={this.nurseSelChange}
            render={record => record.name}
            rowKey={record => record.account}
          />
        </Modal>
        {showRule ? <Rule getUserList={this.getUserList} show={showRule} period={tabs[tabIdx]} cancel={() => this.setState({ showRule: false })} /> : null}
      </div>
    );
  }
}

export default connect()(Index);
