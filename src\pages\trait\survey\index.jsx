import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Link } from 'react-router-dom';
import { Tabs, Table, Button, Modal } from 'antd';

import * as utils from '../../../utils/utils';

import './style.less';

const TabPane = Tabs.TabPane;
const surveyStatus = [
  {
    status: '1',
    txt: '进行中',
  },
  {
    status: '2',
    txt: '未进行',
  },
  {
    status: '3',
    txt: '已结束',
  },
  {
    status: '0',
    txt: '草稿箱',
  },
];

class Survey extends React.Component {
  constructor(props) {
    super(props);
    this.activeKey = '1';
    this.reqData = {
      pageNum: 1,
    };
    this.tabsChange = e => {
      this.activeKey = e;
      this.reqData.pageNum = 1;
      this.getQuestionLst(e);
    };

    this.deleteQuestion = id => {
      const { dispatch } = this.props;
      const { reqData, activeKey } = this;
      Modal.confirm({
        title: '确定删除该问卷或投票吗?',
        onOk() {
          dispatch({
            type: 'trait/deleteQuestionForid',
            payload: {
              id,
              ...reqData,
              status: activeKey,
            },
          });
        },
      });
    };
  }

  componentDidMount() {
    const status = utils.queryStringToJson(this.props.location.search).status;
    this.activeKey = status || '1';
    this.getQuestionLst(status || '1');
  }

  getQuestionLst(status) {
    this.props.dispatch({
      type: 'trait/fetchQuestionLst',
      payload: {
        status,
        ...this.reqData,
      },
    });
  }
  handleSubmit(pageno) {
    this.reqData.pageNum = pageno;
    this.getQuestionLst(this.activeKey);
  }

  render() {
    const { trait, btns = {} } = this.props;

    const data = trait.question.questionList && trait.question.questionList.data ? trait.question.questionList.data.recordList : [];
    data.map(itemList => {
      let pushTimeArr = [];
      if (itemList.pushText) {
        JSON.parse(itemList.pushText).map(item => {
          let pushTime = item.bizValue;
          switch (item.pushType) {
            case '0':
              pushTime = `${pushTime}, 立即接收`;
              break;
            case '2':
              pushTime = `${pushTime}, 定时接收`;
              break;
            case '1':
              pushTime = `${pushTime}, 延时接收`;
              break;
            default:
              break;
          }
          if (item.pushParam) {
            switch (item.pushParam) {
              case '10:00':
                pushTime = `${pushTime}(上午10点)`;
                break;
              case '15:00':
                pushTime = `${pushTime}(下午3点)`;
                break;
              case '19:00':
                pushTime = `${pushTime}(晚上7点)`;
                break;
              case '10':
                pushTime = `${pushTime}(10分钟后)`;
                break;
              case '60':
                pushTime = `${pushTime}(1小时后)`;
                break;
              default:
                // pushTime = `${pushTime}(${item.pushParam / 24 / 60}天后)`;
                pushTime = `${pushTime}${isNaN(item.pushParam / 24 / 60) ? '' : `(${item.pushParam / 24 / 60}天后)`}`;
                break;
            }
          }
          pushTimeArr.push(pushTime);
        });
      } else {
        itemList.pushTime = '所有用户';
        return itemList;
      }
      itemList.pushTime = pushTimeArr.join('; ');
      return itemList;
    });
    const columns = [
      {
        title: '序号',
        width: 150,
        dataIndex: 'id',
        render: (text, record, index) => {
          return <span>{index + 1}</span>;
        },
      },
      {
        title: '问卷标题',
        dataIndex: 'examTitle',
        width: '30%',
      },
      {
        title: '调查范围',
        dataIndex: 'pushTime',
        width: '20%',
      },
      {
        title: '有效期',
        render: record => {
          return (
            <p>
              <span>
                {record.beginTime} -- {record.endTime}
              </span>
            </p>
          );
        },
      },
      {
        title: '操作',
        render: record => {
          return (
            <p className="opt-area">
              {btns['/trait/survey/detail'] ? <Link to={`/trait/survey/detail?id=${record.id}&beginTime=${record.beginTime}&endTime=${record.endTime}`}>查看</Link> : null}
              {btns['/trait/survey/delete'] && this.activeKey != 1 ? (
                <span>
                  <span className="table-header-split">|</span>
                  <a onClick={() => this.deleteQuestion(record.id)}>删除</a>
                </span>
              ) : null}
            </p>
          );
        },
      },
    ];
    return (
      <div className="page-survey">
        <Tabs onTabClick={e => this.tabsChange(e)} activeKey={this.activeKey} animated={{ tabPane: false }} className="11">
          {surveyStatus.map(item => {
            return (
              <TabPane tab={item.txt} key={item.status}>
                <div className="question-list-container">
                  {btns['/trait/survey/create'] ? (
                    <Button
                      style={{ marginBottom: 10 }}
                      type="primary"
                      onClick={() =>
                        history.push({
                          pathname: '/trait/survey/create',
                        })
                      }
                    >
                      新建问卷
                    </Button>
                  ) : null}
                  <Table
                    dataSource={data}
                    columns={columns}
                    rowKey={row => `${row.id}-`}
                    pagination={{
                      total: trait.question.questionList.data ? trait.question.questionList.data.totalCount : 0,
                      onChange: page => {
                        this.handleSubmit(page);
                      },
                    }}
                  />
                </div>
              </TabPane>
            );
          })}
        </Tabs>
      </div>
    );
  }
}

export default connect(state => {
  return {
    trait: state.trait,
    btns: state.root.permissionData.btns,
  };
})(Survey);
