/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select, DatePicker } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { djyjwdjctemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, djyjwdjctemplate: { ...djyjwdjctemplate, ...payload } },
      },
    });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="同意书内容" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="先证者">
                <Row gutter={[16, 0]}>
                  <Col span={6}>
                    <Form.Item label="姓名">
                      <Input placeholder="请输入" value={djyjwdjctemplate.field1} onChange={e => changeData({ field1: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="身份证号码"
                      validateStatus={
                        /^([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}(\d|X|x))$/.test(djyjwdjctemplate.field2) || !djyjwdjctemplate.field2 ? 'success' : 'error'
                      }
                      help={
                        /^([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}(\d|X|x))$/.test(djyjwdjctemplate.field2) || !djyjwdjctemplate.field2 ? '' : '请输入正确的身份证号码'
                      }
                    >
                      <Input placeholder="请输入" value={djyjwdjctemplate.field2} onChange={e => changeData({ field2: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="性别">
                      <Input placeholder="请输入" value={djyjwdjctemplate.field3} onChange={e => changeData({ field3: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="出生年月">
                      <DatePicker format="YYYY-MM-DD" value={djyjwdjctemplate.field4 ? moment(djyjwdjctemplate.field4) : null} onChange={(date, dateString) => changeData({ field4: dateString })} />
                    </Form.Item>
                  </Col>
                </Row>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="父亲">
                <Row gutter={[16, 0]}>
                  <Col span={6}>
                    <Form.Item label="姓名">
                      <Input placeholder="请输入" value={djyjwdjctemplate.field5} onChange={e => changeData({ field5: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="身份证号码"
                      validateStatus={
                        /^([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}(\d|X|x))$/.test(djyjwdjctemplate.field6) || !djyjwdjctemplate.field6 ? 'success' : 'error'
                      }
                      help={
                        /^([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}(\d|X|x))$/.test(djyjwdjctemplate.field6) || !djyjwdjctemplate.field6 ? '' : '请输入正确的身份证号码'
                      }
                    >
                      <Input placeholder="请输入" value={djyjwdjctemplate.field6} onChange={e => changeData({ field6: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="性别">
                      <Input placeholder="请输入" value={djyjwdjctemplate.field7} onChange={e => changeData({ field7: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="出生年月">
                      <DatePicker format="YYYY-MM-DD" value={djyjwdjctemplate.field8 ? moment(djyjwdjctemplate.field8) : null} onChange={(date, dateString) => changeData({ field8: dateString })} />
                    </Form.Item>
                  </Col>
                </Row>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="母亲">
                <Row gutter={[16, 0]}>
                  <Col span={6}>
                    <Form.Item label="姓名">
                      <Input placeholder="请输入" value={djyjwdjctemplate.field9} onChange={e => changeData({ field9: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="身份证号码"
                      validateStatus={
                        /^([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}(\d|X|x))$/.test(djyjwdjctemplate.field10) || !djyjwdjctemplate.field10 ? 'success' : 'error'
                      }
                      help={
                        /^([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}(\d|X|x))$/.test(djyjwdjctemplate.field10) || !djyjwdjctemplate.field10 ? '' : '请输入正确的身份证号码'
                      }
                    >
                      <Input placeholder="请输入" value={djyjwdjctemplate.field10} onChange={e => changeData({ field10: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="性别">
                      <Input placeholder="请输入" value={djyjwdjctemplate.field11} onChange={e => changeData({ field11: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="出生年月">
                      <DatePicker format="YYYY-MM-DD" value={djyjwdjctemplate.field12 ? moment(djyjwdjctemplate.field12) : null} onChange={(date, dateString) => changeData({ field12: dateString })} />
                    </Form.Item>
                  </Col>
                </Row>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="待诊者">
                <Row gutter={[16, 0]}>
                  <Col span={6}>
                    <Form.Item label="姓名">
                      <Input placeholder="请输入" value={djyjwdjctemplate.field13} onChange={e => changeData({ field13: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="身份证号码"
                      validateStatus={
                        /^([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}(\d|X|x))$/.test(djyjwdjctemplate.field14) || !djyjwdjctemplate.field14 ? 'success' : 'error'
                      }
                      help={
                        /^([1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2]\d|3[0-1])\d{3}(\d|X|x))$/.test(djyjwdjctemplate.field14) || !djyjwdjctemplate.field14 ? '' : '请输入正确的身份证号码'
                      }
                    >
                      <Input placeholder="请输入" value={djyjwdjctemplate.field14} onChange={e => changeData({ field14: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="性别">
                      <Input placeholder="请输入" value={djyjwdjctemplate.field15} onChange={e => changeData({ field15: e.target.value })} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="出生年月">
                      <DatePicker format="YYYY-MM-DD" value={djyjwdjctemplate.field16 ? moment(djyjwdjctemplate.field16) : null} onChange={(date, dateString) => changeData({ field16: dateString })} />
                    </Form.Item>
                  </Col>
                </Row>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="单基因病诊断或产前诊断的疾病名称">
                <Input placeholder="请输入" value={djyjwdjctemplate.field17} onChange={e => changeData({ field17: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="检测的基因名称">
                <Input placeholder="请输入" value={djyjwdjctemplate.field18} onChange={e => changeData({ field18: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="检测方法">
                <Input placeholder="请输入" value={djyjwdjctemplate.field19} onChange={e => changeData({ field19: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item>
                <div className="flex-box">
                  <div className="flex-shrink">本检测只针对检测范围内的</div>
                  <Input placeholder="请输入" value={djyjwdjctemplate.field20} onChange={e => changeData({ field20: e.target.value })} />
                  <div className="flex-shrink">基因</div>
                  <Input placeholder="请输入" value={djyjwdjctemplate.field21} onChange={e => changeData({ field21: e.target.value })} />
                  <div className="flex-shrink">导致的</div>
                  <Input placeholder="请输入" value={djyjwdjctemplate.field22} onChange={e => changeData({ field22: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="该遗传病诊断/产前诊断的必要性">
                <Input placeholder="请输入" value={djyjwdjctemplate.field23} onChange={e => changeData({ field23: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="该诊断/产前诊断技术的安全性、有效性、局限性和风险">
                <Input placeholder="请输入" value={djyjwdjctemplate.field24} onChange={e => changeData({ field24: e.target.value })} />
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
