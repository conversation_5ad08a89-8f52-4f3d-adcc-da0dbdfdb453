import React, { Component, Fragment } from 'react';
import { Radio, Input, Table, Upload, Modal, Button, DatePicker, Select, message, Switch } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';
import * as Api from './api';
import styles from './right.less';
import { connect } from 'dva';
import { CheckOutlined } from '@ant-design/icons';
const { Option } = Select;
const { TextArea } = Input;

@connect(({ user = {} }) => {
  return { currentUser: user.currentUser };
})
@Form.create()
class Index extends Component {
  constructor(prop) {
    super(prop);
    this.state = {
      previewVisible: false,
      previewImage: '',
      preZoom: 1,
      preRotate: 0,
      preLight: 1,
      modalInspaceList: [],
      previewObj: {},
      reportTypes: [],
      reportTypeItemList: [],
      modalItemErrKey: -1,
      sourceVisible: false, // 查看原图显示
      sourceImage: '', // 查看原图链接
      showImgOperTool: true,
      sortStatus: 0,
      reportId: '',
      reportlist0: this.props.reportlist0 || [],
      createTime: '',
      handlerAccount: '',
      handlerName: '',
      updateTime: '',
      protocol: '',
    };
  }

  componentDidMount() {
    this.addModalInspaceItem();
    this.getReportTypes();
    this.getProtocal();
  }

  shouldComponentUpdate(nextProps) {
    const { reportlist0 } = this.props;
    const { reportlist0: newReportlist0 } = nextProps;
    if (reportlist0 != newReportlist0) {
      this.setState({ reportlist0: newReportlist0 });
    }
    return true;
  }

  getProtocal = () => {
    const curProtocol = window.location.protocol;
    // console.log(curProtocol, 'getProtocal');
    this.setState({ protocol: curProtocol });
  };

  getReportTypes = async () => {
    const { code, data = [] } = await Api.getReportTypes();
    if (code == 0) {
      this.setState({ reportTypes: data });
      if (data.length) {
        this.getReportTypesItemList({ key: data[0].inspType });
      }
    }
  };

  getReportTypesItemList = async (inspType, modalKey) => {
    const {
      form: { setFieldsValue },
    } = this.props;
    setFieldsValue({ [`${modalKey}modalItem2`]: undefined });
    const { code, data = [] } = await Api.getReportTypesItemList({ inspType: inspType.key });
    if (code == 0) {
      this.setState({ reportTypeItemList: data });
    }
  };

  getBase64 = file => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };

  handleCancel = () =>
    this.setState(
      {
        previewVisible: false,
        previewObj: {},
        previewImage: '',
        preZoom: 1,
        preRotate: 0,
        modalInspaceList: [],
        modalItemErrKey: -1,
      },
      this.addModalInspaceItem,
    );

  handlePreview = async (e, file) => {
    if (e) {
      e.stopPropagation(); //停止冒泡，不触发上传图片事件
    }
    // const { hisId } = this.props;
    if (!file.url && !file.preview) {
      file.preview = await this.getBase64(file.originFileObj);
    }
    const previewImage = file.url || file.preview;
    // const { data = [] } = await Api.getReportTypes({ hisId });
    const preZoom = localStorage.getItem(`${previewImage}-preZoom`) || 1;
    const preRotate = localStorage.getItem(`${previewImage}-preRotate`) || 0;
    const preLight = localStorage.getItem(`${previewImage}-preLight`) || 1;
    const { sortStatus, reportId, createTime, handlerAccount, handlerName, updateTime } = file;
    this.setState({
      previewImage,
      previewVisible: true,
      previewObj: file,
      preZoom: preZoom * 1,
      preRotate: preRotate * 1,
      preLight: preLight * 1,
      sortStatus,
      reportId,
      createTime,
      handlerAccount,
      handlerName,
      updateTime,
    });
  };

  handleSubmit = val => {
    this.setState({ modalItemErrKey: -1 });
    const {
      form: { validateFields },
    } = this.props;
    validateFields(async (err, values) => {
      //确认分类
      if (val == 'confirmClassification') {
        if (!err) {
          const { modalInspaceList, previewObj } = this.state;
          const inspace = modalInspaceList.map(item => {
            const paramDetail = {
              inspTypeName: values[`${item}modalItem1`].key,
              inspType: values[`${item}modalItem1`].label,
              inspName: values[`${item}modalItem2`].key,
              inspCode: values[`${item}modalItem2`].label,
              resultVal: values[`${item}modalItem3`],
              result: values[`${item}modalItem4`],
              content: values[`${item}modalItem5`],
            };
            return paramDetail;
          });
          const { code, data } = await Api.sortReport({
            reportId: previewObj.reportId,
            sex: values.sex,
            checkTime: values.checkTime.format('YYYY-MM-DD'),
            paramDetail: inspace,
            hisId: 242,
          });
          if (code == 0) {
            message.success('分类成功');
            this.handleCancel();
            const { queryReportOutHospital, queryReportOutHospitalDetail, typeSex } = this.props;
            queryReportOutHospital({ sortStatus: 0, sex: typeSex });
            queryReportOutHospitalDetail('F');
            queryReportOutHospitalDetail('M');
          }
        } else {
          const errKeys = Object.keys(err);
          const errKey = errKeys[0];
          if (errKey.indexOf('modalItem') > -1) {
            const modalItemKey = errKey.split('modalItem')[0];
            this.setState({ modalItemErrKey: modalItemKey * 1 });
          } else {
            message.error(err[errKeys[0]].errors[0].message);
          }
        }
      } else {
        const { previewObj } = this.state;
        const { code = '', data } = await Api.sortReport({
          reportId: previewObj.reportId,
          hisId: 242,
          sortStatus: val ? 2 : 0,
          paramDetail: [{ key: '123' }],
        });
        if (code == 0) {
          const { queryReportOutHospital, typeSex } = this.props;
          const { currentUser } = this.props;
          const { reportlist0 } = this.state;
          const { reportId } = this.state;
          message.success('处理成功');
          let sortStatus = 0;
          if (val) {
            sortStatus = 2;
          } else {
            sortStatus = 0;
          }
          let newReportList = reportlist0.map(item => {
            if (item.reportId == reportId) {
              item.sortStatus = sortStatus;
            }
            return item;
          });
          queryReportOutHospital({ sortStatus, sex: typeSex });
          this.setState({ sortStatus, reportlist0: newReportList, handlerName: currentUser.name, updateTime: moment().format('YYYY-MM-DD HH:mm:ss') });
        }
      }
    });
  };

  scrollImgList = direction => {
    if (!this.imgScroll) {
      return false;
    }
    const { scrollLeft } = this.imgScroll;
    this.imgScroll.scrollLeft = scrollLeft + direction * 500;
  };

  queryReportById = async record => {
    const { code, data = {} } = await Api.queryReportById({ reportId: record.reportId });
    if (code == 0) {
      const { fileUrl = '' } = data;
      if (fileUrl) {
        // window.open(fileUrl);
        this.setState({
          sourceImage: fileUrl,
          sourceVisible: true,
        });
      }
    }
  };

  handleSourceCancel = () => {
    this.setState({
      sourceVisible: false,
      sourceImage: '',
      preZoom: 1,
      preRotate: 0,
    });
  };

  deleteReportOutHospital = async () => {
    const { previewObj } = this.state;
    const { code } = await Api.deleteReportOutHospital({ reportId: previewObj.reportId });
    if (code == 0) {
      message.success('删除成功');
      this.handleCancel();
      const { queryReportOutHospital, typeSex } = this.props;
      queryReportOutHospital({ sortStatus: 0, sex: typeSex });
    }
  };

  addModalInspaceItem = () => {
    const { modalInspaceList = [] } = this.state;
    modalInspaceList.push(modalInspaceList.length);
    this.setState({ modalInspaceList });
  };

  removeModalInspaceItem = idx => {
    const { modalInspaceList = [] } = this.state;
    modalInspaceList.splice(idx, 1);
    this.setState({ modalInspaceList: Array.from(modalInspaceList) });
  };

  upFileChange = async e => {
    const { response = {}, status } = e.file;
    if (status === 'done' && response) {
      if (response.code == 0) {
        const { fileUrl, reportId } = response.data || {};
        const { chatInfo, typeSex: sex, queryReportOutHospital } = this.props;
        console.log(response.data);
        const { code: addCode } = await Api.addReportOutHospital({ fileUrl, pid: chatInfo.pid, sex, reportId });
        if (addCode == 0) {
          message.success('添加成功');
          queryReportOutHospital({ sortStatus: 0, sex });
        }
      }
    }
  };

  previewNext = n => {
    const { reportlist0 = [], previewObj = {} } = this.state;
    const idx = reportlist0.findIndex(item => {
      return item.reportId === previewObj.reportId;
    });
    const nextIdx = idx + n;
    if (reportlist0[nextIdx]) {
      this.handlePreview('', reportlist0[nextIdx]);
    }
  };

  zoomImg = n => {
    const { preZoom = 1, previewImage } = this.state;
    const newZoom = preZoom + n * 0.1;
    this.setState({ preZoom: newZoom });
    localStorage.setItem(`${previewImage}-preZoom`, newZoom);
  };

  lightImg = n => {
    const { preLight = 1, previewImage } = this.state;
    const newLight = preLight + n * 0.1;
    this.setState({ preLight: newLight });
    localStorage.setItem(`${previewImage}-preLight`, newLight);
  };

  rotateImg = () => {
    const { preRotate = 0, previewImage } = this.state;
    const newRotate = preRotate + 90;
    this.setState({ preRotate: newRotate });
    localStorage.setItem(`${previewImage}-preRotate`, newRotate);
  };

  printImg = url => {
    let imgUrl = '';
    try {
      const { protocol, host } = window.location;
      const imgUrlArr = url.split('//')[1].split('/');
      imgUrlArr[0] = host;
      const newUrl = `${protocol}//${imgUrlArr.join('/')}`;
      imgUrl = newUrl;
    } catch (error) {
      console.log('域名解析有误');
      imgUrl = url;
    }

    const el = window.document.getElementById('wgt_print_img');
    el.src = imgUrl;
    window.print();
  };

  changeUrl(item) {
    if (!item.url) {
      return false;
    }

    const { protocol, host, port } = window.location;
    if (protocol == 'https:') {
      return item.url;
    }

    if (protocol == 'http:') {
      try {
        const imgUrl = item.url;
        const imgUrlArr = imgUrl.split('//')[1].split('/');
        imgUrlArr[0] = host;
        const newUrl = `${window.location.protocol}//${imgUrlArr.join('/')}`;
        return newUrl;
      } catch (error) {
        console.log('域名解析有误');
        return item.url;
      }
    }
  }

  render() {
    const {
      previewVisible,
      previewImage,
      modalInspaceList,
      reportTypes,
      reportTypeItemList = [],
      modalItemErrKey,
      preZoom,
      preRotate,
      preLight,
      sourceImage,
      sourceVisible,
      showImgOperTool,
      reportlist0 = [],
      sortStatus,
      createTime,
      handlerAccount,
      handlerName,
      updateTime,
      protocol,
    } = this.state;
    console.log(reportlist0, 'reportlist0');
    const {
      outHospReportList = [],
      typeSex,
      form: { getFieldDecorator },
      grid,
      chatInfo: { pid },
    } = this.props;
    const uploadButton = (
      <div>
        <Icon type="plus" />
        <div className="ant-upload-text">Upload</div>
      </div>
    );

    const abnormalList = outHospReportList.filter(item => {
      return item.result == 0;
    });

    const columns = [
      {
        title: '项目名称',
        dataIndex: 'inspName',
        key: 'inspName',
      },
      {
        title: '结果',
        dataIndex: 'result',
        key: 'result',
        render: record => <span>{record == 0 ? '异常' : ''}</span>,
      },
      {
        title: '异常情况',
        dataIndex: 'resultVal',
        key: 'resultVal',
        render: (text, record) => {
          const abnormal = {
            1: '阴性',
            2: '阳性',
            3: '偏高',
            4: '偏低',
          };
          const ret = abnormal[text] || '';
          return <span style={{ color: record.result == 0 ? 'red' : '' }}>{ret}</span>;
        },
      },
      {
        title: '备注',
        dataIndex: 'content',
        key: 'content',
        className: styles.tabColumMax,
      },
      {
        title: '报告时间',
        dataIndex: 'checkTime',
        key: 'checkTime',
      },
      {
        title: '操作',
        render: record => {
          return (
            <span className={styles.tableLineOper} onClick={() => this.queryReportById(record)}>
              查看原图
            </span>
          );
        },
      },
    ];

    return (
      <div className={styles.outExamination}>
        <div>
          <div style={{ marginTop: 10 }}>
            <span>待分类</span>
            <Upload action="/api/customize/uploadFile?_route=h242" showUploadList={false} data={{ hisId: '242', grid, pid }} name="upfile" onChange={this.upFileChange}>
              <Button className={styles.btn}>
                <Icon type="upload" /> 添加新报告
              </Button>
            </Upload>
          </div>
          {reportlist0.length > 0 ? (
            <div className={styles.imglist}>
              <div
                className="out-examination"
                ref={r => {
                  this.imgScroll = r;
                }}
              >
                <Upload
                  listType="picture-card"
                  // fileList={reportlist0}
                  // onPreview={this.handlePreview}
                >
                  {/* {reportlist0.length >= 0 ? null : uploadButton} */}
                  {reportlist0.length >= 0
                    ? reportlist0.map((item, index) => {
                        return (
                          <div className={styles.uImage} onClick={e => this.handlePreview(e, item)}>
                            <img key={index} src={this.changeUrl(item)} style={{ width: '80px', height: '80px' }} />
                            {item.sortStatus == 2 && <CheckOutlined className={styles.icon} />}
                            <div>{item.createTime}</div>
                          </div>
                        );
                      })
                    : null}
                </Upload>
              </div>
              <Modal visible={previewVisible} width={document.body.clientWidth} footer={null} onCancel={this.handleCancel} className={styles.modal} destroyOnClose>
                <Form className={styles.modalBox}>
                  <div className={styles.imgBox}>
                    <img
                      alt="example"
                      className={styles.previewImg}
                      style={{ transform: `scale(${preZoom}) rotate(${preRotate}deg)`, filter: `brightness(${preLight})` }}
                      src={this.changeUrl({ url: previewImage })}
                    />
                    <Icon type="left" className={styles.left} onClick={() => this.previewNext(-1)} />
                    <Icon type="right" className={styles.right} onClick={() => this.previewNext(1)} />
                    <div className={styles.imgOper}>
                      <span className={styles.imgOperBox} style={{ opacity: showImgOperTool ? 1 : 0 }}>
                        <Icon type="zoom-in" className={styles.imgOperItem} onClick={() => this.zoomImg(1)} />
                        <Icon type="zoom-out" className={styles.imgOperItem} onClick={() => this.zoomImg(-1)} />
                        <Icon type="redo" className={styles.imgOperItem} onClick={this.rotateImg} />
                        <Icon type="printer" className={styles.imgOperItem} onClick={() => this.printImg(previewImage)} />
                        <Icon type="eye" className={styles.imgOperItem} onClick={() => this.lightImg(1)} />
                        <Icon type="eye-invisible" className={styles.imgOperItem} onClick={() => this.lightImg(-1)} />
                      </span>
                      <div className={styles.switchTool} onClick={() => this.setState({ showImgOperTool: !showImgOperTool })}>
                        {showImgOperTool ? '隐藏' : '展开'}
                      </div>
                    </div>
                  </div>
                  <div className={styles.preInfo}>
                    <div className={styles.Modaltitle}>报告分类信息</div>
                    <div className={styles.Modalflag}>图片上传时间：{createTime}</div>
                    <div>
                      <div className={styles.Modalflag}>标记性别</div>
                      <div className={styles.Modalradio}>
                        {getFieldDecorator('sex', {
                          initialValue: typeSex,
                          rules: [
                            {
                              required: true,
                              message: '请选择性别',
                            },
                          ],
                        })(
                          <Radio.Group onChange={this.onChange}>
                            <Radio.Button value="F">女方院外报告</Radio.Button>
                            <Radio.Button value="M">男方院外报告</Radio.Button>
                          </Radio.Group>,
                        )}
                      </div>
                      <div className={styles.Modalflag}>
                        <div>检查日期</div>
                        {getFieldDecorator('checkTime', {
                          rules: [
                            {
                              required: true,
                              message: '请选择检查日期',
                            },
                          ],
                        })(<DatePicker onChange={this.onDataChange} className={styles.date} format="YYYY-MM-DD" disabledDate={d => d.valueOf() > moment().valueOf()} />)}
                      </div>
                      <div style={{ maxHeight: 300, overflowY: 'auto' }}>
                        {modalInspaceList.map((item, idx) => {
                          return (
                            <Fragment key={`modalItem${item}`}>
                              <div className={styles.Modalflag}>
                                <div className={styles.record}>
                                  <div>检查检验项目记录</div>
                                  {modalInspaceList.length > 1 ? (
                                    <div className={styles.delete} onClick={() => this.removeModalInspaceItem(idx)}>
                                      删除
                                    </div>
                                  ) : null}
                                </div>
                                <div>
                                  {getFieldDecorator(`${item}modalItem1`, {
                                    rules: [
                                      {
                                        required: true,
                                        message: '请选择',
                                      },
                                    ],
                                  })(
                                    <Select placeholder="检查类型" style={{ width: 120, paddingRight: 10 }} onChange={val => this.getReportTypesItemList(val, item)} labelInValue>
                                      {reportTypes.map(insp => {
                                        return <Option key={insp.inspType}>{insp.inspTypeName}</Option>;
                                      })}
                                    </Select>,
                                  )}
                                  {getFieldDecorator(`${item}modalItem2`, {
                                    rules: [
                                      {
                                        required: true,
                                        message: '请选择',
                                      },
                                    ],
                                  })(
                                    <Select placeholder="检查项目" style={{ width: 120, paddingRight: 10 }} labelInValue>
                                      {reportTypeItemList.map((insp, key) => {
                                        if (!insp.inspCode) {
                                          return null;
                                        }
                                        return (
                                          <Option key={key} value={insp.inspCode}>
                                            {insp.inspName}
                                          </Option>
                                        );
                                      })}
                                    </Select>,
                                  )}
                                  {getFieldDecorator(`${item}modalItem3`, {
                                    rules: [
                                      {
                                        required: true,
                                        message: '请选择',
                                      },
                                    ],
                                  })(
                                    <Select placeholder="请选择" style={{ width: 120, paddingRight: 10 }}>
                                      <Option value="1">阴性</Option>
                                      <Option value="2">阳性</Option>
                                      <Option value="3">偏高</Option>
                                      <Option value="4">偏低</Option>
                                    </Select>,
                                  )}
                                  {getFieldDecorator(`${item}modalItem4`, {
                                    rules: [
                                      {
                                        required: true,
                                        message: '请选择',
                                      },
                                    ],
                                  })(
                                    <Select placeholder="请选择" style={{ width: 120, paddingRight: 10 }}>
                                      <Option value="1">正常</Option>
                                      <Option value="0">异常</Option>
                                    </Select>,
                                  )}
                                </div>
                              </div>
                              <div className={styles.info}>
                                {modalItemErrKey === item ? (
                                  <div className={styles.icon}>
                                    <Icon type="info-circle" /> 该条记录不完整，请补充
                                  </div>
                                ) : null}
                                {getFieldDecorator(`${item}modalItem5`)(<TextArea rows={4} maxLength={200} style={{ marginTop: 5 }} />)}
                              </div>
                            </Fragment>
                          );
                        })}
                      </div>

                      <div className={styles.info}>
                        <div className={styles.addBtn} onClick={this.addModalInspaceItem}>
                          添加新项目记录
                        </div>
                      </div>
                      <div className={styles.btncells}>
                        <div className={styles.handleSwitch} style={{ display: 'flex' }}>
                          <Switch checked={sortStatus == 2 ? true : false} checkedChildren="已处理" unCheckedChildren="未处理" onChange={this.handleSubmit} />
                          {sortStatus == 2 && (
                            <div style={{ paddingLeft: '10px' }}>
                              {handlerName} {updateTime}
                            </div>
                          )}
                        </div>
                        <div className={styles.deleteReport} onClick={() => this.deleteReportOutHospital()}>
                          无意义，删除报告
                        </div>
                        <div className={styles.sureBtn} onClick={() => this.handleSubmit('confirmClassification')}>
                          确认分类{' '}
                        </div>
                      </div>
                    </div>
                  </div>
                </Form>
              </Modal>
              <Icon type="left" className={styles.left} onClick={() => this.scrollImgList(-1)} />
              <Icon type="right" className={styles.right} onClick={() => this.scrollImgList(1)} />
            </div>
          ) : null}
          <div>
            <div className={styles.titleFlag}>异常指标</div>
            <Table columns={columns} rowKey="id" dataSource={abnormalList} pagination={false} />
          </div>
          <div>
            <div className={styles.titleFlag}>报告结果</div>
            <Table columns={columns} rowKey="id" dataSource={outHospReportList} pagination={false} />
          </div>
          <Modal visible={sourceVisible} width={document.body.clientWidth} footer={null} onCancel={this.handleSourceCancel} className={styles.modal} destroyOnClose>
            <Form className={styles.modalBox}>
              <div className={styles.sourceImgBox}>
                <img alt="example" className={styles.previewImg} style={{ transform: `scale(${preZoom}) rotate(${preRotate}deg)` }} src={sourceImage} />
                <div className={styles.imgOper}>
                  <span className={styles.imgOperBox}>
                    <Icon type="zoom-in" className={styles.imgOperItem} onClick={() => this.zoomImg(1)} />
                    <Icon type="zoom-out" className={styles.imgOperItem} onClick={() => this.zoomImg(-1)} />
                    <Icon type="redo" className={styles.imgOperItem} onClick={this.rotateImg} />
                    <Icon type="printer" className={styles.imgOperItem} onClick={() => this.printImg(sourceImage)} />
                  </span>
                </div>
              </div>
            </Form>
          </Modal>
        </div>
      </div>
    );
  }
}

export default Index;
