// @ts-ignore
import { fetch } from 'whatwg-fetch';
import { history } from 'umi';
import { message } from 'antd';
import queryString from 'query-string';
import * as Utils from './utils';
import { DOMAIN, REQUEST_PARAM } from '../config/constant/constant';

// 初始化loading队列
if (typeof window.HC_LOADING_QUEUE === 'undefined') {
  window.HC_LOADING_QUEUE = [];
}

/**
 * post请求封装函数
 * @param type
 * @param header
 * @param url
 * @param param
 * @param showLoading
 * @param showError
 * @returns {{}}
 */
async function request(type = 'GET', header = {}, url = '', param = {}, showLoading = true, showError = true) {
  const backurl = encodeURIComponent(window.location.href);
  if (showLoading) {
    // 全局加载中遮盖可以在这里添加
    if (window.HC_LOADING_QUEUE.length <= 0) {
      message.loading('加载中', 0);
    }
    window.HC_LOADING_QUEUE.push('');
  }

  let data = {};
  let response;
  try {
    response = await fetch(url, {
      method: type,
      body: header['Content-Type'] ? JSON.stringify(param) : Utils.jsonToQueryString(param),
      credentials: 'include',
      headers: {
        Accept: 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        ...header,
      },
    });
  } catch (e) {
    console.log('catch error in request', e);
    response = {
      status: -1,
    };
  }

  window.HC_LOADING_QUEUE.pop();
  if (window.HC_LOADING_QUEUE.length <= 0) {
    message.destroy();
  }

  if (response.status >= 200 && response.status < 300) {
    try {
      data = await response.json();
    } catch (e) {
      data = {
        code: -1,
        msg: '响应数据语法错误',
      };
    }
  } else {
    data = {
      code: -1,
      msg: `抱歉，请求失败，请检查网络后重试 (CODE:${response.status})`,
    };
  }
  if (data.code === 999) {
    // 添加登录失效的处理代码
    if (history.location.pathname !== '/login' && history.location.pathname !== '/download') {
      history.push({ pathname: '/login', search: queryString.stringify({ backurl }) });
    }
    // sleep等待
    // await Utils.sleep(10000);
  }
  if (data.code === 996) {
    history.push(`/resetPwd?resetCode=${data.data.resetCode}`);
  }

  if (showError && data.code !== 0 && data.code !== 999 && data.code !== 996 && data.code !== 992) {
    // 全局报错可以在这里处理
    message.error(data.msg, 3);
  }

  return data;
}

/**
 * post请求封装函数
 * @param url
 * @param param
 * @param showLoading
 * @param showError
 * @returns {{}}
 */
export async function post(url = '', param = {}, showLoading = true, showError = true) {
  url = `${DOMAIN}${url}`;
  param = {
    ...REQUEST_PARAM,
    ...(param.data || {}),
  };

  return request('POST', {}, url, param, showLoading, showError);
}

/**
 * post请求封装函数
 * @param url
 * @param param
 * @param showLoading
 * @param showError
 * @returns {{}}
 */
export async function postjson(url = '', param = {}, showLoading = true, showError = true) {
  url = `${DOMAIN}${url}`;
  param = {
    ...REQUEST_PARAM,
    ...(param.data || {}),
  };

  return request('POST', { 'Content-Type': 'application/json;charset=UTF-8' }, url, param, showLoading, showError);
}

/**
 * get请求封装函数
 * @param url
 * @param param
 * @param showLoading
 * @param showError
 * @returns {{}}
 */
export async function get(url = '', param = {}, showLoading = true, showError = true) {
  const queryStr = Utils.jsonToQueryString({
    ...REQUEST_PARAM,
    ...param,
  });

  url = `${DOMAIN}${url}${url.indexOf('?') >= 0 ? '&' : '?'}${queryStr}`;
  return request('GET', {}, url, {}, showLoading, showError);
}

/**
 * Requests a URL, returning a promise.
 *
 * @param  {string} url       The URL we want to request
 * @param  {object} [options] The options we want to pass to "fetch"
 * @return {object}           An object containing either "data" or "err"
 */
export async function upload(url, options) {
  message.loading('加载中', 0);
  const response = await fetch(`${DOMAIN}${url}`, {
    ...options,
    credentials: 'include',
  });
  message.destroy();
  let data = {
    code: -1,
  };
  if (response.status >= 200 && response.status < 300) {
    data = await response.json();
  } else {
    data = {
      code: -1,
      msg: `发生未知错误(code:${response.status})`,
    };
  }
  if (data.code === 999) {
    history.push('/login');
  }
  if (data.code === 996) {
    history.push(`/resetPwd?resetCode=${data.data.resetCode}`);
  }
  return { data };
}

export async function download(url, param) {
  const queryStr = Utils.jsonToQueryString(param.data || {});
  const response = await fetch(`${DOMAIN}${url}${url.indexOf('?') >= 0 ? '&' : '?'}${queryStr}`, {
    credentials: 'include',
    method: 'GET',
    headers: {
      Accept: 'application/json, text/javascript, */*; q=0.01',
    },
  });
  let data = {
    code: -1,
  };

  if (response.status >= 200 && response.status < 300) {
    const contentType = response.headers.get('content-type');
    if (contentType.indexOf('application/json') > 0) {
      data = await response.json();
    } else {
      const contentDisposition = decodeURIComponent(response.headers.get('content-disposition') || '').match(/filename=([^;]*)/);
      const filename = contentDisposition ? contentDisposition[1] : '';
      const a = document.createElement('a'); // eslint-disable-line
      response.blob().then(blob => {
        const $url = window.URL.createObjectURL(blob); // eslint-disable-line
        a.href = $url;
        a.download = filename || `${new Date().getTime()}`;
        a.click();
        window.URL.revokeObjectURL($url); // eslint-disable-line
      });
      data = {
        code: 0,
      };
    }
  } else {
    data = {
      code: -1,
      error: response.statusText,
    };
  }

  return { data };
}

//去掉merchant，调用无需授权接口
export async function noauthdownload(url, param) {
  const queryStr = Utils.jsonToQueryString(param.data || {});
  const response = await fetch(`${url}${url.indexOf('?') >= 0 ? '&' : '?'}${queryStr}`, {
    credentials: 'include',
    method: 'POST',
    headers: {
      Accept: 'application/json, text/javascript, */*; q=0.01',
    },
  });
  let data = {
    code: -1,
  };

  if (response.status >= 200 && response.status < 300) {
    const contentType = response.headers.get('content-type');
    if (contentType.indexOf('application/json') >= 0) {
      data = await response.json();
    } else {
      const contentDisposition = decodeURIComponent(response.headers.get('content-disposition') || '').match(/filename=([^;]*)/);
      const filename = contentDisposition ? contentDisposition[1] : '';
      const a = document.createElement('a'); // eslint-disable-line
      response.blob().then(blob => {
        const $url = window.URL.createObjectURL(blob); // eslint-disable-line
        a.href = $url;
        a.download = filename || `${new Date().getTime()}`;
        a.click();
        window.URL.revokeObjectURL($url); // eslint-disable-line
      });
      data = {
        code: 0,
      };
    }
  } else {
    data = {
      code: -1,
      msg: response.statusText,
    };
  }

  return { data };
}

export async function downloadReport(url, options) {
  const response = await fetch(`${DOMAIN}${url}`, {
    ...options,
    credentials: 'include',
    headers: {
      mode: 'no-cors',
      Accept: 'application/json, text/javascript, */*; q=0.01',
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
  let data = {
    code: -1,
  };
  if (response.status >= 200 && response.status < 300) {
    const contentType = response.headers.get('content-type');
    const contentDisposition = decodeURIComponent(response.headers.get('content-disposition'));
    if (contentType.indexOf('application/json') >= 0 && contentDisposition.indexOf('filename') == -1) {
      data = await response.json();
      data.code != 0 ? message.error(data.msg || '下载失败') : message.success(data.msg || '下载失败');
    } else {
      const filename = contentDisposition.match(/filename=([^;]*)/)[1];
      const a = document.createElement('a'); // eslint-disable-line
      response.blob().then(blob => {
        const $url = window.URL.createObjectURL(blob); // eslint-disable-line
        a.href = $url;
        a.download = filename || `${new Date().getTime()}`;
        a.click();
        window.URL.revokeObjectURL($url); // eslint-disable-line
      });
      data = {
        code: 0,
      };
    }
  } else {
    data = {
      code: -1,
      error: response.statusText,
    };
  }
  if (data.code === 999) {
    history.push('/login');
  }
  return { data };
}
