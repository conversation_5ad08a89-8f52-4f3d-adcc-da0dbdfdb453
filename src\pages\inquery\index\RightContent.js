import React, { Component, Fragment } from 'react';
import { Select, Tabs, Steps, Input, DatePicker, Modal, Button, message, Tooltip, Popconfirm } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';
import { connect } from 'dva';
import HealthRecord from './HealthRecord';
import Inspection from './Inspection';
import OutExamination from './OutExamination';

import * as Api from './api';

import styles from './right.less';

const { TabPane } = Tabs;
const { Step } = Steps;
const { TextArea } = Input;
@connect(({ user = {} }) => {
  return { currentUser: user.currentUser };
})
class Index extends Component {
  constructor(prop) {
    super(prop);

    this.contentWidth = document.body.clientWidth - 350 - 450;

    this.state = {
      manInfo: {},
      femaleInfo: {},
      isStar: false,
      changeDate: '',
      collectionFlag: false,
      validateStatus: '',
      userInfo: [],
      mGrid: '',
      fGrid: '',
      showAddBox: false,
      recContent: '',
      recordList: [],
      recordType: 'add',
      recordId: '',
      isShowDateModal: false, // 显示记录日期弹窗
      recordDate: '', // 弹窗中的日期了
      recordNo: '', // 记录ID
      recordContent: '', // 记录内容
      outHospReportListF: [],
      outHospReportListM: [],
      reportlistF0: [], // 女方未分类
      reportlistM0: [], // 男方已经分类
      timeAxis: [],
      QYInfo: {}, // 患者签约信息
      QYStatusAll: [], // 签约状态字典
      historyFullScreen: false,
      periodData: {},
      rangeVal: '',
      rangeId: '',
      isVipArr: { '0': '否', '1': '是' },
      currentLoginUser: {},
    };

    this.rangeArr = [
      { name: '仅自己可见', value: 3 },
      { name: '医院内可见', value: 1 },
      { name: '医联体内可见', value: 4 },
      { name: '全部可见', value: 5 },
    ];
  }

  componentDidMount() {
    this.queryMedicalRecords();
    this.queryUserInfo();
    this.queryTimeAxis();
    this.queryCurrent();
    this.queryReportOutHospital({ sortStatus: 0, sex: 'F' }); // 女方未分类
    this.queryReportOutHospital({ sortStatus: 0, sex: 'M' }); // 女方未分类
    this.queryReportOutHospitalDetail('M'); // 男方已经分类
    this.queryReportOutHospitalDetail('F'); // 女方已经分类

    // 查询患者签约状态
    this.queryQYStatusAll();
    this.queryQYStatusByPid();
    this.getPeriod();
  }

  queryCurrent = async () => {
    const { code, data = {} } = await Api.queryCurrent();
    if (code === 0) {
      console.log(data, 'datadatadatadatadatadata1111');
      this.setState({ currentLoginUser: data });
    }
  };

  getTruePhoneNum = async (item, key) => {
    const { currentUser } = this.props;
    if (!item.pid) {
      return false;
    }
    const { code, data = {} } = await Api.getTruePhoneNum({
      pid: item.pid,
      grid: item.grid,
      account: currentUser.account,
    });
    if (code == 0) {
      const { userInfo } = this.state;
      userInfo[key].phoneNumStr = data.phoneNum;
      this.setState({ userInfo });
    }
  };

  getPeriod = async () => {
    const { chatInfo } = this.props;
    if (!chatInfo.pid) {
      return false;
    }
    const { code, data = {} } = await Api.getPeriod({ pid: chatInfo.pid });
    if (code == 0) {
      this.setState({ periodData: data });
    }
  };

  queryQYStatusByPid = async () => {
    const { chatInfo } = this.props;
    if (!chatInfo.pid) {
      return false;
    }
    const { code, data = {} } = await Api.queryQYStatusByPid({ pid: chatInfo.pid });
    if (code == 0) {
      this.setState({ QYInfo: data });
    }
  };

  queryQYStatusAll = async () => {
    const { code, data = [] } = await Api.queryQYStatusAll({ type: 'consultation_status_type' });
    if (code == 0) {
      this.setState({ QYStatusAll: data });
    }
  };

  preUpdateQYStatusByPid = status => {
    Modal.confirm({
      title: '系统提示',
      content: '是否确认更新病友状态？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.updateQYStatusByPid(status);
      },
    });
  };

  callEvent = grid => {
    this.callPatient(grid);
  };

  callPatient = async grid => {
    const { userInfo } = this.state;
    userInfo.map(item => {
      if (item.grid === grid) {
        item.call = false;
      }
    });
    this.setState({ userInfo }, async () => {
      const { code, msg = '', data } = await Api.getCall({ grid });
      if (code !== 0) {
        message.error(msg, 2);
        return;
      }
      const { resultCode, resultMessage } = data;
      if (resultCode !== '0') {
        message.error(resultMessage, 2);
        return;
      }
      message.success('拨打成功！', 2);
      userInfo.map(item => {
        if (item.grid === grid) {
          item.countdown = setInterval(() => {
            item.second--;
            if (item.second === -1) {
              item.second = 30;
              item.call = true;
              this.setState({ userInfo });
              clearInterval(item.countdown);
            }
            this.setState({ userInfo });
          }, 1000);
        }
      });
    });
  };

  updateQYStatusByPid = async (status = '') => {
    const { chatInfo } = this.props;
    if (!chatInfo.pid) {
      return false;
    }
    const statusArr = status.split('___');
    const { code } = await Api.updateQYStatusByPid({
      pid: chatInfo.pid,
      patStatusName: statusArr[1],
      patStatusKey: statusArr[0],
    });
    if (code == 0) {
      const { QYInfo = {} } = this.state;
      this.setState({
        QYInfo: { ...QYInfo, patStatusKey: statusArr[0], patStatusName: statusArr[1] },
      });
    }
  };

  queryTimeAxis = async () => {
    const { chatInfo } = this.props;
    if (!chatInfo.pid) {
      return false;
    }
    const { code, data = [] } = await Api.queryTimeAxis({ pid: chatInfo.pid });
    if (code == 0) {
      this.setState({ timeAxis: data });
    }
  };

  queryUserInfo = async () => {
    const { chatInfo = {} } = this.props;
    if (chatInfo.pid) {
      const { code, data = {} } = await Api.queryUserInfo({ pid: chatInfo.pid });
      if (code == 0) {
        const { patientList = [] } = data;
        let mGrid = '';
        let fGrid = '';
        patientList.map(item => {
          if (item.patSex == '男') {
            mGrid = item.grid;
          }
          if (item.patSex == '女') {
            fGrid = item.grid;
          }
          item.call = true;
          item.second = 30;
        });
        this.setState({ userInfo: patientList, mGrid, fGrid });
      }
    }
  };

  queryReportOutHospital = async (param = {}) => {
    const { chatInfo = {} } = this.props;
    const { pid } = chatInfo;
    const { code, data = [] } = await Api.queryReportOutHospital({ ...param, pid });
    if (code == 0) {
      const fileList = data.map(item => {
        return {
          ...item,
          url: item.fileUrl,
          uid: item.reportId,
          name: `${item.reportId}.jpg`,
          status: 'done',
        };
      });
      this.setState({ [`reportlist${param.sex}${param.sortStatus}`]: fileList });
    }
  };

  queryReportOutHospNum = async () => {
    const { outHospReportNum = {} } = this.state;
    const { chatInfo } = this.props;
    const { pid, hisId } = chatInfo;
    if (hisId) {
      const { data: fSortNum = [] } = await Api.queryReportOutHospital({
        hisId,
        pid,
        sex: 'F',
        sortStatus: 1,
      }); // 获取女性已分类报告列表数量
      const { data: mSortNum = [] } = await Api.queryReportOutHospital({
        hisId,
        pid,
        sex: 'M',
        sortStatus: 1,
      }); // 获取男性已分类报告列表数量
      outHospReportNum.fSortNum = fSortNum.length ? fSortNum.length : 0;
      outHospReportNum.mSortNum = mSortNum.length ? mSortNum.length : 0;
      this.setState({ outHospReportNum });
    }
  };

  queryReportOutHospitalDetail = async sex => {
    const { chatInfo } = this.props;
    const { pid, hisId } = chatInfo;
    if (!pid) {
      return false;
    }
    const param = {
      hisId,
      pid,
      sex,
    };
    const { code, data = [] } = await Api.queryReportOutHospitalDetail(param);
    if (code === 0) {
      this.setState({ [`outHospReportList${sex}`]: data }); // 已分类院外报告列表
    }
  };

  queryMHealthRecord = async chatInfo => {
    const { pid, hisId } = chatInfo;
    const { data = [] } = await Api.queryHealthRecord({ hisId, pid, sex: 'M' });
    if (data.basicInfo) {
      if (data.questionStatus == '1') {
        this.queryDiseaseList({ hisId, type: 'M_disease_type' }, data.questionRel, data, 'M');
      }
      if (data.anamnesisRel) {
        this.queryDiseaseList({ hisId, type: 'anamnesis_record' }, data.anamnesisRel, data, 'M');
      }

      this.setState({ manInfo: data });
    }
    this.queryFHealthRecord(chatInfo);
  };

  queryFHealthRecord = async chatInfo => {
    const { pid, hisId } = chatInfo;
    const { data = [] } = await Api.queryHealthRecord({ hisId, pid, sex: 'F' });
    if (data.basicInfo) {
      if (data.questionStatus == '1') {
        this.queryDiseaseList({ hisId: data.hisId, type: 'F_disease_type' }, data.questionRel, data, 'F');
      }
      if (data.anamnesisRel) {
        this.queryDiseaseList({ hisId: data.hisId, type: 'anamnesis_record' }, data.anamnesisRel, data, 'F');
      }
      this.setState({ femaleInfo: data });
    }
  };

  queryDiseaseList = async (param, key, item = {}, type) => {
    const { data = [] } = await Api.queryDiseaseList(param);
    const dictValue = this.queryText(key, data);
    item[param.type] = dictValue && dictValue.join(',');
    if (type == 'F') {
      this.setState({ femaleInfo: item });
    } else {
      this.setState({ manInfo: item });
    }
  };

  queryMedicalRecords = async () => {
    const { chatInfo = {} } = this.props;
    const { pid, hisId } = chatInfo;
    if (pid) {
      const { code, data = [] } = await Api.queryMedicalRecords({ pid, hisId, type: '1' });
      if (code === 0) {
        const list = data.sort((a, b) => {
          return a.visitType === 3 && b.visitType !== 3 ? -1 : 1;
        });
        this.setState({ recordList: list });
      }
    }
  };

  editMedicalRecord = async () => {
    const { chatInfo = {} } = this.props;
    const { pid, hisId } = chatInfo;
    const { recContent, changeDate = '', recordType = 'add', collectionFlag, recordId, rangeId } = this.state;
    // if (!changeDate) {
    //   message.error('请选择记录时间');
    //   return false;
    // }
    if (!recContent) {
      message.error('请输入记录内容');
      return false;
    }
    const param = {
      pid,
      hisId,
      type: '1',
      content: recContent,
      collectionFlag: collectionFlag ? 1 : 0, // 0不提醒
      collectionTime: changeDate,
      recordId: recordType === 'edit' ? recordId : '',
      type: rangeId,
    };
    if (recordType === 'add') {
      const { code } = await Api.addMedicalRecord(param);
      if (code === 0) {
        message.success('添加成功');
      }
    } else if (recordType === 'edit') {
      const { code } = await Api.updateMedicalRecord(param);
      if (code === 0) {
        message.success('修改成功');
      }
    }
    this.setState({ recContent: '', changeDate: '', showAddBox: false });
    this.queryMedicalRecords(); // 更新记录
    this.queryTimeAxis();
  };

  updateMedicalRecord = async () => {
    const { chatInfo = {} } = this.props;
    const { id, hisId } = chatInfo;
    const { recContent, changeDate, collectionFlag } = this.state;
    if (!changeDate) {
      message.error('请选择记录时间');
      return false;
    }
    if (!recContent) {
      message.error('请输入记录内容');
      return false;
    }
    if (id) {
      const param = {
        groupId: id,
        hisId,
        type: '1',
        content: recContent,
        collectionFlag: collectionFlag ? 1 : 0, // 不提醒
        collectionTime: changeDate,
      };
      const { code } = await Api.updateMedicalRecord(param);
      if (code === 0) {
        message.success('添加成功');
      }
    }
    this.setState({ recContent: '', changeDate: '', showAddBox: false });
    this.queryMedicalRecords(); // 更新记录
  };

  handleEditorChange = item => {
    const { cb } = this.props;
    cb(item);
  };

  changeTab = e => {
    const { chatInfo } = this.props;
    if (e === '1') {
      this.queryMedicalRecords(chatInfo);
    } else if (e === '2') {
      this.queryMHealthRecord(chatInfo);
    } else if (e === '5') {
      // this.queryReportOutHospitalDetail(chatInfo, e);
    } else if (e === '6') {
      // this.queryReportOutHospitalDetail(chatInfo, e);
    }
  };

  // 弹出日期选择框，可以修改日期。
  showDateModal = item => {
    this.setState({
      isShowDateModal: true,
      recordDate: item.collectionTime,
      recordNo: item.recordId,
      recordContent: item.content,
    });
  };

  sureStar = () => {
    const { isStar, changeDate, collectionFlag } = this.state;
    // if (!isStar && !changeDate){
    //   this.setState({ validateStatus: "error",});
    //   return;
    // }
    this.setState({ isStar: !isStar, collectionFlag: !collectionFlag });
  };

  sureDate = (value, dateString) => {
    this.setState({ changeDate: dateString, validateStatus: 'success' });
    if (dateString) {
      this.setState({ isStar: true, collectionFlag: true });
    } else {
      this.setState({ isStar: false, collectionFlag: false });
    }
  };

  changeRecordDate = (date, dateString) => {
    this.setState({ recordDate: dateString });
  };

  selfTip = (currentAccount, { account, type }) => {
    if (currentAccount == account) {
      if (type == '3') {
        return <span style={{ paddingLeft: '20px' }}>仅自己可见</span>;
      }
      if (type == '1') {
        return <span style={{ paddingLeft: '20px' }}>医院内可见</span>;
      }
      if (type == '4') {
        return <span style={{ paddingLeft: '20px' }}>医联体内可见</span>;
      }
      if (type == '5') {
        return <span style={{ paddingLeft: '20px' }}>全部可见</span>;
      }
    }
  };

  // 提交记录修改内容
  submitRecordDate = async () => {
    const { chatInfo = {} } = this.props;
    const { pid, hisId } = chatInfo;
    const { recordDate = '', recordNo = '', recordContent = '' } = this.state;
    const param = {
      pid,
      hisId,
      type: '1',
      content: recordContent,
      collectionFlag: 1,
      collectionTime: recordDate,
      recordId: recordNo,
    };
    const { code } = await Api.updateMedicalRecord(param);
    if (code === 0) {
      message.success('添加成功');
      this.setState({ isShowDateModal: false, recordDate: '', recordNo: '', recordContent: '' });
      this.queryMedicalRecords(); // 更新记录
    }
  };

  // 取消记录提醒
  changeCollectionFlag = async item => {
    const { chatInfo = {} } = this.props;
    const { pid, hisId } = chatInfo;
    const param = {
      pid,
      hisId,
      type: '1',
      content: item.content,
      collectionFlag: 0,
      collectionTime: item.collectionTime,
      recordId: item.recordId,
    };
    const { code } = await Api.updateMedicalRecord(param);
    if (code === 0) {
      message.success('取消成功');
      this.queryMedicalRecords(); // 更新记录
    }
  };

  queryText = (key = '', arr = []) => {
    const idArr = key.split(',');
    let result = idArr.map(id => {
      const findItem = arr.find(v => v.id == id);
      return findItem ? findItem.dictValue : null;
    });
    result = result.filter(s => s);
    return result;
  };

  setRef = (ref, time, key = '') => {
    const refStr = time.match(/\d{4}-\d{2}-\d{2}/)[0];
    const { recordList = [] } = this.state;
    let refKey = key;
    if (key > 0) {
      const preObj = recordList[key - 1];
      const preRefStr = preObj.createTime.match(/\d{4}-\d{2}-\d{2}/)[0];
      if (preRefStr !== refStr) {
        refKey = 0;
      }
    }
    this[`${refStr}${refKey}`] = ref;
  };

  scrollRecord = time => {
    if (!this[`${time}0`]) {
      return false;
    }
    this.refRecordScroll.scrollTop = this[`${time}0`].offsetTop - 6;
  };

  selectRange = value => {
    const statusArr = value.split('___');
    this.setState({ rangeVal: statusArr[0], rangeId: statusArr[1] });
  };

  deleteRecordModal = async item => {
    const { currentLoginUser } = this.state;
    console.log(item, 'itemitem');
    if (item.account !== currentLoginUser.account) {
      message.error('当前记录不属于当前用户添加');
      return;
    }
    const { code, data = {} } = await Api.DeleteMedicalRecord({ recordId: item.recordId });
    if (code === 0) {
      message.success('删除成功');
      //更新记录
      setTimeout(() => {
        this.queryMedicalRecords();
      }, 1500);
    } else {
      message.success('删除失败');
    }
  };

  cancleDeleteRecord = () => {};

  render() {
    const {
      manInfo,
      femaleInfo,
      isStar,
      rangeVal,
      validateStatus,
      userInfo = [],
      showAddBox,
      changeDate,
      isShowDateModal,
      recordDate,
      recContent,
      recordList,
      QYStatusAll,
      QYInfo,
      historyFullScreen,
      periodData,
      outHospReportListM = [],
      mGrid,
      fGrid,
      outHospReportListF = [],
      reportlistF0 = [],
      reportlistM0 = [],
      timeAxis = [],
      isVipArr = {},
    } = this.state;
    const {
      chatInfo = {},
      currentUser: { account },
    } = this.props;
    // console.log(chatInfo, 'sdsdasdasdasdasdsa');
    return (
      <div className={styles.rightContent} style={{ width: this.contentWidth }}>
        <div className={styles.groupUserHeadInfo} style={{ display: historyFullScreen ? 'none' : 'block' }}>
          <div className={styles.baseInfo}>
            {/* <div className={styles.title}>病友基本信息</div> */}
            <div className={styles.pid} style={{ color: PRIMARY_COLOR }}>
              PID:{chatInfo.pid}
            </div>
            <div className={`${styles.pid} ${styles.leftBorder}`} style={{ marginLeft: 15 }}>
              病友状态：
            </div>
            <Select
              size="small"
              style={{ width: 200 }}
              className={styles.selects}
              placeholder="请选择"
              onChange={this.preUpdateQYStatusByPid}
              value={QYInfo.patStatusKey ? `${QYInfo.patStatusKey}___${QYInfo.patStatusName}` : undefined}
            >
              {QYStatusAll.map(item => {
                return <Select.Option key={`${item.dictKey}___${item.dictValue}`}>{item.dictValue}</Select.Option>;
              })}
            </Select>
            <div className={`${styles.pid} ${styles.leftBorder}`} style={{ marginLeft: 15 }}>
              {QYInfo.periodName ? `阶段状态：${QYInfo.periodName}` : ''}
              {periodData.xpCycle || periodData.dpCycle ? (
                <Fragment>
                  &nbsp;&nbsp;|&nbsp;&nbsp;
                  {`${periodData.xpCycle || ''}-${periodData.dpCycle || ''}`}
                </Fragment>
              ) : null}
            </div>
            {periodData.doctorName && (periodData.xpCycle || '').length ? (
              <div className={`${styles.pid} ${styles.leftBorder}`} style={{ marginLeft: 15 }}>
                主管医生：{periodData.doctorName}
              </div>
            ) : null}
            {periodData.transDate && (periodData.xpCycle || '').length ? (
              <div className={`${styles.pid} ${styles.leftBorder}`} style={{ marginLeft: 15 }}>
                移植日期：{periodData.transDate}
              </div>
            ) : null}
            {periodData.isvip ? (
              <div className={`${styles.pid} ${styles.leftBorder}`} style={{ marginLeft: 15 }}>
                VIP：{isVipArr[periodData.isvip]}
              </div>
            ) : null}
          </div>
          <div className={styles.famInfo}>
            {['JT01', 'JT02'].map(rel => {
              return userInfo.map((item, key) => {
                if (rel === item.familyMemberCode) {
                  return (
                    <div className={styles.infoLine} key={item.familyMemberCode}>
                      <div className={`${styles.gutterItem} ${item.patSex === '女' ? styles.sexG : styles.sexB}`} style={{ minWidth: 160 }}>
                        <div className={styles.name}>{item.familyMemberName}：</div>
                        <div className={styles.value}>{item.patName}</div>
                      </div>
                      <div className={styles.gutterItem} style={{ minWidth: 75 }}>
                        <div className={styles.name}>年龄：</div>
                        <div className={styles.value}>{item.patAge}岁</div>
                      </div>
                      <div className={styles.gutterItem} style={{ minWidth: 200 }}>
                        <div className={styles.name}>证件号码：</div>
                        <div className={styles.value}>{item.idNo}</div>
                      </div>
                      <div className={styles.gutterItem} style={{ minWidth: 150 }}>
                        <div className={styles.name}>联系方式：</div>
                        {item.phoneNumStr ? (
                          <Tooltip title={item.phoneNumStr || '未知'} placement="right">
                            <div className={styles.value}>{item.telephone}</div>
                          </Tooltip>
                        ) : (
                          <div className={styles.value} onClick={() => this.getTruePhoneNum(item, key)}>
                            {item.telephone}
                          </div>
                        )}
                      </div>
                      {item.call ? (
                        <Tooltip title="拨打电话">
                          <div className={styles.iconCall} onClick={() => this.callEvent(item.grid)} />
                        </Tooltip>
                      ) : (
                        <div className={styles.iconUnCall} />
                      )}
                      {item.second !== 0 && !item.call && <div>{item.second}s</div>}
                    </div>
                  );
                }
                return null;
              });
            })}
          </div>
        </div>
        <div className={styles.contentCard}>
          <Tabs defaultActiveKey="1" onChange={this.changeTab} className={styles.tabsContent}>
            <TabPane tab="病史概括" key="1" className={styles.bsgkBox}>
              <div className={styles.tabIntro}>
                {historyFullScreen ? null : (
                  <Fragment>
                    <div className={styles.timeline}>就诊时间线</div>
                    <div className={styles.tabStep}>
                      {timeAxis.length ? (
                        <Fragment>
                          <div className={styles.timeDateLine}>
                            {timeAxis.map(item => {
                              return (
                                <div className={styles.timeDate} key={item.axisDate}>
                                  {(item.axisDate || '').replace(/\d{4}-/, '')}
                                </div>
                              );
                            })}
                          </div>
                          <Steps labelPlacement="vertical" current={timeAxis.length} className={styles.timeStep}>
                            {timeAxis.map(item => {
                              return (
                                <Step
                                  icon={<div className={styles[`statusIcon${item.visitType}`]} />}
                                  title={item.visitType == 1 ? '初诊' : item.visitType == 2 ? '复诊' : '备注'}
                                  // description={item.axisDate}
                                  key={item.axisDate}
                                  onClick={() => this.scrollRecord(item.axisDate)}
                                />
                              );
                            })}
                          </Steps>
                        </Fragment>
                      ) : (
                        <span style={{ marginLeft: 20 }}>暂无数据</span>
                      )}
                    </div>
                  </Fragment>
                )}
                <div>
                  <div className={styles.recordlist}>
                    <div className={styles.listLeft}>
                      <div className={styles.title}>就诊内容</div>
                      <Button
                        ghost
                        type="primary"
                        onClick={() => {
                          this.setState({
                            recContent: '',
                            changeDate: '',
                            showAddBox: true,
                            recordType: 'add',
                            recordId: '',
                            validateStatus: '',
                            isStar: false,
                            collectionFlag: false,
                            rangeVal: '',
                          });
                        }}
                      >
                        <Icon type="plus" />
                        添加新纪录
                      </Button>
                    </div>
                    <div className={styles.listRight} onClick={() => this.setState({ historyFullScreen: !historyFullScreen })}>
                      <span>{historyFullScreen ? '收起' : '展开'}</span>
                      <Icon type={historyFullScreen ? 'up' : 'down'} />
                    </div>
                  </div>
                  <div className={`${styles.recordBox} ${showAddBox ? '' : styles.hiddenRecord}`} key={changeDate}>
                    <div className={styles.timeBox}>
                      <div className={styles.time}>
                        <Form>
                          <Form.Item hasFeedback validateStatus={validateStatus} className={styles.formitem}>
                            <DatePicker showTime placeholder="请选择" onChange={this.sureDate} className={styles.datepicker} format="YYYY-MM-DD" value={changeDate ? moment(changeDate) : null} />
                          </Form.Item>
                        </Form>
                      </div>
                      <Select size="small" style={{ width: 200 }} className={styles.selects} placeholder="请选择" onChange={this.selectRange} value={rangeVal || undefined}>
                        {this.rangeArr.map(item => {
                          return <Select.Option key={`${item.name}___${item.value}`}>{item.name}</Select.Option>;
                        })}
                      </Select>
                      <Icon type="star" theme={isStar ? 'filled' : 'outlined'} className={`${styles.star} ${isStar ? styles.onstar : ''}`} onClick={this.sureStar} />
                    </div>
                    <div className={styles.listinfo}>
                      <TextArea
                        value={recContent}
                        rows={4}
                        placeholder="请输入新的记录"
                        onChange={e => {
                          this.setState({ recContent: e.target.value });
                        }}
                      />
                      <div className={styles.btnBox}>
                        <Button type="primary" onClick={this.editMedicalRecord}>
                          保存
                        </Button>
                        <Button
                          type="default"
                          onClick={() => {
                            this.setState({ recContent: '', changeDate: '', showAddBox: false });
                          }}
                        >
                          取消
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                <Modal title="修改日期" visible={isShowDateModal} onOk={this.submitRecordDate} onCancel={() => this.setState({ isShowDateModal: false })} width={330}>
                  <DatePicker placeholder="请选择" format="YYYY-MM-DD" value={recordDate ? moment(recordDate) : null} onChange={this.changeRecordDate} style={{ width: '100%' }} />
                </Modal>
                <div className={styles.recordTop}>
                  <div
                    className={styles.recordScroll}
                    ref={ref => {
                      this.refRecordScroll = ref;
                    }}
                  >
                    {recordList.length > 0 ? (
                      recordList.map((item, key) => {
                        return (
                          <div className={styles.recordBox} key={key} ref={ref => this.setRef(ref, item.createTime, key)}>
                            <div className={styles.timeBox}>
                              <div className={styles.time}>
                                <div>
                                  <span className={`${styles.cicleLabel} ${styles[`label${item.visitType}`]}`} />
                                  {item.createTime}
                                  <span className={styles.msgCreator}>{item.accountName}</span>
                                  {item.recordId ? (
                                    <a
                                      className={styles.edit}
                                      onClick={() => {
                                        this.setState({
                                          recContent: item.content,
                                          changeDate: item.collectionTime,
                                          showAddBox: true,
                                          recordType: 'edit',
                                          recordId: item.recordId,
                                          validateStatus: '',
                                          isStar: item.collectionFlag == 1,
                                          collectionFlag: item.collectionFlag == 1,
                                        });
                                      }}
                                    >
                                      <Icon type="edit" />
                                    </a>
                                  ) : null}
                                  {this.selfTip(account, item)}
                                  <Popconfirm
                                    title="温馨提示：确定删除该条备注内容吗？"
                                    onConfirm={() => this.deleteRecordModal(item)}
                                    onCancel={() => this.cancleDeleteRecord()}
                                    okText="确定"
                                    cancelText="取消"
                                  >
                                    <span style={{ marginLeft: '20px', color: 'red', cursor: 'pointer' }}>删除</span>
                                  </Popconfirm>
                                </div>
                              </div>
                              <div>
                                {item.visitType == 3 && item.collectionFlag == 1 ? (
                                  <Fragment>
                                    <span>{item.collectionTime}&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                    <Icon type="star" theme="filled" className={`${styles.star} ${styles.onstar}`} onClick={() => this.changeCollectionFlag(item)} />
                                  </Fragment>
                                ) : (
                                  <Icon type="star" theme="outlined" className={`${styles.star} ${styles.onstar}`} onClick={() => this.showDateModal(item)} />
                                )}
                              </div>
                            </div>
                            <div className={styles.listinfo}>
                              <div className={styles.item} dangerouslySetInnerHTML={{ __html: item.content }} />
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className={styles.recordBox}>
                        <div className={styles.listinfo}>
                          <div className={styles.item}>暂无您的就诊记录</div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabPane>
            <TabPane tab="健康档案" key="2">
              <HealthRecord manInfo={manInfo} femaleInfo={femaleInfo} />
            </TabPane>
            <TabPane tab="女方院内检查" key="3">
              {Object.keys(userInfo).length ? (
                <Inspection
                  chatInfo={chatInfo}
                  userInfo={userInfo}
                  familyMemberCode="JT01" // "JT01"女方 "JT02" 男方  "JT03" 女方母亲
                />
              ) : null}
            </TabPane>
            <TabPane tab="男方院内检查" key="4">
              {Object.keys(userInfo).length ? (
                <Inspection
                  chatInfo={chatInfo}
                  userInfo={userInfo}
                  familyMemberCode="JT02" // "JT01"女方 "JT02" 男方  "JT03" 女方母亲
                />
              ) : null}
            </TabPane>
            <TabPane tab={`女方院外检查(${reportlistF0.length ? reportlistF0[0].totalNum : 0})`} key="5">
              <OutExamination
                chatInfo={chatInfo}
                grid={fGrid}
                reportlist0={reportlistF0}
                outHospReportList={outHospReportListF}
                queryReportOutHospital={this.queryReportOutHospital}
                typeSex="F"
                queryReportOutHospitalDetail={this.queryReportOutHospitalDetail}
              />
            </TabPane>
            <TabPane tab={`男方院外检查(${reportlistM0.length ? reportlistM0[0].totalNum : 0})`} key="6">
              <OutExamination
                chatInfo={chatInfo}
                grid={mGrid}
                reportlist0={reportlistM0}
                outHospReportList={outHospReportListM}
                queryReportOutHospital={this.queryReportOutHospital}
                typeSex="M"
                queryReportOutHospitalDetail={this.queryReportOutHospitalDetail}
              />
            </TabPane>
          </Tabs>
        </div>
      </div>
    );
  }
}

export default Index;
