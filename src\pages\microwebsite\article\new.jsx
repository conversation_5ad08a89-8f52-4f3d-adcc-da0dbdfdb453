import React from 'react';
import { connect } from 'dva';

import IIHOCArticlePanel from './components/IIHOCArticlePanel';

export default connect(state => {
  return {
    typeList: state.microwebsite.article.typeList,
  };
})(
  class ArticleNew extends React.Component {
    componentDidMount() {
      const { dispatch, typeList } = this.props;
      dispatch({
        type: 'microwebsite/articleTypeList',
      });
    }

    render() {
      const { typeList } = this.props;
      const ArticleNewPanel = IIHOCArticlePanel({ type: 'new', detail: {}, typeList });
      return <ArticleNewPanel />;
    }
  },
);
