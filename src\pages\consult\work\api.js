import { post, postjson } from '@/utils/request';

export function getBusType(param) {
  return post('/api/consult/area/group/list', { data: param }) || {};
}

export function getUserList(param) {
  return post('/api/receive/getReceiveUserlist', { data: param }) || {};
}

export function getAllUser(param) {
  return post('/api/team/getAllUsersGroup', { data: param }) || {};
}

export function addReceiveUserGroup(param) {
  return postjson('/api/receive/addReceiveUserGroup', { data: param }) || {};
}
// 删除成员
export function deletePerson(param) {
  return post('/api/receive/deleteReceiveUser', { data: param }) || {};
}
export function switchStatus(param) {
  return post('/api/receive/changeReceiveUserStatus', { data: param }) || {};
}

export function getMaxReceiveNumber(param) {
  return post('/api/receive/queryMaxReceiveNumber', { data: param }) || {};
}

export function updateMaxReceiveNumber(param) {
  return post('/api/receive/updateMaxReceiveNumber', { data: param }) || {};
}

export function sortData(param) {
  return post('/api/receive/changeReceiveSort', { data: param }) || {};
}
// 修改某行的receiveNum
export function changeReceiveNumDefault(param) {
  return post('/api/receive/updateReceiveUserNum', { data: param }) || {};
}
// 添加医生
export function addDoctor(param) {
  return post('/api/receive/addDoctor', { data: param }) || {};
}
// 编辑护士
export function updateNurse(param) {
  return post('/api/receive/updateNurse', { data: param }) || {};
}
