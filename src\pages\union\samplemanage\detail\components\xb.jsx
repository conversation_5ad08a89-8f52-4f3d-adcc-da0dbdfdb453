/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select, DatePicker } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { xbtemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, xbtemplate: { ...xbtemplate, ...payload } },
      },
    });
  };

  const defaultBS = `生育史：孕（0）次，产（0）次，流产（0）次。
双方不良孕产详述（含孕周、是否做过遗传学检测、病理等）：
现存子女详述（含年龄、健康状况、遗传学检测等）：
有无腮腺炎：  手术情况：  自身免疫药：  药物服用史：  睾丸大小：
有无B超检测结果：`;

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="送检样本信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="病史（请根据实际情况填写）">
                <TextArea placeholder="请输入" autoSize defaultValue={xbtemplate.field1 || defaultBS} onChange={e => changeData({ field1: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="检测结果超过正常值的有">
                <Checkbox.Group value={xbtemplate.field2 ? xbtemplate.field2.split(',') : []} onChange={v => changeData({ field2: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={4}>
                      <Checkbox value="FSH">FSH</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="LH">LH</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="睾酮">睾酮</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="E2">E2</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="PRL">PRL</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="INHB">INHB</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="检测结果低于正常值的有">
                <Checkbox.Group value={xbtemplate.field3 ? xbtemplate.field3.split(',') : []} onChange={v => changeData({ field3: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={4}>
                      <Checkbox value="FSH">FSH</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="LH">LH</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="睾酮">睾酮</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="E2">E2</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="PRL">PRL</Checkbox>
                    </Col>
                    <Col span={4}>
                      <Checkbox value="INHB">INHB</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
