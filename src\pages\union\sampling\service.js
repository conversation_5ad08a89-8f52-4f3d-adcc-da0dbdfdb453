import { post } from '@/utils/request';

export const fetchList = (param = {}) => post('/api/sampling-package/select-page', { data: param });

export const fetchListPerson = (param = {}) => post('/api/sampling-package/select-page-person', { data: param });

export const fetchListTeam = (param = {}) => post('/api/sampling-package/select-page-team', { data: param });

export const findAllUser = (param = {}) => post('/api/userinfo/findAllUser', { data: param });

export const getProduct = (param = {}) => post('/api/product/get-products', { data: param });

export const addPackage = (param = {}) => post('/api/sampling-package/add-package', { data: param });

export const updatePackage = (param = {}) => post('/api/sampling-package/update-package', { data: param });

export const getPackageDetail = (param = {}) => post('/api/sampling-package/get-by-id', { data: param });

export const cancelPackage = (param = {}) => post('/api/sampling-package/cancel-package', { data: param });

export const grantPackage = (param = {}) => post('/api/sampling-package/grant-package', { data: param });

export const grantCheck = (param = {}) => post('/api/sampling-package/grant-check', { data: param }, true, false);

export const forceGrantPackage = (param = {}) => post('/api/sampling-package/force-grant-package', { data: param });

export const forceGrantCheck = (param = {}) => post('/api/sampling-package/force-grant-check', { data: param }, true, false);

export const getContract = (param = {}) => post('/api/contract/get-institution-contract', { data: param });

export const getInstitution = (param = {}) => post('/api/institution/get-by-list', { data: param });
