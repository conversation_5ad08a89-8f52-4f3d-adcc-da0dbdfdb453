import React from 'react';
import { connect } from 'dva';
import { Tabs, Table, Input, Tooltip, Popconfirm, Modal, Button } from 'antd';

import '../style.less';

import * as utils from '../../../utils/utils';

const TabPane = Tabs.TabPane;
const confirm = Modal.confirm;

export default connect(state => {
  return {
    hisList: state.microwebsite.hospital.list,
    ...state.microwebsite.coordinate,
    permissionData: state.root.permissionData,
  };
})(
  class EdificeCoordinate extends React.Component {
    componentDidMount() {
      const { dispatch } = this.props;
      dispatch({
        type: 'microwebsite/hospitalList',
        next: 'edificeList',
      });
    }

    componentWillUnmount() {
      const { dispatch } = this.props;
      clearTimeout(this.timeoutId);
      dispatch({
        type: 'microwebsite/saveCoordinate',
        payload: {
          edificeList: [],
          floorList: [],
          disabled: false,
        },
      });
    }

    edificeList = hisId => {
      const { dispatch } = this.props;
      dispatch({
        type: 'microwebsite/edificeList',
        payload: {
          hisId,
        },
      });
    };

    edificeClickHandler = (idx, edifice) => {
      const { dispatch, edificeList = [] } = this.props;
      edificeList.map(item => delete item.active);
      edificeList[idx].active = true;
      dispatch({
        type: 'microwebsite/saveCoordinate',
        payload: {
          edificeList: [...edificeList],
        },
      });
      dispatch({
        type: 'microwebsite/floorList',
        payload: {
          parentId: edifice.id,
          hisId: edifice.hisId,
        },
      });
    };

    newEdifice = hisId => {
      const { dispatch, edificeList = [] } = this.props;
      edificeList.forEach(item => delete item.active);
      dispatch({
        type: 'microwebsite/saveCoordinate',
        payload: {
          disabled: true,
          floorList: [],
          edificeList: [
            ...edificeList,
            {
              key: `new-edifice-${new Date().getTime()}`,
              hisId,
              active: true,
              editable: true,
              toolTipVisible: false,
              toolTip: '请输入楼栋信息',
            },
          ],
        },
      });
    };

    editEdifice = (idx, value) => {
      const { dispatch, edificeList = [] } = this.props;
      edificeList[idx].newBuildName = value;
      edificeList[idx].toolTipVisible = false;
      dispatch({
        type: 'microwebsite/saveCoordinate',
        payload: {
          edificeList: [...edificeList],
        },
      });
    };

    editEdificeDone = (idx, type, edifice = {}) => {
      const { dispatch, edificeList } = this.props;
      let toolTipVisible = false;
      let toolTip = '请输入楼栋信息';
      switch (type) {
        case 'save':
          if (!edifice.newBuildName || edifice.newBuildName == '') {
            toolTipVisible = true;
            toolTip = '请输入楼栋信息';
          } else if (!/^[\S]{1,20}$/.test(edifice.newBuildName)) {
            toolTipVisible = true;
            toolTip = '楼栋名称最多可输入20个字符';
          }
          if (toolTipVisible) {
            edificeList[idx].toolTipVisible = toolTipVisible;
            edificeList[idx].toolTip = toolTip;
            dispatch({
              type: 'microwebsite/saveCoordinate',
              payload: {
                edificeList: [...edificeList],
              },
            });
          } else {
            edificeList[idx] = {
              ...edifice,
              editable: false,
              buildName: edifice.newBuildName,
              buildDesc: edifice.newBuildDesc,
            };
            dispatch({
              type: 'microwebsite/saveCoordinate',
              payload: {
                disabled: false,
                edificeList: [...edificeList],
              },
            });
            dispatch({
              type: 'microwebsite/submitEdifice',
              next: 'edificeList',
              payload: {
                ...edificeList[idx],
              },
            });
          }
          break;
        case 'cancel':
          if (edifice.id) {
            edificeList[idx].editable = false;
          } else {
            edificeList.splice(idx, 1);
          }
          dispatch({
            type: 'microwebsite/saveCoordinate',
            payload: {
              disabled: false,
              edificeList: [...edificeList],
            },
          });
          break;
        case 'delete':
          edificeList.splice(idx, 1);
          dispatch({
            type: 'microwebsite/edificeDelete',
            payload: {
              ...edifice,
            },
          });
          dispatch({
            type: 'microwebsite/saveCoordinate',
            payload: {
              edificeList: [...edificeList],
            },
          });
          break;
        default:
          break;
      }
    };

    newFloor = hisId => {
      const { dispatch, floorList = [] } = this.props;
      dispatch({
        type: 'microwebsite/saveCoordinate',
        payload: {
          disabled: true,
          floorList: [
            ...floorList,
            {
              key: `new-floor-${new Date().getTime()}`,
              hisId,
              active: true,
              editable: true,
              toolTipVisible: false,
              toolTip: '请输入楼层信息',
            },
          ],
        },
      });
    };

    editFloor = (value, index, key) => {
      const { floorList, dispatch } = this.props;
      floorList[index][`new${key[0].toUpperCase()}${key.slice(1)}`] = value;
      floorList[index].toolTipVisible = false;
      dispatch({
        type: 'microwebsite/saveCoordinate',
        payload: {
          floorList: [...floorList],
        },
      });
    };

    editFloorDone = (idx, type, floor = {}) => {
      const { dispatch, floorList } = this.props;
      let toolTipVisible = false;
      let toolTip = '请输入楼层信息';
      switch (type) {
        case 'save':
          if (!floor.newBuildName || floor.newBuildName == '') {
            toolTipVisible = true;
            toolTip = '请输入楼层信息';
          } else if (!/^[\S]{1,20}$/.test(floor.newBuildName)) {
            toolTipVisible = true;
            toolTip = '楼层名称最多可输入20个字符';
          } else if (!floor.newBuildDesc || floor.newBuildDesc == '') {
            toolTipVisible = true;
            toolTip = '请输入科室信息';
          } else if (!/^[\S\s]{1,2000}$/.test(floor.newBuildDesc)) {
            toolTipVisible = true;
            toolTip = '科室信息最多可输入2000个字符';
          }
          if (toolTipVisible) {
            floorList[idx].toolTipVisible = toolTipVisible;
            floorList[idx].toolTip = toolTip;
            dispatch({
              type: 'microwebsite/saveCoordinate',
              payload: {
                floorList: [...floorList],
              },
            });
          } else {
            floorList[idx] = {
              ...floor,
              editable: false,
              buildName: floor.newBuildName,
              buildDesc: floor.newBuildDesc,
            };

            dispatch({
              type: 'microwebsite/submitFloor',
              payload: {
                ...floorList[idx],
              },
            });
            dispatch({
              type: 'microwebsite/saveCoordinate',
              payload: {
                disabled: false,
                floorList: [...floorList],
              },
            });
          }
          break;
        case 'cancel':
          if (floor.id) {
            floorList[idx].editable = false;
          } else {
            floorList.splice(idx, 1);
          }
          dispatch({
            type: 'microwebsite/saveCoordinate',
            payload: {
              disabled: false,
              floorList: [...floorList],
            },
          });
          break;
        case 'delete':
          floorList.splice(idx, 1);
          dispatch({
            type: 'microwebsite/floorDelete',
            payload: {
              ...floor,
            },
          });
          dispatch({
            type: 'microwebsite/saveCoordinate',
            payload: {
              floorList: [...floorList],
            },
          });
          break;
        default:
          break;
      }
    };

    renderFloorColumns = (floor, index, key, text) => {
      const { editable } = floor;
      const value = floor[key];
      if (!editable) {
        return text;
      }
      let toolTipVisible = false;
      let toolTip = '请输入楼层信息';
      if (key == 'buildName') {
        if (!floor.newBuildName || floor.newBuildName == '') {
          toolTipVisible = true && floor.toolTipVisible;
          toolTip = '请输入楼层信息';
        } else if (!/^[\S]{1,20}$/.test(floor.newBuildName)) {
          toolTipVisible = true && floor.toolTipVisible;
          toolTip = '楼层名称最多可输入20个字符';
        }
      } else if (key == 'buildDesc') {
        if (!floor.newBuildDesc || floor.newBuildDesc == '') {
          toolTipVisible = true && floor.toolTipVisible;
          toolTip = '请输入科室信息';
        } else if (!/^[\S\s]{1,2000}$/.test(floor.newBuildDesc)) {
          toolTipVisible = true && floor.toolTipVisible;
          toolTip = '科室信息最多可输入2000个字符';
        }
      }
      return (
        <div>
          {editable ? (
            <div>
              <Tooltip placement="topRight" title={toolTip} visible={toolTipVisible}>
                <Input.TextArea defaultValue={value} style={{ resize: 'none' }} autosize={{ minRows: 1, maxRows: 2 }} onChange={e => this.editFloor(e.target.value, index, key)} />
              </Tooltip>
            </div>
          ) : (
            <div className="editable-row-text">{value || ' '}</div>
          )}
        </div>
      );
    };

    render() {
      const {
        disabled,
        hisList = [],
        floorList = [],
        edificeList = [],
        location: { search = {} },
        dispatch,
        permissionData = {},
      } = this.props;
      const { btns = {} } = permissionData;
      const hisId = utils.queryStringToJson(search).hisId || (hisList[0] && hisList[0].hisId);

      const floorColumns = [
        {
          title: '楼层',
          dataIndex: 'buildName',
          width: '25%',
          render: (text, record, index) => this.renderFloorColumns(record, index, 'buildName', text),
        },
        {
          title: '科室',
          dataIndex: 'buildDesc',
          width: '55%',
          render: (text, record, index) => this.renderFloorColumns(record, index, 'buildDesc', text),
        },
        {
          title: '操作',
          render: (text, record, index) => {
            const { editable = false } = floorList[index];
            return (
              <div className="editable-row-operations">
                {editable ? (
                  <span>
                    <a
                      onClick={() => {
                        this.editFloorDone(index, 'save', record);
                      }}
                    >
                      保存
                    </a>
                    <Popconfirm
                      title="确定取消吗"
                      onConfirm={() => {
                        dispatch({
                          type: 'microwebsite/saveCoordinate',
                          payload: {
                            disabled: false,
                          },
                        });
                        this.editFloorDone(index, 'cancel', record);
                      }}
                    >
                      <a>取消</a>
                    </Popconfirm>
                  </span>
                ) : (
                  <span>
                    {btns['/microwebsite/deptCoordinate/editFloor'] ? (
                      <a
                        disabled={disabled}
                        onClick={() => {
                          floorList[index].editable = true;
                          floorList[index].toolTipVisible = false;
                          floorList[index].toolTip = '请输入楼层信息';
                          floorList[index].newBuildName = record.buildName;
                          floorList[index].newBuildDesc = record.buildDesc;
                          dispatch({
                            type: 'microwebsite/saveCoordinate',
                            payload: {
                              disabled: true,
                              floorList: [...floorList],
                            },
                          });
                        }}
                      >
                        编辑
                      </a>
                    ) : null}
                    {btns['/microwebsite/deptCoordinate/deleteFloor'] ? (
                      <a
                        disabled={disabled}
                        onClick={() => {
                          const $this = this;
                          confirm({
                            title: (
                              <div>
                                确定删除
                                <span style={{ color: '#f57f17' }}>{record.buildName}</span>
                                吗？
                              </div>
                            ),
                            content: '删除后无法再恢复，请确认是否删除该楼层。',
                            onOk() {
                              $this.editFloorDone(index, 'delete', record);
                            },
                          });
                        }}
                      >
                        删除
                      </a>
                    ) : null}
                  </span>
                )}
              </div>
            );
          },
        },
      ];

      return (
        <div className="page-coordinate">
          {hisList && hisList.length > 0 ? (
            <Tabs
              animated={false}
              defaultActiveKey={`${hisId}`}
              style={{ display: 'flex', flex: 'auto', flexDirection: 'column' }}
              onChange={id => {
                if (disabled) return;
                this.edificeList(id);
              }}
            >
              {hisList.map(item => {
                return (
                  <TabPane disabled={disabled} tab={item.hisName} key={`${item.hisId}`}>
                    <div className="coordinate-body">
                      <div className="list-edifice">
                        <div className="list-title">楼栋</div>
                        <div className="list-body">
                          <div className="list-items" ref={`edifice-list-${item.hisId}`}>
                            {edificeList &&
                              edificeList.length > 0 &&
                              edificeList.map((edifice, idx) => {
                                return (
                                  <div
                                    className={`list-item${edifice.active ? ' active' : disabled ? ' disabled' : ''}`}
                                    key={edifice.id || edifice.key}
                                    onClick={() => {
                                      if (edifice.editable || disabled) return;
                                      this.edificeClickHandler(idx, edifice);
                                    }}
                                  >
                                    {edifice.editable ? (
                                      <div style={{ height: '100%', display: 'flex', alignItems: 'center' }}>
                                        <div>
                                          <Tooltip placement="topRight" title={edifice.toolTip} visible={!!edifice.toolTipVisible}>
                                            <Input
                                              style={{ width: 175 }}
                                              defaultValue={edifice.buildName}
                                              onChange={e => {
                                                this.editEdifice(idx, e.target.value);
                                              }}
                                            />
                                          </Tooltip>
                                        </div>
                                        <div style={{ flex: 1, textAlign: 'right', paddingRight: 4, minWidth: 68 }}>
                                          <a onClick={() => this.editEdificeDone(idx, 'save', edifice)}>保存</a>
                                          <Popconfirm title="确定取消吗？" onConfirm={() => this.editEdificeDone(idx, 'cancel', edifice)}>
                                            <a>取消</a>
                                          </Popconfirm>
                                        </div>
                                      </div>
                                    ) : (
                                      <div style={{ height: '100%', display: 'flex', alignItems: 'center' }}>
                                        <div>{edifice.buildName}</div>
                                        <div style={{ flex: 1, textAlign: 'right', paddingRight: 4, minWidth: 68 }}>
                                          {btns['/microwebsite/deptCoordinate/editEdifice'] ? (
                                            <a
                                              disabled={disabled}
                                              onClick={e => {
                                                e.stopPropagation();
                                                edificeList.forEach(tmp => (tmp.active = false));
                                                edificeList[idx].active = true;
                                                edificeList[idx].editable = true;
                                                edificeList[idx].toolTipVisible = false;
                                                edificeList[idx].toolTip = '内容不能为空';
                                                edificeList[idx].newBuildName = edifice.buildName;
                                                edificeList[idx].newBuildDesc = edifice.buildDesc;
                                                dispatch({
                                                  type: 'microwebsite/saveCoordinate',
                                                  payload: {
                                                    disabled: true,
                                                    edificeList: [...edificeList],
                                                  },
                                                });
                                              }}
                                            >
                                              编辑
                                            </a>
                                          ) : null}
                                          {btns['/microwebsite/deptCoordinate/deleteEdifice'] ? (
                                            <a
                                              disabled={disabled}
                                              onClick={e => {
                                                e.stopPropagation();
                                                const $this = this;
                                                confirm({
                                                  title: (
                                                    <div>
                                                      确定删除
                                                      <span style={{ color: '#f57f17' }}>{edifice.buildName}</span>
                                                      吗？
                                                    </div>
                                                  ),
                                                  content: '删除后无法再恢复，请确认是否删除该楼栋。',
                                                  onOk() {
                                                    $this.editEdificeDone(idx, 'delete', edifice);
                                                  },
                                                });
                                              }}
                                            >
                                              删除
                                            </a>
                                          ) : null}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                );
                              })}
                          </div>
                          <div className="list-button">
                            {btns['/microwebsite/deptCoordinate/newEdifice'] ? (
                              <Button
                                onClick={() => {
                                  this.newEdifice(item.hisId);
                                  this.timeoutId = setTimeout(() => {
                                    const container = this.refs[`edifice-list-${item.hisId}`]; // eslint-disable-line
                                    container.scrollTop = container.scrollHeight;
                                  }, 100);
                                }}
                                disabled={disabled}
                              >
                                添加楼栋
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </div>
                      <div className="list-floor">
                        <div className="list-title">
                          {(() => {
                            if (edificeList && edificeList.length > 0) {
                              for (let i = 0; i < edificeList.length; i++) {
                                const tmp = edificeList[i];
                                if (tmp.active) {
                                  return tmp.buildName;
                                }
                              }
                            }
                            return '点击选择左侧楼栋';
                          })()}
                        </div>
                        <div className="list-body">
                          <div className="list-table" ref={`floor-list-${item.hisId}`}>
                            <Table dataSource={floorList} columns={floorColumns} rowKey={record => record.id || record.key} pagination={false} />
                          </div>
                          <div className="list-button">
                            {btns['/microwebsite/deptCoordinate/newFloor'] ? (
                              <Button
                                onClick={() => {
                                  this.timeoutId = setTimeout(() => {
                                    const container = this.refs[`floor-list-${item.hisId}`]; // eslint-disable-line
                                    container.scrollTop = container.scrollHeight;
                                  }, 100);
                                  this.newFloor(item.hisId);
                                }}
                                disabled={disabled || !edificeList || !edificeList.some(tmp => tmp.active)}
                              >
                                添加楼层
                              </Button>
                            ) : null}
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabPane>
                );
              })}
            </Tabs>
          ) : null}
        </div>
      );
    }
  },
);
