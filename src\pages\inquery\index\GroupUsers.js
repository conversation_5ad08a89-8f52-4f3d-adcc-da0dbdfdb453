import React, { Component } from 'react';
import { List, Avatar } from 'antd';

import styles from './groupoper.less';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {};
  }

  render() {
    const { users = [] } = this.props;
    return (
      <List className={styles.list}>
        {users.map((item, i) => {
          // eslint-disable-next-line no-nested-ternary
          const leftIcon = <Avatar className={styles.avatar} src={item.headImg} />;

          return (
            <List.Item key={item.key || i}>
              <List.Item.Meta
                className={styles.meta}
                avatar={leftIcon}
                title={
                  <div className={styles.title}>
                    {item.userName}
                    {/* <div className={styles.extra}>{item.extra}</div> */}
                  </div>
                }
                description={
                  <div>
                    <div className={styles.description} title={item.description}>
                      {item.account}
                    </div>
                    {/* <div className={styles.datetime}>{item.datetime}</div> */}
                  </div>
                }
              />
            </List.Item>
          );
        })}
      </List>
    );
  }
}

export default Index;
