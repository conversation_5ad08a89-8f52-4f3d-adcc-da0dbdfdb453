import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Link } from 'react-router-dom';
import { Table, Input, Button, Modal, Select, Popover } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { downloadImg } from '../../../utils/utils';
import * as Api from '../service';

import '../style.less';

const FormItem = Form.Item;
const confirm = Modal.confirm;

class AccountQueryForm extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hospitalArr: [],
      allUserData: [],
    };
  }
  componentDidMount() {
    const { refresh, dispatch } = this.props;
    if (refresh) {
      dispatch({
        type: 'system/accountList',
      });
    }
    this.getAllianceinfo();
    this.findAllUser();
  }

  findAllUser = async () => {
    const {
      data: { code, data: allUserData },
    } = await Api.findAllUser();
    if (code == 0) {
      this.setState({
        allUserData: allUserData || [],
      });
    }
  };

  getAllianceinfo = async () => {
    const {
      data: { code, data: allianceData },
    } = await Api.getAllianceinfo();
    if (code == 0) {
      this.setState({
        hospitalArr: allianceData || [],
      });
    }
  };

  render() {
    const { submit, initialValue } = this.props;
    const { getFieldDecorator, setFieldsValue } = this.props.form;
    const { hospitalArr = [], allUserData = [] } = this.state;
    return (
      <Form layout="inline">
        <FormItem label="所属客户" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          {getFieldDecorator('institutionId', { initialValue: '' })(
            <Select style={{ width: 200 }} placeholder="请选择客户" showSearch filterOption={(input, opt) => opt.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}>
              <Select.Option value={''}>{'全部'}</Select.Option>
              {hospitalArr.map(item => (
                <Select.Option value={item.id} key={item.id}>
                  {item.institutionName}
                </Select.Option>
              ))}
            </Select>,
          )}
        </FormItem>
        <FormItem label="关联销售" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          {getFieldDecorator('salesId', { initialValue: '' })(
            <Select style={{ width: 200 }} placeholder="请选择客户" showSearch filterOption={(input, opt) => opt.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}>
              <Select.Option value={''}>{'全部'}</Select.Option>
              {allUserData.map(item => (
                <Select.Option value={item.id} key={item.id}>
                  {item.name}
                </Select.Option>
              ))}
            </Select>,
          )}
        </FormItem>
        <FormItem>
          {getFieldDecorator('inputs', {
            initialValue,
          })(<Input placeholder="请输入姓名/手机号" />)}
        </FormItem>
        <FormItem>
          <Button
            type="primary"
            size="default"
            onClick={() => {
              submit();
            }}
          >
            查询
          </Button>
        </FormItem>
        <FormItem>
          <Button
            size="default"
            onClick={() => {
              setFieldsValue({ institutionId: '', inputs: '', salesId: '' });
              submit();
            }}
          >
            重置
          </Button>
        </FormItem>
      </Form>
    );
  }
}

class SystemAccountList extends React.Component {
  state = {
    qrAccount: '',
  };
  render() {
    const { qrAccount } = this.state;
    const {
      dispatch,
      form,
      account = {},
      loginUserInfo: { userId: loginUserId },
      permissionData = {},
    } = this.props;
    const { btns = {} } = permissionData;
    const { getFieldsValue, setFieldsValue } = form;
    const { recordList: list, inputs, currentPage } = account;

    const queryAccountList = (pageNum = 1) => {
      dispatch({
        type: 'system/accountList',
        payload: {
          ...getFieldsValue(),
          pageNum,
        },
      });
    };

    const deleteAccount = userId => {
      dispatch({
        type: 'system/deleteAccount',
        payload: {
          ...getFieldsValue(),
          userId,
          pageNum: currentPage,
        },
      });
    };

    const resetPassword = userId => {
      dispatch({
        type: 'system/resetPassword',
        payload: {
          userId,
        },
      }).then(res => {
        if (res) {
          Modal.success({
            title: (
              <div>
                重置成功，密码被重置为
                <span style={{ color: '#f57f17' }}>{res}</span>
              </div>
            ),
            content: '重置后将覆盖原来的密码，该用户需要重新登录。',
            onOk() {},
          });
        }
      });
    };

    const changeStatus = (userId, status) => {
      status = status == 1 ? 2 : 1;
      dispatch({
        type: 'system/changeStatus',
        payload: {
          userId,
          status,
        },
      }).then(res => {
        if (res) {
          setFieldsValue({ institutionId: '', inputs: '' });
        }
      });
    };
    const checkCT4 = async (account, allianceId, institutionId, name, institutionName) => {
      const {
        data: { code, data },
      } = await Api.checkCT4({ account, allianceId });
      if (code === 0 && data && data.flg == 1) {
        this.setState({ qrAccount: account });
      } else {
        downloadImg(
          `/api/qrcode/withlogo?msg=${encodeURIComponent(`https://${window.location.hostname}/scancode?acount=${account}&allianceId=${institutionId}`)}&qrtype=2`,
          {},
          `${name}-${institutionName || ''}.jpg`,
        );
      }
    };

    const AccountTable = {
      style: { marginBottom: 14 },
      columns: [
        {
          title: '姓名',
          dataIndex: 'name',
        },
        {
          title: '部门',
          dataIndex: 'deptName',
        },
        {
          title: '角色',
          dataIndex: 'identityName',
        },
        {
          title: '账号',
          dataIndex: 'account',
        },
        {
          title: '所属客户',
          dataIndex: 'institutionName',
        },
        {
          title: '关联销售',
          dataIndex: 'salesName',
        },
        {
          title: '手机',
          dataIndex: 'phone',
        },
        {
          title: '有效期',
          dataIndex: 'validateStart',
          render: (value, row) => {
            const obj = {
              children: `${value}至${row.validateEnd}`,
            };
            return obj;
          },
        },
        {
          title: '状态',
          dataIndex: 'statusDesc',
        },
        {
          title: '操作时间',
          dataIndex: 'updateTime',
        },
        {
          title: '操作',
          render: item => {
            if (item.userId == loginUserId) {
              return btns['/system/account/detail'] ? (
                <span>
                  <Link to={`/system/account/detail?userId=${loginUserId}`}>查看</Link>
                  <span style={{ color: '#4C9CDF' }}>丨</span>
                  <Popover
                    open={qrAccount === item.account}
                    destroyTooltipOnHide
                    placement="bottom"
                    overlayClassName="custom-ewm-popover"
                    content={
                      <div className="custom-ewm-popover-content" onMouseLeave={() => this.setState({ qrAccount: '' })}>
                        <div
                          onClick={() => {
                            downloadImg(
                              `/api/qrcode/withlogo?msg=${encodeURIComponent(
                                `https://${window.location.hostname}/scancode?acount=${item.account}&allianceId=${item.institutionId}&regType=1`,
                              )}&qrtype=2`,
                              {},
                              `${item.name}-${item.institutionName || ''}-转诊二维码.jpg`,
                            );
                          }}
                        >
                          转诊二维码
                        </div>
                        <div
                          onClick={() => {
                            downloadImg(
                              `/api/qrcode/withlogo?msg=${encodeURIComponent(`https://${window.location.hostname}/scancode?acount=${item.account}&allianceId=${item.institutionId}`)}&qrtype=2`,
                              {},
                              `${item.name}-${item.institutionName || ''}-开单二维码.jpg`,
                            );
                          }}
                        >
                          开单二维码
                        </div>
                      </div>
                    }
                  >
                    <a onClick={() => checkCT4(item.account, item.institutionId, item.institutionId, item.name, item.institutionName)}>开单二维码</a>
                  </Popover>
                </span>
              ) : null;
            } else {
              return (
                <span>
                  {btns['/system/account/modify'] ? <Link to={`/system/account/modify?userId=${item.userId}`}>修改</Link> : null}
                  <span style={{ color: '#4C9CDF' }}>丨</span>
                  {btns['/system/account/detail'] ? <Link to={`/system/account/detail?userId=${item.userId}`}>查看</Link> : null}
                  <span style={{ color: '#4C9CDF' }}>丨</span>
                  {/* <a
                    onClick={() => {
                      downloadImg(
                        `/api/qrcode/withlogo?msg=${encodeURIComponent(
                          `https://${window.location.hostname}/scancode?acount=${item.account}&allianceId=${item.institutionId}&regType=${item.account == '***********' ? 1 : ''}`,
                        )}&qrtype=2`,
                        {},
                        `${item.name}-${item.institutionName || ''}.jpg`,
                      );
                    }}
                  >
                    二维码1
                  </a> */}
                  <Popover
                    open={qrAccount === item.account}
                    destroyTooltipOnHide
                    placement="bottom"
                    overlayClassName="custom-ewm-popover"
                    content={
                      <div className="custom-ewm-popover-content" onMouseLeave={() => this.setState({ qrAccount: '' })}>
                        <div
                          onClick={() => {
                            downloadImg(
                              `/api/qrcode/withlogo?msg=${encodeURIComponent(
                                `https://${window.location.hostname}/scancode?acount=${item.account}&allianceId=${item.institutionId}&regType=1`,
                              )}&qrtype=2`,
                              {},
                              `${item.name}-${item.institutionName || ''}-转诊二维码.jpg`,
                            );
                          }}
                        >
                          转诊二维码
                        </div>
                        <div
                          onClick={() => {
                            downloadImg(
                              `/api/qrcode/withlogo?msg=${encodeURIComponent(`https://${window.location.hostname}/scancode?acount=${item.account}&allianceId=${item.institutionId}`)}&qrtype=2`,
                              {},
                              `${item.name}-${item.institutionName || ''}-开单二维码.jpg`,
                            );
                          }}
                        >
                          开单二维码
                        </div>
                      </div>
                    }
                  >
                    <a onClick={() => checkCT4(item.account, item.institutionId, item.institutionId, item.name, item.institutionName)}>开单二维码</a>
                  </Popover>
                  <span style={{ color: '#4C9CDF' }}>丨</span>
                  {btns['/system/account/delete'] ? (
                    <a
                      onClick={() => {
                        confirm({
                          title: (
                            <div>
                              确定删除用户
                              <span style={{ color: '#f57f17' }}>{item.name}</span>
                              吗？
                            </div>
                          ),
                          content: '删除后无法再恢复，请确认是否删除该用户。',
                          onOk() {
                            deleteAccount(item.userId);
                          },
                        });
                      }}
                    >
                      删除
                    </a>
                  ) : null}
                  <span style={{ color: '#4C9CDF' }}>丨</span>
                  {btns['/system/account/freeze'] && item.status == 1 ? (
                    <a
                      onClick={() => {
                        confirm({
                          title: (
                            <div>
                              确定冻结用户
                              <span style={{ color: '#f57f17' }}>{item.name}</span>
                              吗？
                            </div>
                          ),
                          content: '冻结后，该用户无法再使用本系统。',
                          onOk() {
                            changeStatus(item.userId, item.status);
                          },
                        });
                      }}
                    >
                      冻结
                    </a>
                  ) : btns['/system/account/thaw'] && item.status != 1 ? (
                    <a
                      onClick={() => {
                        changeStatus(item.userId, item.status);
                      }}
                    >
                      解冻
                    </a>
                  ) : null}
                  <span style={{ color: '#4C9CDF' }}>丨</span>
                  {btns['/system/account/resetPassword'] ? (
                    <a
                      onClick={() => {
                        confirm({
                          title: (
                            <div>
                              确定重置用户
                              <span style={{ color: '#f57f17' }}>{item.name}</span>
                              的密码吗？
                            </div>
                          ),
                          content: '重置后将覆盖原来的密码，该用户需要重新登录。',
                          onOk() {
                            resetPassword(item.userId);
                          },
                        });
                      }}
                    >
                      重置密码
                    </a>
                  ) : null}
                </span>
              );
            }
          },
        },
      ],
    };

    return (
      <div className="page-account-list">
        <div className="option-panel">
          <AccountQueryForm form={form} dispatch={dispatch} initialValue={''} refresh={true} submit={queryAccountList} />
        </div>
        <div className="account-list">
          <div style={{ paddingBottom: 8 }}>
            <Button
              type="primary"
              onClick={() => {
                history.push('/system/account/new');
              }}
            >
              创建账号
            </Button>
          </div>
          <Table
            style={AccountTable.style}
            columns={AccountTable.columns}
            dataSource={list}
            rowKey={item => item.id}
            pagination={{
              showQuickJumper: true,
              defaultCurrent: account.currentPage || 1,
              current: account.currentPage,
              total: account.totalCount,
              showTotal: () => {
                return `共${account.totalCount}条`;
              },
              onChange: queryAccountList,
            }}
          />
        </div>
      </div>
    );
  }
}

export default connect(state => {
  return {
    loginUserInfo: state.root.loginUserInfo || {},
    account: state.system.account,
    permissionData: state.root.permissionData,
  };
})(Form.create()(SystemAccountList));
