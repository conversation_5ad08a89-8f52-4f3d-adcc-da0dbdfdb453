/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect, history } from 'umi';
import { Form, Row, Col, Select, DatePicker, Input, Button, Table, Divider, Popconfirm, message } from 'antd';
import moment from 'moment';
import queryString from 'query-string';
import { formatMoney, filterObj, getDownload } from '@/utils/utils';
import '../index.less';

const { RangePicker } = DatePicker;

const Index = props => {
  const { dispatch, location, dateData = [] } = props;
  const { billDate = '' } = queryString.parse(location.search);

  const query = pageNum => {
    dispatch({
      type: 'bill/queryByDate',
      payload: { billDate },
    });
  };

  useEffect(() => {
    query(1);
  }, []);

  const dataExport = async record => {
    const url = '/merchant/api/bill/exportByDate';
    const paramStr = queryString.stringify({
      billDate,
    });
    getDownload(`${url}?${paramStr}`);
  };

  const columns = [
    {
      title: '交易时间',
      dataIndex: 'tradeTime',
      // fixed: 'left',
    },
    {
      title: '公众账号ID',
      dataIndex: 'appId',
      // fixed: 'left',
    },
    {
      title: '商户号',
      dataIndex: 'merchantId',
      // fixed: 'left',
    },
    {
      title: '微信订单号',
      dataIndex: 'wechatOrderId',
    },
    {
      title: '商户订单号',
      dataIndex: 'bizOrderId',
    },
    {
      title: '用户标识',
      dataIndex: 'userTag',
    },
    {
      title: '交易类型',
      dataIndex: 'type',
    },
    {
      title: '交易状态',
      dataIndex: 'status',
    },
    {
      title: '付款银行',
      dataIndex: 'payedBank',
    },
    {
      title: '货币种类',
      dataIndex: 'paymentCurrency',
    },
    {
      title: '应结订单金额',
      dataIndex: 'totalPayedFee',
      render: v => formatMoney(v),
    },
    {
      title: '代金券金额',
      dataIndex: 'discountFee',
      render: v => formatMoney(v),
    },
    {
      title: '微信退款单号',
      dataIndex: 'wechatRefundId',
    },
    {
      title: '商户退款单号',
      dataIndex: 'bizRefundId',
    },
    {
      title: '退款金额',
      dataIndex: 'refundFee',
      render: v => formatMoney(v),
    },
    {
      title: '充值券退款金额',
      dataIndex: 'refundDiscountFee',
      render: v => formatMoney(v),
    },
    {
      title: '退款类型',
      dataIndex: 'refundType',
    },
    {
      title: '退款状态',
      dataIndex: 'refundStatus',
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
    },
    {
      title: '商户数据包',
      dataIndex: 'merchantPackage',
    },
    {
      title: '手续费',
      dataIndex: 'serviceFee',
      render: v => formatMoney(v),
    },
    {
      title: '费率',
      dataIndex: 'rate',
    },
    {
      title: '订单金额',
      dataIndex: 'totalPayedFee',
      render: v => formatMoney(v),
      fixed: 'right',
    },
    {
      title: '申请退款金额',
      dataIndex: 'refundFee',
      render: v => formatMoney(v),
      fixed: 'right',
    },
  ];

  return (
    <div className="g-page">
      <div className="container">
        <Col span={24} style={{ marginBottom: '16px', textAlign: 'right' }}>
          <Button type="primary" onClick={dataExport}>
            下载明细
          </Button>
        </Col>
        <Table columns={columns} dataSource={dateData} rowKey="id" scroll={{ x: 'max-content' }} pagination={false} />
      </div>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.bill,
  };
})(Index);
