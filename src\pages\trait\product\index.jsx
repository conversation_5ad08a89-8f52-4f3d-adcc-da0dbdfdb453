/* eslint-disable react/jsx-no-duplicate-props */
import React, { useEffect, useMemo, useState } from 'react';
import { Form, Row, Col, Input, Select, Space, Button, Table, Modal, InputNumber, message, Radio, Popconfirm } from 'antd';
import { useAntdTable } from 'ahooks';
import { merge, values } from 'lodash';
import { formatMoney, filterObj, getDownload } from '@/utils/utils';
import queryString from 'query-string';
import { history, connect } from 'umi';

import * as Api from './service';
import { TYPE } from './_data';
import './style.less';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const Index = props => {
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const { permissionData = {}, menuType = '' } = props;
  const { btns = {} } = permissionData;
  const [userList, setUserList] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSub, setIsSub] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [id, setId] = useState('');
  const [productList, setProductList] = useState([]);
  const [queryParam, setQueryParam] = useState({});

  useEffect(() => {
    getAllUser();
    getProduct();
  }, []);

  const getAllUser = async () => {
    const { data } = await Api.findAllUser({ menuType });
    if (data.code === 0) {
      setUserList(data.data || []);
    }
  };

  const getProduct = async () => {
    const { data } = await Api.getProduct({ parentId: '', menuType });
    if (data.code === 0) {
      setProductList(data.data || []);
    }
  };

  const fetchList = async ({ current = 1, pageSize = 10 }) => {
    try {
      const values = form.getFieldsValue();
      const params = {
        menuType,
        pageNum: current,
        numPerPage: pageSize,
        ...(values || {}),
      };
      setQueryParam(params);

      const { data } = await Api.fetchList({ ...params });
      if (data.code !== 0) {
        message.error(data.msg);
        return;
      }
      return {
        total: Number(data.data.totalCount) || 0,
        list: data?.data?.recordList || [],
      };
    } catch (error) {
      console.log(error);
    }
  };

  const { loading, tableProps, search } = useAntdTable(fetchList, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
  });

  const tableRealProps = useMemo(
    () =>
      merge(tableProps, {
        pagination: {
          // showQuickJumper: true,
          // showSizeChanger: true,
          showTotal: total => `共 ${total} 条`,
        },
      }),
    [tableProps],
  );

  const { submit, reset } = search;

  const handleCancel = () => {
    setIsModalOpen(false);
    addForm.resetFields();
  };

  const handleDelete = async id => {
    const { data = {} } = await Api.deleteProduct({
      id,
    });
    if (data.code !== 0) {
      message.error(data.msg);
      return;
    }
    message.success(data.msg || '删除成功');
    getProduct();
    submit();
  };

  const onFinish = async values => {
    try {
      const { data = {} } = await Api[isEdit ? 'updateProduct' : 'addProduct']({
        ...values,
        // productPrice: values.productPrice * 100,
        ...(isEdit ? { id: id } : {}),
      });
      if (data.code !== 0) {
        message.error(data.msg);
        return;
      }
      message.success(data.msg);
      submit();
      setIsModalOpen(false);
      addForm.resetFields();
      getProduct();
    } catch (error) {
      console.log(error);
    }
  };

  const handleEdit = async id => {
    const { data = {} } = await Api.fetchDetail({
      id,
    });
    if (data.code !== 0) {
      message.error(data.msg);
      return;
    }

    addForm.setFieldsValue({ ...data?.data });
    setId(id);
    setIsSub(!!data?.data?.parentId);
    setIsEdit(true);
    setIsModalOpen(true);
  };

  const dataExport = async () => {
    const url = '/merchant/api/product/export';
    const paramStr = queryString.stringify({
      ...filterObj(queryParam),
    });

    getDownload(`${url}?${paramStr}`);
  };

  const columns = [
    {
      title: '产品线名称',
      dataIndex: 'parentProductName',
      fixed: 'left',
    },
    {
      title: '子产品线名称',
      dataIndex: 'productName',
      fixed: 'left',
    },
    // {
    //   title: '价格（元）',
    //   dataIndex: 'productPrice',
    //   // render: p => formatMoney(p),
    // },
    {
      title: '签约客户数量',
      dataIndex: 'extfiled1',
    },
    {
      title: '已检测数量',
      dataIndex: 'extfiled2',
    },
    {
      title: '同意书模板',
      dataIndex: 'templateTypeLabel',
    },
    {
      title: '产品状态',
      dataIndex: 'productStatus',
      render: status => (status == 1 ? '上架中' : '已下架 '),
    },
    {
      title: '录入人员',
      dataIndex: 'inputPersonName',
    },
    {
      title: '创建日期',
      dataIndex: 'createTime',
    },
    {
      title: '操作',
      // width: 180,
      dataIndex: 'id',
      fixed: 'right',
      render: (id, col) => (
        <div className="edit-btn">
          <span className={col.releaseFlag == 1 && col.introduction ? '' : 'disable'} onClick={() => history.push(`/trait/product/intro?id=${id}`)}>
            产品介绍
          </span>
          <span onClick={() => history.push(`/trait/product/detail?id=${id}&menuType=${menuType}`)}>签约详情</span>
          {btns['/trait/product/edit'] && <span onClick={() => handleEdit(id)}>编辑</span>}
          {btns['/trait/product/delete'] && (
            <Popconfirm title="确定要删除当前产品吗？" onConfirm={() => handleDelete(id)} okText="确认" cancelText="取消">
              <a href="#" style={{ color: '#FF4D4F' }}>
                删除
              </a>
            </Popconfirm>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="g-page p-product-list">
      <div className="g-query-box">
        <Form form={form} {...formItemLayout}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="parentId" label="产品线" initialValue="">
                <Select
                  fieldNames={{ label: 'productName', value: 'id' }}
                  options={[{ productName: '全部', id: '' }, ...productList]}
                  showSearch
                  filterOption={(input, option) => (option?.productName ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="productStatus" label="产品状态" initialValue="">
                <Select
                  options={[
                    { value: '', label: '全部' },
                    { value: '1', label: '上架中' },
                    { value: '2', label: '已下架' },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="templateType" label="同意书模板" initialValue="">
                <Select options={[{ value: '', label: '全部' }, ...TYPE]} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="inputPerson" label="录入人员" initialValue="">
                <Select
                  fieldNames={{ label: 'name', value: 'id' }}
                  options={[{ name: '全部', id: '' }, ...userList]}
                  showSearch
                  filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            {/* <Col span={8}>
              <Form.Item name="productName" label="产品名称">
                <Input placeholder="请输入" allowClear={true} />
              </Form.Item>
            </Col> */}
            <Col span={16} style={{ textAlign: 'right' }}>
              <Space>
                <Button type="primary" onClick={submit} disabled={loading}>
                  查询
                </Button>
                <Button onClick={reset} disabled={loading}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="p-product-table">
        <div className="flex-box">
          <Space>
            {btns['/trait/product/add'] && (
              <Button
                type="primary"
                disabled={loading}
                onClick={() => {
                  setIsSub(false);
                  setIsEdit(false);
                  setIsModalOpen(true);
                }}
              >
                添加产品线
              </Button>
            )}
            {btns['/trait/product/add-children'] && (
              <Button
                type="primary"
                disabled={loading}
                onClick={() => {
                  setIsSub(true);
                  setIsEdit(false);
                  setIsModalOpen(true);
                }}
              >
                添加子产品
              </Button>
            )}
            {btns['/trait/product/export'] && (
              <Button type="primary" disabled={loading} onClick={dataExport}>
                数据导出
              </Button>
            )}
          </Space>
        </div>
        <Table rowKey="id" {...tableRealProps} loading={loading} columns={columns} scroll={{ x: 'max-content' }} />
      </div>
      <Modal
        title={`${isEdit ? '编辑' : '添加'}${isSub ? '子产品' : '产品线'}`}
        open={isModalOpen}
        onOk={() => {
          addForm.submit();
        }}
        onCancel={handleCancel}
      >
        <Form form={addForm} onFinish={onFinish} {...formItemLayout}>
          {isSub && (
            <Form.Item
              name="parentId"
              label="产品线"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Select
                fieldNames={{ label: 'productName', value: 'id' }}
                options={productList}
                showSearch
                filterOption={(input, option) => (option?.productName ?? '').toLowerCase().includes(input.toLowerCase())}
                placeholder="请选择"
              />
            </Form.Item>
          )}
          <Form.Item
            name="productName"
            label={`${isSub ? '子产品' : '产品线'}名称`}
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input maxLength={30} placeholder="请输入名称，限30字内" />
          </Form.Item>
          {/* <Form.Item name="productPrice" label={`${isSub ? '子产品' : '产品线'}价格`} rules={[{ required: true }]}>
            <InputNumber min={0} placeholder="请输入参考价（单位元）" precision="2" style={{ width: '100%' }} />
          </Form.Item> */}
          {!isSub && (
            <Form.Item
              name="templateType"
              label="同意书模板"
              // rules={[
              //   {
              //     required: true,
              //   },
              // ]}
            >
              <Select placeholder="请选择" options={[...TYPE]} />
            </Form.Item>
          )}
          <Form.Item
            name="productStatus"
            label="状态"
            initialValue="1"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Radio.Group>
              <Radio value="1">上架</Radio>
              <Radio value="2">下架</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Index);
