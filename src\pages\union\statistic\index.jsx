import React from 'react';
import { Tabs } from 'antd';
import Detail from './components/Detail';

const Index = ({ menuType = '' }) => {
  const items = [
    { label: '产品明细', key: '1', children: <Detail menuType={menuType} /> }, // 务必填写 key
    { label: '产品合计', key: '2', children: <Detail menuType={menuType} type="total" /> },
  ];

  return <Tabs destroyInactiveTabPane items={items} />;
};

export default Index;
