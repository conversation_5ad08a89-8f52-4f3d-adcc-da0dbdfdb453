import React from 'react';
import { Row, Col, Input, Select, DatePicker, Radio, Upload, Button, message, Checkbox } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { history } from 'umi';
import moment from 'moment';
import * as CONSTANT from '@/config/constant/constant';
import Core from '../../../../components/clip/Index';
import * as Api from '../../service';
import './index.less';

const FormItem = Form.Item;
const Option = Select.Option;
const { RangePicker } = DatePicker;

class SystemAccountEdit extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      roleList: [],
      hospitalArr: [],
      allUserData: [],
      permissions: {
        xzhz: false, // 新增会诊+会诊记录
        kdjl: false, // 开单记录
        jxthjgj: false, // 家系图绘制工具
        hdbmjl: false, // 活动报名记录
        yblr: false, // 样本录入
        ybcx: false, // 样本查询+样本查询记录
      }
    };
  }

  componentDidMount() {
    this.getRoles();
    this.getAllianceinfo();
    this.findAllUser();
    
    // 初始化权限状态
    this.initPermissions();
  }

  // 添加一个单独的方法来初始化权限，方便重用
  initPermissions = () => {
    const { detail = {} } = this.props;
    const { user = {} } = detail;
    console.log('初始化权限状态 - 用户信息:', user);
    
    if (user && user.permission) {
      console.log('原始权限字符串:', user.permission);
      try {
        let permissionObj;
        // 如果permission已经是对象，直接使用
        if (typeof user.permission === 'object') {
          permissionObj = user.permission;
        } else {
          // 否则尝试解析字符串
          permissionObj = JSON.parse(user.permission);
        }
        console.log('解析后的权限对象:', permissionObj);
        
        // 设置权限状态
        const permissions = {
          xzhz: permissionObj.xzhz === "1",
          kdjl: permissionObj.kdjl === "1",
          jxthjgj: permissionObj.jxthjgj === "1",
          hdbmjl: permissionObj.hdbmjl === "1",
          yblr: permissionObj.yblr === "1",
          ybcx: permissionObj.ybcx === "1",
        };
        console.log('设置权限状态:', permissions);
        this.setState({ permissions });
      } catch (e) {
        console.error('权限解析失败', e, typeof user.permission);
      }
    } else {
      console.log('用户无权限数据，使用默认值');
    }
  }

  // 当接收到新的props时处理权限
  componentWillReceiveProps(nextProps) {
    // 如果detail变化，重新初始化权限
    if (nextProps.detail !== this.props.detail) {
      console.log('接收到新的用户数据，重新初始化权限');
      setTimeout(() => {
        this.initPermissions();
      }, 0);
    }
  }

  findAllUser = async () => {
    const {
      data: { code, data: allUserData },
    } = await Api.findAllUser();
    if (code == 0) {
      this.setState({
        allUserData: allUserData || [],
      });
    }
  };
  getAllianceinfo = async () => {
    const {
      data: { code, data: allianceData },
    } = await Api.getAllianceinfo();
    if (code == 0) {
      this.setState({
        hospitalArr: allianceData || [],
      });
    }
  };

  getRoles = () => {
    Api.getRoles({ numPerPage: 9999, pageNum: 1 }).then((res = {}) => {
      const { data: resData = {} } = res;
      const { code, data = {} } = resData;
      if (code == 0) {
        const { recordList = [] } = data;
        this.setState({ roleList: recordList });
      }
    });
  };

  validateHandler = (rule, value, callback) => {
    const { getFieldValue } = this.props.form;
    const { field } = rule;
    let content = '';
    switch (field) {
      case 'name':
        if (!value || value == '') {
          content = '请输入真实姓名!';
        } else if (!/^[\u4e00-\u9fa5]{2,5}$/.test(value)) {
          content = '支持2-5个汉字!';
        }
        break;
      case 'idNo':
        // if (!value || value == '') {
        //   content = '请输入身份证号!';
        // } else if (!/^\d{17}[0-9xX]$/.test(value)) {
        //   content = '请输入正确的身份证号!';
        // }
        if (value && !/^\d{17}[0-9xX]$/.test(value)) {
          content = '请输入正确的身份证号!';
        }
        break;
      case 'phone':
        if (!value || value == '') {
          content = '请输入手机号!';
        } else if (!/^1\d{10}$/.test(value)) {
          content = '请输入正确的手机号!';
        }
        break;
      case 'account':
        if (!value || value == '') {
          content = '请输入账号名!';
        }
        break;
      case 'dateArray':
        if (!value || !value[0] || !value[1]) {
          content = '请选择账号有效期!';
        }
        break;
      case 'password':
        if (!value || value == '') {
          content = '请输入密码!';
        } else if (!/^(?=.*?[A-Za-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,18}$/.test(value)) {
          content = '长度为8-18位字符,需包含数字+字母+符号，空格除外!';
        }
        break;
      case 'confirmPwd':
        if (!value || value == '') {
          content = '请再次输入密码!';
        } else if (!/^(?=.*?[A-Za-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,18}$/.test(value)) {
          content = '长度为8-18位字符,需包含数字+字母+符号，空格除外!';
        } else if (value != getFieldValue('password')) {
          content = '密码不一致!';
        }
        break;
      default:
        break;
    }
    if (content && content != '') {
      callback(content);
    } else {
      callback();
    }
  };

  submitAccountInfo = userId => {
    const {
      dispatch,
      form: { validateFieldsAndScroll, getFieldsValue },
    } = this.props;
    const param = getFieldsValue();
    param.hisId = 242;
    const { dateArray, identity, institutionName, salesName } = param;
    validateFieldsAndScroll(err => {
      if (!err) {
        dispatch({
          type: 'system/saveAccount',
          payload: {
            recordList: [],
          },
        });
        
        // 将权限状态转换为需要的格式: {"xzhz":"0","kdjl":"1",...}
        const { permissions } = this.state;
        const permissionJSON = {};
        Object.keys(permissions).forEach(key => {
          permissionJSON[key] = permissions[key] ? "1" : "0";
        });
        
        // 将对象转为字符串，确保是有效的字符串格式
        const permissionStr = JSON.stringify(permissionJSON);
        console.log('权限JSON字符串:', permissionStr);
        
        const identityArr = identity.split('___');
        const institutionNameArr = institutionName.split('___');
        const salesNameArr = (salesName || '').split('___');
        dispatch({
          type: 'system/submitAccountInfo',
          payload: {
            userId,
            validateStart: dateArray[0].format('YYYY-MM-DD'),
            validateEnd: dateArray[1].format('YYYY-MM-DD'),
            identityId: identityArr[0],
            identityName: identityArr[1],
            ...param,
            institutionId: institutionNameArr[0],
            institutionName: institutionNameArr[1],
            salesId: salesNameArr[0] || '',
            salesName: salesNameArr[1] || '',
            permission: permissionStr, // 使用转义后的字符串
          },
        });
      }
    });
  };

  isAccountAvailable() {
    const {
      dispatch,
      form: { getFieldError, setFields, getFieldValue },
    } = this.props;
    const err = getFieldError('account');
    if (!err) {
      const account = getFieldValue('account');
      dispatch({
        type: 'system/isAccountAvailable',
        payload: { account },
        next: (data = {}) => {
          if (data.isExist) {
            setFields({
              account: {
                value: account,
                errors: [new Error('用户名已存在')],
              },
            });
          }
        },
      });
    }
  }

  afterFileClip = async accountImg => {
    const { type } = this.state;
    if (type === 'detail' || type === 'modify') {
      // 后门，个人中心修改图片
      const { detail = {} } = this.props;
      const { user = {} } = detail;
      const { data } = await Api.modifyUserInfo({
        accountImg,
        userId: user.userId,
      });
      if (data.code === 0) {
        this.props.form.setFieldsValue({ accountImg });
      }
    } else {
      this.props.form.setFieldsValue({ accountImg });
    }
  };

  beforeUpload = file => {
    const isImage = file.type && file.type.indexOf('image') > -1;
    if (!isImage) {
      message.error('请选择图片进行上传!');
    }
    const size = file.size / 1024 / 1024 <= 1;
    if (!size) {
      message.error('图片大小不能超过1MB!');
    }
    return isImage && size;
  };
  uploadOnChange = (e, key) => {
    message.loading('加载中', 0);
    const { response } = e.file;
    if (response) {
      message.destroy();
      let img = '';
      if (response.code != 0) {
        message.error('上传文件失败');
      } else {
        img = (response.data || {}).url;
      }
      this.props.form.setFieldsValue({ [key]: img });
    }
  };
  checkphone = phone => {
    const {
      dispatch,
      form: { getFieldError, setFields },
    } = this.props;
    const err = getFieldError('phone');
    if (!err) {
      dispatch({
        type: 'system/checkphone',
        payload: { phone },
      }).then(res => {
        console.log(res);
        if (!res) {
          setFields({
            phone: {
              value: phone,
              errors: [new Error('该手机号已存在，不能重复，请核对')],
            },
          });
        }
      });
    }
  };

  // 处理权限变更
  handlePermissionChange = (key, checked) => {
    this.setState(prevState => ({
      permissions: {
        ...prevState.permissions,
        [key]: checked
      }
    }));
  };

  render() {
    const { roleList = [], hospitalArr = [], allUserData = [], permissions } = this.state;
    const { detail = {}, form, loginUserInfo = {} } = this.props;
    const { user = {} } = detail;
    
    // 获取当前页面类型
    let type = this.props.type || '';
    const urlParams = new URLSearchParams(window.location.search);
    const pageType = urlParams.get('type') || type;
    
    console.log('当前页面类型:', pageType);
    console.log('当前权限状态:', permissions);
    console.log('当前用户数据:', user);
    
    const { getFieldDecorator, getFieldValue, setFieldsValue } = form;

    return (
      <div className="page-account-edit">
        <Form className="edit-panel">
          <div className="edit-board">
            <div className="edit-title">基本信息</div>
            <div className="edit-tuber">
              <Row>
                <Col span={12}>
                  <FormItem label="真实姓名" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('name', {
                      rules: [
                        {
                          required: true,
                          whitespace: true,
                          validator: this.validateHandler,
                        },
                      ],
                      initialValue: `${user.name || ''}`,
                    })(<Input style={{ width: 320 }} disabled={pageType == 'detail'} placeholder="请输入真实姓名" minLength={2} maxLength={5} />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="身份证号" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('idNo', {
                      rules: [
                        {
                          // required: true,
                          whitespace: true,
                          validator: this.validateHandler,
                        },
                      ],
                      initialValue: `${user.idNo || ''}`,
                    })(<Input style={{ width: 320 }} disabled={pageType == 'detail'} placeholder="请输入身份证号" />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="手机号码" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('phone', {
                      rules: [
                        {
                          required: true,
                          whitespace: true,
                          validator: this.validateHandler,
                        },
                      ],
                      initialValue: `${user.phone || ''}`,
                    })(<Input style={{ width: 320 }} disabled={pageType == 'detail'} placeholder="请输入手机号码" minLength={11} maxLength={11} onBlur={e => this.checkphone(e.target.value)} />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="账号名" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('account', {
                      rules: [
                        {
                          required: true,
                          whitespace: true,
                          validator: this.validateHandler,
                        },
                      ],
                      initialValue: `${user.account || ''}`,
                    })(
                      <Input
                        style={{ width: 320 }}
                        disabled={pageType == 'modify' || pageType == 'detail'}
                        placeholder="请输入账号名"
                        minLength={6}
                        maxLength={20}
                        onBlur={this.isAccountAvailable.bind(this)}
                      />,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="角色" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('identity', {
                      rules: [{ required: true, message: '请选择角色' }],
                      initialValue: user.identityId ? `${user.identityId}___${user.identityName}` : undefined,
                    })(
                      <Select
                        style={{ width: 320 }}
                        placeholder="请选择角色"
                        disabled={pageType == 'detail'}
                        showSearch
                        optionFilterProp="children"
                        filterOption={(input, opt) => opt.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      >
                        {roleList.map(item => (
                          <Option key={item.id} value={`${item.id}___${item.identityName}`}>
                            {`[${item.identityCode || ''}]${item.identityName}`}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="职称" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('title', {
                      initialValue: user.title || null,
                    })(
                      <Select
                        showSearch
                        style={{ width: 320 }}
                        placeholder="请选择职称"
                        disabled={pageType == 'detail'}
                        filterOption={(input, opt) => opt.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      >
                        <Option value="主任医生">主任医生</Option>
                        <Option value="副主任医生">副主任医生</Option>
                        <Option value="主治医生">主治医生</Option>
                        <Option value="住院医师">住院医师</Option>
                        <Option value="医师">医师</Option>
                        <Option value="医士">医士</Option>
                      </Select>,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="所属客户" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('institutionName', {
                      rules: [
                        {
                          required: true,
                          message: '请选择所属客户!',
                        },
                      ],
                      initialValue: user.institutionId ? `${user.institutionId}___${user.institutionName}` : null,
                    })(
                      <Select
                        showSearch
                        style={{ width: 320 }}
                        placeholder="请选择所属客户"
                        optionFilterProp="children"
                        disabled={pageType == 'detail'}
                        filterOption={(input, opt) => opt.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      >
                        {hospitalArr.map(item => {
                          return (
                            <Option value={item.id + '___' + item.institutionName} key={item.id}>
                              {item.institutionName}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="所属部门/科室" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('deptName', {
                      initialValue: `${user.deptName || ''}`,
                    })(<Input style={{ width: 320 }} disabled={pageType == 'detail'} placeholder="请输入所属部门" autoComplete="off" />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="关联销售" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('salesName', {
                      initialValue: user.salesId ? `${user.salesId}___${user.salesName}` : null,
                    })(
                      <Select
                        showSearch
                        style={{ width: 320 }}
                        placeholder="请选择关联销售"
                        optionFilterProp="children"
                        disabled={pageType == 'detail'}
                        filterOption={(input, opt) => opt.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      >
                        {allUserData.map(item => {
                          return (
                            <Option value={item.id + '___' + item.name} key={item.id}>
                              {item.name}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </FormItem>
                </Col>
              </Row>
              {pageType == 'new' ? (
                <Row>
                  <Col span={12}>
                    <FormItem label="设置密码" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                      {getFieldDecorator('password', {
                        rules: [
                          {
                            required: true,
                            whitespace: true,
                            validator: this.validateHandler,
                          },
                        ],
                      })(<Input type="password" style={{ width: 320 }} placeholder="请输入密码" minLength={6} maxLength={14} autoComplete="new-password" />)}
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem label="确认密码" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                      {getFieldDecorator('confirmPwd', {
                        rules: [
                          {
                            required: true,
                            whitespace: true,
                            validator: this.validateHandler,
                          },
                        ],
                      })(<Input type="password" style={{ width: 320 }} placeholder="请确认密码" minLength={6} maxLength={14} />)}
                    </FormItem>
                  </Col>
                </Row>
              ) : null}
              <Row>
                <Col span={12}>
                  <FormItem label="医师资格证书" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    <Upload
                      className={`${form.getFieldValue('yszzzs') || user.yszzzs ? 'deptlogo-has-img' : ''} deptlogo-uploader`}
                      name="upfile"
                      action={`${CONSTANT.DOMAIN}/api/files/uploadpic`}
                      beforeUpload={this.beforeUpload}
                      showUploadList={false}
                      onChange={e => this.uploadOnChange(e, 'yszzzs')}
                      data={{
                        partnerId: 'merchant',
                        serviceType: 'test',
                      }}
                      disabled={pageType == 'detail'}
                    >
                      <div className="deptlogo-uploader-trigger">
                        <Button size="large">
                          <Icon type="upload" />
                          选择图片
                        </Button>
                      </div>
                      <div className="upload-img">
                        <img src={form.getFieldValue('yszzzs') || user.yszzzs} alt="" />
                      </div>
                    </Upload>
                    {getFieldDecorator('yszzzs', {
                      initialValue: user.yszzzs,
                    })(<Input style={{ display: 'none' }} />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="医师执业证书" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    <Upload
                      className={`${form.getFieldValue('yszyzs') || user.yszyzs ? 'deptlogo-has-img' : ''} deptlogo-uploader`}
                      name="upfile"
                      action={`${CONSTANT.DOMAIN}/api/files/uploadpic`}
                      beforeUpload={this.beforeUpload}
                      showUploadList={false}
                      onChange={e => this.uploadOnChange(e, 'yszyzs')}
                      data={{
                        partnerId: 'merchant',
                        serviceType: 'test',
                      }}
                      disabled={pageType == 'detail'}
                    >
                      <div className="deptlogo-uploader-trigger">
                        <Button size="large">
                          <Icon type="upload" />
                          选择图片
                        </Button>
                      </div>
                      <div className="upload-img">
                        <img src={form.getFieldValue('yszyzs') || user.yszyzs} alt="" />
                      </div>
                    </Upload>
                    {getFieldDecorator('yszyzs', {
                      initialValue: user.yszyzs,
                    })(<Input style={{ display: 'none' }} />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="医生头像" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {
                      // pageType == 'detail' ? <div className="m-cover" /> : null
                    }
                    <Core defaultUrl={user.accountImg} afterUpload={this.afterFileClip} aspectRatio={5 / 7} operSize={{ width: '200px', height: '280px' }} />
                    {getFieldDecorator('accountImg', {
                      initialValue: user.accountImg,
                    })(<Input style={{ display: 'none' }} />)}
                  </FormItem>
                </Col>
              </Row>
            </div>
          </div>
          <div className="edit-board">
            <div className="edit-title">服务设置</div>
            <div className="edit-tuber">
              <Row>
                <Col span={12}>
                  <FormItem label="账号有效期" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('dateArray', {
                      rules: [{ required: true, validator: this.validateHandler }],
                      // initialValue: [moment(user.validateStart), moment(user.validateEnd)],
                      initialValue: user.validateStar ? [moment(user.validateStart), moment(user.validateEnd)] : [moment(), moment().add(1, 'years')],
                    })(
                      <RangePicker
                        style={{ width: 320 }}
                        format="YYYY-MM-DD"
                        allowClear={false}
                        disabled={pageType == 'detail'}
                        disabledDate={value => {
                          const today = moment();
                          return moment(value).valueOf() < today.date(today.date() - 1).valueOf();
                        }}
                      />,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="咨询设置" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('zxsz', {
                      rules: [{ required: true, message: '请选择咨询设置!' }],
                      initialValue: user.zxsz || '1',
                    })(
                      <Radio.Group disabled={pageType == 'detail'}>
                        <Radio value="1">允许咨询</Radio>
                        <Radio value="0">不允许咨询</Radio>
                      </Radio.Group>,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="开单设置" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('kdsz', {
                      rules: [{ required: true, message: '请选择开单设置!' }],
                      initialValue: user.kdsz || '1',
                    })(
                      <Radio.Group disabled={pageType == 'detail'}>
                        <Radio value="1">允许开单</Radio>
                        <Radio value="0">不允许开单</Radio>
                      </Radio.Group>,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="移动端样本录入" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('yblr', {
                      rules: [{ required: true, message: '请选择样本录入设置!' }],
                      initialValue: user.yblr || '0',
                    })(
                      <Radio.Group disabled={pageType == 'detail'}>
                        <Radio value="1">允许录入</Radio>
                        <Radio value="0">不允许录入</Radio>
                      </Radio.Group>,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label="快递到付设置" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                    {getFieldDecorator('dfsz', {
                      rules: [{ required: true, message: '请选择样本寄送到付设置!' }],
                      initialValue: user.dfsz || '0',
                    })(
                      <Radio.Group disabled={pageType == 'detail'}>
                        <Radio value="1">允许到付</Radio>
                        <Radio value="0">不允许到付</Radio>
                      </Radio.Group>,
                    )}
                  </FormItem>
                </Col>
              </Row>
            </div>
          </div>
          
          {/* 小程序权限控制 */}
          <div className="edit-board">
            <div className="edit-title">小程序权限控制</div>
            <div className="edit-tuber">
              <Row>
                <Col span={24}>
                  <FormItem wrapperCol={{ span: 24 }}>
                    <div style={{ lineHeight: '32px' }}>
                      <Checkbox disabled={pageType == 'detail'} checked={permissions.xzhz} onChange={e => this.handlePermissionChange('xzhz', e.target.checked)}>新增会诊+会诊记录</Checkbox>
                      <Checkbox disabled={pageType == 'detail'} checked={permissions.kdjl} onChange={e => this.handlePermissionChange('kdjl', e.target.checked)}>开单记录</Checkbox>
                      <Checkbox disabled={pageType == 'detail'} checked={permissions.jxthjgj} onChange={e => this.handlePermissionChange('jxthjgj', e.target.checked)}>家系图绘制工具</Checkbox>
                      <Checkbox disabled={pageType == 'detail'} checked={permissions.hdbmjl} onChange={e => this.handlePermissionChange('hdbmjl', e.target.checked)}>活动报名记录</Checkbox>
                      <Checkbox disabled={pageType == 'detail'} checked={permissions.yblr} onChange={e => this.handlePermissionChange('yblr', e.target.checked)}>样本录入</Checkbox>
                      <Checkbox disabled={pageType == 'detail'} checked={permissions.ybcx} onChange={e => this.handlePermissionChange('ybcx', e.target.checked)}>样本寄送+样本寄送记录</Checkbox>
                      {/* <Checkbox disabled={pageType == 'detail'}>我的患者</Checkbox>
                      <Checkbox disabled={pageType == 'detail'}>取留会诊</Checkbox> */}
                    </div>
                  </FormItem>
                </Col>
              </Row>
            </div>
          </div>
          
          {pageType != 'detail' ? (
            <div className="edit-board">
              <FormItem>
                <Button
                  type="primary"
                  onClick={() => {
                    this.submitAccountInfo(user.userId);
                  }}
                >
                  保存
                </Button>
                <Button
                  style={{ marginLeft: 16 }}
                  onClick={() => {
                    history.goBack();
                  }}
                >
                  取消
                </Button>
              </FormItem>
            </div>
          ) : null}
        </Form>
      </div>
    );
  }
}

export default SystemAccountEdit;
