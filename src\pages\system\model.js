import { message } from 'antd';
import { history } from 'umi';

import * as utils from '@/utils/utils';
import * as service from './service';

const permissionRecursive = (param, selected) => {
  if (param.children && param.children.length > 0) {
    param.children.forEach(item => {
      item.selected = selected;
      permissionRecursive(item, selected);
    });
  }
};

export default {
  namespace: 'system',
  state: {
    account: {
      recordList: [],
      totalCount: 0,
      currentPage: 1,
      detail: {
        user: {},
      },
      hospitals: [],
    },
  },
  reducers: {
    save(state, { payload: { account } }) {
      return {
        ...state,
        account: {
          ...state.account,
          ...account,
        },
      };
    },
    saveHospital(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    saveAccount(state, { payload }) {
      return {
        ...state,
        account: {
          ...state.account,
          ...payload,
        },
      };
    },
    clearAccountDetail(state) {
      return {
        ...state,
        account: {
          ...state.account,
          detail: {
            user: {},
          },
        },
      };
    },
    changePermissionTable(state, { payload }) {
      const { idxs = [], selected } = payload;
      const {
        account: {
          detail: { resources },
        },
      } = state;
      let currentNode = resources;
      idxs.forEach((idx, key) => {
        if (key == 0) {
          currentNode = currentNode[idx];
        } else {
          currentNode = currentNode.children[idx];
        }
        if (!selected) {
          currentNode.selected = true;
        }
      });
      currentNode.selected = !selected;
      permissionRecursive(currentNode, !selected);
      return {
        ...state,
        account: {
          ...state.account,
          detail: {
            ...state.account.detail,
            resources,
          },
        },
      };
    },
    saveAccountManageHis(state, { payload }) {
      return {
        ...state,
        account: {
          ...state.account,
          detail: {
            ...state.account.detail,
            user: {
              ...state.account.detail.user,
              manageHises: payload,
            },
          },
        },
      };
    },
    saveAccountResources(state, { payload }) {
      return {
        ...state,
        account: {
          ...state.account,
          detail: {
            ...state.account.detail,
            resources: payload,
          },
        },
      };
    },
  },
  effects: {
    *modifyPassword({ payload }, { call }) {
      const { data } = yield call(service.modifyPassword, payload);
      if (data.code === 0) {
        message.success('修改密码成功，请重新登录！');
        history.push('/login');
      } else {
        message.error(data.msg);
      }
    },

    *accountList({ payload }, { call, put }) {
      const { data } = yield call(service.accountList, payload);
      if (data.code === 0) {
        yield put({
          type: 'save',
          payload: {
            account: {
              ...data.data,
              ...payload,
            },
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *accountInfo({ payload }, { call, put }) {
      const { data } = yield call(service.accountInfo, payload);
      if (data.code === 0) {
        const detail = data.data || { user: {} };
        const { manageHises } = detail.user || {};
        const hisList = utils.generateLabelValueList({
          list: manageHises || [],
          labelIndex: 'hisName',
          valueIndex: 'hisId',
        });
        yield put({
          type: 'saveAccount',
          payload: {
            detail: {
              ...detail,
              user: {
                ...detail.user,
                manageHises: hisList,
              },
            },
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *manageHisList({ payload }, { call, put }) {
      const { data } = yield call(service.hisList, payload);
      if (data.code === 0) {
        const hisList = utils.generateLabelValueList({
          list: data.data || [],
          labelIndex: 'hisName',
          valueIndex: 'hisId',
        });
        yield put({
          type: 'saveAccountManageHis',
          payload: hisList,
        });
      } else {
        message.error(data.msg);
      }
    },
    *permissionList({ payload }, { call, put }) {
      const { data } = yield call(service.permissionList, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveAccountResources',
          payload: data.data,
        });
      } else {
        message.error(data.msg);
      }
    },
    *submitAccountInfo({ payload }, { call, put }) {
      const { userId } = payload;
      const { data } = yield call(service.submitAccountInfo, payload);
      if (data.code === 0) {
        if (userId) {
          message.success('修改成功');
        } else {
          message.success('创建成功');
        }
        history.push('/system/account');
        put({
          type: 'accountList',
        });
      } else {
        message.destroy();
        message.error(data.msg);
      }
    },
    *deleteAccount({ payload }, { call, put }) {
      if (!payload.inputs) delete payload.inputs;
      const { data } = yield call(service.deleteAccount, { userId: payload.userId });
      if (data.code === 0) {
        message.success('删除成功');
        yield put({
          type: 'accountList',
          payload,
        });
      } else {
        message.error(data.msg);
      }
    },
    *resetPassword({ payload }, { call, put }) {
      const { data } = yield call(service.resetPassword, payload);
      if (data.code === 0) {
        // success({
        //   title: (
        //     <div>
        //       重置成功，密码被重置为
        //       <span style={{ color: '#f57f17' }}>{data.data}</span>
        //     </div>
        //   ),
        //   content: '重置后将覆盖原来的密码，该用户需要重新登录。',
        //   onOk() { },
        // });
        yield put({
          type: 'accountList',
          payload,
        });
        return data.data;
      } else {
        message.error(data.msg);
      }
    },
    *changeStatus({ payload }, { call, put }) {
      const { status } = payload;
      const { data } = yield call(service.changeStatus, payload);
      if (data.code === 0) {
        message.success(`${status == 1 ? '解冻' : '冻结'}成功`);
        yield put({
          type: 'accountList',
          payload,
        });
      } else {
        message.error(data.msg);
      }
      return data.code === 0;
    },
    *isAccountAvailable({ payload, next }, { call }) {
      const { data } = yield call(service.isAccountAvailable, payload);
      if (data.code === 0) {
        if (typeof next === 'function') {
          next(data.data || {});
        }
      }
    },
    // *getAllianceinfo({ payload }, { call }) {
    //   const { code, data, msg = '' } = yield call(service.getAllianceinfo, payload);
    //   if (code === 0) {
    //     yield put({
    //       type: 'saveHospital',
    //       payload: {
    //         hospitals: data.recordList || {},
    //       },
    //     });
    //   } else {
    //     message.error(msg);
    //   }
    // },
    *checkphone({ payload }, { call }) {
      const { data } = yield call(service.checkphone, payload);
      return data.code === 0;
    },
  },
};
