@charset "utf-8";
@import '../../../../resources/styles/mixins';
a {
  cursor: pointer;
}
.create-survey-page {
  display: flex;
  margin: 24px 160px;
  padding: 50px 60px;
  background-color: #fff;
  &.newPage {
    display: block !important;
  }
  .create-survey-notice {
    border: 1px solid #51c488;
    background: rgb(234, 253, 244);
    margin-bottom: 10px;
    line-height: 35px;
    display: flex;
    align-items: center;
    padding-left: 10px;
  }
  .notice-img {
    line-height: 35px;
    margin-right: 6px;
    width: 20px;
    height: 20px;
  }
  .create-survey-main {
    display: flex;
  }
  .create-survey-info {
    flex: 1;
    margin-left: 40px;
  }
  .condition-label {
    margin-top: 8px;
    margin-bottom: 24px;
    padding: 7px 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
    .label-item {
      display: inline-block;
      margin: 0 0 5px 5px;
      padding: 2px 4px;
      background-color: #fff;
      border-radius: 4px;
      border: 1px @border-color solid;
      cursor: pointer;
    }
  }
  .question-option-item {
    padding: 10px 25px;
    &.submit-btn {
      display: flex;
      padding-bottom: 40px;
    }
    .item-margin-left {
      margin-left: 65px;
    }
  }
  .questionlist-option-item {
    padding: 30px 15px 30px;
    border-bottom: 1px @border-color solid;
    .title {
      word-wrap: break-word;
      word-break: break-all;
    }
  }
  .questions-type {
    color: #51c488;
    padding: 2px 9px;
    border-radius: 4px;
    background-color: #ceefdf;
    margin-left: 8px;
    white-space: nowrap;
  }
  .item-margin {
    margin-left: 15px;
  }
  .questions-required {
    background-color: #fee3ce;
    color: #f8811a;
    padding: 2px 9px;
    border-radius: 4px;
    margin-left: 8px;
  }
  .option-list-item {
    margin: 12px 0;
    .preview-img {
      margin: 5px 0;
    }
    &.list-item-flex {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }
    span {
      white-space: pre-wrap;
    }
    .ant-radio-wrapper,
    .ant-checkbox-wrapper {
      margin-bottom: 4px;
    }
  }

  .phone-question-list {
    padding: 15px 0;
    margin: 10px 15px;
    border-bottom: 1px solid @border-color;
    .title {
      word-wrap: break-word;
      word-break: break-all;
    }
    .questions-type {
      white-space: nowrap;
    }
    &:last-child {
      border-bottom: none;
    }
    .bold {
      font-weight: bold;
      padding-bottom: 30rpx;
    }
  }
  .question-list-item {
    margin-bottom: 20px;
    padding-bottom: 15px;
    background-color: #fafafa;
    width: 700px;
    .title {
      padding: 11px 14px;
      border-bottom: 1px @border-color solid;
    }
    .question-option-item-label {
      width: 65px;
    }
    .label-margin {
      margin-left: 65px;
      // padding-right: 50px;
    }
  }
  .question-list-item-mask {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
  }
  .add-question-btn {
    margin: 24px 0;
    padding: 6px 0;
    background-color: #fafafa;
    cursor: pointer;
  }
}
.create-marksurvey-page {
  .create-survey-info {
    .list-item-sel {
      display: flex;
      padding: 0 20px;
      .list-item-type {
        border-right: 1px solid #e9e9e9;
        .type-title {
          margin: 10px 0;
        }
        .type-row {
          margin-bottom: 5px;
          &.type-row-spec {
            display: flex;
          }
          .type-item {
            width: 6em;
            margin-right: 5px;
            text-align: center;
            padding: 0;
            &.type-item-spec {
              margin-right: 20px;
            }
            &.active {
              color: #fff;
              background-color: #3f969d;
              border-color: #3f969d;
            }
          }
        }
      }
      .list-item-deta {
        width: 100%;
        .dimension {
          .question-dimension {
            min-width: 7em;
            padding-right: 6px;

            .dimension-cont {
              min-width: 7em;
              border: 1px solid #d9d9d9;
              border-radius: 2px;
              position: relative;
              line-height: 27px;
              text-align: center;
              cursor: pointer;
              &.dimensionactive {
                color: #fff;
                background-color: #3f969d;
                border: 1px solid #3f969d;
              }
              .dimension-text {
                display: inline-block;
                padding: 0 7px;
              }
              .dimension-img {
                cursor: pointer;
                position: absolute;
                right: -6px;
                top: -6px;
                width: 20px;
                height: 20px;
              }
            }
          }
          .dimension-input {
            width: 6em;
            max-midth: 315px;
            padding-right: 4px;
          }
          .dimension-add {
            img {
              display: flex;
              cursor: pointer;
              width: 20px;
              height: 20px;
            }
          }
        }
        .select-item {
          background: #f1f1f1;
          margin-left: 25px;
          padding: 6px 0;
          .select-item-col {
            margin-right: 10px;
            background: #f1f1f1;
            text-indent: 6px;
            &.item-center {
              margin-left: -1px;
            }
          }
        }
        .option-item {
          padding-right: 0;
          .option-item-col {
            margin-right: 10px;
          }
          .item-margin {
            margin-left: 18px;
          }
          .operate-btn {
            display: flex;
            margin-left: 25px;
            img {
              width: 20px;
              height: 20px;
              display: flex;
              cursor: pointer;
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
}
.condition-modal {
  .time-select {
    min-width: 95px;
    margin-left: 15px;
  }
  .time-select-only {
    margin-right: 10px;
  }
  .ant-radio-wrapper {
    margin: 0 15px;
  }
  .ant-modal {
    width: auto !important;
  }
  // .ant-radio span{
  //   white-space: pre-wrap
  // }
}
.one-line-check {
  display: flex;
  .left-radio-box {
    width: 50%;
    padding-left: 40px;
  }
  .right-check-box {
    display: flex;
    align-items: flex-end;
  }
}
