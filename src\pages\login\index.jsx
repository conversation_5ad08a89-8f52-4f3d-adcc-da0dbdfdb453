import React, { useEffect } from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Input, Button, Modal } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import queryString from 'query-string';
import * as CONSTANT from '../../config/constant/constant';
import './style.less';

import loginLogo from '../../resources/images/common/login-logo.png';

const FormItem = Form.Item;
const confirm = Modal.confirm;

function Login({ dispatch, location, data: loginData, captcha, form }) {
  const { getFieldDecorator } = form;
  const { backurl = '' } = queryString.parse(location.search);
  function changeCaptcha() {
    dispatch({
      type: 'login/changeCaptcha',
    });
  }

  function doLogin(username, password, validateCode) {
    dispatch({
      type: 'login/doLogin',
      payload: { username, password, validateCode },
    }).then(data => {
      if (data.code === 0) {
        if (data.data.effectiveDays <= 7) {
          confirm({
            title: (
              <div>
                您的密码距离过期还有
                <span style={{ color: '#f57f17' }}>{data.data.effectiveDays}</span>
                天，请确认是否修改密码？
              </div>
            ),
            content: '修改密码后，需要重新登录。',
            okText: '修改密码',
            cancelText: '暂不修改',
            onOk() {
              history.push({
                pathname: '/resetPwd',
              });
            },
            onCancel() {
              if (backurl) {
                if (window.history.length > 1) {
                  history.goBack();
                } else {
                  window.location.replace(backurl);
                }
              } else {
                history.push({
                  pathname: '/main',
                });
              }
            },
          });
        } else {
          dispatch({
            type: 'root/initApp',
            payload: '/main',
          });
          if (backurl) {
            if (window.history.length > 1) {
              history.goBack();
            } else {
              window.location.replace(backurl);
            }
          } else {
            history.push({
              pathname: '/main',
            });
          }
        }
      } else {
        dispatch({
          type: 'login/save',
          payload: {
            data,
            captcha: `${CONSTANT.DOMAIN}/api/validate_code?${Date.now()}`,
          },
        });
      }
    });
  }

  function handleSubmit(e) {
    e.preventDefault();
    form.validateFields((err, values) => {
      if (!err) {
        doLogin(values.username, values.password, values.validateCode);
      }
    });
  }
  useEffect(() => {
    return () => {
      dispatch({
        type: 'login/save',
        payload: {
          data: {},
          captcha: `${CONSTANT.DOMAIN}/api/validate_code?${Date.now()}`,
        },
      });
    };
  }, []);

  return (
    <div className="page-login">
      <div className="login-body">
        <div className="login-form">
          <div className="login-form-box">
            <div className="login-form-header">
              <img src={loginLogo} alt="" />
              <div className="head-text">互联网医院后台管理系统</div>
            </div>
            <Form onSubmit={handleSubmit}>
              <FormItem>
                {getFieldDecorator('username', {
                  rules: [{ required: true, message: '请输入账号名!', whitespace: true }],
                })(<Input className="login-input" placeholder="登录账号" style={{ height: 45 }} />)}
              </FormItem>
              <FormItem>
                {getFieldDecorator('password', {
                  rules: [{ required: true, message: '请输入密码!', whitespace: true }],
                })(<Input className="login-input" placeholder="密码" type="password" style={{ height: 45 }} />)}
              </FormItem>
              <FormItem>
                <div style={{ display: 'flex' }}>
                  {getFieldDecorator('validateCode', {
                    rules: [{ required: true, message: '请输入验证码!' }],
                  })(<Input className="login-input" placeholder="验证码" />)}
                  <img alt="" src={captcha} onClick={changeCaptcha} style={{ marginLeft: 16, height: 44, width: 110 }} />
                </div>
              </FormItem>
              <FormItem style={{ marginBottom: 4 }}>
                <Button htmlType="submit" className="login-button" ghost>
                  登 录
                </Button>
              </FormItem>
              {loginData && loginData.code ? (
                <div className="has-error" style={{ textAlign: 'center' }}>
                  <span className="ant-legacy-form-explain">{loginData.msg}</span>
                </div>
              ) : null}
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
}

export default connect(state => state.login)(Form.create()(Login));
