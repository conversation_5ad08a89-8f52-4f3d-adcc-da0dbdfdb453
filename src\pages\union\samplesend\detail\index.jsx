// src/pages/detail/index.jsx
import React from 'react';
import { connect } from 'dva';
import { Modal, Table, Pagination } from 'antd';
import moment from 'moment';
import styles from './index.less';

const columns = [
  {
    title: '产品线',
    dataIndex: 'productName',
    width: 120,
    render: text => text || '-'
  },
  {
    title: '子产品线',
    dataIndex: 'sonProductName',
    width: 120,
    render: text => text || '-'
  },
  {
    title: '样本编号',
    dataIndex: 'sampleNumber',
    width: 180
    // render: text => <span className={styles.code}>{text}</span>
  },
  {
    title: '名字',
    dataIndex: 'sjzName',
    width: 120,
    render: text => text || '-'
  },
  {
    title: '样本录入时间',
    dataIndex: 'receiveTime',
    width: 180,
    render: text => text ? moment(text).format('YYYY-MM-DD HH:mm') : '-'
  }, {
    title: '样本状态',
    dataIndex: 'receiveStatus',
    width: 180,
    render: (text) => {
      const statusMap = {
        1: { text: '已收样', color: '#52c41a' }, // 绿色
        0: { text: '未收样', color: '#f5222d' }  // 红色
      };
      const status = statusMap[text] || { text: text, color: '#000' };
      return <span style={{ color: status.color }}>{status.text}</span>;
    }
  }
];

// @connect(({ sampleDetail }) => ({
//   visible: sampleDetail.detailVisible,
//   data: sampleDetail.listData,       // 修改为直接获取完整数据
// }))

@connect(({ sampleDetail }) => ({
  visible: sampleDetail.detailVisible,
  data: sampleDetail.listData,       // 主数据
  list: sampleDetail.listData?.list || [], // 样本列表数据
  currentDetail: sampleDetail.currentDetail,
  extFields: sampleDetail.extFields,
}))

class DetailModal extends React.Component {
  handlePageChange = (page, pageSize) => {
    this.props.dispatch({
      type: 'sampleDetail/fetchDetail',
      payload: {
        page,
        pageSize,
        id: this.props.currentDetail?.id
      }
    });
  };

  handleClose = () => {
    this.props.dispatch({
      type: 'sampleDetail/closeDetail',
      payload: {
        detailVisible: false
      }
    });
  };

  render() {
    const { visible, data, list,currentDetail } = this.props;
      // 合并主数据和列表数据
      const displayData = {
        ...data,
        list: list.map(item => ({
          ...item,
          // 确保字段名与列定义一致
          productName: item.productName ,
          sonProductName: item.sonProductName ,
          sampleNumber: item.sampleNumber ,
          name: item.name ,
          createTime: item.createTime 
        }))
      };

      // const tableData = list.map(item => ({
      //   ...item,
      //   // 使用主数据中的寄件人信息
      //   name: listData?.senderName,
      //   createTime: listData?.createTime || currentDetail?.createTime
      // }));
  
  //  const { pagination = { current: 1, pageSize: 10, total: 0 } } = this.props;

    return (
      <Modal
        title="样本详情"
        visible={visible}
        onCancel={this.handleClose}
        width={1000}
        footer={null}
        destroyOnClose
        className={styles.modal}
      >
        <Table
         columns={columns}
         dataSource={displayData.list}
         rowKey="sampleNumber"
         pagination={false}
         bordered
         size="small"
         scroll={{ x: 'max-content' }}
         className={styles.table}
        />
        
        {/* <div className={styles.pagination}>
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            showQuickJumper
            showSizeChanger
            onChange={this.handlePageChange}
          />
        </div> */}
        <br/>
        <div className={styles.footer}>
          <span className={styles.remark}>备注：{this.props.data.remark}</span>
         
        </div>
      </Modal>
    );
  }
}

export default DetailModal;