import React, { Component } from 'react';
import { List, Input, Radio, Modal, message } from 'antd';
import * as Api from './api';

import styles from './fastway.less';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      list: [],
      filter: '',
      messageType: '1',
    };
  }

  componentDidMount() {
    this.getList();
  }

  getList = async () => {
    const { messageType } = this.state;
    if (messageType === '1') {
      this.queryMyFastQuestion();
    } else {
      this.querySysData();
    }
  };

  querySysData = async () => {
    this.setState({ list: [] });
    const { type } = this.props;
    const { code, data = {} } = await Api.getFastList({ fastType: type, numPerPage: 100 });
    if (code == 0) {
      const { recordList = [] } = data;
      this.setState({ list: recordList });
    }
  };

  queryMyFastQuestion = async () => {
    this.setState({ list: [] });
    const { code, data = [] } = await Api.queryMyFastQuestion();
    if (code == 0) {
      this.setState({ list: data });
    }
  };

  handleEditorChange = (item = {}) => {
    const { cb } = this.props;
    cb(item.content);
  };

  // preDeleteContent = (item, e) => {
  //   e.stopPropagation();
  //   Modal.confirm({
  //     title: '系统提示',
  //     content: '是否确认删除此项？',
  //     okText: '确认',
  //     cancelText: '取消',
  //     onOk: () => {
  //       this.deleteContent(item);
  //     },
  //   });
  // }

  deleteContent = async (item, e) => {
    e.stopPropagation();
    const { code } = await Api.deleteFastQuestion({ fastQueId: item.fastQueId });
    if (code == 0) {
      this.getList();
      message.success('删除成功');
    }
  };

  filterData = e => {
    const val = e.currentTarget.value;
    this.setState({ filter: val });
  };

  changeTipType = e => {
    const val = e.target.value;
    this.setState({ messageType: val }, this.getList);
  };

  render() {
    const { list, filter = '', messageType } = this.state;

    const showData = list.filter(item => {
      return (!filter && !filter.length) || (item.content || '').indexOf(filter) > -1;
    });

    return (
      <div className={styles.topBox}>
        <Input maxLength={20} onChange={this.filterData} style={{ margin: '10px 3%', width: '94%' }} placeholder="关键字搜索" />
        <div className={styles.content}>
          <List
            dataSource={showData}
            renderItem={item => {
              let showStr;
              if (!filter && !filter.length) {
                showStr = item.content || '';
              } else {
                showStr = (item.content || '').replace(new RegExp(filter, 'g'), `<span class="${styles.keyword}">${filter}</span>`);
              }
              return (
                <List.Item
                  onClick={() => this.handleEditorChange(item)}
                  actions={
                    messageType === '1'
                      ? [
                          <span className={styles.deleteBtn} onClick={e => this.deleteContent(item, e)}>
                            删除
                          </span>,
                        ]
                      : []
                  }
                >
                  <div
                    className={styles.fastContent}
                    dangerouslySetInnerHTML={{
                      __html: showStr,
                    }}
                  />
                </List.Item>
              );
            }}
          />
        </div>
        <div className={styles.footer}>
          <Radio.Group defaultValue="1" buttonStyle="solid" onChange={this.changeTipType}>
            <Radio.Button value="1">我的</Radio.Button>
            <Radio.Button value="2">系统</Radio.Button>
          </Radio.Group>
        </div>
      </div>
    );
  }
}

export default Index;
