import React, { Component } from 'react';
import { connect } from 'dva';
import { Input, Checkbox, Button, message } from 'antd';
import queryString from 'query-string';
import './index.less';

class Index extends Component {
  constructor(props) {
    super(props);
    const { id = '' } = queryString.parse(this.props.location.search);
    this.state = {
      id,
      menuTree: [],
      identityName: '',
      identityCode: '',
      description: '',
      msgLock: false,
    };
  }
  componentDidMount() {
    this.getmenutree();
  }
  getmenutree = () => {
    this.props
      .dispatch({
        type: 'roleMng/getmenutree',
      })
      .then(res => {
        const { data = [] } = res;
        this.setState({ menuTree: data || [] }, () => {
          if (this.state.id) {
            this.props
              .dispatch({
                type: 'roleMng/getrolebyid',
                payload: { identityId: this.state.id },
              })
              .then(res => {
                if (res.code === 0 && res.data) {
                  const { identityName = '', identityCode = '', description = '', extFields = '{}' } = res.data;
                  this.setState({ identityName, identityCode, description });
                  try {
                    this.getroletree(JSON.parse(extFields).ids || []);
                  } catch (error) {}
                }
              });
          }
        });
      });
  };
  getroletree = (ids = []) => {
    console.log(ids);
    const { menuTree } = this.state;
    const setCheckedMenu = tree => {
      tree.forEach(item => {
        item.checked = ids.includes(item.id);
        if (item.children && item.children.length > 0) {
          setCheckedMenu(item.children);
        }
      });
    };
    setCheckedMenu(menuTree);
    this.setState({ menuTree });
  };
  menuChange = (e, menuFirstInd, menuSecondInd, actionInd) => {
    const { menuTree = [] } = this.state;
    const { checked } = e.target;
    if (actionInd || actionInd === 0) {
      if (checked) {
        menuTree[menuFirstInd].checked = checked;
        menuTree[menuFirstInd].children[menuSecondInd].checked = checked;
      }
      menuTree[menuFirstInd].children[menuSecondInd].children[actionInd].checked = checked;
    } else if (menuSecondInd || menuSecondInd === 0) {
      if (checked) {
        menuTree[menuFirstInd].checked = checked;
      }
      this.changeChecked(menuTree[menuFirstInd].children[menuSecondInd], checked);
    } else if (menuFirstInd || menuFirstInd === 0) {
      this.changeChecked(menuTree[menuFirstInd], checked);
    }
    this.setState({ menuTree });
  };
  changeChecked = (item, checked) => {
    item.checked = checked;
    if (item.children && item.children.length > 0) {
      item.children.forEach(v => this.changeChecked(v, checked));
    }
  };
  save = () => {
    const { id, menuTree = [], identityName, identityCode, description } = this.state;
    if (!identityName) {
      message.warning('请输入角色名称');
      return false;
    }
    const resourceIds = [];
    const getCheckedMenu = tree => {
      tree.forEach(item => {
        if (item.checked) {
          resourceIds.push(item.id);
          if (item.children && item.children.length > 0) {
            getCheckedMenu(item.children);
          }
        }
      });
    };
    getCheckedMenu(menuTree);
    const payload = {
      identityName,
      identityCode,
      description,
      extFields: JSON.stringify({
        ids: resourceIds.join(','),
      }),
    };
    if (id) {
      payload.id = id;
    }
    if (this.state.msgLock) {
      return false;
    }
    this.setState({ msgLock: true });
    const { dispatch, history } = this.props;
    dispatch({
      type: `roleMng/${id ? 'updaterole' : 'addrole'}`,
      payload,
    }).then(res => {
      this.setState({ msgLock: false });
      if (res.code === 0) {
        message.success('保存成功！', 2, () => history.goBack());
      }
    });
  };
  render() {
    const { history } = this.props;
    const { menuTree = [], identityName, identityCode, description } = this.state;

    return (
      <div className="g-page page-role-detail">
        <div className="container">
          <div className="flex-box">
            <div className="title">
              角色名称<span className="must">*</span>
            </div>
            <Input className="short-inp" placeholder="请输入(不超过10个字符)" maxLength={10} value={identityName} onChange={e => this.setState({ identityName: e.target.value })} />
          </div>
          <div className="flex-box">
            <div className="title">角色代码：</div>
            <Input placeholder="请输入(不超过20个字符)" maxLength={20} value={identityCode} onChange={e => this.setState({ identityCode: e.target.value })} />
          </div>
          <div className="flex-box">
            <div className="title">角色描述</div>
            <Input placeholder="请输入(不超过20个字符)" maxLength={20} value={description} onChange={e => this.setState({ description: e.target.value })} />
          </div>
          <div className="flex-box">
            <div className="title">角色权限</div>
            <div className="menu-tree-table">
              <div className="table-title">
                <div className="menu-first">一级菜单</div>
                <div className="menu-child">
                  <div>
                    <div className="menu-second">二级菜单</div>
                    <div className="actions">操作</div>
                  </div>
                </div>
              </div>
              <div className="table-body">
                {menuTree.map((menu, menuFirstInd) => (
                  <div className="menu-item" key={menu.id}>
                    <div className="menu-first">
                      <Checkbox checked={menu.checked} onChange={e => this.menuChange(e, menuFirstInd)}>
                        {menu.name || ''}
                      </Checkbox>
                    </div>
                    <div className="menu-child">
                      {(menu.children || []).length > 0 ? (
                        menu.children.map((secondMenu, menuSecondInd) => (
                          <div key={secondMenu.id}>
                            <div className="menu-second">
                              <Checkbox checked={secondMenu.checked} onChange={e => this.menuChange(e, menuFirstInd, menuSecondInd)}>
                                {secondMenu.name || ''}
                              </Checkbox>
                            </div>
                            <div className="actions">
                              {(secondMenu.children || []).map((action, index) => (
                                <Checkbox key={action.id} checked={action.checked} onChange={e => this.menuChange(e, menuFirstInd, menuSecondInd, index)}>
                                  {action.name || ''}
                                </Checkbox>
                              ))}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="auto-height">
                          <div className="menu-second" />
                          <div className="actions">
                            {(menu.children || []).map((action, index) => (
                              <Checkbox key={action.id} checked={action.checked} onChange={e => this.menuChange(e, menuFirstInd, index)}>
                                {action.name || ''}
                              </Checkbox>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="flex-box">
            <div className="title" />
            <div>
              <Button type="primary" onClick={this.save}>
                保 存
              </Button>
              <Button onClick={() => history.goBack()}>取 消</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default connect(state => {
  return { ...state.roleMng };
})(Index);
