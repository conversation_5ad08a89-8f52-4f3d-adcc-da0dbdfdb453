.p-sampling-list {
  .edit-btn {
    display: flex;
    cursor: pointer;
    span {
      color: #3f969d;
      margin-right: 8px;
    }
  }

  .p-sampling-table {
    margin: 24px;
    padding: 24px;
    background-color: #fff;
  }
  .flex-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 16px;
  }
  .ant-form-item {
    width: 100%;
  }
  .ant-input-search-button {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .g-query-box {
    padding-bottom: 0;
  }
  .ant-btn-link {
    color: #3f969d;
  }
  .g-fold-box {
    .ant-col:nth-child(n + 3):not(:last-child) {
      display: none;
    }
  }
  .container {
    display: flex;
    flex-direction: column;
    .table-box {
      flex: 1;
      position: relative;
      .table-content {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        th,
        td {
          min-width: 88px;
          &:first-child {
            min-width: 102px;
          }
        }
      }
    }
  }
}
.remarks {
  display: inline-block;
  white-space: nowrap;
  width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.add-sampling-modal {
  .tip {
    position: absolute;
    top: 132px;
    left: 388px;
    span {
      color: red;
    }
  }
  .ant-picker,
  .ant-input-number-group-wrapper {
    width: 100%;
  }
}
.overlay {
  color: red;
}
