import React, { Component, Fragment } from 'react';
import { Tooltip, Dropdown, Button, Modal, message, Collapse, Upload, Switch } from 'antd';
import { connect } from 'dva';
import GroupHandMove from './GroupHandMove';
import GroupVisit from './GroupVisit';
import GroupTag from './GroupTag';
import GroupUser from './GroupUsers';
import * as Api from './api';
import styles from './groupoper.less';
import { getDownload as download } from '@/utils/utils';
import ConsultationOpinion from './ConsultationOpinion';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
const { Panel } = Collapse;
@connect(({ user = {} }) => {
  return { currentUser: user.currentUser };
})
@withRouter
class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      users: [],
      consultationInfo: false,
      previewImage: '',
      previewTitle: '',
      content: '',
      previewVisible: false,
      endJudge: false,
      consultationStstus: false,
      orderNo: '',
      applyTime: '',
      doctorHospitalName: '',
      doctorName: '',
      patientName: '',
      patientPid: '',
      mainIssue: '',
      nowIssue: '',
      historyIssue: '',
      familyIssue: '',
      personalIssue: '',
      marriageIssue: '',
      periodIssue: '',
      existDiagnosis: '',
      purpose: '',
      reason: '',
      fileList: '',
      patientAge: '',
      patientSex: '',
      consultationId: '',
      shouldEnd: false,
      consultationTitle: '',
      status: '',
      mainDoctor: {},
      reportCreateTime: '',
      controlFlag: 0,
      controlInfo: '已开启自由聊天',
    };
  }

  componentWillMount() {}

  componentWillReceiveProps(nextProps) {
    const { chatInfo } = nextProps;
    this.changeLive(chatInfo);
  }

  shouldComponentUpdate = nextProps => {
    const {
      chatInfo: { consultationId: nowConsultationId = null, live = '' },
    } = nextProps;
    const {
      chatInfo: { consultationId = null },
    } = this.props;
    if (nowConsultationId && nowConsultationId != consultationId) {
      this.getBaseInfo(nowConsultationId);
    } else if (!nowConsultationId && consultationId) {
      this.getBaseInfo(nowConsultationId);
    }
    return true;
  };

  componentWilllUpdate() {}

  changeLive = async chatInfo => {
    const { id = '' } = chatInfo;
    if (id != '') {
      const param = {
        groupId: id,
      };
      const { code = '', msg = '', data = '' } = await Api.getChatGroupInfo(param);
      if (code != 0) {
        message.warn(msg);
        return;
      }
      if (data != '') {
        this.setState({
          controlFlag: data,
          controlInfo: data == 0 ? '已开启自由聊天' : '已关闭自由聊天',
        });
      }
    }
  };

  queryGroupMember = async () => {
    const { chatInfo } = this.props;
    const { code, data = [] } = await Api.queryGroupMember({ groupId: chatInfo.id });
    if (code == 0) {
      this.setState({ users: data });
    }
  };

  groupUserVisableChange = visable => {
    const { users = [] } = this.state;
    if (!visable || !users.length) {
      this.queryGroupMember();
    }
  };

  createFeeChat = async () => {
    const { chatInfo = {}, onChat = {} } = this.props;
    const { code, data } = await Api.creatFeeChatForDoc(chatInfo);
    if (code == 0) {
      onChat(chatInfo);
      if (typeof window.feeChatDateOrderList === 'function') {
        // 刷新日期下订单列表，为了让用户可以正常操作聊天等
        window.feeChatDateOrderList();
      }
    }
  };

  endFeeChat = async () => {
    const { chatInfo = {}, onChat = {} } = this.props;
    const { code } = await Api.endFeeChat(chatInfo);
    if (code == 0) {
      onChat({ ...chatInfo, isEndFeeType: 1 });
    }
  };

  preEndFeeChat = () => {
    Modal.confirm({
      title: '系统提示',
      content: '是否确认结束问诊？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.endFeeChat();
      },
    });
  };

  preReadAllUserMessage = () => {
    Modal.confirm({
      title: '系统提示',
      content: '是否确认设置成已读？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.readAllUserMessage();
      },
    });
  };

  readAllUserMessage = async () => {
    const { chatInfo = {} } = this.props;
    const { code } = await Api.readAllUserMessage({ groupId: chatInfo.id });
    if (code == 0) {
      message.success('设置成功');
    }
  };

  getChatStatus = async () => {
    const { chatInfo } = this.props;
    const { controlFlag } = this.state;
    const param = {
      groupId: chatInfo.id,
      controlFlag: controlFlag == 0 ? 1 : 0,
    };
    const { code, data, msg = '' } = await Api.controlChat(param);
    if (code != 0) {
      message.destroy();
      message.warn({ content: msg, duration: 1 });
      return;
    }
    this.setState({
      controlFlag: controlFlag == 0 ? 1 : 0,
      controlInfo: controlFlag == 0 ? '已关闭自由聊天' : '已开启自由聊天',
    });
    message.destroy();
    message.success(controlFlag == 0 ? '已关闭自由聊天' : '已开启自由聊天');
  };

  refreshPatGroup = async () => {
    const { chatInfo = {} } = this.props;
    const { code } = await Api.refreshPatGroup({ pid: chatInfo.pid });
    if (code == 0) {
      message.success('刷新成功');
    }
  };

  renderFeeOper = () => {
    const { chatInfo = {} } = this.props;
    const { users = [] } = this.state;
    if (!chatInfo.account) {
      return null;
    }
    if (chatInfo.id && chatInfo.isEndFeeType == 1) {
      return null;
    }
    if (chatInfo.id) {
      return (
        <Fragment>
          <Button ghost shape="round" type="primary" size="small" onClick={this.preEndFeeChat}>
            结束问诊
          </Button>
          <Dropdown onVisibleChange={this.groupUserVisableChange} overlay={<GroupUser users={users} chatInfo={chatInfo} />} trigger={['click']}>
            <Button ghost shape="round" style={{ marginLeft: 12 }} type="primary" size="small">
              查看群成员
            </Button>
          </Dropdown>
        </Fragment>
      );
    }
    return (
      <Button ghost shape="round" type="primary" size="small" onClick={this.createFeeChat}>
        开始问诊
      </Button>
    );
  };

  renderNormalOper = () => {
    const { chatInfo = {}, getChatList } = this.props;
    const { users = [], controlFlag, controlInfo } = this.state;
    if (!chatInfo.id) {
      return null;
    }
    return (
      <Fragment>
        {/* <div data-tooltip="hello world" className={styles.chatButton}> */}
        {chatInfo.chatFilterType != '11' && (
          <Tooltip title={controlInfo}>
            <Switch
              checked={controlFlag == 0 ? true : false}
              // checked={false}
              // checkedChildren="已开启自由聊天"
              // unCheckedChildren="已关闭自由聊天"
              onChange={this.getChatStatus}
            />
          </Tooltip>
        )}
        {/* </div> */}
        <Tooltip title="群内消息已阅读">
          <div className={`${styles.chartHradBtn} ${styles.chbtn0}`} onClick={this.readAllUserMessage} />
        </Tooltip>
        <GroupHandMove chatInfo={chatInfo} getChatList={getChatList} />
        <GroupVisit chatInfo={chatInfo} />
        <GroupTag chatInfo={chatInfo} />
        <Tooltip title="查看群成员">
          <Dropdown onVisibleChange={this.groupUserVisableChange} overlay={<GroupUser users={users} chatInfo={chatInfo} />} trigger={['click']}>
            <div className={`${styles.chartHradBtn} ${styles.chbtn4}`} />
          </Dropdown>
        </Tooltip>
        {chatInfo.chatFilterType != '11' && chatInfo.chatFilterType != '12' && (
          <Tooltip title="刷新患者进周状态并转移">
            <div className={`${styles.chartHradBtn} ${styles.chbtn5}`} onClick={this.refreshPatGroup} />
          </Tooltip>
        )}
      </Fragment>
    );
  };

  getBaseInfo = async id => {
    const { setConsultationInfo } = this.props;
    if (!id) {
      setConsultationInfo({ orderNo: '', plannedTime: '', applyTime: '', id: '' });
    } else {
      const { code, data, msg = '' } = await Api.getApplyInfoAndFile({ id });
      if (code == 0) {
        const {
          reportDetail = {},
          status,
          orderNo,
          applyTime,
          doctorHospitalName,
          doctorName,
          patientName,
          patientPid,
          mainIssue,
          nowIssue,
          historyIssue,
          existDiagnosis,
          purpose,
          reason,
          fileList,
          patientVo: { patientAge, patientSex, patientId },
          plannedTime,
          doctorList,
        } = data;
        setConsultationInfo({ orderNo, plannedTime, applyTime, id });
        doctorList.map(item => {
          if (item.userType == 1) {
            this.setState({ mainDoctor: item });
          }
        });
        let imgArr = [];
        if (fileList && fileList.length > 0) {
          fileList.map((item, index) => {
            imgArr.push({ uid: index, name: item.name, url: item.url, status: 'done' });
          });
        }
        this.setState({
          orderNo,
          applyTime,
          doctorHospitalName,
          doctorName,
          patientName,
          patientPid,
          mainIssue,
          nowIssue,
          historyIssue,
          existDiagnosis,
          purpose,
          reason,
          fileList: imgArr,
          patientAge,
          patientSex,
          consultationId: id,
          status,
          patientId,
        });
      }
      const { code: buttonCode, data: buttonData } = await Api.getButtonShow({
        consultationId: id,
      });
      if (buttonCode == 0) {
        this.setState({ shouldEnd: buttonData });
      }
    }
  };

  consultationInfo = () => {
    this.setState({ consultationInfo: true });
  };

  consultationReport = async () => {
    const { consultationId, shouldEnd, content } = this.state;
    let param = { consultationStstus: false, consultationTitle: '创建会诊报告' };
    const { code, data } = await Api.queryReport({ consultationId, reportType: 1 });
    if (code == 0) {
      if (data.length > 0 && data[0].report) {
        param.content = data[0].report;
        param.consultationTitle = '查看会诊报告';
        param.consultationStstus = true;
        param.reportCreateTime = data[0].createTime;
      } else {
        if (shouldEnd) {
          param.consultationStstus = true;
        } else {
          message.destroy();
          message.warn({ content: '暂无会诊报告！', duration: 1 });
        }
      }
    }
    this.setState(param);
  };

  printEvent = () => {
    const title = window.document.title;
    window.document.title = '';
    const el = document.getElementById('consultationReport');
    const iframe = document.createElement('IFRAME');
    let doc = null;
    document.body.appendChild(iframe);
    doc = iframe.contentWindow.document;
    doc.write(el.innerHTML);
    doc.close();
    iframe.contentWindow.focus();
    iframe.contentWindow.print();
    // if (navigator.userAgent.indexOf("MSIE") > 0) {
    document.body.removeChild(iframe);
    // }
    window.document.title = title;
  };

  renderConsultation = () => {
    const { chatInfo = {}, getChatList } = this.props;
    const { users = [], shouldEnd, status } = this.state;
    if (!chatInfo.id) {
      return null;
    }
    return (
      <Fragment>
        <Tooltip title="会诊申请详情">
          <div className={`${styles.chartHradBtn} ${styles.chbtn6}`} onClick={this.consultationInfo} />
        </Tooltip>
        <Tooltip title="查看群成员">
          <Dropdown onVisibleChange={this.groupUserVisableChange} overlay={<GroupUser users={users} chatInfo={chatInfo} />} trigger={['click']}>
            <div className={`${styles.chartHradBtn} ${styles.chbtn4}`} />
          </Dropdown>
        </Tooltip>
        <Tooltip title="会诊报告">
          <div className={`${styles.chartHradBtn} ${styles.chbtn2}`} onClick={this.consultationReport} />
        </Tooltip>
        {shouldEnd && status == '2' && (
          <Tooltip title="取消会诊">
            <div className={`${styles.chartHradBtn} ${styles.chbtn7}`} onClick={() => this.handleConsultation(true)} />
          </Tooltip>
        )}
        {shouldEnd && status == '2' && (
          <Tooltip title="结束会诊">
            <div className={`${styles.chartHradBtn} ${styles.chbtn8}`} onClick={() => this.handleConsultation(false)} />
          </Tooltip>
        )}
      </Fragment>
    );
  };

  updateChatList = async val => {
    let buttonDom = document.getElementsByClassName(' ant-tabs-tab');
    if (val === 'end') {
      buttonDom[1].click();
    } else {
      buttonDom[2].click();
    }
  };

  handleConsultation = val => {
    const { consultationId } = this.state;
    let content = '';
    if (val) {
      content = '确定取消会诊？';
    } else {
      content = '确定结束会诊？结束后讲不能再继续互动';
    }
    Modal.confirm({
      title: '提示',
      content: content,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        if (val) {
          const { code } = await Api.cancelChat({ consultationId, cancelType: 1 });
          if (code == 0) {
            message.success({ content: '取消会诊成功！', duration: 1 });
            this.updateChatList('cancle');
          }
        } else {
          const { code } = await Api.cancelChat({ consultationId, cancelType: 0 });
          if (code == 0) {
            message.success({ content: '结束会诊成功！', duration: 1 });
            this.updateChatList('end');
          }
        }
      },
    });
  };

  renderOper = () => {
    const { chatInfo = {} } = this.props;
    if (chatInfo.chatFilterType === '7') {
      return null;
    }
    if (chatInfo.chatFilterType === '6') {
      return this.renderFeeOper();
    }
    if (chatInfo.chatFilterType === '4') {
      return null;
    }
    if (chatInfo.chatFilterType === '10') {
      return this.renderConsultation();
    }
    return this.renderNormalOper();
  };

  //图片转base64
  getBase64 = file => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };

  downloadPic = file => {
    if (!file.url && !file.preview) {
      file.preview = this.getBase64(file.originFileObj);
    }
    download(file.url || file.preview);
  };

  handlePreview = file => {
    if (!file.url && !file.preview) {
      file.preview = this.getBase64(file.originFileObj);
    }

    this.setState({
      previewImage: file.url || file.preview,
      previewVisible: true,
      previewTitle: file.name || file.url.substring(file.url.lastIndexOf('/') + 1),
    });
  };

  handleCancel = () => this.setState({ previewVisible: false });

  cancelReport = async (val, content) => {
    const { consultationId } = this.state;
    if (val) {
      const { code, data } = await Api.addReport({ content, consultationId, reportType: 1 });
      if (code == 0) {
        message.success({ content: '会诊报告填写成功！', duration: 1 });
      }
    }
    this.setState({
      consultationStstus: false,
    });
  };

  render() {
    const {
      patientId,
      orderNo,
      mainDoctor,
      applyTime,
      doctorHospitalName,
      doctorName,
      patientName,
      patientPid,
      mainIssue,
      nowIssue,
      historyIssue,
      familyIssue,
      personalIssue,
      marriageIssue,
      periodIssue,
      existDiagnosis,
      purpose,
      reason,
      fileList,
      content,
      reportCreateTime,
      consultationTitle,
      consultationInfo,
      previewVisible,
      previewImage,
      previewTitle,
      consultationStstus,
      patientAge,
      patientSex,
    } = this.state;
    const { chatInfo } = this.props;
    const upLoadprops = {
      showUploadList: {
        downloadIcon: 'download ',
        showRemoveIcon: false,
        showDownloadIcon: true,
      },
    };
    return (
      <div
        style={{
          paddingLeft: chatInfo.chatFilterType == '10' ? 0 : '20px',
          display: 'flex',
          alignItems: 'center',
        }}
        className={styles.chartHradOper}
      >
        {this.renderOper()}
        {!content && <ConsultationOpinion onCancel={this.cancelReport} status={consultationStstus} title={consultationTitle} name={'会诊报告'} content={content} />}
        {content && (
          <Modal
            title=""
            visible={consultationStstus}
            onCancel={() => this.setState({ consultationStstus: false, content: '' })}
            width={'50%'}
            footer={
              <>
                <Button
                  type="primary"
                  onClick={() => {
                    this.printEvent();
                  }}
                >
                  打印
                </Button>
                <Button onClick={() => this.setState({ consultationStstus: false, content: '' })}>关闭</Button>
              </>
            }
            style={{ padding: '15px 40px' }}
          >
            <div ref={el => (this.refs = el)} id="consultationReport">
              <h2 style={{ textAlign: 'center' }}>会诊结果单</h2>
              <div
                style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  borderTop: '1px solid #000',
                  borderBottom: '1px solid #000',
                  margin: '17px 0',
                  padding: '10px 0',
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', flex: 1, margin: '10px 0' }}>
                  <span style={{ whiteSpace: 'nowrap' }}>姓名：</span>
                  <div style={{ width: '120px', borderBottom: '1px solid #000', textAlign: 'center' }}>{patientName}</div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', flex: 1, margin: '10px 0' }} className={styles.baseItem}>
                  <span style={{ whiteSpace: 'nowrap' }}>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;性别：</span>
                  <div style={{ width: '120px', borderBottom: '1px solid #000', textAlign: 'center' }} className={styles.content}>
                    {patientSex == 'M' ? '男' : '女'}
                  </div>
                </div>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    flex: 1,
                    margin: '10px 0',
                    justifyContent: 'flex-end',
                  }}
                  className={styles.baseItem}
                >
                  <span style={{ whiteSpace: 'nowrap' }}>年龄：</span>
                  <div style={{ width: '120px', borderBottom: '1px solid #000', textAlign: 'center' }} className={styles.content}>
                    {patientAge}
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', flex: 1, margin: '10px 0' }} className={styles.baseItem}>
                  <span style={{ letterSpacing: '0.12em' }}>PID：</span>
                  <div style={{ width: '120px', borderBottom: '1px solid #000', textAlign: 'center' }} className={styles.content}>
                    {patientId}
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', flex: 2, margin: '10px 0' }} className={styles.baseItem}>
                  <span style={{ whiteSpace: 'nowrap' }}>会诊单号：</span>
                  <div style={{ width: '120px', borderBottom: '1px solid #000', textAlign: 'center' }} className={styles.content}>
                    {orderNo}
                  </div>
                </div>
              </div>
              <div>
                <div style={{ paddingBottom: '10px' }}>临床信息：</div>
                <div className={styles.clinicInfo}>
                  <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                    <span style={{ letterSpacing: '0.88em' }}>主诉：</span>
                    {mainIssue}
                  </div>
                  <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                    <span style={{ letterSpacing: '0.26em' }}>现病史：</span>
                    {nowIssue}
                  </div>
                  <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                    <span style={{ letterSpacing: '0.26em' }}>既往史：</span>
                    {historyIssue}
                  </div>
                  <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                    <span style={{ letterSpacing: '0.26em' }}>家族史：</span>
                    {familyIssue}
                  </div>
                  <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                    <span style={{ letterSpacing: '0.26em' }}>个人史：</span>
                    {personalIssue}
                  </div>
                  <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                    <span style={{ letterSpacing: '0.26em' }}>婚育史：</span>
                    {marriageIssue}
                  </div>
                  <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                    <span style={{ letterSpacing: '0.26em' }}>月经史：</span>
                    {periodIssue}
                  </div>
                  <div style={{ padding: '6px 15px' }} className={styles.clickItem}>
                    <span>现有诊断：</span>
                    {existDiagnosis}
                  </div>
                </div>
                <div>
                  <div style={{ padding: '15px 0 10px 0' }}>会诊报告：</div>
                  <div className={styles.clinicInfo} style={{ padding: '10px 15px' }}>
                    {content}
                  </div>
                </div>
                <div
                  style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    borderTop: '1px solid #000',
                    border: 0,
                  }}
                  className={styles.baseInfo}
                >
                  <div style={{ display: 'flex', alignItems: 'center', flex: 1, margin: '10px 0' }} className={styles.baseItem}>
                    <span>报告医师：</span>
                    <div
                      style={{
                        width: '120px',
                        borderBottom: '1px solid #000',
                        textAlign: 'center',
                        flex: 0.8,
                      }}
                      className={styles.content}
                    >
                      {mainDoctor.allianceName + '-' + mainDoctor.name}
                    </div>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', flex: 1, margin: '10px 0' }} className={styles.baseItem}>
                    <span>报告时间：</span>
                    <div
                      style={{
                        width: '120px',
                        borderBottom: '1px solid #000',
                        textAlign: 'center',
                        flex: 0.8,
                      }}
                      className={styles.content}
                    >
                      {moment(reportCreateTime).format('YYYY-MM-DD hh:mm:ss')}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Modal>
        )}
        <Modal
          title="会诊申请详情"
          visible={consultationInfo}
          onCancel={() => this.setState({ consultationInfo: false })}
          width={'50%'}
          footer={
            <Button type="primary" onClick={() => this.setState({ consultationInfo: false })}>
              关闭
            </Button>
          }
          style={{ padding: '15px 40px' }}
        >
          <div className={styles.infoRow}>
            <span>申请单号:</span>
            <div>{orderNo}</div>
          </div>
          <div className={styles.infoRow}>
            <span>申请时间:</span>
            <div>{applyTime}</div>
          </div>
          <div className={styles.infoRow}>
            <span style={{ letterSpacing: '0.3em' }}>邀请方:</span>
            <div>{`${doctorHospitalName} ${doctorName}`}</div>
          </div>
          <div className={styles.infoRow}>
            <span>患者信息:</span>
            <div>{`${patientName} ${patientAge}岁 ${patientSex == 'M' ? '男' : '女'}`}</div>
          </div>
          <Collapse className={styles.collapse} defaultActiveKey={['1']} expandIconPosition="right" bordered={false}>
            <Panel header="患者详情" key="1">
              <div className={styles.infoRow}>
                <span style={{ letterSpacing: '0.7em' }}>主诉:</span>
                <div>{mainIssue}</div>
              </div>
              <div className={styles.infoRow}>
                <span style={{ letterSpacing: '0.3em' }}>现病史:</span>
                <div>{nowIssue}</div>
              </div>
              <div className={styles.infoRow}>
                <span style={{ letterSpacing: '0.3em' }}>既往史:</span>
                <div>{historyIssue}</div>
              </div>
              <div className={styles.infoRow}>
                <span>现有诊断:</span>
                <div>{existDiagnosis}</div>
              </div>
              <div className={styles.infoRow}>
                <span>会诊目的:</span>
                <div>{purpose}</div>
              </div>
              <div className={styles.infoRow}>
                <span>会诊原因:</span>
                <div>{reason}</div>
              </div>
              <div className={styles.infoRow}>
                <span>附件</span>
                <Upload {...upLoadprops} listType="picture-card" fileList={fileList} onPreview={this.handlePreview} onDownload={this.downloadPic}></Upload>
              </div>
              <Modal visible={previewVisible} title={previewTitle} footer={null} onCancel={this.handleCancel}>
                <img alt="example" style={{ width: '100%' }} src={previewImage} />
              </Modal>
            </Panel>
          </Collapse>
        </Modal>
      </div>
    );
  }
}

export default Index;
