import React from 'react';
import { message, Input, Upload, Radio, Checkbox, Cascader, Button } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import TinymceEditor from '../../../../components/editor/TinymceEditor';
import Core from '../../../../components/clip/Index';

import './style.less';

import * as CONSTANT from '../../../../config/constant/constant';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;

export default class DoctorEdit extends React.Component {
  state = {};

  closeDrawer = () => {};

  validateHandler = (rule, value, callback) => {
    const { field } = rule;
    let content = '';
    switch (field) {
      case 'img':
        if (value) {
          const image = new Image(); // eslint-disable-line
          image.src = value;
          if (!image.width || image.width == 0 || !image.height || image.height == 0) {
            content = '图片资源无法加载或加载过慢!';
          } else {
            // const ratio = image.width / image.height;
            // if (ratio > 0.99 || ratio < 0.79) {
            //   content = '图片尺寸比例与推荐尺寸102*114相差过大!';
            // }
          }
        }
        break;
      case 'name':
        if (!value || value == '') {
          content = '请输入医生姓名!';
        } else if (!/^[\s\S]{1,20}$/.test(value)) {
          content = '最多输入20个非空字符!';
        }
        break;
      case 'no':
        if (!value || value == '') {
          content = '请输入医生编号!';
        } else if (!/^[\S]{1,50}$/.test(value)) {
          content = '最多输入50个非空字符!';
        }
        break;
      case 'title':
        if (!value || value == '') {
          content = '请输入医生职称!';
        } else if (!/^[\s\S]{1,30}$/.test(value)) {
          content = '最多输入30个非空字符!';
        }
        break;
      case 'newDeptNo':
        if (!value || value == '' || value.length == 0) {
          content = '请选择所属科室!';
        }
        break;
      case 'skill':
        if (!value || value == '') {
          content = '请输入医生擅长!';
        } else if (!/^[\s\S]{1,500}$/.test(value)) {
          content = '最多输入500个字符!';
        }
        break;
      case 'summary':
        if (!value || value == '') {
          content = '请输入医生介绍!';
        } else if (!/^[\s\S]{1,30000}$/.test(value)) {
          content = '最多输入30000个字符!';
        }
        break;
      default:
        break;
    }
    if (content && content != '') {
      callback(content);
    } else {
      callback();
    }
  };

  submitDoctorDetail = () => {
    const { form, dispatch } = this.props;
    const { detail } = this.state;
    const { validateFieldsAndScroll } = form;
    validateFieldsAndScroll((err, values) => {
      if (!err) {
        const newDeptNo = values.newDeptNo && values.newDeptNo[values.newDeptNo.length - 1];
        dispatch({
          type: 'microwebsite/submitDoctorDetail',
          payload: {
            ...values,
            method: this.state.type,
            hisId: this.state.hisId,
            newDeptNo,
            deptNo: detail.deptNo || '',
            listQueryParam: this.state.listQueryParam,
          },
        });
      }
    });
  };

  beforeUpload = file => {
    const isImage = file.type && file.type.indexOf('image') > -1;
    if (!isImage) {
      message.error('请选择图片进行上传!');
    }
    const size = file.size / 1024 <= 500;
    if (!size) {
      message.error('图片大小不能超过500K!');
    }
    return isImage && size;
  };

  uploadOnChange = e => {
    const { setFieldsValue } = this.props.form;
    const { response } = e.file;
    if (response) {
      let img = '';
      if (response.code != 0) {
        message.error('上传文件失败');
      } else {
        img = (response.data && response.data.url) || '';
      }
      setFieldsValue({
        img,
      });
      this.setState({
        detail: {
          ...this.state.detail,
          img,
        },
      });
    }
  };

  render() {
    const { detail: doctor, type, hisId } = this.state;
    const { form, deptTree = [] } = this.props;
    const { getFieldDecorator, setFieldsValue } = form;
    return (
      <div className="doctor-drawer">
        <div className="doctor-edit-head">
          <div className="edit-title">{type == 'new' ? '添加医生' : '编辑医生'}</div>
          <Icon
            onClick={() => {
              this.closeDrawer();
            }}
            type="close"
            style={{ color: '#919191', fontSize: 16, cursor: 'pointer' }}
          />
        </div>
        <div className="doctor-edit-body">
          <Form>
            {getFieldDecorator('level', {
              initialValue: `${doctor.level || '1'}`,
            })(<Input style={{ display: 'none' }} />)}
            <div className="horizontal-line" style={{ margin: '0 0 25px 48px' }}>
              <div className="horizontal-title">基本信息</div>
            </div>
            <FormItem label="医生头像" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
              <Core defaultUrl={doctor.img} afterUpload={img => this.props.form.setFieldsValue({ img })} aspectRatio={5 / 7} operSize={{ width: '200px', height: '280px' }} />
              {getFieldDecorator('img', {
                initialValue: doctor.img || '',
              })(<Input style={{ display: 'none' }} />)}
            </FormItem>
            <FormItem label="医生姓名" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
              {getFieldDecorator('name', {
                rules: [{ required: true, validator: this.validateHandler }],
                initialValue: doctor.name || '',
              })(<Input style={{ width: 347 }} placeholder="请输入医生姓名" />)}
            </FormItem>
            <FormItem label="医生性别" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
              {getFieldDecorator('sex', {
                rules: [{ required: true, validator: this.validateHandler }],
                initialValue: `${doctor.sex || 'M'}`,
              })(
                <RadioGroup>
                  <Radio value="M">男</Radio>
                  <Radio value="F">女</Radio>
                </RadioGroup>,
              )}
            </FormItem>
            <FormItem label="医生编号" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
              {getFieldDecorator('no', {
                rules: [{ required: true, validator: this.validateHandler }],
                initialValue: `${doctor.no || ''}`,
              })(<Input style={{ width: 347 }} disabled={type != 'new'} placeholder="请输入医生编号" />)}
            </FormItem>
            <FormItem label="所属科室" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
              {getFieldDecorator('newDeptNo', {
                rules: [{ required: true, validator: this.validateHandler }],
                initialValue: doctor.deptNoList || [],
              })(<Cascader style={{ width: 347 }} placeholder="请选择所属科室" options={deptTree} changeOnSelect showSearch />)}
            </FormItem>
            <FormItem label="医生职称" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
              {getFieldDecorator('title', {
                rules: [{ required: true, validator: this.validateHandler }],
                initialValue: doctor.title || '',
              })(<Input style={{ width: 232 }} placeholder="请输入医生职称" />)}
              <Checkbox
                style={{ marginLeft: 19 }}
                onChange={e => {
                  setFieldsValue({
                    level: e.target.checked ? '2' : '1',
                  });
                }}
                defaultChecked={doctor.level == 2}
              >
                专家
              </Checkbox>
            </FormItem>
            <div className="horizontal-line" style={{ margin: '33px 0 19px 48px' }}>
              <div className="horizontal-title">医生擅长</div>
            </div>
            <FormItem style={{ marginLeft: 48 }}>
              {getFieldDecorator('skill', {
                rules: [{ required: true, validator: this.validateHandler }],
                initialValue: (type == 'modify' && doctor.skill) || '',
              })(<Input.TextArea autosize={{ minRows: 6, maxRows: 8 }} />)}
            </FormItem>
            <div className="horizontal-line" style={{ margin: '0 0 19px 48px' }}>
              <div className="horizontal-title">医生介绍</div>
            </div>
            <FormItem style={{ marginLeft: 48 }}>
              <TinymceEditor
                id={`edit-doctor-summary-${doctor.no}-editor`}
                content={(type == 'modify' && doctor.summary) || ' '}
                height="158"
                url={`${CONSTANT.DOMAIN}/api/his/upfile?hisId=${hisId}`}
                onChange={summary => {
                  setFieldsValue({ summary });
                }}
              />
              {getFieldDecorator('summary', {
                rules: [{ required: true, validator: this.validateHandler }],
                initialValue: (type == 'modify' && doctor.summary) || '',
              })(<Input style={{ display: 'none' }} />)}
            </FormItem>
            <div className="horizontal-line" style={{ marginLeft: 48 }}>
              <FormItem>
                <Button type="primary" onClick={this.submitDoctorDetail}>
                  保存
                </Button>
                <Button
                  size="default"
                  style={{ marginLeft: 16 }}
                  onClick={() => {
                    this.closeDrawer();
                  }}
                >
                  取消
                </Button>
              </FormItem>
            </div>
          </Form>
        </div>
      </div>
    );
  }
}
