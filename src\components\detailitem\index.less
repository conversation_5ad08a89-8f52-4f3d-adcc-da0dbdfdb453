.comDetailItem {
  .title {
    color: rgba(0, 0, 0, 0.9);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 24px;
  }

  .label {
    color: rgba(0, 0, 0, 0.43);
    font-size: 12px;
  }
  .detailItemContent {
    color: #404040;
    font-size: 12px;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    // flex: 1;
  }
  .resOrder {
    margin-left: 50px;
  }

  :global {
    .ant-col {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .ant-divider {
      margin: 8px 0 24px 0;
    }
  }
}
