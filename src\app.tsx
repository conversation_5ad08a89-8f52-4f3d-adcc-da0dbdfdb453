// 运行时配置文件，可以在这里扩展运行时的能力，比如修改路由、修改 render 方法等
import React from 'react';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import 'moment/locale/zh-cn';

export function rootContainer(container: any) {
  // 配置中文环境
  return React.createElement(ConfigProvider, { locale: zhCN }, container);
}

export const dva = {
  config: {
    onError(err: Error) {
      console.error(err.message);
    },
  },
};
