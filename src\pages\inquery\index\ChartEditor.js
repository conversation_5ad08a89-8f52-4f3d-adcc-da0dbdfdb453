import React, { Component, Fragment } from 'react';
import { Upload, message, Tooltip, Modal } from 'antd';
import ImageCompressor from 'image-compressor.js';
import BraftEditor from 'braft-editor';
import { getDefaultKeyBinding, KeyBindingUtil } from 'draft-js';
import { ContentUtils } from 'braft-utils';
import AddChatUser from './AddChatUser';
import ChatHistory from './MsgHistory';
import FastWay from './FastWay';
import Emoji from './Emoji';
import * as Api from './api';
import 'braft-editor/dist/index.css';
import styles from './charteditor.less';

class Index extends Component {
  constructor(prop) {
    super(prop);

    this.state = {
      editorState: BraftEditor.createEditorState(null),
      showEmoji: false,
      webRtcStatus: 'end',
      rtcSigOptions: {},
      rtcRoomOptions: {},
      showAddUser: false,
      showMsgHistory: false,
      editorDisable: false,
      showExitModal: false,
      status: '',
      editConfig: [
        {
          key: 'my-emoji', // 控件唯一标识，必传
          type: 'button',
          title: '表情', // 指定鼠标悬停提示文案
          className: '', // 指定按钮的样式名
          html: null, // 指定在按钮中渲染的html字符串
          text: <div className={styles.iconEmoji} />, // 指定按钮文字，此处可传入jsx，若已指定html，则text不会显示
          onClick: () => {
            this.setState({ showEmoji: !this.state.showEmoji });
          },
        },
        {
          key: 'fast-question',
          type: 'modal',
          title: '快捷提问', // 指定鼠标悬停提示文案
          className: 'my-modal', // 指定触发按钮的样式名
          html: null, // 指定在按钮中渲染的html字符串
          text: <div className={styles.iconQuestion} />, // 指定按钮文字，此处可传入jsx，若已指定html，则text不会显示
          onClick: () => {}, // 指定触发按钮点击后的回调函数
          modal: {
            id: 'edit-question-modal', // 必选属性，传入一个唯一字符串即可
            title: '快捷提问', // 指定弹窗组件的顶部标题
            className: 'my-modal', // 指定弹窗组件样式名
            width: 500, // 指定弹窗组件的宽度
            height: 500, // 指定弹窗组件的高度
            showFooter: false, // 指定是否显示弹窗组件底栏
            showCancel: true, // 指定是否显示取消按钮
            showConfirm: true, // 指定是否显示确认按钮
            confirmable: true, // 指定确认按钮是否可用
            showClose: true, // 指定是否显示右上角关闭按钮
            closeOnBlur: true, // 指定是否在点击蒙层后关闭弹窗(v2.1.24)
            closeOnConfirm: true, // 指定是否在点击确认按钮后关闭弹窗(v2.1.26)
            closeOnCancel: true, // 指定是否在点击取消按钮后关闭弹窗(v2.1.26)
            cancelText: '取消', // 指定取消按钮文字
            confirmText: '确定', // 指定确认按钮文字
            bottomText: null, // 指定弹窗组件底栏左侧的文字，可传入jsx
            onConfirm: () => {}, // 指定点击确认按钮后的回调函数
            onCancel: () => {}, // 指定点击取消按钮后的回调函数
            onClose: () => {}, // 指定弹窗被关闭后的回调函数
            onBlur: () => {}, // 指定蒙层被点击时的回调函数
            onCreate: modalInstance => {
              this.editExtendQuestionModal = modalInstance;
            },
            children: <FastWay cb={this.fastQuestionCallBack} type="question" />, // 指定弹窗组件的内容组件
          },
        },
        {
          key: 'fast-answer',
          type: 'modal',
          title: '快捷回复', // 指定鼠标悬停提示文案
          className: 'my-modal', // 指定触发按钮的样式名
          html: null, // 指定在按钮中渲染的html字符串
          text: <div className={styles.iconAnswer} />, // 指定按钮文字，此处可传入jsx，若已指定html，则text不会显示
          onClick: () => {}, // 指定触发按钮点击后的回调函数
          modal: {
            id: 'edit-answer-modal', // 必选属性，传入一个唯一字符串即可
            title: '快捷回复', // 指定弹窗组件的顶部标题
            className: 'my-modal', // 指定弹窗组件样式名
            width: 800, // 指定弹窗组件的宽度
            height: 500, // 指定弹窗组件的高度
            showFooter: false, // 指定是否显示弹窗组件底栏
            showCancel: true, // 指定是否显示取消按钮
            showConfirm: true, // 指定是否显示确认按钮
            confirmable: true, // 指定确认按钮是否可用
            showClose: true, // 指定是否显示右上角关闭按钮
            closeOnBlur: true, // 指定是否在点击蒙层后关闭弹窗(v2.1.24)
            closeOnConfirm: true, // 指定是否在点击确认按钮后关闭弹窗(v2.1.26)
            closeOnCancel: true, // 指定是否在点击取消按钮后关闭弹窗(v2.1.26)
            cancelText: '取消', // 指定取消按钮文字
            confirmText: '确定', // 指定确认按钮文字
            bottomText: null, // 指定弹窗组件底栏左侧的文字，可传入jsx
            onConfirm: () => {}, // 指定点击确认按钮后的回调函数
            onCancel: () => {}, // 指定点击取消按钮后的回调函数
            onClose: () => {}, // 指定弹窗被关闭后的回调函数
            onBlur: () => {}, // 指定蒙层被点击时的回调函数
            onCreate: modalInstance => {
              this.editExtendAnswerModal = modalInstance;
            },
            children: <FastWay cb={this.fastAnswerCallBack} type="answer" />, // 指定弹窗组件的内容组件
          },
        },
        {
          key: 'upload', // 控件唯一标识，必传
          type: 'button',
          title: '发送图片或文件', // 指定鼠标悬停提示文案
          html: null, // 指定在按钮中渲染的html字符串
          text: (
            <Upload
              action={`${API_DOMAIN}/api/files/uploadpic`}
              data={{
                serviceType: 'medicalprint',
                needCompress: false,
                isPte: true,
                expiredSeconds: 30000,
              }}
              name="upfile"
              beforeUpload={file => {
                this.uploadEditorImg({ file });
                return false;
              }}
              showUploadList={false}
            >
              <div className={styles.iconUpload} />
            </Upload>
          ),
          onClick: () => {},
        },
        {
          key: 'add-user', // 控件唯一标识，必传
          type: 'button',
          title: '添加用户', // 指定鼠标悬停提示文案
          className: 'iconAdduser', // 指定按钮的样式名
          html: null, // 指定在按钮中渲染的html字符串
          text: <div className={styles.iconAdduser} />, // 指定按钮文字，此处可传入jsx，若已指定html，则text不会显示
          onClick: () => {
            this.setState({ showAddUser: true });
          },
        },
        {
          key: 'msg-history', // 控件唯一标识，必传
          type: 'button',
          title: '消息记录', // 指定鼠标悬停提示文案
          className: 'iconMsgHistory', // 指定按钮的样式名
          html: null, // 指定在按钮中渲染的html字符串
          text: <div className={styles.iconMsgHistory} />, // 指定按钮文字，此处可传入jsx，若已指定html，则text不会显示
          onClick: this.showMsgHistory,
        },
        {
          key: 'video-switch', // 控件唯一标识，必传
          type: 'button',
          title: '视频问诊', // 指定鼠标悬停提示文案
          className: 'videoSwitch', // 指定按钮的样式名
          html: null, // 指定在按钮中渲染的html字符串
          text: <div className={styles.iconVideo} />, // 指定按钮文字，此处可传入jsx，若已指定html，则text不会显示
          onClick: () => {
            this.initiateCall();
          },
        },
        {
          key: 'video-reback', // 控件唯一标识，必传
          type: 'button',
          title: '视频复位', // 指定鼠标悬停提示文案
          className: 'iconReback', // 指定按钮的样式名
          html: null, // 指定在按钮中渲染的html字符串
          text: <div className={styles.iconReback} />, // 指定按钮文字，此处可传入jsx，若已指定html，则text不会显示
          onClick: () => {
            window.resetRtcPosition();
          },
        },
        {
          key: 'exit-chart', // 控件唯一标识，必传
          type: 'button',
          title: '退出群聊', // 指定鼠标悬停提示文案
          className: 'iconExit', // 指定按钮的样式名
          html: null, // 指定在按钮中渲染的html字符串
          text: <div className={styles.iconExit} />, // 指定按钮文字，此处可传入jsx，若已指定html，则text不会显示
          onClick: () => {
            this.setState({ showExitModal: true });
          },
        },
        'media',
      ],
    };
    this.replacePool = [
      {
        key: '<',
        value: '&lt;',
      },
      {
        key: '>',
        value: '&gt;',
      },
      {
        key: '&quot;',
        value: '"',
      },
      {
        key: '&apos;',
        value: "'",
      },
    ];
  }

  componentDidMount() {
    const { chatInfo = {} } = this.props;
    const { editConfig } = this.state;
    const {
      chatInfo: { consultationId = null },
    } = this.props;
    if (consultationId) {
      this.getConsultationStatus(consultationId);
    }
    if (chatInfo.chatFilterType === '11') {
      let newEditConfig = [];
      editConfig.map(item => {
        if (item.key != 'add-user' && item.key != 'video-switch' && item.key != 'video-reback') {
          newEditConfig.push(item);
        }
      });
      this.setState({
        editConfig: newEditConfig,
      });
    }
    // if (this.editorInstance) {
    //   if (c.id) {
    //     if (
    //       chatInfo.chatFilterType !== '6' ||
    //       (chatInfo.chatFilterType === '6' && chatInfo.isEndFeeType != 1)
    //     ) {
    //       this.editorInstance.getDraftInstance().focus();
    //     }
    //   }
    // }
    // this.getVideoStatus();
  }

  getConsultationStatus = async consultationId => {
    const { code, data, msg = '' } = await Api.getApplyInfoAndFile({ id: consultationId });
    console.log('订单状态接口调用了1111', data);
    if (code == 0) {
      const { status } = data;
      if (status == 3 || status == 6) {
        this.setState({ editorDisable: true, status });
      }
    }
  };

  componentWillUnmount() {
    this.clearChartEditorTimer();
    this.isDestoryComponet = true;
  }

  clearChartEditorTimer = () => {
    clearTimeout(this.videoStatusTimer);
  };

  submitContent = async (param, noClearContent) => {
    // 在编辑器获得焦点时按下ctrl+s会执行此方法
    // 编辑器内容提交到服务端之前，可直接调用editorState.toHTML()来获取HTML格式的内容
    const { groupId } = this.props;
    const { code, data = {}, msg } = await Api.sendMsg({ ...param, groupId });
    if (code != 0) {
      message.error(msg || '发送失败');
    } else {
      const { getMessage, getChatList } = this.props;
      if (typeof getMessage === 'function') {
        getMessage({ getNewMsg: true });
      }
      if (typeof getChatList === 'function') {
        getChatList();
      }
      if (!noClearContent) {
        this.editorInstance.clearEditorContent();
      }
    }
  };

  saveContent = () => {
    const { editorState } = this.state;
    let sendType = 1;
    let htmlContent = editorState.toText() || '';
    const justEmojReg = /\\em(\d{1,2});$/;
    if (justEmojReg.test(htmlContent.trim())) {
      sendType = 5;
      // htmlContent = htmlContent.replace(justEmojReg, 'https://zxxy.lockai.cn/merchant/umi/emoji/emijo-$1.png');
      htmlContent = htmlContent.replace(justEmojReg, 'https://hlwyy.zxxyyy.cn/merchant/umi/emoji/emijo-$1.png');
    } else {
      // htmlContent = htmlContent.replace(/\\em(\d{1,2});/g, '<img src="https://zxxy.lockai.cn/merchant/umi/emoji/emijo-$1.png" style="width:32px;margin:0 5px;" />');
      htmlContent = htmlContent.replace(/\\em(\d{1,2});/g, '<img src="https://hlwyy.zxxyyy.cn/merchant/umi/emoji/emijo-$1.png" style="width:32px;margin:0 5px;" />');
      this.replacePool.forEach(item => {
        htmlContent = htmlContent.replace(new RegExp(item.key, 'ig'), item.value);
      });
    }
    if (!htmlContent) {
      this.editorInstance.clearEditorContent();
    } else {
      this.submitContent({ type: sendType, content: htmlContent });
    }
  };

  handleEditorChange = editorState => {
    this.setState({ editorState });
  };

  fastQuestionCallBack = str => {
    const { editorState } = this.state;
    this.setState(
      {
        editorState: ContentUtils.insertText(editorState, str),
      },
      () => this.editorInstance.getDraftInstance().focus(),
    );
    this.editExtendQuestionModal.close();
  };

  fastAnswerCallBack = str => {
    const { editorState } = this.state;
    this.setState(
      {
        editorState: ContentUtils.insertText(editorState, str),
      },
      () => this.editorInstance.getDraftInstance().focus(),
    );
    this.editExtendAnswerModal.close();
  };

  uploadEditorImg = async param => {
    let file = param.file;
    if (window.NetEnv === 'out') {
      while (file.size > 1024 * 1024) {
        file = await this.compressImg(file);
      }
    }
    if (!file) {
      return false;
    }
    const formData = new FormData();
    formData.append('upfile', file, param.file.name);
    formData.append('serviceType', 'medicalprint');
    formData.append('needCompress', false);
    formData.append('isPte', false);
    const hide = message.loading('正在上传...', 0);
    const { code, data = {} } = await Api.uploadFile(formData);
    hide();
    if (code == 0) {
      const { url } = data;
      this.submitContent({ type: 3, content: url });
    }
  };

  compressImg = selectedFile => {
    return new Promise((resove, reject) => {
      new ImageCompressor(selectedFile, {
        maxWidth: 500,
        minHeight: 500,
        quality: 0.8,
        success: result => {
          resove(result);
        },
        error(e) {
          reject(false);
          throw '图片压缩异常';
        },
      });
    });
  };

  emojiSelect = emKey => {
    const { showEmoji, editorState } = this.state;
    this.setState(
      {
        showEmoji: !showEmoji,
        editorState: ContentUtils.insertText(editorState, `\\em${emKey};`),
      },
      () => this.editorInstance.getDraftInstance().focus(),
    );
  };

  hideEmoji = () => {
    const { showEmoji } = this.state;
    if (showEmoji) {
      this.setState({ showEmoji: false });
    }
  };

  initiateCall = () => {
    this.getRtcSig();
  };

  getRtcSig = async () => {
    clearTimeout(this.videoStatusTimer);
    const { groupId } = this.props;
    const { code, data: res = {} } = await Api.getRtcSig({ expire: 7200, groupId });
    if (code == 0) {
      const { code: statusCode, data: statusData = {} } = await Api.getVideoStatus({ groupId });
      if (statusCode == 0 && statusData && statusData.status !== 'live') {
        const { code: reqCode } = await Api.startVideo({ groupId });
        if (reqCode !== 0) {
          message.error('发起视频失败');
          return false;
        }
      }
      this.setState({
        rtcSigOptions: {
          userId: res.userId,
          sdkAppId: res.sdkappid,
          userSig: res.userSig,
        },
        rtcRoomOptions: {
          roomId: res.roomId,
          privateMapKey: res.privMapEncrypt,
        },
        webRtcStatus: 'live',
      });
      window.Trtc.startTrtc({
        userId: res.userId,
        sdkAppId: res.sdkappid,
        userSig: res.userSig,
        roomId: res.roomId,
        privateMapKey: res.privMapEncrypt,
        onTrtcEnd: this.endVideo,
      });
      this.getVideoStatus();
    }
  };

  endVideo = async () => {
    // clearTimeout(this.videoStatusTimer);
    // message.warning('对方已挂断，本次视频结束');
    message.warning('视频已挂断，本次视频结束');
    const { groupId } = this.props;
    this.setState({ rtcSigOptions: {}, rtcRoomOptions: {}, webRtcStatus: 'end' });
    const { code, msg = '' } = await Api.endVideo({ groupId, notShowError: true });
    if (code == 0 || msg.indexOf('视频不存在') > 0) {
      window.Trtc.endTrtc();
    }
  };

  // preEndVideo = () => {
  //   Modal.warning({
  //     title: '系统提示',
  //     content: '对方已挂断，本次通话结束。',
  //     okText: '确认',
  //     onOk: () => {
  //       this.endVideo();
  //     },
  //   });
  // }

  preAcceptVideo = () => {
    clearTimeout(this.videoStatusTimer);
    Modal.confirm({
      title: '系统提示',
      content: '对方邀请您进行视频通话，是否接入视频间？',
      okText: '确认',
      onOk: () => {
        this.initiateCall();
      },
      onCancel: () => {
        clearTimeout(this.videoStatusTimer);
      },
    });
  };

  getVideoStatus = async () => {
    clearTimeout(this.videoStatusTimer);
    if (this.isDestoryComponet) {
      return false;
    }
    const { groupId } = this.props;
    const { code, data = {} } = await Api.getVideoStatus({ groupId });
    console.log('状态接口调用了3333333', data);
    if (code == 0) {
      const { rtcSigOptions, webRtcStatus, editConfig } = this.state;
      const { status } = data;
      if (status === 'end') {
        if (webRtcStatus !== 'end') {
          // this.preEndVideo();
          this.endVideo();
        }
        editConfig[6].text = <div className={styles.iconVideo} />;
        this.setState({ editConfig });
      } else if (status === 'live') {
        editConfig[6].text = <div className={`${styles.iconVideo} ${styles.inVideo}`} />;
        this.setState({ editConfig });
        // this.preAcceptVideo();
      }
    }
    this.videoStatusTimer = setTimeout(this.getVideoStatus, 3000);
  };

  keyBindingFn = e => {
    if (e.keyCode === 13) {
      this.saveContent();
      return 'myeditor-send';
    }
    return getDefaultKeyBinding(e);
  };

  handleKeyCommand = command => {
    if (command === 'myeditor-send') {
      return 'handle';
    }
    return 'not-handle';
  };

  showMsgHistory = () => {
    const { showMsgHistory } = this.state;
    this.setState({ showMsgHistory: !showMsgHistory });
  };

  getMaskClass = () => {
    const { chatInfo = {} } = this.props;
    if (!chatInfo.id) {
      return styles.disableMask;
    }
    if (chatInfo.chatFilterType === '6' && chatInfo.id && chatInfo.isEndFeeType == 1) {
      return styles.disableMask;
    }
    return '';
  };
  //退出当前群聊
  exitCurChart = async () => {
    const { groupId } = this.props;
    const { code, data } = await Api.exitMemberToGroup({ groupId });
    if (code == 0) {
      this.setState({ showExitModal: false });
      window.location.reload();
      console.log(11);
    } else {
      return;
    }
  };

  handleCancelExitChart = () => {
    this.setState({ showExitModal: false });
  };

  render() {
    const { status, editorState, showEmoji, showAddUser, showMsgHistory, editConfig, editorDisable } = this.state;
    const { chatInfo = {}, getChatList } = this.props;
    // console.log('chatinfo211221', chatInfo)
    return (
      <Fragment>
        <div onClick={this.hideEmoji} className={this.getMaskClass()}>
          <Tooltip title={Emoji(this.emojiSelect)} placement="topLeft" visible={showEmoji}>
            <div style={{ height: 1 }} />
          </Tooltip>
          <BraftEditor
            value={editorState}
            onChange={this.handleEditorChange}
            stripPastedStyles
            onSave={this.saveContent}
            placeholder={
              !editorDisable ? (
                '按Enter发送'
              ) : status == 3 ? (
                <div style={{ color: '#f00' }}>该笔问诊订单已结束</div>
              ) : status == 6 ? (
                <div style={{ color: '#f00' }}>该笔问诊订单已取消</div>
              ) : (
                '按Enter发送'
              )
            }
            controls={editConfig}
            contentStyle={{ height: 154 }}
            pasteMode="text"
            media={{
              uploadFn: this.uploadEditorImg,
            }}
            handleKeyCommand={this.handleKeyCommand}
            keyBindingFn={this.keyBindingFn}
            ref={instance => {
              this.editorInstance = instance;
            }}
            disabled={editorDisable}
          />
        </div>
        <AddChatUser chatInfo={chatInfo} show={showAddUser} getChatList={getChatList} onCancel={() => this.setState({ showAddUser: false })} />
        {chatInfo.id ? <ChatHistory chatInfo={chatInfo} show={showMsgHistory} onCancel={this.showMsgHistory} /> : null}
        <Modal title="温馨提示" visible={this.state.showExitModal} onOk={this.exitCurChart} onCancel={this.handleCancelExitChart}>
          <p> 确定退出当前会话吗？退出后将在您的会话列表中删除该会话</p>
        </Modal>
      </Fragment>
    );
  }
}

export default Index;
