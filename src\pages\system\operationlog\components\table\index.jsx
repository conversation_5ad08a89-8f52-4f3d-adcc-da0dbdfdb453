import React, { useMemo } from 'react';
import { Table } from 'antd';
import { merge } from 'lodash';

const columns = [
  {
    title: '操作时间',
    dataIndex: 'createTimeStr',
    key: 'createTimeStr',
    sorter: true,
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
  },
  {
    title: '操作模块',
    dataIndex: 'module',
    key: 'module',
  },
  {
    title: '操作类型',
    dataIndex: 'operationType',
    key: 'operationType',
  },
  {
    title: '操作说明',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '操作状态',
    dataIndex: 'state',
    key: 'state',
    render: record => <span>{record === 'success' ? '成功' : '失败'}</span>,
  },
  {
    title: '操作IP',
    dataIndex: 'ipAddr',
    key: 'ipAddr',
  },
];

const Widget = ({ tableProps }) => {
  const tableRealProps = useMemo(
    () =>
      merge(tableProps, {
        pagination: { showTotal: total => `共${total}条`, showQuickJumper: true },
      }),
    [tableProps],
  );
  return (
    <div className="log-table">
      <Table rowKey="id" columns={columns} {...tableRealProps} />
    </div>
  );
};

export default Widget;
