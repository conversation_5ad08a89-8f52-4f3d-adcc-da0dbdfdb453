import * as Api from './api'; // 确保路径正确
const mockData = [
  { 
    id: 1,
    productLine: 'NIPT',
    sampleNumber: 'JH21321321',
    name: '张三',
    createTime: '2025-02-05 09:15'
  },
  { 
    id: 2,
    productLine: 'NIPT',
    sampleNumber: 'JH21321321',
    name: '', // 模拟空值场景
    createTime: null // 模拟空时间
  },
  // 继续添加更多符合图片格式的数据...
];
export default {
    namespace: 'sampleDetail',
    state: {
      detailVisible: false,
      currentDetail: null,
      listData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      products:[],
      subproducts:[]
    },

    reducers: {
      // 模拟数据(如需使用请取消注释)
      // const mockData = [
      //   {
      //     id: 1,
      //     productLine: 'NIPT',
      //     sampleNumber: 'JH21321321',
      //     name: '张三',
      //     createTime: '2025-02-05 09:15'
      //   },
      //   // 添加更多模拟数据...
      // ];
      updateProducts(state, { payload }) {
        return {
          ...state,
          products: payload,
        };
      },
        // 新增子产品更新reducer
    updateSubproducts(state, { payload }) {
      return {
        ...state,
        subproducts: payload,
      };
    },
      openDetail(state, { payload }) {
        return { ...state, ...payload, detailVisible: true };
      },
      closeDetail(state) {
        return { ...state, detailVisible: false };
      },
      updateDetail(state, { payload }) {
        return {
          ...state,
          listData: payload.records,
          pagination: {
            current: payload.current,
            pageSize: payload.size,
            total: payload.total
          }
        };
      }
    },
    effects: {
      *fetchDetail({ payload }, { call, put }) {
        //console.log('方法调用 fetchDetail')
        // 添加调试日志（查看图片中的分页参数）
        //console.log('分页参数:', payload); 
      
        const res = payload;
        // if (res.code === 0) {
         
        // }
        yield put({ 
          type: 'updateDetail',
          payload: {
            records: res
          }
        });
      },
      *getproducts({ payload }, { call, put }) {
        const res = yield call(Api.getproducts, payload);
        if (res.code === 0) {
          if (payload.parentId) {
            yield put({ type: 'updateSubproducts', payload: res.data });
          } else {
            yield put({ type: 'updateProducts', payload: res.data });
          }
        }
      }
    }
   
  };