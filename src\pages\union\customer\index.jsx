/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/jsx-no-duplicate-props */
import React, { useEffect, useMemo, useState, useRef } from 'react';
import { history, connect } from 'umi';
import { Form, Row, Col, Input, Select, Space, Button, Table, Modal, InputNumber, message, Radio, Popconfirm, DatePicker, Upload } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import { useAntdTable } from 'ahooks';
import { merge } from 'lodash';
import { filterObj, getDownload } from '@/utils/utils';
import queryString from 'query-string';
import moment from 'moment';
import * as CONSTANT from '@/config/constant/constant';
import Loading from '@/components/loading/Loading';
import * as Api from './service';
import { CUSTOMER_TYPE, CUSTOMER_POWER, HIS_LEVEL, CO_STATUS, CUSTOMER_TYPE_MAP, HIS_LEVEL_MAP, CO_STATUS_MAP, CUSTOMER_POWER_MAP } from './_data';
import { provinceData } from '../province';
import './style.less';

const provinceMap = provinceData.map(item => {
  return { label: item.label, value: item.label, children: item.children };
});

const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 17 },
};

const { Search } = Input;

const { RangePicker } = DatePicker;

const Dragger = Upload.Dragger;

const MIN_TABLE_HEIGHT = 218;

const Index = props => {
  const [form] = Form.useForm();
  const { permissionData = {}, dispatch, menuType = '' } = props;
  const { btns = {} } = permissionData;
  const [userList, setUserList] = useState([]);
  const [cityList, setCityList] = useState([]);
  const [provinceId, setProvinceId] = useState('');
  const [queryParam, setQueryParam] = useState({});
  const [importShow, setImportShow] = useState(false);

  const [isFold, setIsFold] = useState(true);
  const [tableHeight, setTableHeight] = useState(MIN_TABLE_HEIGHT);
  const tableContent = useRef(null);

  useEffect(() => {
    if (tableContent.current) {
      setTableHeight(Math.max(tableContent.current.offsetHeight, MIN_TABLE_HEIGHT));
    }
  }, [tableContent.current, isFold]);

  useEffect(() => {
    getAllUser();

    // 添加resize监听
    const handleResize = () => {
      if (tableContent.current) {
        setTableHeight(Math.max(tableContent.current.offsetHeight, MIN_TABLE_HEIGHT));
      }
    };

    window.addEventListener('resize', handleResize); // 添加事件监听器

    return () => {
      window.removeEventListener('resize', handleResize); // 移除事件监听器
    };
  }, []);

  useEffect(() => {
    if (provinceId) {
      const list = provinceMap.filter(item => item.value === provinceId);
      setCityList(
        list[0]?.children?.map(item => {
          return { label: item.label, value: item.label, children: item.children };
        }),
      );
    } else {
      setCityList([]);
    }
  }, [provinceId]);

  const onSearch = value => {
    setQueryParam({ ...queryParam, inputData: value });
    submit();
  };

  const getAllUser = async () => {
    const data = await Api.findAllUser({ menuType });
    if (data.code === 0) {
      setUserList(data.data || []);
    }
  };

  const fetchList = async ({ current = 1, pageSize = 10 }) => {
    try {
      const { createTime = [], ...rest } = form.getFieldsValue();
      const params = {
        pageNum: current,
        numPerPage: pageSize,
        beginTime: createTime[0] && moment(createTime[0]).format('YYYY-MM-DD'),
        endTime: createTime[0] && moment(createTime[1]).format('YYYY-MM-DD'),
        inputData: queryParam.inputData,
        ...(rest || {}),
      };
      setQueryParam(params);

      const url = menuType === 'person' ? 'fetchListPerson' : menuType === 'team' ? 'fetchListTeam' : 'fetchList';

      const data = await Api[url]({ ...params });

      return {
        total: Number(data.data.totalCount) || 0,
        list: data?.data?.recordList || [],
      };
    } catch (error) {
      console.log(error);
    }
  };

  const { loading, tableProps, search } = useAntdTable(fetchList, {
    form,
    defaultParams: [{ current: 1, pageSize: 10 }],
  });

  const tableRealProps = useMemo(
    () =>
      merge(tableProps, {
        pagination: {
          // showQuickJumper: true,
          // showSizeChanger: true,
          showTotal: total => `共 ${total} 条`,
        },
      }),
    [tableProps],
  );

  const { submit, reset } = search;

  const dataExport = async () => {
    const url = '/merchant/api/institution/export';
    const paramStr = queryString.stringify({
      ...filterObj(queryParam),
    });
    getDownload(`${url}?${paramStr}`);
  };

  const handleDelete = async id => {
    const data = await Api.deleteCustomer({
      id,
    });
    if (data.code === 0) {
      message.success(data.msg || '删除成功', 1, () => {
        submit();
      });
    }
  };

  const handleAdd = () => {
    dispatch({
      type: 'customer/save',
      payload: {
        detail: {},
      },
    });
    // history.push('/union/customer/detail');
    window.open(`${window.location.href.split('#')[0]}#/union/customer/detail?menuType=${menuType}`, '_blank');
  };

  const columns = [
    {
      title: '客户名称',
      dataIndex: 'institutionName',
      fixed: 'left',
    },
    {
      title: '客户编号',
      dataIndex: 'institutionCode',
    },
    {
      title: '客户类型',
      dataIndex: 'institutionType',
      render: v => CUSTOMER_TYPE_MAP[v].label,
    },
    {
      title: '客户等级',
      dataIndex: 'institutionLevel',
      render: v => HIS_LEVEL_MAP[v]?.label,
    },
    {
      title: '省',
      dataIndex: 'institutionProvince',
    },
    {
      title: '市',
      dataIndex: 'institutionCity',
    },
    {
      title: '合作状态',
      dataIndex: 'cooperateStatus',
      render: v => CO_STATUS_MAP[v].label,
    },
    // {
    //   title: '合同到期日期',
    //   dataIndex: 'cooperateEndDate',
    // },
    {
      title: '负责销售',
      dataIndex: 'contactsName',
    },
    {
      title: '联系人数量',
      dataIndex: 'contactsSize',
    },
    {
      title: '创建日期',
      dataIndex: 'createTime',
    },
    {
      title: '客户权限',
      dataIndex: 'institutionStatus',
      render: v => CUSTOMER_POWER_MAP[v].label,
    },
    {
      title: '录入人员',
      dataIndex: 'inputPersonName',
    },
    {
      title: '最后更新人',
      dataIndex: 'lastUpdateUserName',
    },
    {
      title: '最后更新日期',
      dataIndex: 'updateTime',
    },
    {
      title: '操作',
      dataIndex: 'id',
      fixed: 'right',
      render: id => (
        <div className="edit-btn">
          {/* {btns['/union/customer/edit'] && <span onClick={() => history.push(`/union/customer/detail?id=${id}`)}>编辑</span>} */}
          <span onClick={() => history.push(`/union/customer/detail?id=${id}&menuType=${menuType}`)}>详情</span>
          {btns['/union/customer/delete'] && (!menuType || menuType === 'team') && (
            <Popconfirm title="确定要删除当前客户吗？" onConfirm={() => handleDelete(id)} okText="确认" cancelText="取消">
              <a href="#" style={{ color: '#FF4D4F' }}>
                删除
              </a>
            </Popconfirm>
          )}
        </div>
      ),
    },
  ];

  // if (menuType === 'team') {
  //   //删除columns中的最后更新人、最后更新日期
  //   columns.splice(11, 2);
  // }

  return (
    <div className="g-page p-customer-list">
      <div className="g-query-box">
        <Form form={form} {...formItemLayout}>
          <Row gutter={16} className={isFold ? 'g-fold-box' : ''}>
            <Col span={8}>
              <Form.Item name="createTime" label="创建日期">
                <RangePicker />
              </Form.Item>
            </Col>
            {/* <Col span={8}>
              <Form.Item name="inputData" label="客户名称或编号" initialValue="">
                <Input placeholder="请输入" />
              </Form.Item>
            </Col> */}
            <Col span={8}>
              <Form.Item name="institutionType" label="客户类型" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...CUSTOMER_TYPE]} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionLevel" label="客户等级" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...HIS_LEVEL]} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionProvince" label="所在省份" initialValue="">
                <Select
                  options={[{ label: '全部', value: '' }, ...provinceMap]}
                  onChange={v => {
                    setProvinceId(v);
                    form.setFieldsValue({ institutionCity: '' });
                  }}
                  showSearch
                  filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionCity" label="所在城市" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...cityList]} showSearch filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())} />
              </Form.Item>
            </Col>
            {menuType !== 'person' && (
              <Col span={8}>
                <Form.Item name="inputPerson" label="录入人员" initialValue="">
                  <Select
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={[{ name: '全部', id: '' }, ...userList]}
                    showSearch
                    filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}
                  />
                </Form.Item>
              </Col>
            )}
            {menuType !== 'person' && (
              <Col span={8}>
                <Form.Item name="institutionContacts" label="销售人员" initialValue="">
                  <Select
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={[{ name: '全部', id: '' }, ...userList]}
                    showSearch
                    filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}
                  />
                </Form.Item>
              </Col>
            )}
            <Col span={8}>
              <Form.Item name="cooperateStatus" label="合作状态" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...CO_STATUS]} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="institutionStatus" label="客户权限" initialValue="">
                <Select options={[{ label: '全部', value: '' }, ...CUSTOMER_POWER]} />
              </Form.Item>
            </Col>
            <Col span={isFold ? 8 : menuType == 'person' ? 16 : 24} style={{ textAlign: 'right', display: 'block', marginBottom: '24px' }}>
              <Space>
                <Button type="primary" onClick={submit} disabled={loading}>
                  查询
                </Button>
                <Button onClick={reset} disabled={loading}>
                  重置
                </Button>
                <Button type="link" onClick={() => setIsFold(!isFold)}>
                  {isFold ? '展开' : '收起'}
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="container p-customer-table">
        <Row>
          <Col span={8}>
            <Search
              placeholder="请输入客户名称或编号搜索"
              allowClear
              onSearch={onSearch}
              style={{
                width: 260,
                marginBottom: 16,
              }}
            />
          </Col>
          {(!menuType || menuType === 'team') && (
            <Col span={16} style={{ textAlign: 'right' }}>
              <Space>
                {btns['/union/customer/add'] && (
                  <Button type="primary" disabled={loading} onClick={handleAdd}>
                    添加客户
                  </Button>
                )}
                {btns['/union/customer/import'] && (
                  <Button type="primary" disabled={loading} onClick={() => setImportShow(true)}>
                    批量导入
                  </Button>
                )}
                {btns['/union/customer/export'] && (
                  <Button type="primary" disabled={loading} onClick={dataExport}>
                    数据导出
                  </Button>
                )}
              </Space>
            </Col>
          )}
        </Row>
        <div className="table-box" style={{ minHeight: MIN_TABLE_HEIGHT }}>
          <div className="table-content" ref={tableContent}>
            <Table rowKey="id" {...tableRealProps} loading={loading} columns={columns} scroll={{ x: 'max-content', y: tableHeight - 55 - 48 }} />
          </div>
        </div>
      </div>
      <Modal
        title="导入客户"
        open={importShow}
        onCancel={() => {
          setImportShow(false);
        }}
        maskClosable={false}
        footer={null}
      >
        <div className="report-pdf-list">
          <div>
            请导入要添加的客户EXCEL表格，请准保数据的准确性。如果您还没有模板，请<a href="./客户批量导入模板.xls">点击下载</a>
          </div>
          <div style={{ width: '380px', height: '194px' }}>
            <Dragger
              name="idFile"
              showUploadList={false}
              action={`${CONSTANT.DOMAIN}/api/institution/batch-upload`}
              onChange={info => {
                Loading.show();
                const { response } = info.file;
                if (response) {
                  Loading.destroy();
                  if (response.code == 0) {
                    message.success(response.msg || '导入完成！', 5, () => {
                      setImportShow(false);
                      submit();
                    });
                  } else {
                    message.error(response.msg);
                  }
                }
              }}
            >
              <div>
                <InboxOutlined style={{ fontSize: 40, color: '#3F969D' }} />
              </div>
              <p style={{ marginTop: 24 }} className="ant-upload-text">
                点击这里批量上传客户信息
              </p>
            </Dragger>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default connect(state => {
  return {
    ...state.customer,
    permissionData: state.root.permissionData,
  };
})(Index);
