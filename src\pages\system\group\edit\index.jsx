import React, { Component } from 'react';
import { Button, Input, message, Select } from 'antd';
import { history as router } from 'umi';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import * as Api from './api';

@Form.create()
class Index extends Component {
  constructor(prop) {
    super(prop);

    const {
      location: { query = {} },
    } = this.props;
    this.query = query;

    this.state = {
      userList: [],
      manageList: [],
      info: {},
    };

    this.formItemLayout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 10,
      },
    };
  }

  componentDidMount() {
    this.getUsrs();
    this.groupInfo();
    this.getManageList();
  }

  getUsrs = async () => {
    const { code, data = [] } = await Api.getUserList();
    if (code == 0) {
      this.setState({ userList: data });
    }
  };

  getManageList = async () => {
    const { code, data = [] } = await Api.getManageList({
      type: 2,
      numPerPage: 10000,
    });
    if (code == 0) {
      const { recordList = [] } = data;
      this.setState({ manageList: recordList });
    }
  };

  groupInfo = async () => {
    const { teamId = '' } = this.query;
    if (!teamId) {
      return false;
    }
    const { code, data = {} } = await Api.groupInfo({ teamId });
    if (code == 0) {
      data.parentTeamsIds = (data.parentTeams || []).map(item => `${item.id}`);
      data.menberIds = (data.members || []).map(item => `${item.id}`);
      data.adminIds = (data.admins || []).map(item => `${item.id}`);
      this.setState({ info: data });
    }
  };

  submitData = async () => {
    const {
      form: { validateFields },
    } = this.props;
    validateFields(async (err, values) => {
      if (!err) {
        const { teamId = '' } = this.query;
        values.type = '1';
        values.adminIds = (values.adminIds || []).join();
        values.menberIds = (values.menberIds || []).join();
        values.parentTeamIds = (values.parentTeamIds || []).join();
        values.id = teamId;

        const requestApi = !teamId ? Api.addTeam : Api.updateTeam;

        const { code } = await requestApi(values);
        if (code == 0) {
          message.success(`${!teamId ? '添加' : '修改'}成功`);
          router.goBack();
        }
      }
    });
  };

  getUserOption = () => {
    const { userList = [] } = this.state;
    return (
      <Select
        placeholder="请选择"
        showSearch
        mode="multiple"
        filterOption={(ipt, node) => {
          if (!ipt && !ipt.length) {
            return true;
          }
          if (node.label) {
            return node.label.indexOf(ipt) >= 0;
          }
          return (node.value || '').indexOf(ipt) >= 0 || (node.children || '').indexOf(ipt) >= 0;
        }}
      >
        {userList.map(item => {
          return (
            <Select.OptGroup label={item.identityName} key={item.identityId}>
              {(item.users || []).map(user => {
                return <Select.Option key={`${user.id}`}>{user.name}</Select.Option>;
              })}
            </Select.OptGroup>
          );
        })}
      </Select>
    );
  };

  render() {
    const {
      form: { getFieldDecorator },
    } = this.props;
    const { info = {}, manageList = [] } = this.state;
    const { teamId } = this.query;
    if (!!teamId && !info.id) {
      return null;
    }
    return (
      <div className="g-page">
        <div className="container">
          <Form {...this.formItemLayout}>
            <Form.Item label="团队名称">
              {getFieldDecorator('teamName', {
                initialValue: info.teamName,
                rules: [
                  {
                    required: true,
                    message: '请输入团队名称',
                  },
                ],
              })(<Input placeholder="请输入" maxLength={100} showCount />)}
            </Form.Item>
            <Form.Item label="团队介绍">
              {getFieldDecorator('description', {
                initialValue: info.description,
              })(<Input.TextArea rows="6" placeholder="请输入" maxLength={200} showCount />)}
            </Form.Item>
            <Form.Item label="团队管理员">
              {getFieldDecorator('adminIds', {
                initialValue: info.adminIds,
                rules: [
                  {
                    required: true,
                    message: '请选择团队管理员',
                  },
                ],
              })(this.getUserOption())}
            </Form.Item>
            <Form.Item label="团队成员">
              {getFieldDecorator('menberIds', {
                initialValue: info.menberIds,
                rules: [
                  {
                    required: true,
                    message: '请选择团队成员',
                  },
                ],
              })(this.getUserOption())}
            </Form.Item>
            <Form.Item label="所属管理团队">
              {getFieldDecorator('parentTeamIds', {
                initialValue: info.parentTeamsIds,
              })(
                <Select
                  placeholder="请选择"
                  showSearch
                  mode="multiple"
                  filterOption={(ipt, node) => {
                    if (!ipt && !ipt.length) {
                      return true;
                    }
                    return (node.value || '').indexOf(ipt) >= 0 || (node.children || '').indexOf(ipt) >= 0;
                  }}
                >
                  {manageList.map(item => {
                    return <Select.Option key={item.idText}>{item.teamName}</Select.Option>;
                  })}
                </Select>,
              )}
            </Form.Item>
            <div style={{ paddingLeft: 300 }}>
              <Button type="primary" onClick={this.submitData}>
                {!teamId ? '提交' : '确定'}
              </Button>
            </div>
          </Form>
        </div>
      </div>
    );
  }
}

export default Index;
