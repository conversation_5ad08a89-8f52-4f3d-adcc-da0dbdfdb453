import { history as hashHistory } from 'umi';
import { message } from 'antd';

import * as utils from '../../utils/utils';
import * as Api from './service';

export default {
  namespace: 'trait',
  state: {
    question: {
      questionList: [],
      questionInfo: {},
      surveyList: [],
      conditionLst: [],
      answerUser: [],
      userInfo: {},
      dimensionList: [],
      questionStatistic: {},
      questionTypeList: {},
    },
    complain: {
      complainTypeList: {},
      selectTypeList: [],
      complainRecordList: [],
      complainDetail: {},
      deptList: [],
      doctorList: {},
      department: {},
    },
    pregnancy: {
      course: {
        list: {},
        detail: {},
        reserveState: {},
      },
    },
    financialPay: {
      payList: {},
      payListDetail: {},
      orderList: {},
      channelPart: {
        channelTypes: [],
        payWays: {},
      },
      totalData: {},
      imgData: {},
    },
    typeDetail: {},
    inhospital: {
      list: {},
      detail: {},
      deptList: {},
      allDeptList: {},
      cancelConfig: {},
      accountDept: {},
      menuTree: null,
    },
  },

  effects: {
    // 问卷题目类型查询
    *getQuestionType({ payload: reqData }, { call, put }) {
      const { data = {} } = yield call(Api.getQuestionType, reqData);
      yield put({ type: 'updateQuestionType', payload: data.data });
    },
    // 查询评分维度
    *fetchDimensionList({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getDimensionList, reqData);
      yield put({ type: 'updateDimensionList', payload: data });
    },
    // 保存评分维度
    *saveDimension({ payload: reqData }, { call, put }) {
      const resData = yield call(Api.saveDimension, reqData);
      if (reqData.resolve) {
        setTimeout(() => {
          reqData.resolve(resData);
        }, 200);
      }
      const { data } = yield call(Api.getDimensionList, {
        examId: reqData.examId,
      });
      yield put({ type: 'updateDimensionList', payload: data });
    },
    // 删除评分维度
    *deleteDimension({ payload: reqData }, { call, put }) {
      const resData = yield call(Api.deleteDimension, reqData);
      if (resData.data.code == 0) {
        message.success('删除成功');
        const { data } = yield call(Api.getDimensionList, {
          examId: reqData.examId,
        });
        yield put({ type: 'updateDimensionList', payload: data });
      } else {
        message.error('删除失败');
      }
    },

    *fetchQuestionCondition({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getQuestionCondition, reqData);
      yield put({ type: 'updateQuestionCondition', payload: data });
    },

    *deleteQuestionForid({ payload: reqData }, { call, put }) {
      const resData = yield call(Api.deleteQuestionForid, reqData);
      if (resData.data.code == 0) {
        message.success('删除成功');
        const { data } = yield call(Api.getQuestionList, reqData);
        yield put({ type: 'updateQuestionLst', payload: data });
      } else {
        message.error('删除失败');
      }
    },

    *fetchQuestionLst({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getQuestionList, reqData);
      yield put({ type: 'updateQuestionLst', payload: data });
    },
    // 获取问卷的统计信息
    *fetchQuestionStatistic({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getQuestionStatistic, reqData.reqData);
      if (data.code == 0) {
        reqData.resolve();
        yield put({ type: 'updateQuestionStatistic', payload: data });
      }
    },

    *fetchQuestionInfo({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getQuestionInfo, reqData);
      yield put({ type: 'updateQuestionInfo', payload: data });
    },
    *fetchUserInfo({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getUserInfo, reqData);
      yield put({ type: 'updateUserInfo', payload: data });
    },

    // 问卷列表
    *fetchSurveyList({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getPatientList, reqData);
      yield put({ type: 'updateSurveyList', payload: data });

      return data;
    },

    *saveTitleAndOption({ payload: reqData }, { call, put }) {
      const resData = yield call(Api.saveTitleAndOption, reqData);
      if (reqData.resolve) {
        setTimeout(() => {
          reqData.resolve(resData);
        }, 200);
      }
      const id = { examId: reqData.examId };
      const { data } = yield call(Api.getQuestionInfo, id);
      yield put({ type: 'updateQuestionInfo', payload: data });
    },

    *deleteTitleForid({ payload: reqData }, { call, put }) {
      yield call(Api.deleteTitleForid, reqData);
      const id = { examId: reqData.examId };
      const { data } = yield call(Api.getQuestionInfo, id);
      yield put({ type: 'updateQuestionInfo', payload: data });
    },

    *releaseQuestion({ payload: reqData }, { call, put }) {
      const resData = yield call(Api.releaseQuestion, reqData.data);
      if (resData.data.code == 0) {
        message.success(resData.data.data || '发布成功');
        const { data } = yield call(Api.getQuestionList, reqData);
        yield put({ type: 'updateQuestionLst', payload: data });
        setTimeout(() => {
          hashHistory.push({
            pathname: '/trait/survey',
            query: {
              status: reqData.status,
            },
          });
        }, 1500);
      } else {
        message.error(resData.data.msg || '发布失败');
      }
    },

    *copyQuestion({ payload: reqData }, { call, put }) {
      const resData = yield call(Api.copyQuestion, reqData);
      if (resData.data.code == 0) {
        message.success('操作成功');
        const param = {
          pageNum: 1,
          status: reqData.status,
        };
        const { data } = yield call(Api.getQuestionList, param);
        yield put({ type: 'updateQuestionLst', payload: data });
        setTimeout(() => {
          hashHistory.push({
            pathname: '/trait/survey',
            query: {
              status: reqData.status,
            },
          });
        }, 1500);
      } else {
        message.error(resData.data.msg || '操作失败');
      }
      // const { data } = yield call(Api.copyQuestion, reqData);
      // if (data.code == 0) {
      //   message.success('操作成功');
      //   setTimeout(() => {
      //     window.__HIS_MCH_HISTORY__.push({
      //       pathname: '/trait/survey',
      //       query: {
      //         status: reqData.jumpTabs,
      //       },
      //     });
      //   }, 1500);
      //   // window.__HIS_MCH_HISTORY__.push({
      //   //   pathname: '/trait/survey',
      //   //   query: {
      //   //     status: reqData.jumpTabs,
      //   //   },
      //   // });
      // }
    },

    *getQuestionAnsweruserList({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getQuestionAnsweruserList, reqData);
      yield put({ type: 'updateAnsweruserList', payload: data });
    },

    *saveQuestionToDrafts({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.saveQuestionToDrafts, reqData);
      if (data.code == 0) {
        message.success('保存成功');
        const { data: list } = yield call(Api.getQuestionList, reqData);
        yield put({ type: 'updateQuestionLst', payload: list });
        setTimeout(() => {
          hashHistory.push({
            pathname: '/trait/survey',
            query: {
              status: reqData.jumpTabs,
            },
          });
        }, 1500);
      } else {
        message.error(data.msg || '保存失败');
      }
    },
    *exportSurvey({ payload: reqData }, { call }) {
      const { data } = yield call(Api.exportSurvey, reqData.reqData);
      reqData.resolve(data);
      if (data.data && data.code == 0) {
        if (data.code == 0 && data.data.status == '1') {
          const a = document.createElement('a'); // eslint-disable-line
          a.href = data.data.url;
          a.download = 'question.xls';
          a.click();
          message.destroy();
          message.success('导出成功');
        }
      } else {
        message.error('导出失败');
      }
    },
    /**
     * 投诉接口部分
     */

    // 首页-投诉类型列表查询
    *getComplainTypeList({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getComplainTypeList, reqData);
      if (data.code == 0) {
        yield put({ type: 'updateComplainTypeList', payload: data });
      } else {
        message.error(data.msg || '查询投诉类型列表失败');
      }
    },

    // 首页-新增时保存投诉类型
    *saveComplainType({ payload }, { call }) {
      const { data } = yield call(Api.saveComplainType, payload.payload);
      if (data.code == 0) {
        payload.resolve();
        message.success('保存成功');
      } else {
        message.error(data.msg);
      }
    },

    // 首页-修改投诉类型上下架状态
    *manageType({ payload }, { call }) {
      const { data } = yield call(Api.manageType, payload.payload);
      if (data.code == 0) {
        payload.resolve();
        message.success('操作成功');
      } else {
        message.error(data.msg);
      }
    },

    // 类型详情列表页-查询投诉类型列表 (用户搜索下拉框)
    *getSelectTypeList({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getSelectTypeList, reqData);
      if (data.code == 0) {
        yield put({ type: 'saveSelectTypeList', payload: data });
      } else {
        message.error(data.msg || '查询投诉类型列表失败');
      }
    },

    // 类型详情列表页-分页查询投诉记录
    *getComplainRecordPage({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getComplainRecordPage, reqData);
      if (data.code == 0) {
        yield put({ type: 'saveComplainRecord', payload: data });
      } else {
        message.error(data.msg || '查询投诉记录失败');
      }
    },

    // 类型详情列表页-回复投诉
    *reply({ payload: reqData, success }, { call }) {
      const { data } = yield call(Api.reply, reqData);
      if (data.code == 0) {
        success();
        message.success('回复成功');
      } else {
        message.error(data.msg || '回复失败');
      }
    },
    // 具体投诉详情页-获取投诉详情
    *getComplainDetail({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getComplainDetail, reqData);
      if (data.code == 0) {
        yield put({ type: 'saveComplainDetail', payload: data });
      } else {
        message.error(data.msg || '获取投诉详情失败');
      }
    },

    // 类型详情列表页-科室树
    *departmentTree({ payload }, { call, put }) {
      const { data } = yield call(Api.departmentTree, payload);
      if (data.code === 0) {
        const tree = utils.generateLabelValueList({
          list: data.data || [],
          labelIndex: 'name',
          valueIndex: 'no',
          keyIndex: 'no',
          isLeafIndex: 'hasChild',
          childIndex: 'children',
        });
        yield put({
          type: 'saveDepartment',
          payload: {
            tree,
          },
        });
      } else {
        message.error(data.msg);
      }
    },

    //  投诉列表详情下载
    *exportComplainExcel({ payload: reqData }) {
      const data = utils.jsonToQueryString({ ...reqData });
      utils.download(`api/complain/recordexcel?${data}`, reqData);
    },
    *getDoctorList({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getDoctorList, reqData);
      if (data.code == 0) {
        yield put({ type: 'saveDoctorList', payload: data });
      } else {
        message.error(data.msg);
      }
    },

    *exportAnwsersExcel({ payload: reqData }) {
      const data = utils.jsonToQueryString({ ...reqData });
      const resData = yield utils.download(`/api/specialquestionnaire/exportanwsersexcel?${data}`);
      if (!resData.data || resData.data.code != 0) {
        message.error((resData.data || {}).msg || '下载失败');
      }
    },

    *modifyQuestionTime({ payload: reqData }, { call, put }) {
      const resData = yield call(Api.saveQuestion, reqData);
      if (resData.data.code == 0) {
        message.success('修改成功');
        const { data } = yield call(Api.getQuestionInfo, reqData);
        yield put({ type: 'updateQuestionInfo', payload: data });
      } else {
        message.error(resData.data.msg || '修改失败');
      }
    },

    *saveQuestion({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.saveQuestion, reqData);
      if (!reqData.jumpLst && data.data && data.data.examId) {
        hashHistory.push({
          pathname: '/trait/survey/createQuestion',
          query: {
            id: data.data.examId,
          },
        });
      } else if (reqData.jumpLst && data.data && data.data.examId) {
        const { data: list } = yield call(Api.getQuestionList, reqData);
        yield put({ type: 'updateQuestionLst', payload: list });
        hashHistory.push({
          pathname: '/trait/survey',
          query: {
            status: reqData.jumpTabs,
          },
        });
      } else {
        message.error('保存失败');
      }
    },

    *courseList({ payload }, { call, put }) {
      const { data } = yield call(Api.courseList, payload);
      if (data.code === 0) {
        yield put({
          type: 'savePregnancyCourse',
          payload: {
            list: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },

    *courseDetail({ payload }, { call, put }) {
      const { data } = yield call(Api.courseDetail, payload);
      if (data.code === 0) {
        yield put({
          type: 'savePregnancyCourse',
          payload: {
            detail: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },

    *submitCourseDetail({ payload }, { call }) {
      const { data } = yield call(Api.submitCourseDetail, payload);
      if (data.code === 0) {
        message.success('发布成功');
        hashHistory.push({
          pathname: '/trait/course',
        });
      } else {
        message.error(data.msg);
      }
    },
    *courseDelete({ payload }, { call, put }) {
      const { data } = yield call(Api.courseDelete, { id: payload.id });
      if (data.code === 0) {
        message.success('删除成功');
        yield put({
          type: 'courseList',
          payload: {
            ...payload.listQueryParam,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *courseReserveState({ payload }, { call, put }) {
      const { data } = yield call(Api.courseReserveState, payload);
      if (data.code === 0) {
        yield put({
          type: 'savePregnancyCourse',
          payload: {
            reserveState: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    // 查询第三方平台跳转地址
    *getThirdComUrl({ payload }, { call, put }) {
      const { data = {} } = yield call(Api.getExtraMenu, {});
      if (data.code === 0) {
        const url = (data.data || {}).url;
        if (url) {
          location.href = url;
        }
      } else {
        message.error(data.msg);
      }
    },

    // 财务缴费模块接口部分
    /* 获取医院列表 */
    // *getHisLst({ payload: reqData }, { call, put, select }) {
    //   const { data } = yield call(Api.getHisList, reqData);
    //   const loginUserInfo = yield select(state => state.root);
    //   yield put({
    //     type: 'saveHisLst',
    //     payload: {
    //       hisListData: data,
    //       ...loginUserInfo,
    //     },
    //   });
    // },
    *getPayList({ payload }, { call, put }) {
      const { data } = yield call(Api.getPayList, payload);
      if (data.code == 0) {
        yield put({
          type: 'savePayList',
          payload: {
            payList: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *getPayListDetail({ payload }, { call, put }) {
      const { data } = yield call(Api.getPayList, payload);
      if (data.code == 0) {
        yield put({
          type: 'savePayListDetail',
          payload: {
            payListDetail: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *addPayType({ payload: reqData }, { call }) {
      const { data } = yield call(Api.addPayType, reqData.reqData);
      if (data.code == 0) {
        reqData.resolve();
        setTimeout(() => {
          message.success('添加成功');
        }, 500);
      } else {
        message.error(data.msg);
      }
    },
    *getTotalData({ payload }, { call, put }) {
      const { data } = yield call(Api.getTotalData, payload);
      if (data.code == 0) {
        yield put({
          type: 'saveTotalData',
          payload: {
            totalData: data.data,
          },
        });
      }
    },
    *upOrDown({ payload: reqData }, { call }) {
      const { data } = yield call(Api.upOrDown, reqData.reqData);
      if (data.code == 0) {
        reqData.resolve();
        setTimeout(() => {
          message.success('状态修改成功');
        }, 500);
      } else {
        message.error(data.msg);
      }
    },
    *getTypeDetail({ payload: reqData }, { call, put }) {
      const { data } = yield call(Api.getTypeDetail, reqData.reqData);
      if (data.code == 0) {
        reqData.resolve();
        yield put({
          type: 'saveTypeDetail',
          payload: {
            typeDetail: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *editType({ payload: reqData }, { call }) {
      const { data } = yield call(Api.editType, reqData.reqData);
      if (data.code === 0) {
        reqData.resolve();
        message.success('状态修改成功');
      } else {
        message.error(data.msg);
      }
    },
    /* 报表下载 */
    *downloadFile({ payload: reqData }) {
      const data = utils.jsonToQueryString({ ...reqData });
      utils.download(`/api/ext/downloadfinancialstatistics?${data}`, reqData);
    },
    // 获取住院预约申请记录列表
    *getInhospitalList({ payload }, { call, put }) {
      const { data } = yield call(Api.getInhospitalList, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveInhospitalList',
          payload: data.data,
        });
      } else {
        message.error(data.msg);
      }
    },
    // 处理住院预约申请
    *handleInhospitalReserve({ payload }, { call }) {
      const { data } = yield call(Api.handleInhospitalReserve, payload.payload);
      if (data.code == 0) {
        payload.resolve(data.data);
        setTimeout(() => {
          message.success('操作成功');
        }, 500);
      } else {
        message.error(data.msg);
      }
    },
    // 获取住院预约申请记录详情
    *getInhospitalDetail({ payload }, { call, put }) {
      const { data } = yield call(Api.getInhospitalDetail, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveInhospitalDetail',
          payload: data.data,
        });
      } else {
        message.error(data.msg);
      }
    },
    // 获取住院预约科室列表
    *getReserveDeptList({ payload }, { call, put }) {
      const { data } = yield call(Api.getReserveDeptList, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveReserveDeptList',
          payload: data.data,
        });
      } else {
        message.error(data.msg);
      }
    },
    *getReserveAllDeptList({ payload }, { call, put }) {
      const { data } = yield call(Api.getReserveAllDeptList, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveReserveAllDeptList',
          payload: data.data,
        });
      } else {
        message.error(data.msg);
      }
    },
    // 导出住院预约记录列表
    *exportInhospitalExcel({ payload }) {
      const data = utils.jsonToQueryString({ ...payload });
      const resData = yield utils.download(`/api/hospitalappointment/exportrecord?${data}`);
      if (!resData.data || resData.data.code != 0) {
        message.error((resData.data || {}).msg || '下载失败');
      }
    },
    // 查询住院预约自动取消相关配置
    *getAutoCancelConfig({ payload }, { call, put }) {
      const { data } = yield call(Api.getAutoCancelConfig, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveAutoCancelConfig',
          payload: data.data,
        });
      } else {
        message.error(data.msg);
      }
    },
    // 添加或修改自动取消预约的时限
    *defineAutoCancelRule({ payload }, { call }) {
      const { data } = yield call(Api.defineAutoCancelRule, payload.payload);
      if (data.code == 0) {
        payload.resolve();
        message.success(data.msg || '操作成功');
      } else {
        message.error(data.msg);
      }
    },
    // 查询住院预约自动取消相关配置
    *getAccountDept({ payload }, { call, put }) {
      const { data } = yield call(Api.getAccountDept, payload.payload);
      if (data.code === 0) {
        payload.resolve(data.data);
        yield put({
          type: 'saveAccountDept',
          payload: data.data,
        });
      } else {
        message.error(data.msg);
      }
    },
    // 编辑住院预约自动取消相关配置
    *editAccountDept({ payload }, { call }) {
      const { data } = yield call(Api.editAccountDept, payload.payload);
      if (data.code == 0) {
        payload.resolve();
        message.success(data.msg || '操作成功');
      } else {
        message.error(data.msg);
      }
    },
    /* 报表下载 */
    *downloadfinancialdetailstatistics({ payload: reqData }) {
      const data = utils.jsonToQueryString({ ...reqData.reqData });
      const resData = yield utils.download(`/api/ext/downloadfinancialdetailstatistics?${data}`);
      reqData.resolve(resData);
      if (!resData.data || resData.data.code != 0) {
        message.error((resData.data || {}).msg || '下载失败');
      }
    },
    // 获取权限树
    *permissionList({ payload }, { call, put }) {
      const { data } = yield call(Api.permissionList, payload);
      if (data.code == 0) {
        yield put({
          type: 'saveMenuTree',
          payload: data.data,
        });
      } else {
        message.error(data.msg);
      }
    },
    *showBelongTeamSelector({ payload }, { call, put }) {
      const { data } = yield call(Api.showBelongTeamSelector, payload);
      if (data.code == 0) {
        yield put({
          type: 'save',
          payload: {
            showFlag: data.data.showFlag,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *onSaveHandleOpinion({ payload }, { call, put }) {
      const { data } = yield call(Api.onSaveHandleOpinion, payload);
      if (data.code == 0) {
        message.success('保存成功', 2);
        return true;
      } else {
        message.error(data.msg);
        return false;
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
    updateQuestionType(state, { payload: resData }) {
      return {
        ...state,
        question: {
          ...state.question,
          questionTypeList: resData,
        },
      };
    },
    updateDimensionList(state, { payload: resData }) {
      return {
        ...state,
        question: {
          ...state.question,
          dimensionList: resData,
        },
      };
    },
    updateQuestionLst(state, { payload: resData }) {
      return {
        ...state,
        question: {
          ...state.question,
          questionList: resData,
        },
      };
    },
    updateSurveyList(state, { payload: resData }) {
      return {
        ...state,
        question: {
          ...state.question,
          surveyList: resData,
        },
      };
    },
    updateSurveyInfo(state, { payload: resData }) {
      return {
        ...state,
        question: {
          ...state.question,
          surveyInfo: resData,
        },
      };
    },
    updateQuestionCondition(state, { payload: resData }) {
      return {
        ...state,
        question: {
          ...state.question,
          conditionLst: resData,
        },
      };
    },
    updateQuestionInfo(state, { payload: resData }) {
      return {
        ...state,
        question: {
          ...state.question,
          questionInfo: resData,
        },
      };
    },
    updateQuestionStatistic(state, { payload: resData }) {
      return {
        ...state,
        question: {
          ...state.question,
          questionStatistic: resData,
        },
      };
    },
    updateUserInfo(state, { payload: resData }) {
      return {
        ...state,
        question: {
          ...state.question,
          userInfo: resData,
        },
      };
    },
    updateAnsweruserList(state, { payload: resData }) {
      return {
        ...state,
        question: {
          ...state.question,
          answerUser: resData,
        },
      };
    },
    clear(state) {
      return {
        ...state,
        question: {
          ...state.question,
          questionInfo: [],
          questionStatistic: {},
        },
        complain: {
          complainTypeList: {},
        },
      };
    },
    savePregnancyCourse(state, { payload }) {
      return {
        ...state,
        pregnancy: {
          ...state.pregnancy,
          course: {
            ...state.pregnancy.course,
            ...payload,
          },
        },
      };
    },
    savePayList(state, { payload }) {
      return {
        ...state,
        financialPay: {
          ...state.financialPay,
          ...payload,
        },
      };
    },
    saveTypeDetail(state, { payload: resData }) {
      return {
        ...state,
        financialPay: {
          ...state.financialPay,
          ...resData,
        },
      };
    },
    savePayListDetail(state, { payload }) {
      return {
        ...state,
        financialPay: {
          ...state.financialPay,
          ...payload,
        },
      };
    },
    saveTotalData(state, { payload }) {
      return {
        ...state,
        financialPay: {
          ...state.financialPay,
          ...payload,
        },
      };
    },
    saveHisLst(state, { payload: resData }) {
      return {
        ...state,
        hisList: resData.hisListData,
        hisName: resData.loginUserInfo ? resData.loginUserInfo.hisName : '',
      };
    },
    // 投诉部分接口
    updateComplainTypeList(state, { payload: resData }) {
      return {
        ...state,
        complain: {
          ...state.complain,
          complainTypeList: resData.data,
        },
      };
    },
    saveSelectTypeList(state, { payload: resData }) {
      return {
        ...state,
        complain: {
          ...state.complain,
          selectTypeList: resData.data,
        },
      };
    },
    saveComplainRecord(state, { payload: resData }) {
      return {
        ...state,
        complain: {
          ...state.complain,
          complainRecordList: resData.data,
        },
      };
    },
    saveComplainDetail(state, { payload: resData }) {
      return {
        ...state,
        complain: {
          ...state.complain,
          complainDetail: resData.data,
        },
      };
    },
    // saveDeptList(state, { payload: resData }) {
    //   return {
    //     ...state,
    //     complain: {
    //       ...state.complain,
    //       deptList: resData.data,
    //     },
    //   };
    // },
    saveDepartment(state, { payload: resData }) {
      return {
        ...state,
        complain: {
          ...state.complain,
          department: resData,
        },
      };
    },
    saveDoctorList(state, { payload: resData }) {
      return {
        ...state,
        complain: {
          ...state.complain,
          doctorList: resData.data,
        },
      };
    },
    saveInhospitalList(state, { payload }) {
      return {
        ...state,
        inhospital: {
          ...state.inhospital,
          list: payload,
        },
      };
    },
    saveInhospitalDetail(state, { payload }) {
      return {
        ...state,
        inhospital: {
          ...state.inhospital,
          detail: payload,
        },
      };
    },
    saveReserveDeptList(state, { payload }) {
      return {
        ...state,
        inhospital: {
          ...state.inhospital,
          deptList: payload,
        },
      };
    },
    saveReserveAllDeptList(state, { payload }) {
      return {
        ...state,
        inhospital: {
          ...state.inhospital,
          allDeptList: payload,
        },
      };
    },
    saveAutoCancelConfig(state, { payload }) {
      return {
        ...state,
        inhospital: {
          ...state.inhospital,
          cancelConfig: payload,
        },
      };
    },
    saveAccountDept(state, { payload }) {
      return {
        ...state,
        inhospital: {
          ...state.inhospital,
          accountDept: payload,
        },
      };
    },
    saveMenuTree(state, { payload }) {
      return {
        ...state,
        inhospital: {
          ...state.inhospital,
          menuTree: payload,
        },
      };
    },
    saveMenuTree(state, { payload }) {
      return {
        ...state,
        inhospital: {
          ...state.inhospital,
          menuTree: payload,
        },
      };
    },
  },
};
