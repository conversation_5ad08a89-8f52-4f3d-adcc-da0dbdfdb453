import React from 'react';
import { connect } from 'dva';

import './style.less';

export default connect()(({ dispatch, open, children, style, className }) => {
  if (open) {
    document.getElementById('root').style.overflow = 'hidden'; // eslint-disable-line
  } else {
    document.getElementById('root').style.overflow = ''; // eslint-disable-line
  }
  return (
    <div id="drawer">
      <div
        className="drawer-mask"
        style={{ display: `${open ? '' : 'none'}` }}
        onClick={() => {
          dispatch({
            type: 'root/closeDrawer',
          });
        }}
      />
      <div className={`drawer-body${open ? ' drawer-appear' : ''} ${className || ''}`} style={style}>
        {children}
      </div>
    </div>
  );
});
