import React from 'react';
import { history } from 'umi';
import { connect } from 'dva';
import { Input, Upload, Button, message } from 'antd';
import { Form, Icon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';

import TinymceEditor from '../../../components/editor/TinymceEditor';
import Phone from './components/Phone';

import '../style.less';

import * as CONSTANT from '../../../config/constant/constant';
import * as utils from '../../../utils/utils';

const FormItem = Form.Item;
const TextArea = Input.TextArea;

class HospitalEdit extends React.Component {
  componentDidMount() {
    const { location, detail = { hisId: undefined } } = this.props;
    const hisId = utils.queryStringToJson(location.search).hisId;
    if (hisId && (hisId != detail.hisId || detail.hisId == undefined)) {
      this.hospitalDetail(hisId);
    }
  }

  componentWillUnmount() {
    this.clearHospitalDetail();
  }

  hospitalDetail = hisId => {
    const { dispatch } = this.props;
    dispatch({
      type: 'microwebsite/hospitalDetail',
      payload: {
        hisId,
      },
    });
  };

  beforeUpload = file => {
    const isImage = file.type && file.type.indexOf('image') > -1;
    if (!isImage) {
      message.error('请选择图片进行上传!');
    }
    const size = file.size / 1024 / 1024 <= 1;
    if (!size) {
      message.error('图片大小不能超过1MB!');
    }
    return isImage && size;
  };

  uploadOnChange = e => {
    const { response } = e.file;
    if (response) {
      let imgPath = '';
      if (response.code != 0) {
        message.error('上传文件失败');
      } else {
        imgPath = (response.data && response.data.url) || '';
      }
      this.saveHospitalDetail({
        imgPath,
      });
    }
  };

  validateHandler = (rule, value, callback) => {
    const { field } = rule;
    let content = '';
    switch (field) {
      case 'name':
        if (!value || value == '') {
          content = '请输入医院名称!';
        } else if (!/^[\S]{1,20}$/.test(value)) {
          content = '最多输入20个非空字符!';
        }
        break;
      case 'levelName':
        if (!value || value == '') {
          content = '请输入医院级别!';
        } else if (!/^[\S]{1,20}$/.test(value)) {
          content = '最多输入20个非空字符!';
        }
        break;
      case 'telNo':
        if (!value || value == '') {
          content = '请输入医院电话!';
        } else if (!/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(value) && !/^1(3[0-9]|4[57]|5[0-35-9]|7[01678]|8[0-9])\d{8}$/.test(value)) {
          content = '请输入正确的电话号!';
        }
        break;
      case 'address':
        if (!value || value == '') {
          content = '请输入医院地址!';
        } else if (!/^[\s\S]{1,50}$/.test(value)) {
          content = '最多输入50个字符!';
        }
        break;
      case 'imgPath':
        if (!value || value == '') {
          content = '请上传封面图片!';
        } else {
          const image = new Image(); // eslint-disable-line
          image.src = value;
          if (!image.width || image.width == 0 || !image.height || image.height == 0) {
            content = '图片资源无法加载或加载过慢!';
          } else {
            // const ratio = image.width / image.height;
            // if (ratio > 1.6 || ratio < 0.7) {
            //   content = '图片尺寸与推荐尺寸750*520相差过大!';
            // }
          }
        }
        break;
      case 'introduction':
        if (!value || value == '') {
          content = '请输入医院介绍!';
        } else if (!/^[\s\S]{1,30000}$/.test(value)) {
          content = '最多输入30000个字符!';
        }
        break;
      default:
        break;
    }
    if (content && content != '') {
      callback(content);
    } else {
      callback();
    }
  };

  saveHospitalDetail = param => {
    const { dispatch, detail = {}, form } = this.props;
    const { setFieldsValue, getFieldsValue } = form;
    setFieldsValue({
      ...param,
    });
    dispatch({
      type: 'microwebsite/saveHospital',
      payload: {
        detail: {
          ...detail,
          ...getFieldsValue(),
        },
      },
    });
  };

  clearHospitalDetail = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'microwebsite/saveHospital',
      payload: {
        detail: {},
      },
    });
  };

  submitHospitalDetail = () => {
    const {
      dispatch,
      detail,
      form: { validateFieldsAndScroll },
    } = this.props;
    validateFieldsAndScroll(err => {
      if (!err) {
        dispatch({
          type: 'microwebsite/submitHospitalDetail',
          payload: {
            hisId: detail.id,
            ...detail,
          },
        });
      }
    });
  };

  render() {
    const { detail = {}, form } = this.props;
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };
    return (
      <div className="page-hospital">
        <div className="hospital-detail" style={{ margin: '24px 170px' }}>
          <div style={{ padding: '20px 71.6px 0 24px' }}>
            <Phone detail={detail} />
          </div>
          <div className="hospital-content">
            <Form style={{ marginTop: 12 }} className="hospital-info-edit">
              <FormItem {...formItemLayout} label="医院名称">
                {getFieldDecorator('name', {
                  rules: [{ required: true, whitespace: true, validator: this.validateHandler }],
                  initialValue: detail.name,
                  onChange: e => {
                    this.saveHospitalDetail({ name: e.target.value });
                  },
                })(<Input style={{ maxWidth: 388 }} placeholder="请输入医院名称" />)}
              </FormItem>
              <FormItem label="医院级别" {...formItemLayout}>
                {getFieldDecorator('levelName', {
                  rules: [{ required: true, whitespace: true, validator: this.validateHandler }],
                  initialValue: detail.levelName,
                  onChange: e => {
                    this.saveHospitalDetail({ levelName: e.target.value });
                  },
                })(<Input style={{ maxWidth: 388 }} placeholder="请输入医院级别" />)}
              </FormItem>
              <FormItem label="医院电话" {...formItemLayout}>
                {getFieldDecorator('telNo', {
                  rules: [{ required: true, whitespace: true, validator: this.validateHandler }],
                  initialValue: detail.telNo,
                  onChange: e => {
                    this.saveHospitalDetail({ telNo: e.target.value });
                  },
                })(<Input style={{ maxWidth: 388 }} placeholder="请输入医院电话" />)}
              </FormItem>
              <FormItem label="医院地址" {...formItemLayout}>
                {getFieldDecorator('address', {
                  rules: [{ required: true, whitespace: true, validator: this.validateHandler }],
                  initialValue: detail.address,
                  onChange: e => {
                    this.saveHospitalDetail({ address: e.target.value });
                  },
                })(<TextArea style={{ maxWidth: 388, height: 50, resize: 'none' }} placeholder="请输入医院地址" />)}
              </FormItem>
              <FormItem label="封面图片" {...formItemLayout}>
                {detail.imgPath ? (
                  <div>
                    <img src={detail.imgPath} width="350px" height="174px" alt="" className="avatar" />
                    <div>
                      <Upload
                        data={{
                          partnerId: 'merchant',
                          serviceType: 'test',
                        }}
                        name="upfile"
                        action={`${CONSTANT.DOMAIN}/api/files/uploadpic`}
                        beforeUpload={this.beforeUpload}
                        showUploadList={false}
                        onChange={this.uploadOnChange}
                      >
                        <a>更换图片（建议尺寸750*520，大小不超过1M）</a>
                      </Upload>
                    </div>
                  </div>
                ) : (
                  <Upload
                    className="avatar-uploader"
                    data={{
                      partnerId: 'merchant',
                      serviceType: 'test',
                    }}
                    name="upfile"
                    action={`${CONSTANT.DOMAIN}/api/files/uploadpic`}
                    beforeUpload={this.beforeUpload}
                    showUploadList={false}
                    onChange={this.uploadOnChange}
                  >
                    <div className="avatar-uploader-trigger">
                      <Icon type="cloudupload" style={{ fontSize: 32, color: '#3F969D', marginBottom: 14 }} />
                      <div style={{ color: '#404040', lineHeight: 1.5, marginBottom: 7 }}>点击此区域上传图片</div>
                      <div style={{ color: '#919191', lineHeight: 1.5 }}>大小不超过1M，建议尺寸750*520</div>
                    </div>
                  </Upload>
                )}
                {getFieldDecorator('imgPath', {
                  rules: [{ required: true, whitespace: true, validator: this.validateHandler }],
                  initialValue: detail.imgPath,
                })(<Input style={{ display: 'none' }} />)}
              </FormItem>
              <FormItem>
                <TinymceEditor
                  id={`update-hospital-${detail.id}-editor`}
                  content={detail.introduction || ' '}
                  url={`${CONSTANT.DOMAIN}/api/his/upfile?hisId=${detail.id}`}
                  onChange={introduction => {
                    this.saveHospitalDetail({ introduction });
                  }}
                />
                {getFieldDecorator('introduction', {
                  rules: [{ validator: this.validateHandler }],
                  initialValue: detail.introduction,
                })(<Input style={{ display: 'none' }} />)}
              </FormItem>
              <FormItem>
                <Button type="primary" onClick={this.submitHospitalDetail}>
                  保存
                </Button>
                <Button
                  style={{ marginLeft: 16 }}
                  onClick={() => {
                    history.push('/microwebsite/hospital');
                  }}
                >
                  取消
                </Button>
              </FormItem>
            </Form>
          </div>
        </div>
      </div>
    );
  }
}

export default connect(state => {
  return state.microwebsite.hospital || {};
})(Form.create()(HospitalEdit));
