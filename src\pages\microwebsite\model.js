import { history } from 'umi';
import { message } from 'antd';

import * as service from './service';
import * as utils from '../../utils/utils';

const mountChildren = (pid, list = [], children, activeId) => {
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    if (item.active) {
      item.indeterminate = true;
      item.active = false;
    }
    if (activeId && item.no == activeId) {
      item.active = true;
    }
    if (item.no === pid) {
      for (let j = 0; j < children.length; j++) {
        const tmp = children[j];
        if (activeId && tmp.no == activeId) {
          tmp.active = true;
          break;
        }
      }
      item.children = children;
      item.hasChild = 0;
      break;
    } else if (item.hasChild == 0) {
      mountChildren(pid, item.children, children, activeId);
    }
  }
};

export default {
  namespace: 'microwebsite',
  state: {
    article: {
      typeList: [],
      list: {},
      detail: {},
    },
    imgData: {},
    coordinate: {
      edificeList: [],
      floorList: [],
      disabled: false,
    },
    department: {
      list: [],
      subList: [],
      detail: false,
      tree: [],
    },
    doctor: {
      list: {},
      detail: {},
    },
    hospital: {
      list: [],
      detail: {},
    },
    sortable: {
      backIds: [],
      dept: [],
      doctor: [],
    },
  },
  reducers: {
    saveArticle(state, { payload }) {
      return {
        ...state,
        article: {
          ...state.article,
          ...payload,
        },
      };
    },
    saveCoordinate(state, { payload }) {
      return {
        ...state,
        coordinate: {
          ...state.coordinate,
          ...payload,
        },
      };
    },
    saveDepartment(state, { payload }) {
      return {
        ...state,
        department: {
          ...state.department,
          ...payload,
        },
      };
    },
    saveDoctor(state, { payload }) {
      return {
        ...state,
        doctor: {
          ...state.doctor,
          ...payload,
        },
      };
    },
    saveHospital(state, { payload }) {
      return {
        ...state,
        hospital: {
          ...state.hospital,
          ...payload,
        },
      };
    },
    saveSortable(state, { payload }) {
      return {
        ...state,
        sortable: {
          ...state.sortable,
          ...payload,
        },
      };
    },
    saveImg(state, { payload: resData }) {
      return {
        ...state,
        imgData: resData,
      };
    },
  },
  effects: {
    *articleTypeList({ payload }, { call, put }) {
      const { data } = yield call(service.articleTypeList, payload);
      if (data.code === 0) {
        const typeList = utils.generateLabelValueList({
          list: data.data || [],
          labelIndex: 'typeName',
          valueIndex: 'typeId',
        });
        yield put({
          type: 'saveArticle',
          payload: {
            typeList,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *articleTypeDelete({ payload }, { call, put }) {
      const param = { ...payload.listQueryParam };
      const { data } = yield call(service.articleTypeDelete, payload);
      if (data.code === 0) {
        message.success('删除成功');
        yield put({
          type: 'articleTypeList',
          payload: param,
        });
      } else {
        message.error(data.msg);
      }
    },
    *submitArticleTypeDetail({ payload }, { call, put }) {
      const param = { ...payload.listQueryParam };
      const { data } = yield call(service.submitArticleTypeDetail, payload);
      if (data.code === 0) {
        message.success('保存成功');
        yield put({
          type: 'articleTypeList',
          payload: param,
        });
      } else {
        message.error(data.msg);
      }
    },
    *submitArticleTypeSortable({ payload }, { call, put }) {
      const { data } = yield call(service.submitArticleTypeSortable, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveArticle',
          payload: {
            typeList: [],
          },
        });
        message.success('保存成功');
        yield put({
          type: 'articleTypeList',
          payload: {
            hisId: payload.hisId,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *articleList({ payload }, { call, put }) {
      const { data } = yield call(service.articleList, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveArticle',
          payload: {
            list: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *articleDelete({ payload }, { call, put }) {
      const param = { ...payload.listQueryParam };
      const { data } = yield call(service.articleDelete, payload);
      if (data.code === 0) {
        message.success('删除成功');
        yield put({
          type: 'articleList',
          payload: param,
        });
      } else {
        message.error(data.msg);
      }
    },
    *articleDetail({ payload }, { call, put }) {
      const { data } = yield call(service.articleDetail, payload);
      if (data.code === 0) {
        const detail = data.data;
        // let content = {};
        // if (detail.content) {
        //   content = yield call(service.loadStaticJSONData, {url: detail.content});
        // }
        // detail.content = (content && content.content) || '';
        if (detail.position) {
          detail.position = detail.position.split(',');
        }
        yield put({
          type: 'saveArticle',
          payload: {
            detail,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *submitArticleDetail({ payload }, { call, put }) {
      const { data } = yield call(service.submitArticleDetail, payload);
      const { status } = payload;
      if (data.code === 0) {
        if (status == 0) {
          message.success('保存草稿成功');
        } else if (status == 1) {
          message.success('保存并发布成功');
        } else {
          message.success(data.msg);
        }
        yield put({
          type: 'saveArticle',
          payload: {
            detail: {},
          },
        });
        history.push('/microwebsite/article');
      } else {
        message.error(data.msg);
      }
    },
    *edificeList({ payload, activeId }, { call, put }) {
      const { data } = yield call(service.edificeList, payload);
      if (data.code === 0) {
        const edificeList = data.data || [];
        edificeList.forEach(edifice => {
          if (edifice.id == activeId) {
            edifice.active = true;
          }
        });
        yield put({
          type: 'saveCoordinate',
          payload: {
            edificeList: [...edificeList],
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *edificeDelete({ payload }, { call }) {
      const { data } = yield call(service.edificeDelete, payload);
      if (data.code === 0) {
        message.success('删除成功');
      } else {
        message.error(data.msg);
      }
    },
    *floorList({ payload }, { call, put }) {
      const { data } = yield call(service.floorList, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveCoordinate',
          payload: {
            floorList: data.data || [],
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *floorDelete({ payload }, { call }) {
      const { data } = yield call(service.floorDelete, payload);
      if (data.code === 0) {
        message.success('删除成功');
      } else {
        message.error(data.msg);
      }
    },
    *submitFloor({ payload }, { call, select }) {
      const { edificeList } = yield select(state => state.microwebsite.coordinate);
      let parentId = -1;
      edificeList.forEach(edifice => {
        if (edifice.active) {
          parentId = edifice.id;
        }
      });
      const { data } = yield call(service.submitDeptCoordinate, { ...payload, parentId });
      if (data.code === 0) {
        message.success('保存成功');
      } else {
        message.error(data.msg);
      }
    },
    *submitEdifice({ payload, next }, { call, put }) {
      const { hisId } = payload;
      const { data } = yield call(service.submitDeptCoordinate, payload);
      if (data.code === 0) {
        message.success('保存成功');
        if (typeof next == 'string') {
          yield put({
            type: next,
            activeId: data.data.id,
            payload: {
              hisId,
            },
          });
        } else if (next instanceof Array) {
          for (let i = 0; i < next.length; i++) {
            const effect = next[i];
            yield put({
              type: effect,
              activeId: data.data.id,
              payload: {
                hisId,
              },
            });
          }
        }
      } else {
        message.error(data.msg);
      }
    },
    *submitEdificeCoordinate({ payload }, { call }) {
      const { data } = yield call(service.submitEdificeCoordinate, payload);
      if (data.code === 0) {
        message.success('保存成功');
      } else {
        message.error(data.msg);
      }
    },
    *departmentList({ payload, success }, { call, put, select }) {
      const { pid = 0, activeId = false } = payload;
      const { data } = yield call(service.departmentList, payload);
      if (data.code === 0) {
        let list = [];
        let subList = [];
        if (pid && pid != 0) {
          list = yield select(state => {
            return state.microwebsite.department.list;
          });
          const children = utils.generateLabelValueList({
            list: data.data || [],
            labelIndex: 'name',
            valueIndex: 'no',
            isLeafIndex: 'hasChild',
            childIndex: 'children',
          });
          mountChildren(pid, list, children, activeId);
        } else {
          list = utils.generateLabelValueList({
            list: data.data || [],
            labelIndex: 'name',
            valueIndex: 'no',
            isLeafIndex: 'hasChild',
            childIndex: 'children',
          });
          list.forEach(item => {
            if (item.no == activeId) {
              item.active = true;
            }
          });
        }
        for (let i = 0; i < list.length; i++) {
          const item = list[i];
          if (item.active || item.indeterminate) {
            subList = item.children || [];
            break;
          }
        }
        yield put({
          type: 'saveDepartment',
          payload: {
            list,
            subList,
          },
        });
        if (success) {
          success();
        }
      } else {
        message.error(data.msg);
      }
    },
    *departmentTree({ payload }, { call, put }) {
      const { data } = yield call(service.departmentTree, payload);
      if (data.code === 0) {
        const tree = utils.generateLabelValueList({
          list: data.data || [],
          labelIndex: 'name',
          valueIndex: 'no',
          keyIndex: 'no',
          isLeafIndex: 'hasChild',
          childIndex: 'children',
        });
        yield put({
          type: 'saveDepartment',
          payload: {
            tree,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *departmentDelete({ payload }, { call }) {
      const { data } = yield call(service.departmentDelete, payload);
      if (data.code === 0) {
        message.success('删除成功');
      } else {
        message.error(data.msg);
      }
    },
    *batchDeleteDepts({ payload }, { call, put }) {
      const { data } = yield call(service.batchDeleteDepts, payload);
      if (data.code === 0) {
        message.success('批量删除科室成功');
        yield put({
          type: 'saveDepartment',
          payload: {
            list: [],
            subList: [],
            detail: {},
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *departmentDetail({ payload }, { call, put }) {
      const { data } = yield call(service.departmentDetail, payload);
      if (data.code === 0) {
        const detail = data.data;
        // let summary = {};
        // if (detail.summary) {
        //   summary = yield call(service.loadStaticJSONData, {url: detail.summary});
        // }
        // detail.summary = (summary && summary.content) || '';
        yield put({
          type: 'saveDepartment',
          payload: {
            detail,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *submitDepartmentDetail({ payload, success }, { call, put }) {
      const { data } = yield call(service.submitDepartmentDetail, payload);
      if (data.code === 0) {
        message.success('保存成功');
        const dept = data.data;
        yield put({
          type: 'departmentList',
          success,
          payload: {
            hisId: dept.hisId,
            activeId: dept.no,
            pid: dept.pid,
          },
        });
        yield put({
          type: 'saveDepartment',
          payload: {
            detail: dept,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *doctorList({ payload }, { call, put }) {
      const { data } = yield call(service.doctorList, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveDoctor',
          payload: {
            list: data.data,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *doctorDelete({ payload }, { call, put }) {
      const param = { ...payload.listQueryParam };
      const { data } = yield call(service.doctorDelete, payload);
      if (data.code === 0) {
        message.success('删除成功');
        yield put({
          type: 'doctorList',
          payload: param,
        });
      } else {
        message.error(data.msg);
      }
    },
    *batchDelDoctor({ payload }, { call, put }) {
      const { data } = yield call(service.batchDelDoctor, { ids: payload.ids, type: payload.type, hisId: payload.hisId });
      if (data.code === 0) {
        message.success('批量删除医生成功');
        yield put({
          type: 'doctorList',
          payload: {
            ...payload.listQueryParam,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *doctorDetail({ payload }, { call, put }) {
      const { data } = yield call(service.doctorDetail, payload);
      if (data.code === 0) {
        const detail = data.data;

        // let summary = {};
        // if (detail.summary) {
        //   summary = yield call(service.loadStaticJSONData, {url: detail.summary});
        // }
        // detail.summary = (summary && summary.content) || '';
        yield put({
          type: 'saveDoctor',
          payload: {
            detail,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *submitDoctorDetail({ payload }, { call, put }) {
      const param = { ...payload.listQueryParam };
      const { data } = yield call(service.submitDoctorDetail, payload);
      if (data.code === 0) {
        message.success('保存成功');
        yield put({
          type: 'root/closeDrawer',
        });
        yield put({
          type: 'saveDoctor',
          payload: {
            detail: {},
            list: [],
          },
        });
        yield put({
          type: 'doctorList',
          payload: param,
        });
      } else {
        message.error(data.msg);
      }
    },
    *hospitalList({ payload, next }, { call, put }) {
      const { data } = yield call(service.hospitalList, payload);
      if (data.code === 0) {
        const list = data.data;
        yield put({
          type: 'saveHospital',
          payload: {
            list,
          },
        });
        const hisId = (payload || {}).hisIdStr || list[0].hisId;
        if (list && list.length > 0 && next) {
          if (typeof next == 'string') {
            yield put({
              type: next,
              payload: {
                hisId,
              },
            });
          } else if (next instanceof Array) {
            for (let i = 0; i < next.length; i++) {
              const effect = next[i];
              yield put({
                type: effect,
                payload: {
                  hisId,
                },
              });
            }
          }
        }
      } else {
        message.error(data.msg);
      }
    },
    *hospitalDetail({ payload }, { call, put }) {
      const { data } = yield call(service.hospitalDetail, payload);
      if (data.code === 0) {
        const detail = data.data;
        // let introduction = {};
        // if (detail.introduction) {
        //   introduction = yield call(service.loadStaticJSONData, {url: detail.introduction});
        // }
        // detail.introduction = (introduction && introduction.content) || '';
        yield put({
          type: 'saveHospital',
          payload: {
            detail,
          },
        });
      } else {
        message.error(data.msg);
        yield put({
          type: 'saveHospital',
          payload: {
            detail: {},
          },
        });
      }
    },
    *submitHospitalDetail({ payload }, { call }) {
      const { data } = yield call(service.submitHospitalDetail, payload);
      if (data.code === 0) {
        message.success('保存成功');
        history.push('/microwebsite/hospital');
      } else {
        message.error(data.msg);
      }
    },
    *deptSortable({ payload }, { call, put }) {
      yield put({
        type: 'saveSortable',
        payload: {
          dept: false,
        },
      });
      const { data } = yield call(service.deptSortable, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveSortable',
          payload: {
            dept: data.data,
          },
        });
      } else {
        message.error(data.msg);
        yield put({
          type: 'saveSortable',
          payload: {
            dept: [],
          },
        });
      }
    },
    *submitDeparmentSortable({ payload }, { call, put }) {
      const { data } = yield call(service.submitDeparmentSortable, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveSortable',
          payload: {
            dept: false,
          },
        });
        message.success('保存成功');
        yield put({
          type: 'deptSortable',
          payload: {
            hisId: payload.hisId,
            pid: payload.deptNo,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *doctorSortable({ payload }, { call, put }) {
      const { data } = yield call(service.doctorSortable, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveSortable',
          payload: {
            doctor: data.data,
          },
        });
      } else {
        message.error(data.msg);
        yield put({
          type: 'saveSortable',
          payload: {
            doctor: [],
          },
        });
      }
    },
    *upFloorImg({ payload }, { call, put }) {
      const { data } = yield call(service.upFloorImg, payload);
      if (data.code === 0) {
        message.success('保存成功');
        const { floorImgUrl } = payload || {};
        yield put({
          type: 'saveHospital',
          payload: {
            detail: {
              floorImgPath: floorImgUrl,
            },
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    *submitDoctorSortable({ payload }, { call, put }) {
      const { data } = yield call(service.submitDoctorSortable, payload);
      if (data.code === 0) {
        yield put({
          type: 'saveSortable',
          payload: {
            doctor: false,
          },
        });
        message.success('保存成功');
        yield put({
          type: 'doctorSortable',
          payload: {
            hisId: payload.hisId,
            deptNo: payload.deptNo,
          },
        });
      } else {
        message.error(data.msg);
      }
    },
    /* 同步科室医生 */
    *syncDocImg({ payload: reqData }, { call, put }) {
      const { data } = yield call(service.syncDocImg, reqData.reqData);
      // hide();
      if (data.code === 0) {
        reqData.resolve();
        yield put({ type: 'saveImg', payload: data.data });
      } else {
        message.error('上传失败');
      }
    },
  },
};
