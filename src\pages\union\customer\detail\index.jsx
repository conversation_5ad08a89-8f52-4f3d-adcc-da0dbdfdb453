import React, { useEffect, useMemo, useState } from 'react';
import { Card, Space, Button, Row, Col, Form, Select, Input, Table, Popconfirm, message, InputNumber, Cascader, Modal, Checkbox } from 'antd';
import { history, connect } from 'umi';
import queryString from 'query-string';

import * as Api from '../service';
import { provinceData } from '../../province';
import './style.less';
import { CUSTOMER_TYPE, CUSTOMER_POWER, HIS_LEVEL, HIS_TYPE, FLAG_STATUS, DOC_TITLE } from '../_data';
import { set } from 'lodash';

const { TextArea } = Input;
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};
const provinceMap = provinceData.map(item => {
  return { label: item.label, value: item.label, children: item.children };
});

const Page = props => {
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const institutionProvince = Form.useWatch('institutionProvince', form);
  const institutionType = Form.useWatch('institutionType', form);
  const autoReport = Form.useWatch('autoReport', form);

  const { location, dispatch, detail = {}, userList, cityList, permissionData = {} } = props;
  const { btns = {} } = permissionData;
  const { id = '', menuType = '' } = queryString.parse(location.search);
  const [isEdit, setIsEdit] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [contactList, setContactList] = useState([]);
  const [editIndex, setEditIndex] = useState(0);

  const [disabled, setDisabled] = useState(false);

  useEffect(() => {
    dispatch({ type: 'customer/getAllUser', payload: { menuType } });
    if (id) {
      setDisabled(true);
      getDetail();
    }
  }, []);

  useEffect(() => {
    if (institutionProvince) {
      const list = provinceMap.filter(item => item.value === institutionProvince);
      dispatch({
        type: 'customer/save',
        payload: {
          cityList: list[0]?.children?.map(item => {
            return { label: item.label, value: item.label, children: item.children };
          }),
        },
      });
    }
  }, [institutionProvince]);

  // const provinceChange = () => {
  //   form.setFieldValue('institutionCity', null);
  //   const list = provinceMap.filter(item => item.value === institutionProvince);
  //   dispatch({
  //     type: 'customer/save',
  //     payload: {
  //       cityList: list[0]?.children?.map(item => {
  //         return { label: item.label, value: item.label, children: item.children };
  //       }),
  //     },
  //   });
  // };

  const getDetail = async () => {
    const data = await Api.getDetail({ id });
    if (data.code === 0) {
      dispatch({
        type: 'customer/save',
        payload: {
          detail: {
            ...data?.data,
            contactsJson: data.data?.contactsJson ? JSON.parse(data.data.contactsJson) : [],
          },
        },
      });
      form.resetFields();
      form.setFieldsValue({ ...data?.data, emailArea: data?.data?.emailArea?.split(',') });
    }
  };

  const onFinish = async values => {
    try {
      const data = await Api[`${id ? 'updateCustomer' : 'addCustomer'}`]({
        id,
        ...values,
        emailArea: values?.emailArea?.join(','),
        contactsName: userList.filter(item => item.id === values?.institutionContacts)[0]?.name,
        contactsJson: JSON.stringify(detail.contactsJson),
      });
      if (data.code !== 0) {
        return;
      }
      message.success('操作成功！', 1, () => {
        if (id) {
          history.goBack();
        } else {
          dispatch({
            type: 'customer/save',
            payload: { detail: {} },
          });
          setTimeout(() => {
            form.resetFields();
            window.scrollTo(0, 0);
          }, 0);
        }
      });
      // clearDetail();
    } catch (error) {
      console.log(error);
    }
  };

  const onAddFinish = values => {
    const { mobile, name } = values;
    const { contactsJson = [] } = detail;
    // 如果mobile和name和之前都重复, 提示，如果isEdit为true则要排除当前编辑的项
    let contacts = contactsJson;
    if (isEdit) {
      contacts = contactsJson.toSpliced(editIndex, 1);
    }
    const isRepeat = contacts.some(item => item.mobile === mobile && item.name === name);
    if (isRepeat) {
      message.error('已存在相同的联系人，请重新输入');
      return;
    }

    if (isEdit) {
      let list = contactsJson;
      list[editIndex] = values;
      dispatch({
        type: 'customer/save',
        payload: {
          detail: {
            ...detail,
            contactsJson: [...list],
          },
        },
      });
      setIsModalOpen(false);
      return;
    }
    dispatch({
      type: 'customer/save',
      payload: {
        detail: {
          ...detail,
          contactsJson: contactsJson.length > 0 ? [...contactsJson, values] : [values],
        },
      },
    });
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    addForm.resetFields();
  };

  const handleEdit = (record, index) => {
    addForm.setFieldsValue(record);
    setIsEdit(true);
    setEditIndex(index);
    setIsModalOpen(true);
  };

  const saveDetail = values => {
    dispatch({
      type: 'customer/save',
      payload: {
        detail: { ...detail, ...values },
      },
    });
  };

  const handleDelete = index => {
    const list = detail.contactsJson.toSpliced(index, 1);
    dispatch({
      type: 'customer/save',
      payload: {
        detail: {
          ...detail,
          contactsJson: [...list],
        },
      },
    });
  };

  const clearDetail = () => {
    dispatch({
      type: 'customer/save',
      payload: { detail: {} },
    });
    if (window.history.length > 1) {
      history.goBack();
    } else {
      window.opener = null;
      window.close();
    }
  };

  // 处理报告邮件发送
  const handleReportMailChange = checked => {
    form.setFieldValue('autoReport', checked ? '1' : '0');
    // setIsReportMail(checked);
    if (!checked) {
      form.setFieldValue('mailAddress', '');
    }
  };

  const columns = [
    {
      title: '联系人姓名',
      dataIndex: 'name',
    },
    {
      title: '联系人电话',
      dataIndex: 'mobile',
    },
    {
      title: '科室/部门',
      dataIndex: 'deptName',
    },
    {
      title: '职称',
      dataIndex: 'title',
    },
    {
      title: '备注',
      dataIndex: 'notes',
    },
    {
      title: '操作',
      dataIndex: 'id',
      width: 160,
      render: (id, record, index) => (
        <div>
          <Button type="link" onClick={() => handleEdit(record, index)} disabled={disabled}>
            编辑
          </Button>
          <Popconfirm title="确定要删除当前联系人吗？" onConfirm={() => handleDelete(index)} okText="确认" cancelText="取消">
            <Button danger type="link" disabled={disabled}>
              删除
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  return (
    <div className="p-cus-detail">
      <Form form={form} layout="vertical" scrollToFirstError onFinish={onFinish} onValuesChange={changedValues => saveDetail(changedValues)} initialValues={detail} disabled={disabled}>
        <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
          {id && (!menuType || menuType === 'team') ? (
            <Button
              type="link"
              style={{ textAlign: 'right', width: '100%' }}
              disabled={false}
              onClick={() => history.push(`/union/changerecord?moduleCode=com.anyi.his.entity.TAnyiInstitution&moduleId=${id}`)}
            >
              变更记录
            </Button>
          ) : null}
          <Card title="客户信息">
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="institutionName" label="客户名称" rules={[{ required: true }]}>
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="institutionType" label="客户类型" rules={[{ required: true }]}>
                  <Select placeholder="请选择" options={CUSTOMER_TYPE} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="institutionContacts" label="负责销售">
                  <Select
                    allowClear
                    fieldNames={{ label: 'name', value: 'id' }}
                    options={userList}
                    placeholder="请选择"
                    showSearch
                    filterOption={(input, option) => (option?.name ?? '').toLowerCase().includes(input.toLowerCase())}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="institutionProvince" label="客户所属省份" rules={[{ required: true }]}>
                  <Select
                    placeholder="请选择"
                    options={provinceMap}
                    showSearch
                    filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                    onChange={() => form.setFieldValue('institutionCity', null)}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="institutionCity" label="客户所属城市" rules={[{ required: true }]}>
                  <Select placeholder="请选择" options={cityList} showSearch filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="institutionArea" label="客户详细地址">
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="institutionContactsName" label="客户对接人">
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="deptName" label="对接人所属部门/科室">
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="jobName" label="对接人职位">
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="institutionContactsTel" label="客户对接人电话">
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="institutionStatus" label="客户权限" rules={[{ required: true }]} initialValue={1}>
                  <Select placeholder="请选择" options={CUSTOMER_POWER} />
                </Form.Item>
              </Col>
            </Row>
          </Card>
          {institutionType === 1 ? (
            <Card title="医院信息">
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name="nature" label="医院性质">
                    <Select placeholder="请选择" options={HIS_TYPE} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="institutionLevel" label="医院等级">
                    <Select placeholder="请选择" options={HIS_LEVEL} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="isPrenatal" label="产前诊断机构">
                    <Select placeholder="请选择" options={FLAG_STATUS} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="mechanismType" label="产筛机构">
                    <Select placeholder="请选择" options={FLAG_STATUS} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="outpatientNum" label="门诊量（例/年）">
                    <InputNumber placeholder="请输入" min={0} precision={0} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="sieveNum" label="唐筛量（例/年）">
                    <InputNumber placeholder="请输入" min={0} precision={0} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="deliveryNum" label="分娩量（例/年）">
                    <InputNumber placeholder="请输入" min={0} precision={0} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="produceNum" label="产诊量（例/年）">
                    <InputNumber placeholder="请输入" min={0} precision={0} />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          ) : (
            ''
          )}
          <Card title="客户邮寄信息">
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="emailArea" label="客户邮寄地址地区">
                  <Cascader
                    placeholder="请选择"
                    options={provinceData}
                    fieldNames={{ label: 'label', value: 'label', children: 'children' }}
                    showSearch={{
                      filter: (inputValue, path) => path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1),
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="emailAddr" label="客户邮寄详细地址">
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="emailContactsName" label="客户邮寄联系人">
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="emailContactsTel"
                  label="客户邮寄联系人电话"
                  rules={[
                    {
                      pattern: /^0\d{2,4}\d{7,8}$|^1[3456789]\d{9}$/,
                      message: '电话格式不正确',
                    },
                  ]}
                >
                  <Input placeholder="请输入" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
          <Card title="报告邮件发送">
            <Row gutter={16}>
              <Col span={2}>
                <Form.Item name="autoReport">
                  <Checkbox defaultChecked={autoReport === '1'} onChange={e => handleReportMailChange(e.target.checked)}>
                    报告邮件发送
                  </Checkbox>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="mailAddress"
                  label=""
                  rules={[
                    {
                      pattern: /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/,
                      message: '邮箱格式不正确',
                    },
                  ]}
                >
                  <Input placeholder="请输入接收报告邮箱" disabled={disabled || autoReport === '0' || !autoReport} />
                </Form.Item>
              </Col>
            </Row>
          </Card>
          <Card
            title="客户联系人"
            extra={
              <Button
                type="primary"
                onClick={() => {
                  setIsEdit(false);
                  addForm.resetFields();
                  setIsModalOpen(true);
                }}
              >
                + 添加联系人
              </Button>
            }
          >
            <Table rowKey="id" columns={columns} dataSource={detail.contactsJson} pagination={false} />
          </Card>
          <Card title="备注">
            <Form.Item name="notes">
              <TextArea rows={5} placeholder="请输入" maxLength={500} />
            </Form.Item>
          </Card>
          {(!menuType || menuType === 'team') && (
            <Row>
              <Col span={24} style={{ textAlign: 'center' }}>
                {disabled ? (
                  btns['/union/customer/edit'] && (
                    <Button
                      disabled={false}
                      type="primary"
                      onClick={() => {
                        setDisabled(false);
                      }}
                    >
                      编辑
                    </Button>
                  )
                ) : (
                  <Space>
                    <Button onClick={clearDetail}>取消</Button>
                    <Button type="primary" htmlType="submit">
                      确定
                    </Button>
                  </Space>
                )}
              </Col>
            </Row>
          )}
        </Space>
      </Form>

      <Modal
        title={`${isEdit ? '编辑' : '添加'}联系人`}
        open={isModalOpen}
        onOk={() => {
          addForm.submit();
        }}
        onCancel={handleCancel}
      >
        <Form form={addForm} onFinish={onAddFinish} {...formItemLayout}>
          <Form.Item
            name="name"
            label="姓名"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input placeholder="请输入" />
          </Form.Item>
          <Form.Item
            name="mobile"
            label="联系电话"
            rules={[
              // {
              //   required: true,
              // },
              {
                pattern: /^0\d{2,4}\d{7,8}$|^1[3456789]\d{9}$/,
                message: '联系电话格式不正确',
              },
            ]}
          >
            <Input placeholder="请输入" />
          </Form.Item>
          <Form.Item name="deptName" label="科室/部门">
            <Input placeholder="请输入" />
          </Form.Item>
          <Form.Item name="title" label="职称">
            <Select placeholder="请选择" options={DOC_TITLE} />
          </Form.Item>
          <Form.Item name="notes" label="备注">
            <TextArea placeholder="请输入" rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default connect(state => {
  return { ...state.customer, permissionData: state.root.permissionData };
})(Page);
