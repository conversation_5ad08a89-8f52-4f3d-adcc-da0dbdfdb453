import React from 'react';
import { Link } from 'react-router-dom';
import { history } from 'umi';
import { connect } from 'dva';
import { Select, DatePicker, Modal, Button, Input, Table, message } from 'antd';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import moment from 'moment';

import '../style.less';

const DataRangePicker = DatePicker.RangePicker;
const FormItem = Form.Item;
const confirm = Modal.confirm;

const positionType = { 1: '患者端科普', 2: '医院动态', 3: '医生端科普' };

export default connect(state => {
  return {
    list: state.microwebsite.article.list,
    typeList: state.microwebsite.article.typeList,
    permissionData: state.root.permissionData,
  };
})(
  Form.create()(
    class ArticleList extends React.Component {
      state = {
        selectedRowKeys: [],
        typeId: '',
      };

      componentDidMount() {
        this.articleTypeList();
        this.articleList();
      }

      componentWillUnmount() {
        this.props.dispatch({
          type: 'microwebsite/saveArticle',
          payload: {
            typeList: [],
            list: {},
            detail: {},
          },
        });
      }

      onSelectChange = selectedRowKeys => {
        this.setState({ selectedRowKeys });
      };

      articleTypeList = () => {
        const { dispatch } = this.props;
        dispatch({
          type: 'microwebsite/articleTypeList',
        });
      };

      articleList = (pageNum = 1, typeId = '') => {
        const { dispatch, form } = this.props;
        const params = form.getFieldsValue();
        const title = params.title || '';
        const startTime = (params.dateArr.length > 0 && params.dateArr[0].format('YYYY-MM-DD')) || '';
        const endTime = (params.dateArr.length > 1 && params.dateArr[1].format('YYYY-MM-DD')) || '';
        dispatch({
          type: 'microwebsite/articleList',
          payload: {
            pageNum,
            title,
            position: params.position || '',
            typeId,
            startTime,
            endTime,
          },
        });
      };

      articleSingleDelete = id => {
        this.articleDelete([id]);
      };

      articleBatchDelete = () => {
        const $this = this;
        const articleIds = this.state.selectedRowKeys;
        if (!articleIds || articleIds.length == 0) {
          message.error('请选择要删除的文章');
        } else {
          confirm({
            title: <div>确定删除所选文章吗？</div>,
            content: '删除文章后用户端将同步不显示，你还要继续吗？',
            onOk() {
              $this.articleDelete(articleIds);
            },
          });
        }
      };

      articleDelete = articleIds => {
        const { dispatch, form, list } = this.props;
        const params = form.getFieldsValue();
        const title = params.title || '';
        const startTime = (params.dateArr.length > 0 && params.dateArr[0].format('YYYY-MM-DD')) || '';
        const endTime = (params.dateArr.length > 1 && params.dateArr[1].format('YYYY-MM-DD')) || '';
        dispatch({
          type: 'microwebsite/articleDelete',
          payload: {
            articleIds,
            listQueryParam: {
              pageNum: list.currentPage,
              title,
              typeId: this.state.typeId,
              startTime,
              endTime,
            },
          },
        });
      };

      reset = () => {
        this.props.form.resetFields();
        this.articleList();
      };

      render() {
        const { typeList = [], list = {}, form, permissionData = {} } = this.props;
        const { btns = {} } = permissionData;
        const { getFieldDecorator } = form;

        const { selectedRowKeys } = this.state;
        const rowSelection = {
          selectedRowKeys,
          onChange: this.onSelectChange,
        };

        const articleTable = {
          style: { marginBottom: 14 },
          columns: [
            {
              title: '文章标题',
              dataIndex: 'title',
              width: 300,
            },
            {
              title: '文章类型',
              dataIndex: 'typeName',
            },
            {
              title: '展示位置',
              dataIndex: 'position',
              render: v =>
                v
                  ? v
                      .split(',')
                      .map(position => positionType[position])
                      .join('；')
                  : '',
            },
            {
              title: '发布时间',
              dataIndex: 'createTime',
              render: v => {
                try {
                  return moment(v).format('YYYY-MM-DD HH:mm:ss');
                } catch (error) {
                  return '';
                }
              },
            },
            {
              title: '状态',
              dataIndex: 'statusDesc',
            },
            {
              title: '操作',
              width: 260,
              render: record => {
                return (
                  <span>
                    {btns['/microwebsite/article/edit'] ? <Link to={`/microwebsite/article/edit?id=${record.articleId}`}>修改</Link> : null}
                    <span style={{ color: '#4C9CDF' }}>丨</span>
                    {btns['/microwebsite/article/detail'] ? <Link to={`/microwebsite/article/detail?id=${record.articleId}`}>查看</Link> : null}
                    <span style={{ color: '#4C9CDF' }}>丨</span>
                    {btns['/microwebsite/article/delete'] ? (
                      <a
                        onClick={() => {
                          const $this = this;
                          confirm({
                            title: (
                              <div>
                                确定删除文章
                                <span style={{ color: '#f57f17' }}>{record.title}</span>
                                吗？
                              </div>
                            ),
                            content: '删除文章后用户端将同步不显示，你还要继续吗？',
                            onOk() {
                              $this.articleSingleDelete(`${record.articleId}`);
                            },
                          });
                        }}
                      >
                        删除
                      </a>
                    ) : null}
                  </span>
                );
              },
            },
          ],
        };

        return (
          <div className="page-article-list">
            <div className="article-list-body">
              <div className="article-list-option">
                <Form
                  layout="inline"
                  onSubmit={e => {
                    e.preventDefault();
                    this.articleList();
                  }}
                >
                  <FormItem>
                    {getFieldDecorator('position', { initialValue: null })(
                      <Select
                        placeholder="展示位置"
                        style={{ width: 211 }}
                        options={[
                          { label: '全部', value: '' },
                          { label: '患者端科普', value: '1' },
                          { label: '医院动态', value: '2' },
                          { label: '医生端科普', value: '3' },
                        ]}
                      />,
                    )}
                  </FormItem>
                  <FormItem>{getFieldDecorator('title', { initialValue: '' })(<Input placeholder="文章标题" style={{ width: 211 }} />)}</FormItem>
                  <FormItem>
                    {getFieldDecorator('dateArr', { initialValue: [] })(
                      <DataRangePicker
                        style={{ width: 248, float: 'left', textAlign: 'left', margin: '4px 0' }}
                        format="YYYY-MM-DD"
                        disabledDate={date => {
                          const maxDate = moment(this.endDate, 'YYYY-MM-DD');
                          return date.valueOf() > maxDate.valueOf();
                        }}
                      />,
                    )}
                  </FormItem>
                  <FormItem>
                    <Button type="primary" size="default" htmlType="submit">
                      查询
                    </Button>
                  </FormItem>
                  <FormItem>
                    <Button onClick={this.reset} size="default">
                      重置
                    </Button>
                  </FormItem>
                </Form>
              </div>
              <div className="article-list-option">
                <div className="tab-label">文章类型：</div>
                <div className="tab-btns">
                  <span
                    className={`tab-btn${this.state.typeId == '' ? ' active' : ''}`}
                    onClick={() => {
                      this.setState({
                        typeId: '',
                      });
                      this.articleList(1, '');
                    }}
                  >
                    全部
                  </span>
                  {typeList &&
                    typeList.map(item => {
                      return (
                        <span
                          key={item.typeId}
                          className={`tab-btn${this.state.typeId == item.typeId ? ' active' : ''}`}
                          onClick={() => {
                            this.setState({
                              typeId: item.typeId,
                            });
                            this.articleList(1, item.typeId);
                          }}
                        >
                          {item.typeName}
                        </span>
                      );
                    })}
                  {btns['/microwebsite/article/types'] ? <Link to="/microwebsite/article/types">编辑文章类型</Link> : null}
                </div>
              </div>
              <div className="article-list">
                <div style={{ paddingBottom: 8 }}>
                  {btns['/microwebsite/article/new'] ? (
                    <Button
                      type="primary"
                      style={{ marginRight: 12 }}
                      onClick={() => {
                        history.push('/microwebsite/article/new');
                      }}
                    >
                      添加文章
                    </Button>
                  ) : null}
                  {btns['/microwebsite/article/batchDelete'] ? <Button onClick={this.articleBatchDelete}>批量删除</Button> : null}
                </div>
                <Table
                  rowSelection={rowSelection}
                  rowKey={row => row.articleId}
                  columns={articleTable.columns}
                  dataSource={list.recordList}
                  pagination={{
                    showQuickJumper: true,
                    defaultCurrent: list.currentPage || 1,
                    current: list.currentPage || 1,
                    total: list.totalCount || 0,
                    showTotal: () => {
                      return `共${list.totalCount || 0}条`;
                    },
                    onChange: pageNum => {
                      this.articleList(pageNum, this.state.typeId);
                    },
                  }}
                />
              </div>
            </div>
          </div>
        );
      }
    },
  ),
);
