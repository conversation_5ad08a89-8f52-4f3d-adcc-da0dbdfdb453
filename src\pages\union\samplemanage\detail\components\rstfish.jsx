/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, Table, Button, Select, DatePicker } from 'antd';
import moment from 'moment';
import '../../index.less';

const { TextArea } = Input;
const { Option } = Select;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { rstfishtemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, rstfishtemplate: { ...rstfishtemplate, ...payload } },
      },
    });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="申请单信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="采集的样本">
                <Checkbox.Group value={rstfishtemplate.field1 ? rstfishtemplate.field1.split(',') : []} onChange={v => changeData({ field1: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="外周血">外周血</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="绒毛">绒毛</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="羊水">羊水</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="皮肤">皮肤</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="脐血">脐血</Checkbox>
                    </Col>
                    <Col span={18}>
                      <div className="flex-box">
                        <Checkbox value="其他">其他</Checkbox>
                        <div className="flex-box">
                          <Input placeholder="请输入" value={rstfishtemplate.field2} onChange={e => changeData({ field2: e.target.value })} />
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
