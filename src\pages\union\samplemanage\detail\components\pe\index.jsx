/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { Tabs, Collapse, Form, Row, Col, Input, Radio, Checkbox, DatePicker, Select } from 'antd';
import moment from 'moment';
import '../../index.less';

const { Option } = Select;
const { TextArea } = Input;

const Index = props => {
  const { dispatch, detail = {} } = props;
  const { pejhbbTemplate = {} } = detail;

  const changeData = payload => {
    dispatch({
      type: 'sample/save',
      payload: {
        detail: { ...detail, pejhbbTemplate: { ...pejhbbTemplate, ...payload } },
      },
    });
  };

  return (
    <div className="other-msg">
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="其他基本信息" key="1">
          <Row gutter={[16, 0]}>
            <Col span={8}>
              <Form.Item label="月经周期（天）">
                <Input placeholder="请输入" value={pejhbbTemplate.field1} onChange={e => changeData({ field1: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="末次月经">
                <DatePicker
                  format="YYYY-MM-DD"
                  style={{ width: '100%' }}
                  value={pejhbbTemplate.field2 ? moment(pejhbbTemplate.field2) : null}
                  onChange={(date, dateString) => changeData({ field2: dateString })}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="孕周（周、天）">
                <div className="flex-box">
                  <Input placeholder="请输入周" value={pejhbbTemplate.field3} onChange={e => changeData({ field3: e.target.value })} />
                  <Input placeholder="请输入天" value={pejhbbTemplate.field4} onChange={e => changeData({ field4: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="分娩史">
                <Radio.Group value={pejhbbTemplate.field5} onChange={e => changeData({ field5: e.target.value })}>
                  <Radio value="初产妇">初产妇</Radio>
                  <Radio value="经产妇">经产妇</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="分娩日期">
                <DatePicker
                  format="YYYY-MM-DD"
                  style={{ width: '100%' }}
                  value={pejhbbTemplate.field6 ? moment(pejhbbTemplate.field6) : null}
                  onChange={(date, dateString) => changeData({ field6: dateString })}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="分娩孕周（周、天）">
                <div className="flex-box">
                  <Input placeholder="请输入周" value={pejhbbTemplate.field7} onChange={e => changeData({ field7: e.target.value })} />
                  <Input placeholder="请输入天" value={pejhbbTemplate.field8} onChange={e => changeData({ field8: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="孕次">
                <Input placeholder="请输入" value={pejhbbTemplate.field9} onChange={e => changeData({ field9: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="产次">
                <Input placeholder="请输入" value={pejhbbTemplate.field10} onChange={e => changeData({ field10: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="吸烟史">
                <Select placeholder="请选择" value={pejhbbTemplate.field11} onChange={v => changeData({ field11: v })}>
                  <Option value="否">否</Option>
                  <Option value="是">是</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="饮酒">
                <Select placeholder="请选择" value={pejhbbTemplate.field12} onChange={v => changeData({ field12: v })}>
                  <Option value="否">否</Option>
                  <Option value="是">是</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="怀孕方式">
                <Select placeholder="请选择" value={pejhbbTemplate.field13} onChange={v => changeData({ field13: v })}>
                  <Option value="自然受孕">自然受孕</Option>
                  <Option value="辅助生殖-使用避排卵药">辅助生殖-使用避排卵药</Option>
                  <Option value="体外受精">体外受精</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="单胎或多胎妊娠">
                <Select placeholder="请选择" value={pejhbbTemplate.field14} onChange={v => changeData({ field14: v })}>
                  <Option value="单胎妊娠">单胎妊娠</Option>
                  <Option value="双胞胎">双胞胎</Option>
                  <Option value="多胎妊娠">多胎妊娠</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="既往病史" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item>
                <Checkbox.Group value={pejhbbTemplate.field15 ? pejhbbTemplate.field15.split(',') : []} onChange={v => changeData({ field15: v.join(',') })}>
                  <Row gutter={[20, 8]}>
                    <Col span={6}>
                      <Checkbox value="子痫前期史">子痫前期史</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="慢性高血压">慢性高血压</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="慢性肾病">慢性肾病</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="I型糖尿病">I型糖尿病</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="II型糖尿病">II型糖尿病</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="系统性红斑狼疮">系统性红斑狼疮</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="抗磷脂综合症">抗磷脂综合症</Checkbox>
                    </Col>
                    <Col span={6}>
                      <Checkbox value="妊娠期高血压">妊娠期高血压</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="患者母亲是否有子痫前期史">
                <Select placeholder="请选择" value={pejhbbTemplate.field16} onChange={v => changeData({ field16: v })}>
                  <Option value="否">否</Option>
                  <Option value="是">是</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse defaultActiveKey="1" expandIconPosition="end">
        <Collapse.Panel header="辅助检查" key="1">
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item label="双臂血压测量日期">
                <DatePicker
                  format="YYYY-MM-DD"
                  style={{ width: '50%' }}
                  value={pejhbbTemplate.field17 ? moment(pejhbbTemplate.field17) : null}
                  onChange={(date, dateString) => changeData({ field17: dateString })}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="左臂血压第一次（mmHg）">
                <div className="flex-box">
                  <Input placeholder="请输入高压值" value={pejhbbTemplate.field19} onChange={e => changeData({ field19: e.target.value })} />
                  <Input placeholder="请输入低压值" value={pejhbbTemplate.field18} onChange={e => changeData({ field18: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="右臂血压第一次（mmHg）">
                <div className="flex-box">
                  <Input placeholder="请输入高压值" value={pejhbbTemplate.field21} onChange={e => changeData({ field21: e.target.value })} />
                  <Input placeholder="请输入低压值" value={pejhbbTemplate.field20} onChange={e => changeData({ field20: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="左臂血压第二次（mmHg）">
                <div className="flex-box">
                  <Input placeholder="请输入高压值" value={pejhbbTemplate.field23} onChange={e => changeData({ field23: e.target.value })} />
                  <Input placeholder="请输入低压值" value={pejhbbTemplate.field22} onChange={e => changeData({ field22: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="右臂血压第二次（mmHg）">
                <div className="flex-box">
                  <Input placeholder="请输入高压值" value={pejhbbTemplate.field25} onChange={e => changeData({ field25: e.target.value })} />
                  <Input placeholder="请输入低压值" value={pejhbbTemplate.field24} onChange={e => changeData({ field24: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="B超测量日期">
                <DatePicker
                  format="YYYY-MM-DD"
                  style={{ width: '50%' }}
                  value={pejhbbTemplate.field26 ? moment(pejhbbTemplate.field26) : null}
                  onChange={(date, dateString) => changeData({ field26: dateString })}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="双顶径 BPD（mm）">
                <Input placeholder="请输入" value={pejhbbTemplate.field27} onChange={e => changeData({ field27: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="头臂长 RCL（mm）">
                <Input placeholder="请输入" value={pejhbbTemplate.field28} onChange={e => changeData({ field28: e.target.value })} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="子宫动脉搏动指数UTPI（ua）">
                <div className="flex-box">
                  <Input placeholder="左边指数" value={pejhbbTemplate.field29} onChange={e => changeData({ field29: e.target.value })} />
                  <Input placeholder="右边指数" value={pejhbbTemplate.field30} onChange={e => changeData({ field30: e.target.value })} />
                </div>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="检测项目">
                <Checkbox.Group value={pejhbbTemplate.field31 ? pejhbbTemplate.field31.split(',') : []} onChange={v => changeData({ field31: v.join(',') })}>
                  <Row gutter={[0, 8]}>
                    <Col span={24}>
                      <Checkbox value="PlGF、sFIt-1">PlGF、sFIt-1</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="检测孕周范围">
                <Checkbox.Group value={pejhbbTemplate.field32 ? pejhbbTemplate.field32.split(',') : []} onChange={v => changeData({ field32: v.join(',') })}>
                  <Row gutter={[0, 8]}>
                    <Col span={24}>
                      <Checkbox value="12-34+6周">12-34+6周</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="样本性状">
                <Checkbox.Group value={pejhbbTemplate.field33 ? pejhbbTemplate.field33.split(',') : []} onChange={v => changeData({ field33: v.join(',') })}>
                  <Row gutter={[0, 8]}>
                    <Col span={8}>
                      <Checkbox value="正常">正常</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="溶血">溶血</Checkbox>
                    </Col>
                    <Col span={8}>
                      <Checkbox value="脂血">脂血</Checkbox>
                    </Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};
export default connect(state => {
  return {
    btns: state.root.permissionData.btns,
    ...state.sample,
  };
})(Index);
