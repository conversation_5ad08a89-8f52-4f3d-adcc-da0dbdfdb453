import React from 'react';
import { connect } from 'dva';
import { history } from 'umi';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import * as utils from '@/utils/utils';
import SystemAccountEdit from './components/SystemAccountEdit';

const type = 'new';

class IIHOC extends SystemAccountEdit {
  constructor(props) {
    super(props);
    this.state = {
      type: type == 'personal' ? 'detail' : type,
      ...this.state,
    };
  }

  componentDidMount() {
    if (typeof super.componentDidMount === 'function') {
      super.componentDidMount();
    }
    const {
      dispatch,
      location,
      loginUserInfo: { userId: loginUserId },
    } = this.props;
    const userId = utils.queryStringToJson(location.search).userId;
    if (type == 'modify') {
      if (loginUserId == userId) {
        history.push(`/system/account/detail?userId=${loginUserId}`);
      } else {
        dispatch({
          type: 'system/accountInfo',
          payload: {
            userId,
          },
        });
      }
    } else if (type == 'personal' || type == 'detail') {
      dispatch({
        type: 'system/accountInfo',
        payload: {
          userId: userId || loginUserId,
        },
      });
    } else {
      // dispatch({
      //   type: 'system/manageHisList',
      // });
      // dispatch({
      //   type: 'system/permissionList',
      // });
    }
  }

  componentWillUnmount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'system/clearAccountDetail',
    });
  }

  render() {
    // 确保type通过props传递给父组件
    this.props.type = type;

    if (type != 'personal') {
      return <div style={{ padding: '0 146px' }}>{super.render()}</div>;
    } else {
      return super.render();
    }
  }
}

export default connect(state => {
  return {
    loginUserInfo: state.root.loginUserInfo || {},
    detail: state.system.account.detail,
  };
})(Form.create()(IIHOC));
