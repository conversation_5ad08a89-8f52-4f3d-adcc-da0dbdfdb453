import React, { useEffect, useState } from 'react';
import { Form, Card, Space, Row, Col, Image, Divider, Button, Modal, message, Input } from 'antd';
import queryString from 'query-string';
import { connect } from 'umi';
import * as Api from '../service';
import { USER_STATUS_MAP } from '../_data';
import './style.less';

const { TextArea } = Input;

const Page = props => {
  const [form] = Form.useForm();
  const { id = '' } = queryString.parse(props.location.search);
  const { permissionData = {} } = props;
  const { btns = {} } = permissionData;
  const [detail, setDetail] = useState({});
  const [orderDetail, setOrderDetail] = useState({});
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (id) {
      getDetail();
    }
  }, []);

  const getDetail = async () => {
    try {
      const { data = {} } = await Api.getApplyDetail({ id });
      setDetail(data);
      if (data.extOrderNo) {
        getorderdetail(data.extOrderNo);
      }
    } catch (error) {
      console.log(error);
    }
  };
  const getorderdetail = async id => {
    try {
      const { data = {} } = await Api.getorderdetail({ busType: 'outside_check', id });
      setOrderDetail(data);
    } catch (error) {
      console.log(error);
    }
  };

  const confirm = () => {
    Modal.confirm({
      title: '报名审核',
      content: '确认报名信息无误，审核通过？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        approve();
      },
    });
  };

  const approve = async () => {
    try {
      const data = await Api.approve({ id });
      if (data.code === 0) {
        await message.success(data.msg, 1);
        getDetail();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onFinish = async values => {
    try {
      const data = await Api.reject({ id, ...values });
      if (data.code === 0) {
        setIsModalOpen(false);
        await message.success(data.msg, 1);
        getDetail();
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="page-apply-detail">
      <Space direction="vertical" style={{ display: 'flex' }} size={24}>
        <Card title="申请人信息" bordered={false}>
          <Row gutter={[16, 24]}>
            <Col span={6}>姓名：{detail.name}</Col>
            <Col span={6}>身份证号码：{detail.idNo}</Col>
            <Col span={6}>联系电话：{detail.phone}</Col>
            <Col span={6}>微信/邮箱：{detail.contact}</Col>
            <Col span={6}>学历：{detail.education}</Col>
            <Col span={6}>所学专业：{detail.major}</Col>
            <Col span={6}>职务：{detail.jobName}</Col>
            <Col span={6}>职称：{detail.jobTitle}</Col>
            <Col span={6}>所在单位：{detail.organization}</Col>
            <Col span={6}>所在科室：{detail.deptName}</Col>
            <Col span={6}>申请时间：{detail.createTime}</Col>
          </Row>
        </Card>
        {detail.totalFee > 0 ? (
          <Card title="缴费信息" bordered={false}>
            <Row gutter={[16, 24]}>
              {detail.amount ? <Col span={6}>报名学习月数：{detail.amount}</Col> : null}
              <Col span={6}>缴费金额：{(detail.totalFee / 100).toFixed(2)}元</Col>
              {detail.extOrderNo ? (
                <>
                  <Col span={6}>缴费时间：{orderDetail.paydTime}</Col>
                  <Col span={6}>支付流水号：{orderDetail.agtOrdNum}</Col>
                </>
              ) : (
                <Col span={6}>缴费状态：未支付</Col>
              )}
            </Row>
          </Card>
        ) : null}
        <Card title="其他信息" bordered={false}>
          <div className="title">进修申请表照片：</div>
          <Space>
            {detail.jobCertificate?.split(',')?.map((item, index) => {
              return <Image key={index} width={104} src={item} />;
            })}
          </Space>
          <div className="title">执业证书照片：</div>
          {detail.credentials?.split(',')?.map((item, index) => {
            return <Image key={index} width={104} src={item} />;
          })}
          <div className="title">主要学习与工作经历：</div>
          <p>{detail.workExp}</p>
          <div className="title">本人专业水平：</div>
          <p>{detail.professionalism}</p>
          {detail.docDesc ? (
            <>
              <div className="title">{detail.docDesc}：</div>
              <p>{detail.docs}</p>
              {detail.docsImg?.split(',')?.map((item, index) => {
                return <Image key={index} width={104} src={item} />;
              })}
            </>
          ) : (
            ''
          )}

          {btns['/trait/train/examine'] && detail.status === 1 && (
            <>
              <Divider />
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <Space>
                  <Button type="primary" onClick={confirm}>
                    通过
                  </Button>
                  <Button onClick={() => setIsModalOpen(true)}>驳回</Button>
                </Space>
              </div>
            </>
          )}
        </Card>
        {detail.status !== 1 && (
          <Card title="审核信息" bordered={false}>
            <Row gutter={[16, 24]}>
              <Col span={8}>审核时间：{detail.auditTime}</Col>
              <Col span={8}>审核人：{detail.auditUser}</Col>
              <Col span={8}>审核结果：{USER_STATUS_MAP[detail.status]?.label}</Col>
              {detail.status === 3 && <Col span={8}>驳回原因：{detail.reason}</Col>}
            </Row>
          </Card>
        )}
      </Space>
      <Modal title="报名审核" open={isModalOpen} onOk={form.submit} onCancel={() => setIsModalOpen(false)}>
        <p style={{ marginBottom: '24px' }}>请输入驳回原因，该内容用户可见。</p>
        <Form form={form} onFinish={onFinish}>
          <Form.Item name="reason" label="驳回原因" rules={[{ required: true }]}>
            <TextArea placeholder="请输入" width="240px" rows={4} maxLength={50} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Page);
