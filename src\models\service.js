import * as utils from '@/utils/utils';

export function loginUserInfo(param) {
  return utils.request(
    '/api/userinfo/merchantuser',
    {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    },
    false,
  );
}

export function logout(param) {
  return utils.request('/api/logout', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function getPermissionData(param) {
  return utils.request(
    '/api/userinfo/resources',
    {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    },
    false,
  );
}

export function getMenuTree(param) {
  return utils.request(
    '/api/userpatient/userpermission',
    {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    },
    false,
  );
}

export function getTypeData(param) {
  return utils.request('/api/order/getHisBusTypeList', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取第三方平台url
 * @param {object} param
 */
export function getExtraMenu(param) {
  return utils.request('/api/hiond/getssourl', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
