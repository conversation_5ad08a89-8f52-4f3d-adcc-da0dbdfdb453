import { query as queryUsers, queryCurrent, userpermission, logout, queryResource } from '@/services/user';
import defDocImg from '@/assets/icon-group-video.png';

export default {
  namespace: 'user',

  state: {
    list: [],
    currentUser: {},
    authResource: {},
  },

  effects: {
    *fetch(_, { call, put }) {
      const response = yield call(queryUsers);
      yield put({
        type: 'save',
        payload: response,
      });
    },
    *logout(_, { call, put }) {
      const { code } = yield call(logout);
      if (code == 0) {
        window.location.replace('/merchant/index.html#/login');
      }
    },
    *fetchUserInfo(_, { call, put }) {
      const { hash = '' } = window.location;
      if (hash.includes('inquery/chatinfo')) {
        return;
      }
      const { code, data = {} } = yield call(queryCurrent);
      if (code == 0) {
        yield put({
          type: 'saveCurrentUser',
          payload: {
            allianceName: data.allianceName,
            name: data.userName,
            accountImg: data.accountImg || defDocImg,
            account: data.account,
            id: data.userId,
            allianceId: data.allianceId,
          },
        });
      }
    },
    *fetchResource(_, { call, put }) {
      const { hash = '' } = window.location;
      if (hash.includes('inquery/chatinfo')) {
        return;
      }
      const { code, data = {} } = yield call(queryResource);
      if (code == 0) {
        yield put({
          type: 'update',
          payload: { authResource: data },
        });
      }
    },
    *getUserPoint(_, { call, put, select }) {
      // const { code } = yield call(logout);
      // if (code == 0) {
      //   window.location.replace('/merchant/mch/index.html#/login');
      // }
      const { menuResource } = yield select(state => state.user);
      const zhuanzhenMenuList = menuResource['/patient'] || [];
      const zzMenu = zhuanzhenMenuList.find(i => {
        return i.path === '/patient/referralservice/manage';
      });
      // if (zzMenu) {
      //   zzMenu.point = !zzMenu.point;
      // }
      yield put({
        type: 'update',
        payload: { menuResource: Object.assign({}, menuResource) },
      });
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        list: action.payload,
      };
    },
    update(state, { payload = {} }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveCurrentUser(state, action) {
      return {
        ...state,
        currentUser: action.payload || {},
      };
    },
    changeNotifyCount(state, action) {
      return {
        ...state,
        currentUser: {
          ...state.currentUser,
          notifyCount: action.payload.totalCount,
          unreadCount: action.payload.unreadCount,
        },
      };
    },
  },
};
