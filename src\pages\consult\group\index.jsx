import React, { useEffect, useState } from 'react';
import { Form, Input, InputNumber, Button, Table, Select, Modal, message, Popconfirm } from 'antd';
import queryString from 'query-string';
import { useAntdTable } from 'ahooks';
import { merge } from 'lodash';
import moment from 'moment';
import { connect } from 'umi';
import area from '@/utils/area.js';
import * as Api from './service';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const Index = props => {
  const [addForm] = Form.useForm();
  const { institutionId, contractId, contractNumber, institutionName } = queryString.parse(props.location.search);
  const { permissionData = {}, roleType = '' } = props;
  const { btns = {} } = permissionData;
  const type = roleType === 'doc' ? 1 : 0;

  const [id, setId] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [list, setList] = useState([]);

  useEffect(() => {
    fetchList();
  }, []);

  const handleCancel = () => {
    setIsModalOpen(false);
    setId('');
    addForm.resetFields();
  };

  const handleEdit = async record => {
    addForm.setFieldsValue({
      ...record,
      unionPro: JSON.parse(record.unionPro),
    });
    setId(record.id);
    setIsModalOpen(true);
  };

  const onAddFinish = async values => {
    const url = id ? 'updateGroup' : 'addGroup';
    try {
      const data = await Api[url]({
        ...values,
        unionPro: JSON.stringify(values.unionPro),
        id,
        type,
      });
      if (data.code === 0) {
        message.success('操作成功');
        handleCancel();
        fetchList();
      }
    } catch (error) {}
  };

  const handleDelete = async id => {
    const data = await Api.deleteGroup({
      groupId: id,
      type,
    });
    if (data.code === 0) {
      message.success(data.msg || '删除成功', 1, () => {
        fetchList();
      });
    }
  };

  const fetchList = async () => {
    try {
      const params = {
        institutionId,
        contractId,
        type,
      };
      const data = await Api.fetchList({ ...params });
      setList(data.data || []);
    } catch (error) {
      console.log(error);
    }
  };

  const columns = [
    {
      title: '分组名称',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '负责省份',
      dataIndex: 'unionPro',
      render: text => {
        const arr = text ? JSON.parse(text) : [];
        return arr.join('、');
      },
    },
    {
      title: '操作',
      dataIndex: 'id',
      width: 150,
      render: (id, record) =>
        record.isDefault !== '0' && (
          <div className="edit-btn">
            {btns['/union/collection/edit'] && (
              <span
                onClick={() => {
                  handleEdit(record);
                }}
              >
                编辑
              </span>
            )}
            {btns['/union/collection/delete'] && (
              <Popconfirm title="确定要删除当前分组吗？" onConfirm={() => handleDelete(id)} okText="确认" cancelText="取消">
                <a href="#" style={{ color: '#FF4D4F' }}>
                  删除
                </a>
              </Popconfirm>
            )}
          </div>
        ),
    },
  ];

  return (
    <div className="g-page p-product-list">
      <div className="p-product-table">
        <div className="flex-box">
          {btns['/union/collection/add'] && (
            <Button
              type="primary"
              onClick={() => {
                setIsModalOpen(true);
              }}
            >
              添加分组
            </Button>
          )}
        </div>
        <Table rowKey="id" dataSource={list} columns={columns} pagination={false} scroll={{ x: 'max-content' }} />
      </div>

      <Modal
        title={`${id ? '编辑' : '添加'}分组`}
        open={isModalOpen}
        onOk={() => {
          addForm.submit();
        }}
        onCancel={handleCancel}
        className="add-sampling-modal"
      >
        <Form form={addForm} onFinish={onAddFinish} {...formItemLayout}>
          <Form.Item name="name" label="分组名称" rules={[{ required: true }]}>
            <Input placeholder="请输入" />
          </Form.Item>
          <Form.Item name="unionPro" label="负责省份" rules={[{ required: true }]}>
            <Select placeholder="请选择" mode="multiple" options={area} fieldNames={{ label: 'label', value: 'label' }} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default connect(state => {
  return {
    permissionData: state.root.permissionData,
  };
})(Index);
