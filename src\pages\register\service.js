import * as utils from '../../utils/utils';

/**
 * 加载静态JSON数据文件
 * @param {*} param
 */
export function loadStaticJSONData({ url }) {
  return utils.loadStaticJSONData(url, {
    method: 'POST',
  });
}

/**
 * 新增his需要的身体部位
 */
export function addHisBodyPart(param) {
  return utils.request('/api/guide/savehisbodypart', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 更新his需要的身体部位
 */
export function updateHisBodyPart(param) {
  return utils.request('/api/guide/updatebodypart', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 新增或保存病症
 */
export function saveOrUpdateDisease(param) {
  return utils.request('/api/guide/saveorupdatedisease', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除his需要的身体部位
 */
export function deleteHisBodyPart(param) {
  return utils.request('/api/guide/delhisbodypart', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/* 通过部位id获取部位详情*/
export function getBodypartDetail(param) {
  return utils.request('/api/guide/getbodypartdetail', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取部位所有病例
 */
export function getDiseasesList(param) {
  return utils.request('/api/guide/getdiseases', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取所有部位
 */
export function getBodyPartsList(param) {
  return utils.request('/api/guide/getbodyparts', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取病症详情
 */
export function getDiseaseDetail(param) {
  const id = param.diseaseId;
  return utils.request('/api/guide/getdiseasedetail', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...{ diseaseId: id } }),
  });
}

/**
 * 新增或者更新推荐科室
 */
export function saveRecommendDept(param) {
  return utils.request('/api/guide/saveorupdaterecommenddept', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除病症
 */
export function delDisease(param) {
  return utils.request('/api/guide/deldiseases', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除科室
 */
export function delRecommendDept(param) {
  return utils.request('/api/guide/deldepts', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 获取所有科室
 */
export function getDeptList(param) {
  return utils.request('/api/deptinfo/getdeptlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 批量导出
 */
export function exportDisExcel(param) {
  return utils.request('/api/guide/exportdisexcel', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 批量导入
 */
export function uploadExcel(param) {
  return utils.request('/api/guide/uploadexcel', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 科室列表
 * @param {*} param
 */
export function departmentList(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/deptinfo/getdeptlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 科室结构树
 * @param {*} param
 */
export function departmentTree(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/deptinfo/getdepttree', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 科室信息
 * @param {*} param
 */
export function departmentDetail(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/deptinfo/getdeptinfo', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 医院列表
 * @param {*} param
 */
export function hospitalList(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/userinfo/gethislist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除科室
 * @param {*} param
 */
export function departmentDelete(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/deptinfo/deletedept', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 编辑科室
 * @param {*} param
 */
export function submitDepartmentDetail(param = {}) {
  param.showMode = 'register';
  const { method } = param;
  if (method == 'modify') {
    delete param.method;
    return utils.request('/api/deptinfo/modifydept', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  } else {
    delete param.method;
    return utils.request('/api/deptinfo/savedept', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  }
}

/**
 * 医生列表
 * @param {*} param
 */
export function doctorList(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/doctorinfo/getdoctorlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 医生详细信息
 * @param {*} param
 */
export function doctorDetail(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/doctorinfo/getdoctorinfo', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 删除医生
 * @param {*} param
 */
export function doctorDelete(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/doctorinfo/deletedoctor', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 批量删除医生
 * @param {*} param
 */
export function batchDelDoctor(param = {}) {
  return utils.request('/api/doctorinfo/batchdeletedoctors', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

export function submitDoctorDetail(param = {}) {
  param.showMode = 'register';
  delete param.listQueryParam;
  const { method } = param;
  if (method == 'modify') {
    delete param.method;
    return utils.request('/api/doctorinfo/modifydoctor', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  } else {
    delete param.method;
    param.deptNo = param.newDeptNo;
    return utils.request('/api/doctorinfo/savedoctor', {
      method: 'POST',
      body: utils.jsonToQueryString({ ...param }),
    });
  }
}

/**
 * 挂号医生信息同步到微网站
 * @param {*} param
 */
export function syncDoctorToMicro(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/doctorinfo/synctomicrosite', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 科室排序列表
 * @param {*} param
 */
export function deptSortable(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/deptinfo/getdeptlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 提交科室排序
 * @param {*} param
 */
export function submitDeparmentSortable(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/deptinfo/deptsort', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 挂号科室信息同步到微网站
 * @param {*} param
 */
export function syncDepartmentToMicro(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/deptinfo/synctomicrosite', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 医生排序列表
 * @param {*} param
 */
export function doctorSortable(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/doctorinfo/getalldoctorlist', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 提交医生排序
 * @param {*} param
 */
export function submitDoctorSortable(param = {}) {
  param.showMode = 'register';
  return utils.request('/api/doctorinfo/doctorsort', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 病症特定搜索
 * @param {*} param
 */
export function queryDiseases(param = {}) {
  return utils.request('/api/guide/querydiseases', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 身体部位批量导出
 */
export function exportDisBodyExcel(param) {
  return utils.request('/api/guide/exportbodyxls', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 身体部位批量导入
 */
export function uploadDisBodyExcel(param) {
  return utils.request('/api/guide/uploadbodyxls', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 季节病症删除
 */
export function removeDisSeason(param) {
  return utils.request('/api/guide/rmdisseason', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 手动同步医生信息
 */
export function syncDoctorInfo(param) {
  return utils.request('/api/doctorinfo/manualsyncdoctor', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 手动同步科室信息
 */
export function syncDeptInfo(param) {
  return utils.request('/api/deptinfo/manualsyncdept', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}

/**
 * 批量上传医生头像
 */
export function syncDocImg(param) {
  return utils.request('/api/doctorinfo/autoormanualsyncdocimg', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 触发异步拉取科室/医生信息
 */
export function asyncManual(param) {
  return utils.request('/api/async/manualsync', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 获取异步拉取科室/医生结果
 */
export function getManualStatus(param) {
  return utils.request('/api/async/manualstatus', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
/**
 * 获取异步拉取科室/医生结果
 */
export function batchDeleteDepts(param) {
  return utils.request('/api/deptinfo/batchdeletedepts', {
    method: 'POST',
    body: utils.jsonToQueryString({ ...param }),
  });
}
